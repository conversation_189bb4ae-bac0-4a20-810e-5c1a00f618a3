import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import type { CustomJwtSessionClaims } from "@/types/auth";
import { isAdminFromSessionClaims } from "@/lib/org-role";

const isOnboardingRoute = createRouteMatcher(["/onboarding"]);
const isPublicRoute = createRouteMatcher([
    "/",
    "/login(.*)",
    "/signup(.*)",
    "/sso-callback",
    "/contact",
    "/subscriptions",
    "/privacy-policy",
    "/terms-of-use",
    "/scholarships",
    "/scholarships/(.*)",
    "/api/webhooks(.*)"
]);
const isAdminRoute = createRouteMatcher(["/admin(.*)"]);
const isDashboardRoute = createRouteMatcher(["/dashboard(.*)"]);

export default clerkMiddleware(async (auth, req) => {
    const url = new URL(req.url);

    if (url.pathname.startsWith("/api/")) {
        return NextResponse.next();
    }

    if (url.pathname === "/") {
        const milga = url.searchParams.get("milga");
        if (milga) {
            const safeMilga = /^[_a-zA-Z0-9]+$/.test(milga) ? milga : null;
            if (safeMilga) {
                if (safeMilga.startsWith("_")) {
                    return Response.redirect(
                        new URL(`/scholarships/${encodeURIComponent(safeMilga.slice(1))}`, req.url)
                    );
                } else {
                    return Response.redirect(new URL(`/scholarships/groups/${encodeURIComponent(safeMilga)}`, req.url));
                }
            }
        }
    }

    const { userId, sessionClaims, redirectToSignIn } = await auth();
    const isOnboardingComplete = (sessionClaims as unknown as CustomJwtSessionClaims)?.metadata?.onboardingComplete;

    if (userId && isOnboardingRoute(req) && !isOnboardingComplete) {
        return NextResponse.next();
    }

    if (!userId && !isPublicRoute(req)) return redirectToSignIn({ returnBackUrl: req.url });

    if (userId && !isOnboardingComplete) {
        const onboardingUrl = new URL("/onboarding", req.url);
        return NextResponse.redirect(onboardingUrl);
    }

    if (isDashboardRoute(req) && !userId) {
        return Response.redirect(new URL("/login", req.url));
    }

    if (isAdminRoute(req)) {
        if (!isAdminFromSessionClaims(sessionClaims)) {
            return Response.redirect(new URL("/dashboard", req.url));
        }
    }
});

export const config = {
    matcher: ["/((?!_next/static|_next/image|favicon.ico|.*\.(?:svg|png|jpg|jpeg|gif|webp|webmanifest)$).*)"]
};
