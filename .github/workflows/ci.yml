name: CI

on:
    push:
        branches:
            - main
    pull_request:
        branches:
            - main

jobs:
    build:
        runs-on: ubuntu-latest
        permissions:
            contents: write

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "22"
                  cache: "yarn"

            - name: Install dependencies
              run: yarn install --frozen-lockfile

            - name: Run linter
              run: yarn lint

            - name: Run tests
              run: |
                  if yarn test:ci; then
                    echo "status=passing" >> $GITHUB_ENV
                  else
                    echo "status=failing" >> $GITHUB_ENV
                    exit 1
                  fi

            - name: Generate coverage badge
              run: |
                  mkdir -p .badge
                  COVERAGE=$(jq -r '.total.statements.pct' coverage/coverage-summary.json)
                  if (( $(echo "$COVERAGE > 90" | bc -l) )); then
                    COLOR="brightgreen"
                  elif (( $(echo "$COVERAGE > 75" | bc -l) )); then
                    COLOR="green"
                  elif (( $(echo "$COVERAGE > 60" | bc -l) )); then
                    COLOR="yellow"
                  else
                    COLOR="red"
                  fi
                  curl "https://img.shields.io/badge/coverage-$COVERAGE%25-$COLOR.svg" > .badge/coverage.svg

            - name: Commit and push badge
              if: github.ref == 'refs/heads/main'
              run: |
                  git config user.name "github-actions[bot]"
                  git config user.email "github-actions[bot]@users.noreply.github.com"
                  git add .badge/coverage.svg

                  if ! git diff --staged --quiet; then
                      echo "Changes detected in build badge. Committing and pushing..."
                      COVERAGE=$(jq -r '.total.statements.pct' coverage/coverage-summary.json)
                      git commit -m "Update coverage badge to ${COVERAGE}%" || { echo "::error::Failed to commit badge changes."; exit 1; }
                      git push || { echo "::error::Failed to push badge changes."; exit 1; }
                  else
                      echo "No changes to build badge. Skipping commit."
                  fi
