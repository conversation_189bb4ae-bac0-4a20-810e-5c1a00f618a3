import { useCallback, useEffect, useState } from "react";
import { getDocumentTypes, type DocumentTypeWithUrls } from "@/app/actions/document-type-actions";

export function useDocumentTypes() {
    const [items, setItems] = useState<DocumentTypeWithUrls[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchDocumentTypes = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getDocumentTypes();
            if (result.success && result.data) {
                setItems(result.data);
            } else {
                setError(result.error || "Failed to fetch document types");
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchDocumentTypes();
    }, [fetchDocumentTypes]);

    return { items, loading, error, refetch: fetchDocumentTypes };
}
