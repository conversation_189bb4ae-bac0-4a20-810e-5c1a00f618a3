"use client";

import { useState, useEffect, useCallback } from "react";
import { ScholarshipGroup } from "@/lib/scholarship-group-constants";
import { getScholarshipGroupsWithCounts } from "@/app/actions/scholarship-group-actions";

interface ScholarshipGroupsState {
    items: ScholarshipGroup[];
    loading: boolean;
    error: Error | null;
    displayedGroups: ScholarshipGroup[];
    hasMore: boolean;
    loadMore: () => void;
    refetch: () => Promise<void>;
}

const ITEMS_PER_PAGE = 8;

export function useScholarshipGroups(): ScholarshipGroupsState {
    const [state, setState] = useState<ScholarshipGroupsState>({
        items: [],
        displayedGroups: [],
        loading: true,
        error: null,
        hasMore: false,
        loadMore: () => {},
        refetch: async () => {}
    });

    const loadMore = useCallback(() => {
        setState((prev) => {
            const nextCount = prev.displayedGroups.length + ITEMS_PER_PAGE;
            const currentCount = prev.displayedGroups.length;

            const newItems = prev.items.slice(currentCount, nextCount).map((group) => ({
                ...group,
                isNew: true
            }));

            const existingItems = prev.displayedGroups.map((group) => ({
                ...group,
                isNew: false
            }));

            const newDisplayedGroups = [...existingItems, ...newItems];

            return {
                ...prev,
                displayedGroups: newDisplayedGroups,
                hasMore: newDisplayedGroups.length < prev.items.length
            };
        });
    }, []);

    const fetchScholarshipGroups = useCallback(async () => {
        try {
            setState((prev) => ({ ...prev, loading: true }));

            const result = await getScholarshipGroupsWithCounts();
            if (!result.success || !result.data) {
                throw new Error(result.error || "Failed to fetch scholarship groups");
            }
            const allGroups = result.data;
            const initialDisplayCount = Math.min(ITEMS_PER_PAGE, allGroups.length);
            const initialGroups = allGroups.slice(0, initialDisplayCount).map((group) => ({
                ...group,
                isNew: true
            }));

            setState((prev) => ({
                ...prev,
                items: allGroups,
                displayedGroups: initialGroups,
                loading: false,
                hasMore: allGroups.length > initialDisplayCount,
                loadMore,
                refetch: fetchScholarshipGroups
            }));

            setTimeout(() => {
                setState((prev) => ({
                    ...prev,
                    displayedGroups: prev.displayedGroups.map((group) => ({
                        ...group,
                        isNew: false
                    }))
                }));
            }, 1000);
        } catch (err) {
            setState((prev) => ({
                ...prev,
                error: err instanceof Error ? err : new Error("Failed to fetch scholarship groups"),
                loading: false
            }));
        }
    }, [loadMore]);

    useEffect(() => {
        fetchScholarshipGroups();
    }, [fetchScholarshipGroups]);

    return state;
}
