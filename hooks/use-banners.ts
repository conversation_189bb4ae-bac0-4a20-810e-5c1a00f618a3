"use client";

import { useAuth } from "@clerk/nextjs";
import { type Tables } from "@/types/database.types";
import { useState, useEffect } from "react";
import { getActiveBanners } from "@/app/actions/banner-actions";

type Banner = Tables<"banners">;

interface BannersState {
    items: Banner[];
    loading: boolean;
    error: string | null;
}

export function useBanners() {
    const [state, setState] = useState<BannersState>({
        items: [],
        loading: true,
        error: null
    });

    const { isLoaded } = useAuth();

    useEffect(() => {
        if (!isLoaded) return;

        let cancelled = false;

        async function fetchBanners() {
            setState((prev) => ({ ...prev, loading: true, error: null }));

            try {
                const result = await getActiveBanners();

                if (cancelled) return;

                if (!result.success || !result.data) {
                    throw new Error(result.error || "Failed to fetch banners");
                }

                setState({
                    items: result.data,
                    loading: false,
                    error: null
                });
            } catch (err) {
                if (!cancelled) {
                    setState({
                        items: [],
                        loading: false,
                        error: err instanceof Error ? err.message : "An unknown error occurred"
                    });
                }
            }
        }

        fetchBanners();

        return () => {
            cancelled = true;
        };
    }, [isLoaded]);

    return state;
}
