import { useCallback, useEffect, useState } from "react";
import { getCoupons } from "@/app/actions/coupon-actions";
import { type Coupon } from "@/lib/coupon-constants";

export function useCoupons() {
    const [items, setItems] = useState<Coupon[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchCoupons = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const result = await getCoupons();
            if (result.success && result.data) {
                setItems(result.data);
            } else {
                throw new Error(result.error || "Failed to fetch coupons");
            }
        } catch (err) {
            console.error("Error fetching coupons:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchCoupons();
    }, [fetchCoupons]);

    return { items, loading, error, refetch: fetchCoupons };
}
