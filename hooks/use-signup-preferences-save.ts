"use client";

import { useUser } from "@clerk/nextjs";
import { useEffect, useRef, useState } from "react";

import { saveSignupPreferences } from "@/app/actions/signup-actions";
import { useSignupPreferences } from "@/contexts/signup-preferences-context";
import { getUserClaim, setUserClaim, UserClaimKey } from "@/utils/user-claims-client";
import { getSupabaseClient } from "@/utils/supabase/client";

interface SaveResult {
    success: boolean;
    error?: string;
    usedDefaults?: boolean;
}

export function useSignupPreferencesSave() {
    const { user } = useUser();
    const { state, clearPreferences, hasPreferences } = useSignupPreferences();
    const [saveResult, setSaveResult] = useState<SaveResult | null>(null);
    const [isSaving, setIsSaving] = useState(false);
    const hasTriedSave = useRef(false);

    const checkExistingPreferences = async (userId: string): Promise<boolean> => {
        try {
            const supabase = getSupabaseClient();
            const [acceptedTerms, subscribedToUpdates] = await Promise.all([
                getUserClaim(supabase, userId, UserClaimKey.ACCEPTED_TERMS),
                getUserClaim(supabase, userId, UserClaimKey.SUBSCRIBED_TO_UPDATES)
            ]);

            // Return true if either preference exists (user has been here before)
            return acceptedTerms !== null || subscribedToUpdates !== null;
        } catch (error) {
            console.error("Error checking existing preferences:", error);
            return false; // Assume no existing preferences on error
        }
    };

    const saveDefaultPreferences = async (userId: string): Promise<SaveResult> => {
        try {
            const supabase = getSupabaseClient();
            await Promise.all([
                setUserClaim(supabase, userId, UserClaimKey.ACCEPTED_TERMS, true),
                setUserClaim(supabase, userId, UserClaimKey.SUBSCRIBED_TO_UPDATES, false)
            ]);

            // TODO: Add source tracking in future enhancement
            return { success: true, usedDefaults: true };
        } catch (error) {
            console.error("Error saving default preferences:", error);
            return { 
                success: false, 
                error: "Failed to save default preferences",
                usedDefaults: true 
            };
        }
    };

    const saveContextPreferences = async (userId: string): Promise<SaveResult> => {
        try {
            const result = await saveSignupPreferences({
                acceptedTerms: state.preferences.acceptedTerms,
                subscribeNewsletter: state.preferences.subscribeNewsletter
            });

            return { 
                success: result.success, 
                error: result.error,
                usedDefaults: false 
            };
        } catch (error) {
            console.error("Error saving context preferences:", error);
            return { 
                success: false, 
                error: "Failed to save preferences",
                usedDefaults: false 
            };
        }
    };

    const performSave = async () => {
        if (!user?.id || hasTriedSave.current) {
            return;
        }

        setIsSaving(true);
        hasTriedSave.current = true;

        try {
            // Step 1: Check if preferences already exist
            const existingPreferences = await checkExistingPreferences(user.id);
            
            if (existingPreferences) {
                setSaveResult({ success: true });
                clearPreferences();
                return;
            }

            // Step 2: Determine what to save
            let result: SaveResult;
            
            if (hasPreferences()) {
                // User set preferences during signup - save them
                result = await saveContextPreferences(user.id);
                
                // If context save fails, fall back to defaults
                if (!result.success) {
                    result = await saveDefaultPreferences(user.id);
                }
            } else {
                // No context preferences (race condition) - use defaults
                result = await saveDefaultPreferences(user.id);
            }

            setSaveResult(result);
            
            // Clear context after save attempt (success or failure)
            clearPreferences();
            
        } catch (error) {
            console.error("Error in performSave:", error);
            setSaveResult({ 
                success: false, 
                error: "Unexpected error during save" 
            });
        } finally {
            setIsSaving(false);
        }
    };

    // Trigger save when user is available and we haven't tried yet
    useEffect(() => {
        if (user?.id && !hasTriedSave.current) {
            performSave();
        }
    }, [user?.id]);

    return {
        isSaving,
        saveResult,
        performSave: () => {
            hasTriedSave.current = false;
            performSave();
        }
    };
}
