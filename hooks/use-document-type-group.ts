import { useCallback, useEffect, useState } from "react";
import { getDocumentTypeGroup } from "@/app/actions/document-type-group-actions";
import { Tables } from "@/types/database.types";

export function useDocumentTypeGroup(groupId?: string) {
    const [group, setGroup] = useState<Tables<"groups_document_type"> | null>(null);
    const [loading, setLoading] = useState(!!groupId);
    const [error, setError] = useState<string | null>(null);

    const fetchGroup = useCallback(async () => {
        if (!groupId) return;
        setLoading(true);
        setError(null);
        try {
            const result = await getDocumentTypeGroup(groupId);
            if (!result.success || !result.data) {
                throw new Error(result.error || "Failed to fetch document type group");
            }
            setGroup(result.data);
        } catch (err: any) {
            setError(err.message || "Unknown error");
            setGroup(null);
        } finally {
            setLoading(false);
        }
    }, [groupId]);

    useEffect(() => {
        if (groupId) fetchGroup();
    }, [groupId, fetchGroup]);

    return { group, loading, error, refetch: fetchGroup };
}
