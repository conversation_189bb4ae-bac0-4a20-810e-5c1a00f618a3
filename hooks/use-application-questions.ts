"use client";

import { useCallback, useEffect, useState } from "react";

import { Option } from "@/components/forms/fields/dropdown-base";
import { QuestionFromDB, ApplicationQuestionMetadata } from "@/lib/eligible-scholarship-card-constants";
import type { Database } from "@/types/database.types";
import { mapIdsToOptions } from "@/utils/form-utils";
import { getSupabaseClient } from "@/utils/supabase/client";

const TEXTS = {
    defaultApplicationLabel: "האם תרצה להגיש בקשה למלגה זו?"
};

interface DatabaseOption {
    id?: string;
    value?: string;
    label?: string;
}

interface ApplicationQuestion {
    id: string;
    label: string;
    placeholder?: string;
    options: Option[];
}

export function useApplicationQuestions() {
    const [applicationQuestion, setApplicationQuestion] = useState<ApplicationQuestion | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchApplicationQuestions = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const supabase = getSupabaseClient();
            const { data, error } = await supabase
                .from("questions")
                .select("id, type, metadata, section")
                .eq("section", "specific_scholarship")
                .eq("type", "single_select")
                .order("created_at", { ascending: false })
                .limit(1);

            if (error) throw error;

            if (data && data.length > 0) {
                const question = data[0] as QuestionFromDB;
                const metadata = question.metadata as ApplicationQuestionMetadata;

                if (metadata?.options && Array.isArray(metadata.options)) {
                    let mappedOptions: Option[];

                    const firstOption = metadata.options[0];
                    if (
                        firstOption &&
                        typeof firstOption === "object" &&
                        "id" in firstOption &&
                        "label" in firstOption
                    ) {
                        mappedOptions = metadata.options as Option[];
                    } else {
                        const rawOptions = metadata.options as unknown as DatabaseOption[];
                        const optionIds = rawOptions.map((opt: DatabaseOption) => opt.id || opt.value || String(opt));
                        mappedOptions = mapIdsToOptions(
                            optionIds,
                            rawOptions,
                            (opt: DatabaseOption) => opt.id || opt.value || String(opt),
                            (opt: DatabaseOption) => opt.label || String(opt),
                            (id: string) => id
                        );
                    }

                    setApplicationQuestion({
                        id: question.id,
                        label: metadata.label || TEXTS.defaultApplicationLabel,
                        placeholder: metadata.placeholder,
                        options: mappedOptions
                    });
                } else {
                    setApplicationQuestion(null);
                }
            } else {
                setApplicationQuestion(null);
            }

            setError(null);
        } catch (err) {
            console.error("Error fetching application questions:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
            setApplicationQuestion(null);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchApplicationQuestions();
    }, [fetchApplicationQuestions]);

    return {
        applicationQuestion,
        loading,
        error,
        refetch: fetchApplicationQuestions
    };
}
