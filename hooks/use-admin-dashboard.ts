"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@clerk/nextjs";
import { getSubscriptionStats, getUserStats } from "@/app/actions/admin-dashboard-actions";
import { SubscriptionStat, UserStat } from "@/lib/admin-dashboard-constants";

interface DashboardStats {
    subscriptions: {
        data: SubscriptionStat[];
        loading: boolean;
        error: Error | null;
    };
    users: {
        data: UserStat | null;
        loading: boolean;
        error: Error | null;
    };
}

export function useAdminDashboard() {
    const [stats, setStats] = useState<DashboardStats>({
        subscriptions: {
            data: [],
            loading: true,
            error: null
        },
        users: {
            data: null,
            loading: true,
            error: null
        }
    });

    const { isLoaded } = useAuth();

    const fetchAllStats = useCallback(async () => {
        if (!isLoaded) return;

        setStats((prev) => ({
            ...prev,
            subscriptions: { ...prev.subscriptions, loading: true, error: null },
            users: { ...prev.users, loading: true, error: null }
        }));

        try {
            const [subscriptionStatsResponse, userStatsResponse] = await Promise.all([
                getSubscriptionStats(),
                getUserStats()
            ]);

            if (!subscriptionStatsResponse.success || !userStatsResponse.success) {
                const errorMessages = [subscriptionStatsResponse.error, userStatsResponse.error]
                    .filter(Boolean)
                    .join(", ");
                throw new Error(errorMessages || "Failed to fetch stats");
            }

            setStats({
                subscriptions: {
                    data: subscriptionStatsResponse.data || [],
                    loading: false,
                    error: null
                },
                users: {
                    data: userStatsResponse.data || null,
                    loading: false,
                    error: null
                }
            });
        } catch (err) {
            console.error("Error fetching dashboard stats:", err);
            const error = err instanceof Error ? err : new Error("Unknown error occurred");
            setStats({
                subscriptions: { data: [], loading: false, error },
                users: { data: null, loading: false, error }
            });
        }
    }, [isLoaded]);

    useEffect(() => {
        fetchAllStats();
    }, [fetchAllStats]);

    return {
        stats,
        refetch: fetchAllStats
    };
}
