import { useEffect, useMemo, useState } from "react";
import { ScholarshipEligibility } from "@/app/services/scholarship-eligibility";

export const TEXTS = {
    errorLoadingScholarships: "אירעה שגיאה בטעינת המלגות. אנא נסה שוב מאוחר יותר."
};

export function useScholarshipEligibility() {
    const [eligibility, setEligibility] = useState<ScholarshipEligibility[] | undefined>(undefined);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const abortController = new AbortController();

        async function fetchEligibility() {
            try {
                const response = await fetch("/api/scholarships/eligibility?include_details=true", {
                    signal: abortController.signal,
                    cache: "no-store"
                });
                if (response.ok) {
                    const data = await response.json();
                    setEligibility(Array.isArray(data.eligibility) ? data.eligibility : []);
                    setError(null);
                } else {
                    setEligibility([]);
                    setError(TEXTS.errorLoadingScholarships);
                }
            } catch (error: unknown) {
                if (!(error instanceof Error && error.name === "AbortError")) {
                    setEligibility([]);
                    setError(TEXTS.errorLoadingScholarships);
                }
            } finally {
                setLoading(false);
            }
        }

        fetchEligibility();
        return () => abortController.abort();
    }, []);

    const eligibleScholarships = useMemo(() => (eligibility ?? []).filter((item) => item.isEligible), [eligibility]);

    const totalMinAmount = eligibleScholarships.reduce((sum, scholarship) => sum + (scholarship.min_amount || 0), 0);
    const totalMaxAmount = eligibleScholarships.reduce((sum, scholarship) => sum + (scholarship.max_amount || 0), 0);

    return {
        eligibility,
        eligibleScholarships,
        totalMinAmount,
        totalMaxAmount,
        loading,
        error
    };
}
