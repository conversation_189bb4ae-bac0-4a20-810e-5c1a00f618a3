"use client";

import { useState, useCallback, useEffect } from "react";
import { getPublicFaqs } from "@/app/actions/faq-actions";
import { type Faq } from "@/lib/faq-constants";

export function useFAQ() {
    const [items, setItems] = useState<Faq[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchFAQ = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const result = await getPublicFaqs();
            if (result.error) {
                throw new Error(result.error);
            }
            setItems(result.items);
        } catch (err) {
            setItems([]);
            setError(err instanceof Error ? err.message : "Error fetching FAQs");
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchFAQ();
    }, [fetchFAQ]);

    return { items, loading, error, refetch: fetchFAQ };
}
