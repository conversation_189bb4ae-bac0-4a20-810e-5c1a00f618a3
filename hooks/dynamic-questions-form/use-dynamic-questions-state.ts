import { useReducer } from "react";

import type {
    Condition,
    Question,
    QuestionConditionLink,
    QuestionGroup
} from "../../components/forms/dynamic-questions-form/types";

interface QuestionsState {
    questions: Question[];
    questionGroups: Record<string, QuestionGroup>;
    conditions: Condition[];
    questionConditionLinks: QuestionConditionLink[];
    loading: boolean;
    error: string | null;
    lastFetchedSections: string[];
    lastFetchedScholarshipId?: string;
}

export type QuestionsAction =
    | { type: "SET_LOADING"; payload: boolean }
    | { type: "SET_ERROR"; payload: string | null }
    | {
          type: "SET_DATA";
          payload: {
              questions: Question[];
              questionGroups: Record<string, QuestionGroup>;
              conditions: Condition[];
              questionConditionLinks: QuestionConditionLink[];
              sections: string[];
              scholarshipId?: string;
          };
      }
    | { type: "RESET" };

const initialState: QuestionsState = {
    questions: [],
    questionGroups: {},
    conditions: [],
    questionConditionLinks: [],
    loading: true,
    error: null,
    lastFetchedSections: [],
    lastFetchedScholarshipId: undefined
};

function questionsReducer(state: QuestionsState, action: QuestionsAction): QuestionsState {
    switch (action.type) {
        case "SET_LOADING":
            return { ...state, loading: action.payload };
        case "SET_ERROR":
            return { ...state, error: action.payload, loading: false };
        case "SET_DATA":
            return {
                ...state,
                questions: action.payload.questions,
                questionGroups: action.payload.questionGroups,
                conditions: action.payload.conditions,
                questionConditionLinks: action.payload.questionConditionLinks,
                lastFetchedSections: action.payload.sections,
                lastFetchedScholarshipId: action.payload.scholarshipId,
                loading: false,
                error: null
            };
        case "RESET":
            return initialState;
        default:
            return state;
    }
}

export function useDynamicQuestionsState() {
    const [state, dispatch] = useReducer(questionsReducer, initialState);

    return {
        ...state,
        dispatch
    };
}
