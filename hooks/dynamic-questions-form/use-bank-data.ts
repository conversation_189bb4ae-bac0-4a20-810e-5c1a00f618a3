import { useEffect, useState } from "react";

import type { Bank } from "../../components/forms/dynamic-questions-form/types";

export function useBankData() {
    const [banks, setBanks] = useState<Bank[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // Check if data is already cached in window
        const cachedBanks = (window as unknown as { __BANKS_CACHE__?: Bank[] }).__BANKS_CACHE__;
        if (cachedBanks && Array.isArray(cachedBanks) && cachedBanks.length > 0) {
            setBanks(cachedBanks);
            return;
        }

        const controller = new AbortController();

        const fetchBanks = async () => {
            setLoading(true);
            setError(null);

            try {
                const response = await fetch("/api/banks", {
                    signal: controller.signal
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                const banksData = data.banks ?? [];

                setBanks(banksData);
                (window as unknown as { __BANKS_CACHE__?: Bank[] }).__BANKS_CACHE__ = banksData;
            } catch (e) {
                if (e instanceof Error && e.name !== "AbortError") {
                    console.error("Failed loading banks:", e);
                    setError("Failed to load bank data");
                }
            } finally {
                setLoading(false);
            }
        };

        fetchBanks();

        return () => controller.abort();
    }, []);

    return { banks, loading, error };
}
