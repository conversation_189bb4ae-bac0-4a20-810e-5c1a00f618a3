"use client";

import { useState, useEffect, useCallback } from "react";
import { type Database } from "@/types/database.types";
import { getScholarshipsPage } from "@/app/actions/scholarship-actions";

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"] & { isNew?: boolean };

interface ScholarshipsState {
    scholarships: Scholarship[];
    displayedScholarships: Scholarship[];
    loading: boolean;
    error: string | null;
    hasMore: boolean;
    totalCount: number;
}

export function useScholarships(groupSlug?: string) {
    const [state, setState] = useState<ScholarshipsState>({
        scholarships: [],
        displayedScholarships: [],
        loading: true,
        error: null,
        hasMore: false,
        totalCount: 0
    });

    const [currentPage, setCurrentPage] = useState(1);

    const fetchScholarshipsPage = useCallback(
        async (page: number) => {
            if (page === 1) {
                setState((prev) => ({ ...prev, loading: true, error: null }));
            }

            try {
                const result = await getScholarshipsPage(page, groupSlug);

                if (result.error) {
                    throw new Error(result.error);
                }
                const { data, count } = result;

                const newItems: Scholarship[] = (data || []).map((scholarship) => ({
                    ...scholarship,
                    isNew: true
                }));

                setState((prev) => {
                    const existingItems =
                        page === 1 ? [] : prev.displayedScholarships.map((s) => ({ ...s, isNew: false }));
                    const allItems = [...existingItems, ...newItems];

                    return {
                        ...prev,
                        scholarships: page === 1 ? newItems : [...prev.scholarships, ...newItems],
                        displayedScholarships: allItems,
                        loading: false,
                        hasMore: count ? allItems.length < count : false,
                        totalCount: count || 0
                    };
                });

                setTimeout(() => {
                    setState((prev) => ({
                        ...prev,
                        displayedScholarships: prev.displayedScholarships.map((s) => ({
                            ...s,
                            isNew: false
                        }))
                    }));
                }, 1000);
            } catch (error) {
                setState((prev) => ({
                    ...prev,
                    loading: false,
                    error: error instanceof Error ? error.message : "An unknown error occurred"
                }));
            }
        },
        [groupSlug]
    );

    const loadMore = useCallback(() => {
        if (state.hasMore && !state.loading) {
            const nextPage = currentPage + 1;
            setCurrentPage(nextPage);
            fetchScholarshipsPage(nextPage);
        }
    }, [currentPage, state.hasMore, state.loading, fetchScholarshipsPage]);

    useEffect(() => {
        setCurrentPage(1);
        fetchScholarshipsPage(1);
    }, [groupSlug, fetchScholarshipsPage]);

    return { ...state, loadMore };
}
