"use client";

import { useState, useEffect, useCallback } from "react";
import { getCouponGroups } from "@/app/actions/coupon-group-actions";
import { type Tables } from "@/types/database.types";

export type CouponGroupWithCount = Tables<"groups_coupon"> & {
    coupons_count: number;
};

export function useCouponGroups() {
    const [items, setItems] = useState<CouponGroupWithCount[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchGroups = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const result = await getCouponGroups();

            if (result.success && result.data) {
                setItems(result.data as CouponGroupWithCount[]);
            } else {
                setError(result.error || "Failed to fetch coupon groups");
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchGroups();
    }, [fetchGroups]);

    return {
        items,
        loading,
        error,
        refetch: fetchGroups
    };
}
