import { useCallback, useEffect, useState } from "react";
import {
    getUserScholarshipApplications,
    updateUserScholarshipApplication
} from "@/app/actions/user-scholarship-applications-actions";

export function useUserScholarshipApplications(scholarshipIds: string[]) {
    const [shouldApplyMap, setShouldApplyMap] = useState<Record<string, boolean>>({});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchStatus = useCallback(async () => {
        if (!scholarshipIds || scholarshipIds.length === 0) return;

        setLoading(true);
        setError(null);
        try {
            const result = await getUserScholarshipApplications(scholarshipIds);
            if (!result.success || !result.applications) {
                throw new Error(result.error || "Failed to fetch user applications");
            }

            const map: Record<string, boolean> = {};
            result.applications.forEach((a) => {
                map[a.scholarship_id] = !!a.should_apply;
            });
            setShouldApplyMap(map);
        } catch (e: any) {
            setError(e.message);
        } finally {
            setLoading(false);
        }
    }, [JSON.stringify(scholarshipIds)]);

    useEffect(() => {
        fetchStatus();
    }, [fetchStatus]);

    const updateShouldApply = useCallback(async (scholarshipId: string, value: boolean) => {
        setShouldApplyMap((prev) => ({ ...prev, [scholarshipId]: value }));

        try {
            const result = await updateUserScholarshipApplication(scholarshipId, value);
            if (!result.success) {
                setShouldApplyMap((prev) => ({ ...prev, [scholarshipId]: !value }));
                setError(result.error || "Failed to save application preference");
            } else {
                setError(null);
            }
        } catch (e) {
            setShouldApplyMap((prev) => ({ ...prev, [scholarshipId]: !value }));
            setError(e instanceof Error ? e.message : "Unknown error occurred");
        }
    }, []);

    return { shouldApplyMap, updateShouldApply, loading, error, refetch: fetchStatus };
}
