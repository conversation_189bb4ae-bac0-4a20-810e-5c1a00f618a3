"use client";

import { useAuth } from "@clerk/nextjs";
import { useCallback, useEffect, useState, useRef } from "react";
import { toast } from "sonner";

import {
    getRequiredAndUploadedDocuments,
    uploadUserDocument,
    UserDocumentStatus as BaseUserDocumentStatus
} from "@/app/actions/user-document-actions";

const TEXTS = {
    DOCUMENT_UPLOADED_SUCCESS: "המסמך הועלה בהצלחה.",
    DOCUMENT_UPLOAD_FAILED: "העלאת המסמך נכשלה.",
    DOCUMENT_UPLOADING: "מעלה את המסמך, אנא המתן..."
};

interface UserDocumentStatus extends BaseUserDocumentStatus {
    exampleFileUrl?: string;
    uploadedFileUrl?: string;
}

interface UseUserDocumentsState {
    documents: UserDocumentStatus[];
    loading: boolean;
    error: string | null;
    refetchDocuments: () => Promise<void>;
    handleDocumentUpload: (documentTypeId: string, file: File) => Promise<void>;
}

export function useUserDocuments(overrideUserId?: string): UseUserDocumentsState {
    const { userId, isLoaded } = useAuth();
    const [documents, setDocuments] = useState<UserDocumentStatus[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    // Use override user ID if provided (for admin), otherwise use current user
    const effectiveUserId = overrideUserId || userId;

    const documentFetchControllerRef = useRef({ isCurrent: true });

    const fetchDocumentsLogic = useCallback(
        async (controller?: { isCurrent: boolean }) => {
            const activeController = controller || { isCurrent: true };

            try {
                const result = await getRequiredAndUploadedDocuments(overrideUserId);
                if (activeController.isCurrent) {
                    setDocuments(result.documents || []);
                    if (!result.success) {
                        setError(result.error || "Failed to fetch documents.");
                    } else {
                        setError(null);
                    }
                }
            } catch (e) {
                if (activeController.isCurrent) {
                    setError(e instanceof Error ? e.message : "An unknown error occurred while fetching documents.");
                    setDocuments([]);
                }
            } finally {
                if (activeController.isCurrent) {
                    setLoading(false);
                }
            }
        },
        [overrideUserId, setDocuments, setError, setLoading]
    );

    useEffect(() => {
        documentFetchControllerRef.current.isCurrent = false;

        const newController = { isCurrent: true };
        documentFetchControllerRef.current = newController;

        if (overrideUserId || isLoaded) {
            if (effectiveUserId) {
                setLoading(true);
                setError(null);
                fetchDocumentsLogic(newController);
            } else {
                setDocuments([]);
                setLoading(false);
                setError("User not authenticated. Please log in.");
            }
        } else {
            setLoading(true);
        }

        return () => {
            newController.isCurrent = false;
        };
    }, [overrideUserId, effectiveUserId, isLoaded, fetchDocumentsLogic]);

    const handleDocumentUpload = useCallback(
        async (documentTypeId: string, file: File) => {
            if (!effectiveUserId) {
                toast.error("User not authenticated. Please log in.");
                return;
            }
            const toastId = toast.loading(TEXTS.DOCUMENT_UPLOADING);

            try {
                const result = await uploadUserDocument(documentTypeId, file, overrideUserId);
                if (result.success) {
                    toast.success(TEXTS.DOCUMENT_UPLOADED_SUCCESS, { id: toastId });
                    setLoading(true);
                    setError(null);
                    await fetchDocumentsLogic();
                } else {
                    toast.error(result.error || TEXTS.DOCUMENT_UPLOAD_FAILED, { id: toastId });
                }
            } catch (e) {
                toast.error(
                    e instanceof Error ? e.message : `An unknown error occurred during upload of ${file.name}.`,
                    {
                        id: toastId
                    }
                );
            }
        },
        [effectiveUserId, overrideUserId, fetchDocumentsLogic]
    );

    const refetchDocuments = useCallback(() => {
        if (effectiveUserId && (overrideUserId || isLoaded)) {
            setLoading(true);
            setError(null);
            return fetchDocumentsLogic();
        }

        return Promise.resolve();
    }, [effectiveUserId, overrideUserId, isLoaded, fetchDocumentsLogic]);

    return {
        documents,
        loading,
        error,
        refetchDocuments,
        handleDocumentUpload
    };
}
