import { useCallback, useEffect, useState } from "react";
import { getDocumentTypeGroups, type DocumentTypeGroupWithCount } from "@/app/actions/document-type-group-actions";

export function useDocumentTypeGroups() {
    const [items, setItems] = useState<DocumentTypeGroupWithCount[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchGroups = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getDocumentTypeGroups();
            if (!result.success || !result.data) {
                throw new Error(result.error || "Failed to fetch document type groups");
            }
            setItems(result.data);
        } catch (err: any) {
            setError(err.message || "Unknown error");
            setItems([]);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchGroups();
    }, [fetchGroups]);

    return {
        items,
        loading,
        error,
        refetch: fetchGroups
    };
}
