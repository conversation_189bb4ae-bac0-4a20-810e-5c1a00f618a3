"use client";

import { useState, useEffect, useCallback } from "react";
import { getSupabaseClient } from "@/utils/supabase/client";

export interface QuestionGroup {
    id: string;
    name: string;
    questions_count: number;
    created_at: string;
    updated_at: string;
}

export function useQuestionGroups() {
    const [items, setItems] = useState<QuestionGroup[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchQuestionGroups = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const supabase = getSupabaseClient();

            const { data: groupsData, error: groupsError } = await supabase
                .from("groups_question")
                .select("id, name, created_at, updated_at")
                .order("created_at", { ascending: false });

            if (groupsError) throw groupsError;

            const { data: questionsData, error: questionsError } = await supabase
                .from("questions")
                .select("group_id")
                .not("group_id", "is", null);

            if (questionsError) throw questionsError;

            const countsMap: Record<string, number> = {};
            (questionsData || []).forEach((question: { group_id: string }) => {
                if (question.group_id) {
                    countsMap[question.group_id] = (countsMap[question.group_id] || 0) + 1;
                }
            });

            const groupsWithCount = (groupsData || []).map((group) => ({
                ...group,
                questions_count: countsMap[group.id] || 0
            }));

            setItems(groupsWithCount);
            setError(null);
        } catch (err) {
            console.error("Error fetching question groups:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchQuestionGroups();
    }, [fetchQuestionGroups]);

    return { items, loading, error, refetch: fetchQuestionGroups };
}
