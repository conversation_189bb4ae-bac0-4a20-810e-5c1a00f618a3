"use client";

import { useState, useEffect, useCallback } from "react";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";
import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";

export function useSubscription() {
    const [subscription, setSubscription] = useState<UserSubscriptionWithPlan | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchSubscription = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getCurrentUserSubscription();
            setSubscription(result);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
            setError(new Error(errorMessage));
            setSubscription(null);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchSubscription();
    }, [fetchSubscription]);

    return { subscription, loading, error, refetch: fetchSubscription };
}
