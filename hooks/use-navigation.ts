"use client";

import { usePathname } from "next/navigation";
import * as React from "react";
import { LucideIcon } from "lucide-react";

import { isNavItemActive } from "@/components/layout/navigation/nav-utils";
import { BaseNavItem } from "@/components/layout/navigation/types";
import { useSidebar } from "@/components/ui/sidebar";

interface NavMainItemInput {
    title: string;
    url: string;
    icon: string;
    disabled?: boolean;
    locked?: boolean;
}

interface NavSecondaryItemInput extends Omit<BaseNavItem, "href" | "label"> {
    title?: string;
    url?: string;
    href?: string;
    label?: string;
    icon?: LucideIcon;
}

type InputNavItem = NavMainItemInput | NavSecondaryItemInput;

export interface NormalizedNavigationItem extends BaseNavItem {
    originalIcon?: string;
}

interface UseNavigationOptions {
    exactMatch?: boolean;

    onLinkClick?: () => void;
}

interface UseNavigationReturn {
    normalizedItems: NormalizedNavigationItem[];
    handleLinkClick: (itemHref?: string) => void;
    isItemActive: (href: string) => boolean;
    sidebarState?: "expanded" | "collapsed";
    isMobile?: boolean;
    pathname: string;
}

export function useNavigation(items: InputNavItem[], options: UseNavigationOptions = {}): UseNavigationReturn {
    const { exactMatch = true, onLinkClick } = options;
    const pathname = usePathname();

    let sidebarContext;
    try {
        sidebarContext = useSidebar();
    } catch (e) {
        sidebarContext = {
            isMobile: false,
            setOpenMobile: () => {},
            state: "expanded" as "expanded" | "collapsed"
        };
    }

    const { isMobile, setOpenMobile, state: sidebarState } = sidebarContext;

    const handleLinkClick = React.useCallback(() => {
        if (isMobile) {
            setOpenMobile(false);
        }
        if (onLinkClick) {
            onLinkClick();
        }
    }, [isMobile, setOpenMobile, onLinkClick]);

    const normalizedItems = React.useMemo(() => {
        return items.map((itemInput) => {
            const { icon, ...restOfItem } = itemInput;

            const href = (restOfItem as NavSecondaryItemInput).href || (restOfItem as NavMainItemInput).url || "#";
            const label = (restOfItem as NavSecondaryItemInput).label || (restOfItem as NavMainItemInput).title || "";

            const normalized: Partial<NormalizedNavigationItem> = {
                ...(restOfItem as Omit<InputNavItem, "icon">),
                href,
                label
            };

            if (typeof icon === "string") {
                normalized.originalIcon = icon;
            } else if (icon) {
                normalized.icon = icon as LucideIcon;
            }

            return normalized as NormalizedNavigationItem;
        });
    }, [items]);

    const isItemActive = React.useCallback(
        (href: string) => {
            return isNavItemActive(pathname, href, exactMatch);
        },
        [pathname, exactMatch]
    );

    return {
        normalizedItems,
        handleLinkClick,
        isItemActive,
        sidebarState: sidebarContext ? sidebarState : undefined,
        isMobile: sidebarContext ? isMobile : undefined,
        pathname
    };
}
