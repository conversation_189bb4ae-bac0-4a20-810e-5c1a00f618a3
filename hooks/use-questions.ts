"use client";

import { useCallback, useEffect, useState } from "react";
import { getSupabaseClient } from "@/utils/supabase/client";

export interface Question {
    id: string;
    type: string;
    metadata: {
        label: string;
        required?: boolean;
    };
    group_id: string;
    section: "personal_details" | "data_entry" | "specific_scholarship";
    groups_question: {
        id: string;
        name: string;
    } | null;
}

export function useQuestions() {
    const [items, setItems] = useState<Question[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchQuestions = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            const supabase = getSupabaseClient();
            const { data, error } = await supabase
                .from("questions")
                .select(
                    `
                    id,
                    type,
                    metadata,
                    group_id,
                    section,
                    groups_question (
                        id,
                        name
                    )
                `
                )
                .order("created_at", { ascending: false });

            if (error) throw error;

            const transformedData = (data || []).map((item) => {
                const group = Array.isArray(item.groups_question) ? item.groups_question[0] : item.groups_question;

                return {
                    ...item,
                    groups_question: group
                        ? {
                              id: group.id || "",
                              name: group.name || ""
                          }
                        : null
                };
            });

            setItems(transformedData);
            setError(null);
        } catch (err) {
            console.error("Error fetching questions:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchQuestions();
    }, [fetchQuestions]);

    return { items, loading, error, refetch: fetchQuestions };
}
