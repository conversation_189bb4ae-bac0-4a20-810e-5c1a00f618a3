import { useCallback, useEffect, useState } from "react";
import { FilterValue } from "@/components/table/types";
import { getAdminScholarships } from "@/app/actions/scholarship-actions";
import { ScholarshipWithGroups } from "@/lib/scholarship-constants";

interface UseAdminScholarshipsParams {
    page?: number;
    pageSize?: number;
    filters?: Record<string, FilterValue>;
}

export function useAdminScholarships({ page = 1, pageSize = 10, filters = {} }: UseAdminScholarshipsParams = {}) {
    const [items, setItems] = useState<ScholarshipWithGroups[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);
    const [totalItems, setTotalItems] = useState(0);
    const [totalPages, setTotalPages] = useState(1);

    const fetchScholarships = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            const result = await getAdminScholarships({ page, pageSize, filters });

            if (result.error) {
                throw new Error(result.error);
            }

            const total = result.count || 0;
            setItems(result.data);
            setTotalItems(total);
            setTotalPages(Math.max(1, Math.ceil(total / pageSize)));
        } catch (err) {
            console.error("Error fetching scholarships:", err);
            const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
            setError(new Error(errorMessage));
        } finally {
            setLoading(false);
        }
    }, [page, pageSize, filters]);

    useEffect(() => {
        fetchScholarships();
    }, [fetchScholarships]);

    return {
        items,
        loading,
        error,
        refetch: fetchScholarships,
        totalItems,
        totalPages,
        currentPage: page
    };
}
