import { useState, useEffect } from "react";
import { getAllCollaborations } from "@/app/actions/collaboration-actions";
import { type Tables } from "@/types/database.types";

export function useCollaborations() {
    const [items, setItems] = useState<Tables<"collaborations">[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    async function fetchCollaborations() {
        try {
            setLoading(true);
            const result = await getAllCollaborations();

            if (!result.success) {
                throw new Error(result.error || "Failed to fetch collaborations");
            }

            setItems(result.data || []);
            setError(null);
        } catch (err) {
            console.error("Error in useCollaborations:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchCollaborations();
    }, []);

    return {
        items,
        loading,
        error,
        refetch: fetchCollaborations
    };
}
