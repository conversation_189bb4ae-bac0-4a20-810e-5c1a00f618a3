"use client";

import { useState, useEffect } from "react";
import { getScholarshipConditionGroups } from "@/app/actions/scholarship-condition-group-actions";

export interface ScholarshipConditionGroup {
    id: string;
    name: string;
    conditions_count: number;
    created_at: string;
    updated_at: string;
}

export function useScholarshipConditionGroups() {
    const [items, setItems] = useState<ScholarshipConditionGroup[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    async function fetchGroups() {
        try {
            setLoading(true);
            setError(null);
            const result = await getScholarshipConditionGroups();

            if (result.success && result.data) {
                setItems(result.data as ScholarshipConditionGroup[]);
            } else {
                setError(new Error(result.error));
            }
        } catch (err) {
            setError(err instanceof Error ? err : new Error("Failed to fetch scholarship condition groups"));
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchGroups();
    }, []);

    return {
        items,
        loading,
        error,
        refetch: fetchGroups
    };
}
