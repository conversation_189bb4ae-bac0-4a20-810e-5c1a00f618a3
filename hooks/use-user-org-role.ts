import { useAuth } from "@clerk/nextjs";

export type UserOrgRole = "admin" | "employee" | "user";

export function useUserOrgRole(): { role: UserOrgRole; orgId: string | undefined; isLoaded: boolean } {
    const { sessionClaims, isLoaded } = useAuth();
    let role: UserOrgRole = "user";
    let orgId: string | undefined = undefined;

    if (isLoaded) {
        const orgRoles = sessionClaims?.organizations || {};
        const entries = Object.entries(orgRoles);
        if (entries.length > 0) {
            orgId = entries[0][0];
            const orgRole = entries[0][1];
            if (orgRole === "org:admin") role = "admin";
            else if (orgRole === "org:employee") role = "employee";
            else role = "user";
        }
    }

    return { role, orgId, isLoaded };
}
