"use client";

import { useState, useEffect, useCallback } from "react";
import { getContacts } from "@/app/actions/contact-actions";
import { Database } from "@/types/database.types";

export type Contact = Database["public"]["Tables"]["contact"]["Row"];

export function useContacts() {
    const [items, setItems] = useState<Contact[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const fetchContacts = useCallback(async () => {
        setLoading(true);
        try {
            const result = await getContacts();
            if (result.success) {
                setItems(result.data || []);
                setError(null);
            } else {
                throw new Error(result.error || "Failed to fetch contacts");
            }
        } catch (err) {
            console.error("Error in useContacts hook:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchContacts();
    }, [fetchContacts]);

    return { items, loading, error, refetch: fetchContacts };
}
