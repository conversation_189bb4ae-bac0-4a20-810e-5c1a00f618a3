"use client";

import { useCallback, useEffect, useState } from "react";

import { deleteBanner, getBanners } from "@/app/actions/banner-actions";
import { type Tables } from "@/types/database.types";

type Banner = Tables<"banners">;

interface AdminBannersState {
    items: Banner[];
    loading: boolean;
    error: Error | null;
}

export function useAdminBanners() {
    const [state, setState] = useState<AdminBannersState>({
        items: [],
        loading: true,
        error: null
    });

    const fetchBanners = useCallback(async () => {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        try {
            const result = await getBanners();
            if (result.success && result.data) {
                setState({ items: result.data, loading: false, error: null });
            } else {
                throw new Error(result.error || "Failed to fetch banners");
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error("An unknown error occurred");
            setState({ items: [], loading: false, error: err });
            console.error("Failed to fetch banners:", err);
        }
    }, []);

    useEffect(() => {
        fetchBanners();
    }, [fetchBanners]);

    const handleDelete = useCallback(
        async (id: string) => {
            const result = await deleteBanner(id);
            if (result.success) {
                await fetchBanners();
            }
            return result;
        },
        [fetchBanners]
    );

    return {
        ...state,
        handleDelete,
        refetch: fetchBanners
    };
}
