import { useCallback, useEffect, useState } from "react";

import { getUserNotes } from "@/app/actions/user-notes-actions";
import { type UserNote } from "@/lib/user-notes-constants";

interface UseUserNotesResult {
    notes: UserNote[];
    loading: boolean;
    error: Error | null;
    refetch: () => void;
}

export function useUserNotes(reportedUserId: string): UseUserNotesResult {
    const [notes, setNotes] = useState<UserNote[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const fetchNotes = useCallback(async () => {
        if (!reportedUserId.trim()) {
            setNotes([]);
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const result = await getUserNotes(reportedUserId);

            if (result.success && result.notes) {
                setNotes(result.notes);
            } else {
                setError(new Error(result.error || "שגיאה בטעינת הערות"));
                setNotes([]);
            }
        } catch (err) {
            setError(err instanceof Error ? err : new Error("שגיאה בטעינת הערות"));
            setNotes([]);
        } finally {
            setLoading(false);
        }
    }, [reportedUserId]);

    useEffect(() => {
        fetchNotes();
    }, [fetchNotes]);

    const refetch = useCallback(() => {
        fetchNotes();
    }, [fetchNotes]);

    return {
        notes,
        loading,
        error,
        refetch
    };
}
