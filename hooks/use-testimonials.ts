"use client";

import { useState, useEffect } from "react";
import { getTestimonials } from "@/app/actions/testimonial-actions";
import { type Testimonial } from "@/lib/testimonial-constants";

export function useTestimonials(ids?: string[]) {
    const [items, setItems] = useState<Testimonial[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        let isMounted = true;

        async function fetchTestimonials() {
            try {
                setLoading(true);
                setError(null);

                const result = await getTestimonials(ids);

                if (isMounted) {
                    if (result.success && result.data) {
                        setItems(result.data);
                    } else {
                        throw new Error(result.error || "Failed to fetch testimonials");
                    }
                }
            } catch (err) {
                console.error("Error fetching testimonials:", err);
                if (isMounted) {
                    setError(err instanceof Error ? err.message : "Failed to fetch testimonials");
                }
            } finally {
                if (isMounted) {
                    setLoading(false);
                }
            }
        }

        fetchTestimonials();

        return () => {
            isMounted = false;
        };
    }, [ids]);

    return { items, loading, error };
}
