export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
    // Allows to automatically instanciate createClient with right options
    // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
    __InternalSupabase: {
        PostgrestVersion: "12.2.3 (519615d)";
    };
    graphql_public: {
        Tables: {
            [_ in never]: never;
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            graphql: {
                Args: {
                    operationName?: string;
                    query?: string;
                    variables?: Json;
                    extensions?: Json;
                };
                Returns: Json;
            };
        };
        Enums: {
            [_ in never]: never;
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
    public: {
        Tables: {
            _migration_scholarships_slugs: {
                Row: {
                    created_at: string;
                    id: string;
                    new_slug: string;
                    old_slug: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    new_slug: string;
                    old_slug: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    new_slug?: string;
                    old_slug?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            answers: {
                Row: {
                    answer: string;
                    created_at: string | null;
                    id: string;
                    question_id: string;
                    updated_at: string | null;
                    user_id: string;
                };
                Insert: {
                    answer: string;
                    created_at?: string | null;
                    id?: string;
                    question_id: string;
                    updated_at?: string | null;
                    user_id: string;
                };
                Update: {
                    answer?: string;
                    created_at?: string | null;
                    id?: string;
                    question_id?: string;
                    updated_at?: string | null;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "answers_question_id_fkey";
                        columns: ["question_id"];
                        isOneToOne: false;
                        referencedRelation: "questions";
                        referencedColumns: ["id"];
                    }
                ];
            };
            banners: {
                Row: {
                    audience: Database["public"]["Enums"]["banners_audience"];
                    background_color: string;
                    created_at: string;
                    cta_link: string | null;
                    cta_text: string | null;
                    days_to_live: number;
                    enable_dismiss: boolean;
                    enabled: boolean;
                    icon: string;
                    id: string;
                    seconds_before_show: number;
                    text: string;
                    text_color: string;
                    title: string;
                    updated_at: string;
                };
                Insert: {
                    audience?: Database["public"]["Enums"]["banners_audience"];
                    background_color: string;
                    created_at?: string;
                    cta_link?: string | null;
                    cta_text?: string | null;
                    days_to_live?: number;
                    enable_dismiss?: boolean;
                    enabled?: boolean;
                    icon?: string;
                    id?: string;
                    seconds_before_show?: number;
                    text: string;
                    text_color: string;
                    title: string;
                    updated_at?: string;
                };
                Update: {
                    audience?: Database["public"]["Enums"]["banners_audience"];
                    background_color?: string;
                    created_at?: string;
                    cta_link?: string | null;
                    cta_text?: string | null;
                    days_to_live?: number;
                    enable_dismiss?: boolean;
                    enabled?: boolean;
                    icon?: string;
                    id?: string;
                    seconds_before_show?: number;
                    text?: string;
                    text_color?: string;
                    title?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            collaborations: {
                Row: {
                    api_endpoint: string;
                    auth_type: Database["public"]["Enums"]["auth_type"];
                    auth_value: string | null;
                    created_at: string;
                    description: string | null;
                    id: string;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    api_endpoint: string;
                    auth_type: Database["public"]["Enums"]["auth_type"];
                    auth_value?: string | null;
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    api_endpoint?: string;
                    auth_type?: Database["public"]["Enums"]["auth_type"];
                    auth_value?: string | null;
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            collaborations_history: {
                Row: {
                    collaboration_id: string;
                    created_at: string;
                    id: string;
                    request_payload: Json;
                    response_body: Json | null;
                    response_status_code: number | null;
                    updated_at: string;
                    user_id: string;
                };
                Insert: {
                    collaboration_id: string;
                    created_at?: string;
                    id?: string;
                    request_payload: Json;
                    response_body?: Json | null;
                    response_status_code?: number | null;
                    updated_at?: string;
                    user_id: string;
                };
                Update: {
                    collaboration_id?: string;
                    created_at?: string;
                    id?: string;
                    request_payload?: Json;
                    response_body?: Json | null;
                    response_status_code?: number | null;
                    updated_at?: string;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "collaborations_history_collaboration_id_fkey";
                        columns: ["collaboration_id"];
                        isOneToOne: false;
                        referencedRelation: "collaborations";
                        referencedColumns: ["id"];
                    }
                ];
            };
            conditions: {
                Row: {
                    created_at: string;
                    group_id: string | null;
                    id: string;
                    question_id: string;
                    type: Database["public"]["Enums"]["condition_type"];
                    updated_at: string;
                    value: Json;
                };
                Insert: {
                    created_at?: string;
                    group_id?: string | null;
                    id?: string;
                    question_id: string;
                    type: Database["public"]["Enums"]["condition_type"];
                    updated_at?: string;
                    value: Json;
                };
                Update: {
                    created_at?: string;
                    group_id?: string | null;
                    id?: string;
                    question_id?: string;
                    type?: Database["public"]["Enums"]["condition_type"];
                    updated_at?: string;
                    value?: Json;
                };
                Relationships: [
                    {
                        foreignKeyName: "conditions_group_id_fkey";
                        columns: ["group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_condition";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "conditions_question_id_fkey";
                        columns: ["question_id"];
                        isOneToOne: false;
                        referencedRelation: "questions";
                        referencedColumns: ["id"];
                    }
                ];
            };
            contact: {
                Row: {
                    created_at: string;
                    email: string;
                    id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    email: string;
                    id?: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    email?: string;
                    id?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            coupons: {
                Row: {
                    coupon_code: string;
                    coupon_group_id: string | null;
                    coupon_type: Database["public"]["Enums"]["coupon_type_enum"];
                    created_at: string;
                    discount_value: number;
                    expiration_date: string | null;
                    id: string;
                    updated_at: string;
                    usage_limit: number | null;
                    used_count: number;
                };
                Insert: {
                    coupon_code: string;
                    coupon_group_id?: string | null;
                    coupon_type?: Database["public"]["Enums"]["coupon_type_enum"];
                    created_at?: string;
                    discount_value: number;
                    expiration_date?: string | null;
                    id?: string;
                    updated_at?: string;
                    usage_limit?: number | null;
                    used_count?: number;
                };
                Update: {
                    coupon_code?: string;
                    coupon_group_id?: string | null;
                    coupon_type?: Database["public"]["Enums"]["coupon_type_enum"];
                    created_at?: string;
                    discount_value?: number;
                    expiration_date?: string | null;
                    id?: string;
                    updated_at?: string;
                    usage_limit?: number | null;
                    used_count?: number;
                };
                Relationships: [
                    {
                        foreignKeyName: "coupons_coupon_group_id_fkey";
                        columns: ["coupon_group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_coupon";
                        referencedColumns: ["id"];
                    }
                ];
            };
            document_types: {
                Row: {
                    allowed_mime_types: Json;
                    created_at: string;
                    description: string | null;
                    example_file_path: string | null;
                    group_id: string;
                    id: string;
                    link_url: string | null;
                    max_file_size_mb: number | null;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    allowed_mime_types?: Json;
                    created_at?: string;
                    description?: string | null;
                    example_file_path?: string | null;
                    group_id: string;
                    id?: string;
                    link_url?: string | null;
                    max_file_size_mb?: number | null;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    allowed_mime_types?: Json;
                    created_at?: string;
                    description?: string | null;
                    example_file_path?: string | null;
                    group_id?: string;
                    id?: string;
                    link_url?: string | null;
                    max_file_size_mb?: number | null;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "document_types_group_id_fkey";
                        columns: ["group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_document_type";
                        referencedColumns: ["id"];
                    }
                ];
            };
            faq: {
                Row: {
                    answer: string;
                    created_at: string;
                    id: string;
                    order_index: number;
                    question: string;
                    updated_at: string;
                };
                Insert: {
                    answer: string;
                    created_at?: string;
                    id?: string;
                    order_index: number;
                    question: string;
                    updated_at?: string;
                };
                Update: {
                    answer?: string;
                    created_at?: string;
                    id?: string;
                    order_index?: number;
                    question?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            groups_condition: {
                Row: {
                    created_at: string;
                    id: string;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            groups_coupon: {
                Row: {
                    created_at: string;
                    description: string | null;
                    id: string;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            groups_document_type: {
                Row: {
                    created_at: string;
                    description: string | null;
                    id: string;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    description?: string | null;
                    id?: string;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            groups_question: {
                Row: {
                    created_at: string;
                    id: string;
                    name: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    name: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    name?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            groups_scholarship: {
                Row: {
                    created_at: string;
                    description: string;
                    icon: string;
                    id: string;
                    image_url: string | null;
                    slug: string;
                    title: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    description: string;
                    icon: string;
                    id?: string;
                    image_url?: string | null;
                    slug: string;
                    title: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    description?: string;
                    icon?: string;
                    id?: string;
                    image_url?: string | null;
                    slug?: string;
                    title?: string;
                    updated_at?: string;
                };
                Relationships: [];
            };
            link_collaboration_to_condition: {
                Row: {
                    collaboration_id: string;
                    condition_id: string;
                    created_at: string;
                    id: string;
                    updated_at: string;
                };
                Insert: {
                    collaboration_id: string;
                    condition_id: string;
                    created_at?: string;
                    id?: string;
                    updated_at?: string;
                };
                Update: {
                    collaboration_id?: string;
                    condition_id?: string;
                    created_at?: string;
                    id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_collaboration_to_condition_collaboration_id_fkey";
                        columns: ["collaboration_id"];
                        isOneToOne: false;
                        referencedRelation: "collaborations";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_collaboration_to_condition_condition_id_fkey";
                        columns: ["condition_id"];
                        isOneToOne: false;
                        referencedRelation: "conditions";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_question_to_collaboration: {
                Row: {
                    collaboration_id: string;
                    created_at: string;
                    id: string;
                    question_id: string;
                    updated_at: string;
                };
                Insert: {
                    collaboration_id: string;
                    created_at?: string;
                    id?: string;
                    question_id: string;
                    updated_at?: string;
                };
                Update: {
                    collaboration_id?: string;
                    created_at?: string;
                    id?: string;
                    question_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_question_to_collaboration_collaboration_id_fkey";
                        columns: ["collaboration_id"];
                        isOneToOne: false;
                        referencedRelation: "collaborations";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_question_to_collaboration_question_id_fkey";
                        columns: ["question_id"];
                        isOneToOne: false;
                        referencedRelation: "questions";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_question_to_condition: {
                Row: {
                    condition_id: string;
                    created_at: string;
                    question_id: string;
                    updated_at: string;
                };
                Insert: {
                    condition_id: string;
                    created_at?: string;
                    question_id: string;
                    updated_at?: string;
                };
                Update: {
                    condition_id?: string;
                    created_at?: string;
                    question_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_question_to_condition_condition_id_fkey";
                        columns: ["condition_id"];
                        isOneToOne: false;
                        referencedRelation: "conditions";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_question_to_condition_question_id_fkey";
                        columns: ["question_id"];
                        isOneToOne: false;
                        referencedRelation: "questions";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_question_to_scholarship: {
                Row: {
                    created_at: string;
                    question_id: string;
                    scholarship_id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    question_id: string;
                    scholarship_id: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    question_id?: string;
                    scholarship_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_question_to_scholarship_question_id_fkey";
                        columns: ["question_id"];
                        isOneToOne: false;
                        referencedRelation: "questions";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_question_to_scholarship_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_groups_to_testimonial: {
                Row: {
                    created_at: string;
                    scholarship_group_id: string;
                    testimonial_id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    scholarship_group_id: string;
                    testimonial_id: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    scholarship_group_id?: string;
                    testimonial_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_groups_to_testimonia_scholarship_group_id_fkey";
                        columns: ["scholarship_group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_scholarship";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_groups_to_testimonial_testimonial_id_fkey";
                        columns: ["testimonial_id"];
                        isOneToOne: false;
                        referencedRelation: "testimonials";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_to_condition: {
                Row: {
                    condition_id: string;
                    created_at: string;
                    scholarship_id: string;
                    updated_at: string;
                };
                Insert: {
                    condition_id: string;
                    created_at?: string;
                    scholarship_id: string;
                    updated_at?: string;
                };
                Update: {
                    condition_id?: string;
                    created_at?: string;
                    scholarship_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_to_condition_condition_id_fkey";
                        columns: ["condition_id"];
                        isOneToOne: false;
                        referencedRelation: "conditions";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_to_condition_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_to_condition_groups: {
                Row: {
                    created_at: string;
                    group_id: string;
                    scholarship_id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    group_id: string;
                    scholarship_id: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    group_id?: string;
                    scholarship_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_to_condition_groups_group_id_fkey";
                        columns: ["group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_condition";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_to_condition_groups_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_to_document_type: {
                Row: {
                    created_at: string;
                    document_type_id: string;
                    scholarship_id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    document_type_id: string;
                    scholarship_id: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    document_type_id?: string;
                    scholarship_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_to_document_type_document_type_id_fkey";
                        columns: ["document_type_id"];
                        isOneToOne: false;
                        referencedRelation: "document_types";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_to_document_type_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_to_scholarship_groups: {
                Row: {
                    created_at: string | null;
                    scholarship_group_id: string;
                    scholarship_id: string;
                };
                Insert: {
                    created_at?: string | null;
                    scholarship_group_id: string;
                    scholarship_id: string;
                };
                Update: {
                    created_at?: string | null;
                    scholarship_group_id?: string;
                    scholarship_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_to_scholarship_group_scholarship_group_id_fkey";
                        columns: ["scholarship_group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_scholarship";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_to_scholarship_groups_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            link_scholarship_to_testimonial: {
                Row: {
                    created_at: string;
                    scholarship_id: string;
                    testimonial_id: string;
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    scholarship_id: string;
                    testimonial_id: string;
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    scholarship_id?: string;
                    testimonial_id?: string;
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "link_scholarship_to_testimonial_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    },
                    {
                        foreignKeyName: "link_scholarship_to_testimonial_testimonial_id_fkey";
                        columns: ["testimonial_id"];
                        isOneToOne: false;
                        referencedRelation: "testimonials";
                        referencedColumns: ["id"];
                    }
                ];
            };
            questions: {
                Row: {
                    created_at: string;
                    group_id: string;
                    id: string;
                    metadata: Json | null;
                    section: Database["public"]["Enums"]["question_section"];
                    type: Database["public"]["Enums"]["question_type"];
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    group_id: string;
                    id?: string;
                    metadata?: Json | null;
                    section?: Database["public"]["Enums"]["question_section"];
                    type: Database["public"]["Enums"]["question_type"];
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    group_id?: string;
                    id?: string;
                    metadata?: Json | null;
                    section?: Database["public"]["Enums"]["question_section"];
                    type?: Database["public"]["Enums"]["question_type"];
                    updated_at?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "questions_group_id_fkey";
                        columns: ["group_id"];
                        isOneToOne: false;
                        referencedRelation: "groups_question";
                        referencedColumns: ["id"];
                    }
                ];
            };
            scholarships: {
                Row: {
                    benefits: Json;
                    contact_email: string | null;
                    contact_person: string | null;
                    contact_phone: string | null;
                    created_at: string;
                    description: string;
                    end_date: string;
                    id: string;
                    image_url: string | null;
                    internal_notes: string | null;
                    is_active: boolean;
                    is_public: boolean;
                    max_amount: number;
                    min_amount: number;
                    requirements: Json;
                    response_date: string | null;
                    scholarship_type: Database["public"]["Enums"]["scholarship_type"];
                    short_description: string;
                    slug: string;
                    start_date: string;
                    target_audience: string;
                    title: string;
                    updated_at: string;
                    url: string | null;
                    volunteer_hours: number;
                };
                Insert: {
                    benefits?: Json;
                    contact_email?: string | null;
                    contact_person?: string | null;
                    contact_phone?: string | null;
                    created_at?: string;
                    description: string;
                    end_date?: string;
                    id?: string;
                    image_url?: string | null;
                    internal_notes?: string | null;
                    is_active?: boolean;
                    is_public?: boolean;
                    max_amount?: number;
                    min_amount?: number;
                    requirements?: Json;
                    response_date?: string | null;
                    scholarship_type?: Database["public"]["Enums"]["scholarship_type"];
                    short_description: string;
                    slug: string;
                    start_date?: string;
                    target_audience?: string;
                    title: string;
                    updated_at?: string;
                    url?: string | null;
                    volunteer_hours?: number;
                };
                Update: {
                    benefits?: Json;
                    contact_email?: string | null;
                    contact_person?: string | null;
                    contact_phone?: string | null;
                    created_at?: string;
                    description?: string;
                    end_date?: string;
                    id?: string;
                    image_url?: string | null;
                    internal_notes?: string | null;
                    is_active?: boolean;
                    is_public?: boolean;
                    max_amount?: number;
                    min_amount?: number;
                    requirements?: Json;
                    response_date?: string | null;
                    scholarship_type?: Database["public"]["Enums"]["scholarship_type"];
                    short_description?: string;
                    slug?: string;
                    start_date?: string;
                    target_audience?: string;
                    title?: string;
                    updated_at?: string;
                    url?: string | null;
                    volunteer_hours?: number;
                };
                Relationships: [];
            };
            testimonials: {
                Row: {
                    created_at: string;
                    id: string;
                    name: string;
                    text: string;
                    type: Database["public"]["Enums"]["testimonial_type"];
                    updated_at: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    name: string;
                    text: string;
                    type?: Database["public"]["Enums"]["testimonial_type"];
                    updated_at?: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    name?: string;
                    text?: string;
                    type?: Database["public"]["Enums"]["testimonial_type"];
                    updated_at?: string;
                };
                Relationships: [];
            };
            user_claims: {
                Row: {
                    claim_key: string;
                    claim_value: Json | null;
                    created_at: string;
                    id: string;
                    updated_at: string;
                    user_id: string;
                };
                Insert: {
                    claim_key: string;
                    claim_value?: Json | null;
                    created_at?: string;
                    id?: string;
                    updated_at?: string;
                    user_id?: string;
                };
                Update: {
                    claim_key?: string;
                    claim_value?: Json | null;
                    created_at?: string;
                    id?: string;
                    updated_at?: string;
                    user_id?: string;
                };
                Relationships: [];
            };
            user_notes: {
                Row: {
                    created_at: string;
                    id: string;
                    note: string;
                    reported_user_id: string;
                    updated_at: string;
                    user_id: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    note: string;
                    reported_user_id: string;
                    updated_at?: string;
                    user_id?: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    note?: string;
                    reported_user_id?: string;
                    updated_at?: string;
                    user_id?: string;
                };
                Relationships: [];
            };
            user_scholarship_applications: {
                Row: {
                    created_at: string;
                    id: string;
                    scholarship_id: string;
                    should_apply: boolean;
                    updated_at: string;
                    user_id: string;
                };
                Insert: {
                    created_at?: string;
                    id?: string;
                    scholarship_id: string;
                    should_apply: boolean;
                    updated_at?: string;
                    user_id: string;
                };
                Update: {
                    created_at?: string;
                    id?: string;
                    scholarship_id?: string;
                    should_apply?: boolean;
                    updated_at?: string;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "user_scholarship_applications_scholarship_id_fkey";
                        columns: ["scholarship_id"];
                        isOneToOne: false;
                        referencedRelation: "scholarships";
                        referencedColumns: ["id"];
                    }
                ];
            };
            user_subscriptions: {
                Row: {
                    coupon_id: string | null;
                    created_at: string;
                    expiration_date: string | null;
                    id: string;
                    is_active: boolean;
                    order_id: string | null;
                    paid_amount: number | null;
                    payment_details: Json | null;
                    plan_id: string;
                    plan_price: number | null;
                    start_date: string;
                    transaction_id: string | null;
                    updated_at: string;
                    user_id: string;
                };
                Insert: {
                    coupon_id?: string | null;
                    created_at?: string;
                    expiration_date?: string | null;
                    id?: string;
                    is_active?: boolean;
                    order_id?: string | null;
                    paid_amount?: number | null;
                    payment_details?: Json | null;
                    plan_id: string;
                    plan_price?: number | null;
                    start_date?: string;
                    transaction_id?: string | null;
                    updated_at?: string;
                    user_id: string;
                };
                Update: {
                    coupon_id?: string | null;
                    created_at?: string;
                    expiration_date?: string | null;
                    id?: string;
                    is_active?: boolean;
                    order_id?: string | null;
                    paid_amount?: number | null;
                    payment_details?: Json | null;
                    plan_id?: string;
                    plan_price?: number | null;
                    start_date?: string;
                    transaction_id?: string | null;
                    updated_at?: string;
                    user_id?: string;
                };
                Relationships: [
                    {
                        foreignKeyName: "user_subscriptions_coupon_id_fkey";
                        columns: ["coupon_id"];
                        isOneToOne: false;
                        referencedRelation: "coupons";
                        referencedColumns: ["id"];
                    }
                ];
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            create_collaboration_with_dependencies: {
                Args: {
                    p_name: string;
                    p_description: string;
                    p_api_endpoint: string;
                    p_auth_type: Database["public"]["Enums"]["auth_type"];
                    p_auth_value: string;
                    p_dependencies?: Json;
                    p_question_ids?: string[];
                };
                Returns: Json;
            };
            create_question_conditions: {
                Args: { p_question_id: string; p_dependencies: Json };
                Returns: Json;
            };
            deactivate_expired_subscriptions: {
                Args: Record<PropertyKey, never>;
                Returns: undefined;
            };
            delete_question_conditions: {
                Args: { p_question_id: string };
                Returns: Json;
            };
            increment_coupon_usage: {
                Args: { p_coupon_code: string };
                Returns: {
                    incremented: boolean;
                }[];
            };
            is_admin: {
                Args: Record<PropertyKey, never>;
                Returns: boolean;
            };
            is_authenticated: {
                Args: Record<PropertyKey, never>;
                Returns: boolean;
            };
            is_owner_user_document: {
                Args: { file_name: string };
                Returns: boolean;
            };
            requesting_user_id: {
                Args: Record<PropertyKey, never>;
                Returns: string;
            };
            update_collaboration_with_dependencies: {
                Args: {
                    p_collaboration_id: string;
                    p_name: string;
                    p_description: string;
                    p_api_endpoint: string;
                    p_auth_type: Database["public"]["Enums"]["auth_type"];
                    p_auth_value: string;
                    p_dependencies?: Json;
                    p_question_ids?: string[];
                };
                Returns: Json;
            };
            update_user_subscription: {
                Args: { p_user_id: string; p_subscription_data: Json };
                Returns: undefined;
            };
            update_user_subscription_with_coupon: {
                Args: {
                    p_user_id: string;
                    p_plan_id: string;
                    p_subscription_data: Json;
                    p_coupon_code?: string;
                };
                Returns: {
                    success: boolean;
                    error_message: string;
                    subscription_id: string;
                }[];
            };
        };
        Enums: {
            auth_type: "bearer_token" | "none";
            banners_audience: "Guest" | "User" | "Subscriber";
            condition_type: "range" | "date_range" | "in";
            coupon_type_enum: "fixed_amount" | "percentage";
            question_section: "personal_details" | "data_entry" | "specific_scholarship";
            question_type:
                | "single_select"
                | "number_input"
                | "short_text"
                | "long_text"
                | "multi_select"
                | "date_picker"
                | "address_select"
                | "bank_select";
            scholarship_type: "submission" | "guidance";
            testimonial_type: "personal" | "institution";
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
};

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">;

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">];

export type Tables<
    DefaultSchemaTableNameOrOptions extends
        | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
        | { schema: keyof DatabaseWithoutInternals },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof DatabaseWithoutInternals;
    }
        ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
              DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
        : never = never
> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
}
    ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
          DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
          Row: infer R;
      }
        ? R
        : never
    : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
      ? (DefaultSchema["Tables"] & DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
            Row: infer R;
        }
          ? R
          : never
      : never;

export type TablesInsert<
    DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] | { schema: keyof DatabaseWithoutInternals },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof DatabaseWithoutInternals;
    }
        ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
        : never = never
> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
}
    ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
          Insert: infer I;
      }
        ? I
        : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
      ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
            Insert: infer I;
        }
          ? I
          : never
      : never;

export type TablesUpdate<
    DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"] | { schema: keyof DatabaseWithoutInternals },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof DatabaseWithoutInternals;
    }
        ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
        : never = never
> = DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
}
    ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
          Update: infer U;
      }
        ? U
        : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
      ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
            Update: infer U;
        }
          ? U
          : never
      : never;

export type Enums<
    DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"] | { schema: keyof DatabaseWithoutInternals },
    EnumName extends DefaultSchemaEnumNameOrOptions extends {
        schema: keyof DatabaseWithoutInternals;
    }
        ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
        : never = never
> = DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
}
    ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
    : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
      ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
      : never;

export type CompositeTypes<
    PublicCompositeTypeNameOrOptions extends
        | keyof DefaultSchema["CompositeTypes"]
        | { schema: keyof DatabaseWithoutInternals },
    CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
        schema: keyof DatabaseWithoutInternals;
    }
        ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
        : never = never
> = PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
}
    ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
    : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
      ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
      : never;

export const Constants = {
    graphql_public: {
        Enums: {}
    },
    public: {
        Enums: {
            auth_type: ["bearer_token", "none"],
            banners_audience: ["Guest", "User", "Subscriber"],
            condition_type: ["range", "date_range", "in"],
            coupon_type_enum: ["fixed_amount", "percentage"],
            question_section: ["personal_details", "data_entry", "specific_scholarship"],
            question_type: [
                "single_select",
                "number_input",
                "short_text",
                "long_text",
                "multi_select",
                "date_picker",
                "address_select",
                "bank_select"
            ],
            scholarship_type: ["submission", "guidance"],
            testimonial_type: ["personal", "institution"]
        }
    }
} as const;
