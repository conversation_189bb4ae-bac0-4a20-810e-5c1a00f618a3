module.exports = {
    testEnvironment: "jsdom",
    setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
    transform: {
        "^.+\\.(ts|tsx)$": ["ts-jest", { tsconfig: "tsconfig.jest.json" }]
    },
    moduleNameMapper: {
        "\\.(css|less|scss|sass)$": "identity-obj-proxy",
        "^@/(.*)$": "<rootDir>/$1",
        "^isows$": "<rootDir>/node_modules/isows/_cjs/index.js"
    },
    transformIgnorePatterns: [
        "/node_modules/(?!(uuid|@supabase/ssr|@supabase/functions-js|@supabase/gotrue-js|@supabase/realtime-js|@supabase/postgrest-js|@supabase/storage-js|@clerk/nextjs|@clerk/shared|@clerk/backend|@clerk/clerk-js|isows)/)"
    ],
    collectCoverageFrom: [
        "app/**/*.{ts,tsx}",
        "components/**/*.{ts,tsx}",
        "config/**/*.{ts,tsx}",
        "hooks/**/*.{ts,tsx}",
        "lib/**/*.{ts,tsx}",
        "utils/**/*.{ts,tsx}",
        "!**/*.d.ts",
        "!**/node_modules/**",
        "!lib/**/*-constants.ts",
        "!lib/resend.ts",
        "!lib/stage-wise.tsx",
        "!tests/**/*.{ts,tsx}",
        "!types/**/*.{ts,tsx}",
        "!app/layout.tsx",
        "!app/not-found.tsx",
        "!app/(auth)/**/*.tsx",
        "!components/theme-provider.tsx",
        "!components/PostHogProvider.tsx",
        "!config/admin.ts",
        "!config/dashboard.ts",
        "!components/forms/fields/types.ts",
        "!components/layout/navigation/nav-config.ts",
        "!**/*constant*.ts",
        "!**/*constant*.tsx",
        "!**/*skeleton*.ts",
        "!**/*skeleton*.tsx",
        "!components/ui/**/*.{ts,tsx}",
        "!**/page.tsx"
    ],
    coverageDirectory: "coverage",
    coverageReporters: ["json-summary"]
};
