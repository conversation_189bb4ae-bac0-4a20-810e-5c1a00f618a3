import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: "http",
                hostname: "localhost"
            },
            {
                protocol: "http",
                hostname: "127.0.0.1"
            },
            {
                protocol: "https",
                hostname: "img.clerk.com"
            }
        ]
    },
    experimental: {
        authInterrupts: true
    },
    async rewrites() {
        return [
            {
                source: "/ingest/static/:path*",
                destination: "https://us-assets.i.posthog.com/static/:path*"
            },
            {
                source: "/ingest/:path*",
                destination: "https://us.i.posthog.com/:path*"
            },
            {
                source: "/ingest/decide",
                destination: "https://us.i.posthog.com/decide"
            }
        ];
    },
    // This is required to support PostHog trailing slash API requests
    skipTrailingSlashRedirect: true
};

export default nextConfig;
