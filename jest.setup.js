if (typeof TextEncoder === "undefined") {
    global.TextEncoder = require("util").TextEncoder;
    global.TextDecoder = require("util").TextDecoder;
}

require("@testing-library/jest-dom");
const { JSDOM } = require("jsdom");
const dompurify = require("dompurify");

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

// Mock Clerk server components
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() =>
        Promise.resolve({
            userId: "test-user-id",
            getToken: jest.fn(() => Promise.resolve("mock-jwt-token"))
        })
    )
}));

const { window } = new JSDOM("");
const DOMPurify = dompurify(window);

jest.mock("dompurify", () => ({
    sanitize: DOMPurify.sanitize
}));

global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

if (typeof Element !== "undefined") {
    Element.prototype.scrollIntoView = jest.fn();
}

beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation((...args) => {});
});

beforeAll(() => {
    jest.spyOn(console, "warn").mockImplementation((...args) => {});
});
