/**
 * Sanitizes a string to prevent XSS attacks by escaping HTML special characters
 * @param html The string that might contain unsafe HTML
 * @returns A sanitized string safe for rendering
 */
export function sanitizeHtml(html: string): string {
    return html
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
