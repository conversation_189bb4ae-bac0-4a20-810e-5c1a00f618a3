import "../globals.css";

import { auth } from "@clerk/nextjs/server";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { redirect } from "next/navigation";

import { PostHogProvider } from "@/components/PostHogProvider";
import { StagewiseProvider } from "@/lib/stage-wise";

export default async function AuthLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    const { userId } = await auth();
    if (userId) {
        return redirect("/");
    }

    return (
        <PostHogProvider>
            <StagewiseProvider />
            <div
                className="text-foreground flex flex-col min-h-screen overflow-hidden bg-gradient-to-b from-secondary/70 to-background/95"
                dir="rtl"
            >
                {children}

                <SpeedInsights />
                <Analytics />
            </div>
        </PostHogProvider>
    );
}
