"use client";

import { AuthForm } from "@/components/auth/auth-form";
import { AuthLayout } from "@/components/auth/auth-layout";
import { SiteLogo } from "@/components/layout/site-logo";
import { SignupPreferencesProvider } from "@/contexts/signup-preferences-context";

export default function Login() {
    return (
        <SignupPreferencesProvider>
            <AuthLayout showSkeleton={true} skeletonMode="signin">
                <div className="relative flex flex-col items-center min-h-[70vh] justify-center">
                    <div className="relative flex flex-col items-center w-full max-w-md mx-auto" style={{ zIndex: 1 }}>
                        <SiteLogo href="/" className="mx-auto mb-6 mt-6" size="xl" color="white" />
                        <AuthForm mode="signin" />
                    </div>
                </div>
            </AuthLayout>
        </SignupPreferencesProvider>
    );
}
