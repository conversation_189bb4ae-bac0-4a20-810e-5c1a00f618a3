import type { MetadataRoute } from "next";

import { createClientFromRequest } from "@/utils/supabase/server";

const getBaseUrl = () => process.env.NEXT_PUBLIC_VERCEL_URL || "http://localhost:3000";

const getScholarshipGroups = async () => {
    try {
        const supabase = await createClientFromRequest();
        const { data } = await supabase.from("groups_scholarship").select("*");
        return data ?? [];
    } catch (error) {
        console.error("Error fetching scholarship groups:", error);
        return [];
    }
};

const getScholarships = async () => {
    try {
        const supabase = await createClientFromRequest();
        const { data } = await supabase.from("scholarships").select("*").eq("is_public", true);
        return data ?? [];
    } catch (error) {
        console.error("Error fetching scholarships:", error);
        return [];
    }
};

const createSitemapItem = (
    path: string,
    priority = 0.5,
    changeFrequency: MetadataRoute.Sitemap[number]["changeFrequency"] = "monthly"
): MetadataRoute.Sitemap[number] => ({
    url: `${getBaseUrl()}${path}`,
    lastModified: new Date(),
    changeFrequency,
    priority
});

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const scholarshipGroups = await getScholarshipGroups();
    const scholarships = await getScholarships();

    const scholarshipSitemapItems = scholarships
        .filter((scholarship) => scholarship?.slug)
        .map((scholarship) => createSitemapItem(`/scholarships/${scholarship.slug}`));

    const scholarshipGroupSitemapItems = scholarshipGroups
        .filter((scholarshipGroup) => scholarshipGroup?.slug)
        .map((scholarshipGroup) => createSitemapItem(`/scholarships/groups/${scholarshipGroup.slug}`));

    return [
        {
            url: getBaseUrl(),
            lastModified: new Date(),
            changeFrequency: "yearly" as const,
            priority: 1
        },
        ...scholarshipSitemapItems,
        ...scholarshipGroupSitemapItems
    ];
}
