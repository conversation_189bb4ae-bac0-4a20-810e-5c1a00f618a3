"use client";

import { useEffect, useRef, useState } from "react";

import { OnboardingLoader } from "@/components/onboarding/onboarding-loader";
import { EligibleScholarshipsSummaryBanner } from "@/components/scholarship/eligible-scholarships-summary-banner";
import { Fireworks } from "@/components/ui/fireworks";
import { useScholarshipEligibility } from "@/hooks/use-scholarship-eligibility";

import { useOnboarding } from "../onboarding-context";

const TEXTS = {
    shareTitle: (count: number, amount: string) => `מצאתי ${count} מלגות מתאימות בסכום של עד ${amount}!`
};

export function EligibilityStep() {
    const { eligibleScholarships, totalMinAmount, totalMaxAmount, loading } = useScholarshipEligibility();
    const { dispatch } = useOnboarding();
    const [loaderDone, setLoaderDone] = useState(false);
    const prefersReducedMotion = useRef<boolean>(false);

    const isStepLoading = loading || !loaderDone;

    useEffect(() => {
        dispatch({ type: "SET_STEP_LOADING", payload: isStepLoading });
        return () => {
            dispatch({ type: "SET_STEP_LOADING", payload: false });
        };
    }, [isStepLoading, dispatch]);

    useEffect(() => {
        if (typeof window !== "undefined") {
            const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
            prefersReducedMotion.current = mediaQuery.matches;

            const handleChange = (e: MediaQueryListEvent) => {
                prefersReducedMotion.current = e.matches;
            };

            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener("change", handleChange);
                return () => mediaQuery.removeEventListener("change", handleChange);
            } else {
                // Fallback for older browsers
                mediaQuery.addListener(handleChange);
                return () => mediaQuery.removeListener(handleChange);
            }
        }
    }, []);

    const shareUrl = typeof window !== "undefined" ? window.location.href : "";
    const shareTitle = TEXTS.shareTitle(eligibleScholarships.length, totalMaxAmount.toLocaleString());

    return (
        <div className="flex flex-col w-full min-h-full px-4 md:px-12">
            {isStepLoading ? (
                <OnboardingLoader onComplete={() => setLoaderDone(true)} />
            ) : (
                <div className="flex flex-col items-center justify-center flex-1 w-full">
                    {loaderDone && eligibleScholarships.length > 0 && !prefersReducedMotion.current && (
                        <Fireworks show={true} duration={5000} />
                    )}
                    <EligibleScholarshipsSummaryBanner
                        eligibleCount={eligibleScholarships.length}
                        totalMinAmount={totalMinAmount}
                        totalMaxAmount={totalMaxAmount}
                        shareUrl={shareUrl}
                        shareTitle={shareTitle}
                    />
                </div>
            )}
        </div>
    );
}
