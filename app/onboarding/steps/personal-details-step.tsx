"use client";

import React from "react";

import type { DynamicQuestionsFormProps } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import { DynamicQuestionsForm } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import { useSignupPreferencesSave } from "@/hooks/use-signup-preferences-save";

import { useOnboarding } from "../onboarding-context";

const TEXTS = {
    titleStart: "ברוך הבא",
    titleEnd: "למילגפו!"
};

interface PersonalDetailsStepProps {
    prefetchedQuestions: NonNullable<DynamicQuestionsFormProps["prefetchedData"]>;
}

export function PersonalDetailsStep({ prefetchedQuestions }: PersonalDetailsStepProps) {
    const { personalDetailsFormRef, dispatch } = useOnboarding();

    // Background save of signup preferences
    const { isSaving, saveResult } = useSignupPreferencesSave();

    // Debug logging
    React.useEffect(() => {
        if (saveResult) {
            console.log("🔧 Preference save result:", saveResult);
        }
    }, [saveResult]);

    const handleFormSubmitEnd = React.useCallback(
        (success: boolean) => {
            dispatch({ type: "END_SUBMIT", payload: { success } });
        },
        [dispatch]
    );

    return (
        <div className="w-full min-h-full flex flex-col px-4 md:px-12 pb-8">
            <h1 className="text-3xl md:text-3xl font-extrabold text-primary mb-6 text-center leading-tight">
                {TEXTS.titleStart} <span className="text-blue-600">{TEXTS.titleEnd}</span>
            </h1>
            <div className="pb-8">
                <DynamicQuestionsForm
                    ref={personalDetailsFormRef}
                    sections={["personal_details"]}
                    hideSubmit
                    onSubmitEnd={handleFormSubmitEnd}
                    alwaysSuccessOnNoChanges
                    suppressSuccessToast
                    prefetchedData={prefetchedQuestions}
                />
            </div>
        </div>
    );
}
