"use client";

import { useEffect, useState } from "react";

import { calculateFinalPrice } from "@/app/actions/subscriptions-actions";
import { CouponSection } from "@/components/subscriptions/coupon-section";
import { OnboardingSubscriptionCard } from "@/components/subscriptions/onboarding-subscription-card";
import { SubscriptionsSkeleton } from "@/components/subscriptions/skeleton/subscriptions-skeleton";
import { FEATURES, PRICING_PLANS } from "@/config/subscriptions";
import type { CouponAppliedInfo } from "@/lib/subscription-constants";

import { useOnboarding } from "../onboarding-context";

const TEXTS = {
    title: "בחירת תוכנית תשלום"
};

export function PricingPlanStep() {
    const { selectedPlanId, validatedCoupon, dispatch } = useOnboarding();

    const [planDetails, setPlanDetails] = useState<{
        [planId: string]: { displayPrice: number; originalPrice: number; discountApplied: number };
    }>({});
    const [calculatedCouponSectionTotal, setCalculatedCouponSectionTotal] = useState<number | undefined>();
    const [isLoading, setIsLoading] = useState(true);

    const handlePlanClick = (itemId: string) => {
        dispatch({ type: "SELECT_PLAN", payload: itemId });
    };

    const handleCouponApplied = (couponInfo: CouponAppliedInfo) => {
        dispatch({ type: "SET_COUPON", payload: couponInfo });
    };

    const handleCouponInvalid = () => {
        dispatch({ type: "SET_COUPON", payload: null });
    };

    const getFeatureByKey = (featureId: string) => FEATURES[featureId] || { id: featureId, name: featureId };

    useEffect(() => {
        async function updatePricing() {
            setIsLoading(true);
            const newPlanDetails: {
                [planId: string]: { displayPrice: number; originalPrice: number; discountApplied: number };
            } = {};
            for (const item of PRICING_PLANS) {
                const originalPrice = item.price;
                const displayPrice = await calculateFinalPrice(originalPrice, validatedCoupon);
                let discountApplied = 0;
                if (displayPrice < originalPrice) {
                    discountApplied = Math.round((originalPrice - displayPrice) * 100) / 100;
                }
                newPlanDetails[item.id] = { displayPrice, originalPrice, discountApplied };
            }
            setPlanDetails(newPlanDetails);
            setIsLoading(false);
        }
        updatePricing();
    }, [validatedCoupon]);

    useEffect(() => {
        async function updateCouponSectionTotal() {
            const selectedPlanOriginalPrice = selectedPlanId
                ? PRICING_PLANS.find((p) => p.id === selectedPlanId)?.price
                : undefined;
            if (selectedPlanOriginalPrice !== undefined) {
                const couponSectionTotal = await calculateFinalPrice(selectedPlanOriginalPrice, validatedCoupon);
                setCalculatedCouponSectionTotal(couponSectionTotal);
            } else {
                setCalculatedCouponSectionTotal(undefined);
            }
        }
        updateCouponSectionTotal();
    }, [selectedPlanId, validatedCoupon]);

    if (isLoading) {
        return (
            <div className="w-full min-h-full flex flex-col px-4 md:px-12">
                <h2 className="text-2xl font-bold text-primary mb-4 text-center">{TEXTS.title}</h2>
                <SubscriptionsSkeleton cardCount={PRICING_PLANS.length} hideComparisonTable />
            </div>
        );
    }

    return (
        <div className="w-full min-h-full flex flex-col px-4 md:px-12">
            <h2 className="text-2xl font-bold text-primary mb-4 text-center">{TEXTS.title}</h2>
            <div className="w-full max-w-md mx-auto mb-0">
                <CouponSection
                    hideTitle
                    onCouponApplied={handleCouponApplied}
                    onCouponInvalid={handleCouponInvalid}
                    currentTotalAmount={calculatedCouponSectionTotal}
                />
            </div>
            <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-start mt-0">
                {PRICING_PLANS.map((item) => {
                    const details = planDetails[item.id];
                    if (!details) return null;

                    return (
                        <OnboardingSubscriptionCard
                            key={item.id}
                            item={{
                                ...item,
                                price: details.displayPrice,
                                originalPrice:
                                    details.originalPrice > details.displayPrice ? details.originalPrice : undefined
                            }}
                            onSelect={handlePlanClick}
                            getFeatureByKey={getFeatureByKey}
                            selected={selectedPlanId === item.id}
                            appliedDiscount={details.discountApplied > 0 ? details.discountApplied : undefined}
                        />
                    );
                })}
            </div>
        </div>
    );
}
