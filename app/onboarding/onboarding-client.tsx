"use client";
import { useUser } from "@clerk/nextjs";
import * as React from "react";

import type { DynamicQuestionsFormProps } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import { SignupPreferencesProvider } from "@/contexts/signup-preferences-context";

import { OnboardingNavigation } from "./components/onboarding-navigation";
import { OnboardingProvider, useOnboarding } from "./onboarding-context";
import { EligibilityStep } from "./steps/eligibility-step";
import { PersonalDetailsStep } from "./steps/personal-details-step";
import { PricingPlanStep } from "./steps/pricing-plan-step";

const TEXTS = {
    steps: ["פרטים אישיים", "פוטנציאל התאמה", "בחירת תוכנית תשלום"]
};

function OnboardingSteps({
    prefetchedQuestions
}: {
    prefetchedQuestions: NonNullable<DynamicQuestionsFormProps["prefetchedData"]>;
}) {
    const { step, showEligibility } = useOnboarding();

    if (step === 0) return <PersonalDetailsStep prefetchedQuestions={prefetchedQuestions} />;
    if (step === 1) {
        if (showEligibility) return <EligibilityStep />;
        return null;
    }
    if (step === 2) return <PricingPlanStep />;

    return null;
}

interface OnboardingClientProps {
    prefetchedQuestions: NonNullable<DynamicQuestionsFormProps["prefetchedData"]>;
    initialStage?: string | null;
}

export function OnboardingClientView({ prefetchedQuestions }: OnboardingClientProps) {
    const { error, step } = useOnboarding();
    return (
        <div className="flex flex-col items-center justify-center flex-1 h-full md:min-h-[92vh] py-0 bg-background">
            <div className="w-full max-w-7xl flex flex-col items-stretch justify-center relative">
                <div className="flex flex-col h-[80vh] min-h-[80vh] max-h-[80vh] items-center justify-center flex-1 relative bg-white md:bg-white/70 md:backdrop-blur-md md:rounded-2xl md:shadow-2xl md:border md:border-white/30 animate-fade-in p-0 md:p-6">
                    {error && (
                        <div className="mb-4 w-full max-w-md mx-auto bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-center">
                            {error}
                        </div>
                    )}
                    {step > -1 && (
                        <div className="flex items-center justify-center w-full px-4 md:px-12 pb-6 pt-10">
                            {TEXTS.steps.map((_, idx) => (
                                <React.Fragment key={idx}>
                                    <div
                                        className={`h-2 rounded flex-1 transition-all duration-300 ${step >= idx ? "bg-primary" : "bg-gray-200"}`}
                                        style={{
                                            boxShadow: step === idx ? "0 0 8px 2px #2563eb33" : undefined,
                                            opacity: step === idx ? 1 : 0.7
                                        }}
                                    />
                                    {idx < TEXTS.steps.length - 1 && <div className="w-4" />}
                                </React.Fragment>
                            ))}
                        </div>
                    )}
                    <div className="flex-1 w-full overflow-y-auto flex transition-opacity duration-500 opacity-100 animate-fade-in">
                        <OnboardingSteps prefetchedQuestions={prefetchedQuestions} />
                    </div>
                    <div
                        className="pointer-events-none absolute left-0 w-full h-8 bg-gradient-to-t from-white to-transparent z-20"
                        style={{ bottom: "64px" }}
                    />
                    <OnboardingNavigation />
                </div>
            </div>
        </div>
    );
}

export default function OnboardingClient({ prefetchedQuestions, initialStage }: OnboardingClientProps) {
    const { user } = useUser();
    return (
        <SignupPreferencesProvider>
            <OnboardingProvider userId={user?.id} initialStage={initialStage}>
                <OnboardingClientView prefetchedQuestions={prefetchedQuestions} />
            </OnboardingProvider>
        </SignupPreferencesProvider>
    );
}
