"use client";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";

import { completeOnboarding } from "@/app/actions/onboarding-actions";
import { calculateFinalPrice, incrementCouponUsage, updateUserSubscription } from "@/app/actions/subscriptions-actions";
import { PRICING_PLANS } from "@/config/subscriptions";
import type { CouponAppliedInfo } from "@/lib/subscription-constants";
import { getSupabaseClient } from "@/utils/supabase/client";
import { setUserClaim, UserClaimKey } from "@/utils/user-claims-client";

import { useOnboarding } from "../onboarding-context";

interface OnboardingSubmitOptions {
    selectedPlanId: string | null;
    validatedCoupon: CouponAppliedInfo | null;
}

export function useOnboardingSubmit() {
    const { dispatch } = useOnboarding();
    const { user } = useUser();
    const router = useRouter();

    const handleFinalSubmit = async ({ selectedPlanId, validatedCoupon }: OnboardingSubmitOptions) => {
        if (!selectedPlanId) {
            dispatch({ type: "SET_ERROR", payload: "יש לבחור תוכנית תשלום כדי להמשיך" });
            return;
        }
        const selectedPlan = PRICING_PLANS.find((p) => p.id === selectedPlanId);
        if (!selectedPlan) {
            dispatch({ type: "SET_ERROR", payload: "תוכנית לא נמצאה" });
            return;
        }

        dispatch({ type: "START_SUBMIT" });

        try {
            const finalPrice = await calculateFinalPrice(selectedPlan.price, validatedCoupon);

            if (finalPrice === 0) {
                if (!user?.id) {
                    dispatch({ type: "SET_ERROR", payload: "משתמש לא מזוהה" });
                    dispatch({ type: "END_SUBMIT", payload: { success: false } });
                    return;
                }
                const res = await updateUserSubscription(user.id, selectedPlan.id);
                if (!res.success) {
                    dispatch({ type: "SET_ERROR", payload: res.error || "שגיאה בשמירת המנוי" });
                    dispatch({ type: "END_SUBMIT", payload: { success: false } });
                    return;
                }
                if (validatedCoupon) {
                    await incrementCouponUsage(validatedCoupon.couponCode);
                }
                const onboardingRes = await completeOnboarding();
                if (onboardingRes?.message && typeof onboardingRes.message !== "string") {
                    if (user?.id) {
                        const supabase = getSupabaseClient();
                        await setUserClaim(supabase, user.id, UserClaimKey.ONBOARDING_STAGE, "completed");
                    }
                    await user?.reload();
                    router.push("/dashboard");
                } else if (onboardingRes && typeof onboardingRes.message === "string") {
                    dispatch({ type: "SET_ERROR", payload: onboardingRes.message });
                }
            } else {
                const userInfo = {
                    name: user?.firstName || user?.fullName?.split(" ")[0] || "",
                    lastName: user?.lastName || user?.fullName?.split(" ").slice(1).join(" ") || "",
                    email: user?.emailAddresses?.[0]?.emailAddress || "",
                    phone: user?.phoneNumbers?.[0]?.phoneNumber || ""
                };
                if (finalPrice <= 0) {
                    dispatch({
                        type: "SET_ERROR",
                        payload: "שגיאה בתמחור התוכנית לאחר הנחה."
                    });
                    return;
                }
                const response = await fetch("/api/generate-checkout-url", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        total_amount: finalPrice,
                        order_id: selectedPlan.id,
                        planName: selectedPlan.title,
                        customer_info: userInfo,
                        coupon_code: validatedCoupon?.couponCode
                    })
                });
                const result = await response.json();
                if (response.ok && result.success && result.signedUrl) {
                    if (user?.id) {
                        const supabase = getSupabaseClient();
                        await setUserClaim(supabase, user.id, UserClaimKey.ONBOARDING_STAGE, "completed");
                    }
                    window.location.href = result.signedUrl;
                } else {
                    dispatch({ type: "SET_ERROR", payload: result.error || "שגיאה בתשלום" });
                }
            }
        } catch {
            dispatch({ type: "SET_ERROR", payload: "אירעה שגיאה לא צפויה. אנא נסה שוב או פנה לתמיכה." });
        }
    };

    return { handleFinalSubmit };
}
