import { currentUser } from "@clerk/nextjs/server";

import { createClientFromRequest } from "@/utils/supabase/server";
import { getUserClaim, UserClaim<PERSON>ey } from "@/utils/user-claims-client";

import { fetchQuestionsData } from "../actions/dynamic-questions-form-actions";
import OnboardingClient from "./onboarding-client";

export default async function OnboardingPage() {
    const user = await currentUser();
    const userId = user?.id;

    if (!userId) return <div>Not logged in</div>;

    const supabase = await createClientFromRequest();

    const [prefetched, onboardingStageClaim] = await Promise.all([
        fetchQuestionsData(["personal_details", "specific_scholarship", "data_entry"], userId),
        getUserClaim(supabase, userId, UserClaimKey.ONBOARDING_STAGE)
    ]);

    if (!prefetched.data) return <div>Failed to load onboarding data.</div>;
    return (
        <OnboardingClient prefetchedQuestions={prefetched.data} initialStage={onboardingStageClaim as string | null} />
    );
}
