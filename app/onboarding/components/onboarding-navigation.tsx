"use client";

import { useOnboardingSubmit } from "../hooks/use-onboarding-submit";
import { useOnboarding } from "../onboarding-context";

const TEXTS = {
    steps: ["פרטים אישיים", "פוטנציאל התאמה", "בחירת תוכנית תשלום"],
    back: "חזור",
    next: "המשך",
    finish: "סיום"
};

export function OnboardingNavigation() {
    const { step, isSubmitting, selectedPlanId, personalDetailsFormRef, validatedCoupon, dispatch, isStepLoading } =
        useOnboarding();
    const { handleFinalSubmit } = useOnboardingSubmit();

    const handleNext = () => {
        if (step === 0) {
            dispatch({ type: "START_SUBMIT" });
            personalDetailsFormRef.current?.submit();
        } else if (step < TEXTS.steps.length - 1) {
            dispatch({ type: "NEXT_STEP" });
        } else {
            handleFinalSubmit({ selectedPlanId, validatedCoupon });
        }
    };

    const handleBack = () => {
        dispatch({ type: "PREVIOUS_STEP" });
    };

    if (step === -1) return null;

    return (
        <div className="flex justify-between items-center w-full px-4 md:px-12 pb-6 gap-4 bg-white z-30 absolute left-0 bottom-0">
            <button
                type="button"
                className="bg-gray-200 text-gray-700 rounded-lg py-2 px-8 font-bold hover:bg-gray-300 transition disabled:opacity-50 w-full md:w-auto"
                onClick={handleBack}
                disabled={step === 0 || isSubmitting}
            >
                {TEXTS.back}
            </button>
            <button
                type="button"
                className="relative bg-primary text-white rounded-lg py-2 px-8 font-bold hover:bg-primary/90 transition w-full md:w-auto disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center"
                onClick={handleNext}
                disabled={isSubmitting || isStepLoading || (step === TEXTS.steps.length - 1 && !selectedPlanId)}
            >
                {step === TEXTS.steps.length - 1 ? TEXTS.finish : TEXTS.next}
                {isSubmitting && (
                    <svg
                        className="animate-spin h-5 w-5 mr-2 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                    </svg>
                )}
            </button>
        </div>
    );
}
