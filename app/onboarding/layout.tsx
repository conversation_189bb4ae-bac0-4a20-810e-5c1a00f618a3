import "../globals.css";

import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

import { LogoutButton } from "@/components/auth/logout-button";
import { SiteLogo } from "@/components/layout/site-logo";
import { Banner } from "@/components/ui/banner";
import { StagewiseProvider } from "@/lib/stage-wise";
import type { CustomJwtSessionClaims } from "@/types/auth";

export default async function OnboardingLayout({ children }: { children: React.ReactNode }) {
    const sessionClaims = (await auth()).sessionClaims as unknown as CustomJwtSessionClaims;
    if (sessionClaims?.metadata?.onboardingComplete === true) {
        redirect("/");
    }

    return (
        <div className="min-h-screen bg-background font-sans antialiased" dir="rtl">
            <StagewiseProvider />
            <div className="flex min-h-screen flex-col">
                <div className="flex flex-col">
                    <Banner />
                    <div className="relative w-full z-40 bg-white">
                        <div className="container flex h-16 items-center justify-between">
                            <SiteLogo href="/" size="lg" />
                            <LogoutButton />
                        </div>
                    </div>
                </div>
                <main className="flex-1">{children}</main>
            </div>
        </div>
    );
}
