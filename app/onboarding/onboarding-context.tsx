"use client";

import type { SupabaseClient } from "@supabase/supabase-js";
import { createContext, createRef, Dispatch, useContext, useEffect, useMemo, useReducer, useRef } from "react";

import type { DynamicQuestionsFormHandle } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import type { CouponAppliedInfo } from "@/lib/subscription-constants";
import type { Database } from "@/types/database.types";
import { getSupabaseClient } from "@/utils/supabase/client";
import { setUserClaim, UserClaimKey } from "@/utils/user-claims-client";

interface OnboardingState {
    step: number;
    selectedPlanId: string | null;
    isSubmitting: boolean;
    error: string;
    showEligibility: boolean;
    validatedCoupon: CouponAppliedInfo | null;
    personalDetailsFormRef: React.RefObject<DynamicQuestionsFormHandle | null>;
    isStepLoading: boolean;
}

type OnboardingAction =
    | { type: "SET_STEP"; payload: number }
    | { type: "NEXT_STEP" }
    | { type: "PREVIOUS_STEP" }
    | { type: "START_SUBMIT" }
    | { type: "END_SUBMIT"; payload: { success: boolean } }
    | { type: "SELECT_PLAN"; payload: string }
    | { type: "SET_COUPON"; payload: CouponAppliedInfo | null }
    | { type: "SET_ERROR"; payload: string }
    | { type: "SET_STEP_LOADING"; payload: boolean };

export const initialState: OnboardingState = {
    step: 0,
    selectedPlanId: null,
    isSubmitting: false,
    error: "",
    showEligibility: false,
    validatedCoupon: null,
    personalDetailsFormRef: createRef<DynamicQuestionsFormHandle | null>(),
    isStepLoading: false
};

const stepToStage: Record<number, string> = {
    0: "personal_details",
    1: "potential_teaser",
    2: "pricing_plan"
};

const stageToStep: Record<string, number> = Object.fromEntries(
    Object.entries(stepToStage).map(([step, stage]) => [stage, Number(step)])
);

export function onboardingReducer(state: OnboardingState, action: OnboardingAction): OnboardingState {
    switch (action.type) {
        case "SET_STEP":
            return { ...state, step: action.payload, error: "" };
        case "NEXT_STEP":
            return { ...state, step: state.step + 1 };
        case "PREVIOUS_STEP":
            if (state.step <= 0) return state;
            return { ...state, step: state.step - 1 };
        case "START_SUBMIT":
            return { ...state, isSubmitting: true, error: "" };
        case "END_SUBMIT":
            if (action.payload.success) {
                return { ...state, isSubmitting: false, step: state.step + 1, showEligibility: true };
            }
            return { ...state, isSubmitting: false };
        case "SELECT_PLAN":
            return { ...state, selectedPlanId: action.payload, error: "" };
        case "SET_COUPON":
            return { ...state, validatedCoupon: action.payload, error: "" };
        case "SET_ERROR":
            return { ...state, error: action.payload };
        case "SET_STEP_LOADING":
            return { ...state, isStepLoading: action.payload };
        default:
            return state;
    }
}

interface OnboardingProviderProps {
    children: React.ReactNode;
    userId?: string;
    initialSupabaseClient?: SupabaseClient<Database> | null;
    initialStage?: string | null;
}

export const OnboardingContext = createContext<
    | (OnboardingState & {
          dispatch: Dispatch<OnboardingAction>;
          userId?: string | null;
          supabase: SupabaseClient<Database> | null;
      })
    | undefined
>(undefined);

export function OnboardingProvider({
    children,
    userId,
    initialSupabaseClient,
    initialStage
}: Readonly<OnboardingProviderProps>) {
    const getInitialStep = () => {
        if (initialStage && stageToStep[initialStage] !== undefined) {
            return stageToStep[initialStage];
        }
        return 0;
    };
    const initialStep = getInitialStep();

    const [state, dispatch] = useReducer(onboardingReducer, {
        ...initialState,
        step: initialStep,
        showEligibility: initialStep >= 1,
        personalDetailsFormRef: useRef<DynamicQuestionsFormHandle>(null)
    });

    const supabase = useMemo(() => {
        if (initialSupabaseClient !== undefined) return initialSupabaseClient;
        try {
            return getSupabaseClient();
        } catch (error) {
            console.error("Failed to create Supabase client:", error);
            return null;
        }
    }, [initialSupabaseClient]);

    useEffect(() => {
        const updateUserClaim = async () => {
            if (userId && state.step in stepToStage && supabase) {
                await setUserClaim(supabase, userId, UserClaimKey.ONBOARDING_STAGE, stepToStage[state.step]);
            }
        };

        updateUserClaim().catch((err) => {
            console.error("Failed to set user claim for onboarding stage:", err);
        });
    }, [state.step, userId, supabase]);

    const contextValue = useMemo(() => ({ ...state, dispatch, userId, supabase }), [state, dispatch, userId, supabase]);

    return <OnboardingContext.Provider value={contextValue}>{children}</OnboardingContext.Provider>;
}

export const useOnboarding = () => {
    const context = useContext(OnboardingContext);
    if (context === undefined) {
        throw new Error("useOnboarding must be used within an OnboardingProvider");
    }
    return context;
};
