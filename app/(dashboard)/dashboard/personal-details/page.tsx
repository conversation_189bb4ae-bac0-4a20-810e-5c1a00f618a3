"use client";

import { LoadingIcon } from "@/components/common/loading-icon";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { DynamicQuestionsForm } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import type { QuestionSection } from "@/components/forms/dynamic-questions-form/types";
import { Card } from "@/components/ui/card";
import { useSubscription } from "@/hooks/use-subscription";

const TEXTS = {
    pageTitle: "פרטי חשבון",
    pageDescription: "כאן תוכל לעדכן את פרטי החשבון שלך, כולל פרטים אישיים והגדרות.",
    loading: "טוען נתוני מנוי..."
};

export default function AccountPage() {
    const { subscription, loading } = useSubscription();

    const sections: QuestionSection[] =
        subscription?.planType === "free" ? ["personal_details"] : ["personal_details", "data_entry"];

    if (loading) {
        return (
            <div className="fixed inset-0 flex items-center justify-center bg-background">
                <LoadingIcon text={TEXTS.loading} />
            </div>
        );
    }

    return (
        <div className="p-8 h-full w-full overflow-auto" dir="rtl">
            <Card className="w-full p-8">
                <DashboardHeader title={TEXTS.pageTitle} description={TEXTS.pageDescription} />
                <DynamicQuestionsForm sections={sections} columns={2} />
            </Card>
        </div>
    );
}
