"use client";

import { useEffect, useMemo, useRef } from "react";
import { toast } from "sonner";

import { EmptyState } from "@/components/common/empty-state";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { EligibleScholarshipCardWithSkeleton } from "@/components/scholarship/eligible-scholarship-card";
import { Card } from "@/components/ui/card";
import { useScholarshipEligibility } from "@/hooks/use-scholarship-eligibility";
import { useUserScholarshipApplications } from "@/hooks/use-user-scholarship-applications";

const TEXTS = {
    pageTitle: "מלגות מתאימות עבורך",
    pageDescription: "בחר את המלגות אליהן תרצה להיות מוגש או מודרך.",
    noEligibleScholarships: "לא נמצאו מלגות מתאימות. נסה לעדכן את הפרופיל שלך או לענות על שאלות נוספות.",
    noScholarshipsData: "לא נמצאו נתוני מלגות זמינים כרגע. אנא נסה שוב מאוחר יותר.",
    errorLoadingScholarships: "אירעה שגיאה בטעינת המלגות. אנא נסה שוב מאוחר יותר."
};

export default function EligibleScholarshipsPage() {
    const prefersReducedMotion = useRef<boolean>(false);
    const { eligibility, eligibleScholarships, loading, error } = useScholarshipEligibility();

    useEffect(() => {
        if (typeof window !== "undefined") {
            const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
            prefersReducedMotion.current = mediaQuery.matches;

            const handleChange = (e: MediaQueryListEvent) => {
                prefersReducedMotion.current = e.matches;
            };

            mediaQuery.addEventListener("change", handleChange);
            return () => mediaQuery.removeEventListener("change", handleChange);
        }
    }, []);

    const eligibleScholarshipIds = useMemo(
        () => eligibleScholarships.map((s) => s.scholarshipId),
        [eligibleScholarships]
    );
    const {
        shouldApplyMap,
        updateShouldApply,
        error: applicationError
    } = useUserScholarshipApplications(eligibleScholarshipIds);

    useEffect(() => {
        if (applicationError) {
            toast.error(applicationError);
        }
    }, [applicationError]);

    return (
        <div className="p-8 h-full w-full overflow-auto" dir="rtl">
            <Card className="w-full p-8">
                <DashboardHeader title={TEXTS.pageTitle} description={TEXTS.pageDescription} />

                {loading || eligibility === undefined ? (
                    <div className="columns-1 md:columns-2 gap-6">
                        {[1, 2, 3, 4, 5, 6].map((i) => (
                            <EligibleScholarshipCardWithSkeleton key={i} userPlan="premium" />
                        ))}
                    </div>
                ) : error ? (
                    <EmptyState
                        message={typeof error === "string" ? error : TEXTS.errorLoadingScholarships}
                        variant="error"
                    />
                ) : eligibleScholarships.length === 0 ? (
                    <EmptyState message={TEXTS.noScholarshipsData} variant="warning" />
                ) : eligibleScholarships.length > 0 ? (
                    <div className="columns-1 md:columns-2 gap-6">
                        {eligibleScholarships.map((scholarship, index) => (
                            <EligibleScholarshipCardWithSkeleton
                                key={scholarship.scholarshipId}
                                scholarship={scholarship}
                                isEligible={true}
                                index={index}
                                shouldApply={shouldApplyMap[scholarship.scholarshipId]}
                                onShouldApplyChange={(val) => updateShouldApply(scholarship.scholarshipId, val)}
                                scholarshipType={scholarship.scholarship_type || "submission"}
                                userPlan="premium"
                            />
                        ))}
                    </div>
                ) : (
                    <EmptyState message={TEXTS.noEligibleScholarships} variant="warning" />
                )}
            </Card>
        </div>
    );
}
