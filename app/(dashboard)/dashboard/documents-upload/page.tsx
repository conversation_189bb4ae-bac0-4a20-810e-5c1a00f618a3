"use client";

import { useAuth } from "@clerk/nextjs";
import React from "react";

import { LoadingIcon } from "@/components/common/loading-icon";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { DynamicDocumentUploadForm } from "@/components/forms/dynamic-document-upload-form";
import { Card } from "@/components/ui/card";

const TEXTS = {
    pageTitle: "העלאת מסמכים",
    pageDescription: "כאן תוכל להעלות את המסמכים הנדרשים עבור המלגות שאליהן אתה זכאי.",
    loadingAuth: "טוען אימות משתמש...",
    loginPrompt: "נראה שאינך מחובר. אנא התחבר כדי להעלות מסמכים.",
    loginRequiredTitle: "נדרשת התחברות"
};

export default function DocumentsUploadPage() {
    const { isLoaded: authLoaded } = useAuth();

    if (!authLoaded) {
        return (
            <div className="flex items-center justify-center h-full p-8" dir="rtl">
                <LoadingIcon text={TEXTS.loadingAuth} className="w-10 h-10" />
            </div>
        );
    }

    return (
        <div className="p-8 h-full w-full overflow-auto" dir="rtl">
            <Card className="w-full p-8">
                <DashboardHeader title={TEXTS.pageTitle} description={TEXTS.pageDescription} />
                <DynamicDocumentUploadForm />
            </Card>
        </div>
    );
}
