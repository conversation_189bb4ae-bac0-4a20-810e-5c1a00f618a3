"use client";

import { useEffect, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { SubscriptionStatus } from "@/components/account/subscription-status";
import { EmptyState } from "@/components/common/empty-state";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { EligibleScholarshipCardWithSkeleton } from "@/components/scholarship/eligible-scholarship-card";
import { EligibleScholarshipsSummaryBanner } from "@/components/scholarship/eligible-scholarships-summary-banner";
import { Card } from "@/components/ui/card";
import { useScholarshipEligibility } from "@/hooks/use-scholarship-eligibility";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

const TEXTS = {
    shareUrl: "https://app.milgapo.com/",
    shareTitle: "גיליתי מלגות מתאימות עבורי במילגפו!",
    pageTitle: "לוח בקרה",
    pageDescription: "כאן תוכל לראות את המלגות אליהן אתה זכאי, לנהל את המנוי שלך ולעדכן את פרטי החשבון שלך.",
    noScholarshipsFound: "לא נמצאו מלגות זמינות כרגע"
};

export default function DashboardPage() {
    const {
        eligibility,
        eligibleScholarships,
        totalMinAmount,
        totalMaxAmount,
        loading: scholarshipsLoading
    } = useScholarshipEligibility();
    const [currentSubscription, setCurrentSubscription] = useState<UserSubscriptionWithPlan | null | undefined>(
        undefined
    );

    useEffect(() => {
        async function fetchSubscription() {
            try {
                const subscription = await getCurrentUserSubscription();
                setCurrentSubscription(subscription);
            } catch (error) {
                console.error("Error fetching subscription:", error);
            }
        }

        fetchSubscription();
    }, []);

    const dummyShouldApplyMap: Record<string, boolean | undefined> = {};
    const dummyUpdateShouldApply = () => {};

    return (
        <div className="p-8 h-full w-full overflow-auto" dir="rtl">
            <Card className="w-full p-8">
                <DashboardHeader title={TEXTS.pageTitle} description={TEXTS.pageDescription} />

                <SubscriptionStatus currentSubscription={currentSubscription} />

                {!scholarshipsLoading && (
                    <EligibleScholarshipsSummaryBanner
                        eligibleCount={eligibleScholarships.length}
                        totalMinAmount={totalMinAmount}
                        totalMaxAmount={totalMaxAmount}
                        shareUrl={TEXTS.shareUrl}
                        shareTitle={TEXTS.shareTitle}
                    />
                )}

                <div className="mt-8">
                    {scholarshipsLoading || eligibility === undefined ? (
                        <div className="columns-1 md:columns-2 gap-6">
                            {[1, 2, 3, 4].map((i) => (
                                <EligibleScholarshipCardWithSkeleton key={i} userPlan="free" />
                            ))}
                        </div>
                    ) : eligibleScholarships.length > 0 ? (
                        <div className="columns-1 md:columns-2 gap-6">
                            {eligibleScholarships.slice(0, 6).map((scholarship, index) => (
                                <EligibleScholarshipCardWithSkeleton
                                    key={scholarship.scholarshipId}
                                    scholarship={scholarship}
                                    isEligible={true}
                                    index={index}
                                    shouldApply={dummyShouldApplyMap[scholarship.scholarshipId]}
                                    onShouldApplyChange={dummyUpdateShouldApply}
                                    scholarshipType={scholarship.scholarship_type || "submission"}
                                    userPlan="free"
                                />
                            ))}
                        </div>
                    ) : (
                        <EmptyState message={TEXTS.noScholarshipsFound} variant="warning" />
                    )}
                </div>
            </Card>
        </div>
    );
}
