"use client";

import { useUser } from "@clerk/nextjs";
import type { LucideIcon } from "lucide-react";
import { Menu } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { ClientOnly } from "@/components/common/client-only";
import { LoadingIcon } from "@/components/common/loading-icon";
import { PlanAccessGuard } from "@/components/layout/plan-access-guard";
import { AppSidebar, type GroupNavItem } from "@/components/layout/sidebar/app-sidebar";
import { hasAccess } from "@/components/layout/sidebar/plan-access";
import { SidebarSkeleton } from "@/components/layout/sidebar/sidebar-skeleton";
import { PostHogProvider } from "@/components/PostHogProvider";
import { Banner } from "@/components/ui/banner";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { SubscriptionBanner } from "@/components/ui/subscription-banner";
import { sidebarData } from "@/config/dashboard";
import { StagewiseProvider } from "@/lib/stage-wise";
import { PlanType } from "@/lib/subscription-constants";

export default function AccountLayout({ children }: { children: React.ReactNode }) {
    const { isLoaded } = useUser();

    const [userPlan, setUserPlan] = useState<PlanType | null>(null);
    const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(true);

    useEffect(() => {
        async function fetchSubscription() {
            try {
                const subscription = await getCurrentUserSubscription();
                setUserPlan(subscription?.planType || "free");
            } catch (error) {
                console.error("Error fetching subscription in AccountLayout:", error);
                setUserPlan("free");
            } finally {
                setIsSubscriptionLoading(false);
            }
        }
        if (isLoaded) {
            fetchSubscription();
        }
    }, [isLoaded]);

    type DashboardConfigItem = {
        name: string;
        url: string;
        icon?: LucideIcon;
        requiredPlan?: PlanType;
        title?: string;
    };

    const processGroupItems = useCallback(
        (
            group: { title: string; icon?: LucideIcon; items: DashboardConfigItem[] } | undefined,
            currentPlan: PlanType
        ): GroupNavItem[] => {
            if (!group || !group.items) return [];
            return group.items
                .map((item: DashboardConfigItem): GroupNavItem | null => {
                    const accessible = hasAccess(currentPlan, item.requiredPlan);
                    return {
                        name: item.name,
                        url: item.url,
                        icon: item.icon,
                        disabled: !accessible,
                        locked: !accessible,
                        requiredPlan: !accessible ? item.requiredPlan : undefined
                    };
                })
                .filter((item): item is GroupNavItem => item !== null);
        },
        []
    );

    const navGroupsForSidebar = useMemo(() => {
        const groups = [];
        const currentLoadedPlan = userPlan || "free";

        if (sidebarData.personalDataGroup) {
            const processedItems = processGroupItems(sidebarData.personalDataGroup, currentLoadedPlan);
            groups.push({
                title: sidebarData.personalDataGroup.title,
                icon: sidebarData.personalDataGroup.icon,
                items: processedItems
            });
        }
        if (sidebarData.scholarshipTrackingGroup) {
            const processedItems = processGroupItems(sidebarData.scholarshipTrackingGroup, currentLoadedPlan);
            groups.push({
                title: sidebarData.scholarshipTrackingGroup.title,
                icon: sidebarData.scholarshipTrackingGroup.icon,
                items: processedItems
            });
        }
        return groups;
    }, [userPlan, processGroupItems]);

    const LayoutFallback = () => (
        <div className="fixed inset-0 flex items-center justify-center bg-background">
            <LoadingIcon text="טוען..." />
        </div>
    );

    const configForGuard = useMemo(() => {
        return {
            ...sidebarData,
            navMain: (sidebarData.navMain || []).map((item) => ({ ...item, name: item.title || "" })),
            navSecondary: (sidebarData.navSecondary || []).map((item) => ({ ...item, name: item.title || "" }))
        };
    }, []);

    return (
        <PostHogProvider>
            <StagewiseProvider />
            <ClientOnly fallback={<LayoutFallback />}>
                <SidebarProvider defaultOpen={false}>
                    <div className="fixed inset-0 flex flex-row-reverse">
                        {!isLoaded || isSubscriptionLoading ? (
                            <SidebarSkeleton />
                        ) : (
                            <AppSidebar
                                navMain={(sidebarData.navMain || []).map((item) => ({
                                    ...item,
                                    name: item.title || ""
                                }))}
                                navGroups={navGroupsForSidebar}
                                navSecondary={(sidebarData.navSecondary || []).map((item) => ({
                                    ...item,
                                    name: item.title || ""
                                }))}
                            />
                        )}
                        <main className="flex-1 overflow-auto bg-gradient-to-br from-background to-background/80">
                            <Banner />
                            <SubscriptionBanner />
                            <div className="sticky top-0 z-10 flex items-center justify-between p-4 md:hidden bg-background/80 backdrop-blur-sm border-b">
                                <div className="flex-1"></div>
                                <SidebarTrigger className="z-20 relative">
                                    <Menu className="h-5 w-5" />
                                </SidebarTrigger>
                            </div>
                            <PlanAccessGuard dashboardConfig={configForGuard}>{children}</PlanAccessGuard>
                        </main>
                    </div>
                </SidebarProvider>
            </ClientOnly>
        </PostHogProvider>
    );
}
