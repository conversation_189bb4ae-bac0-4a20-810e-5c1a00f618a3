"use server";

import { TESTIMONIAL_TEXTS, type TestimonialFormValues } from "@/lib/testimonial-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export async function getTestimonial(
    id: string
): Promise<{ success: boolean; data?: Tables<"testimonials">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("testimonials").select("*").eq("id", id).single();

        if (error) throw error;

        if (!data) {
            return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_NOT_FOUND };
        }
        return { success: true, data };
    } catch (error) {
        console.error("Error fetching testimonial:", error);
        return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_FETCH_ERROR };
    }
}

export async function createTestimonial(data: TestimonialFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        if (!data.name || data.name.trim() === "") {
            return { success: false, error: TESTIMONIAL_TEXTS.nameRequired };
        }

        if (!data.text || data.text.trim() === "") {
            return { success: false, error: TESTIMONIAL_TEXTS.textRequired };
        }

        const testimonialData: TablesInsert<"testimonials"> = {
            name: data.name,
            text: data.text,
            type: data.type.id
        };

        const supabase = await createClientFromRequest();
        const { error: testimonialError } = await supabase.from("testimonials").insert(testimonialData);

        if (testimonialError) throw testimonialError;

        return { success: true };
    } catch (error) {
        console.error("Error creating testimonial:", error);
        return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_CREATE_ERROR };
    }
}

export async function updateTestimonial(
    id: string,
    data: TestimonialFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        if (!data.name || data.name.trim() === "") {
            return { success: false, error: TESTIMONIAL_TEXTS.nameRequired };
        }

        if (!data.text || data.text.trim() === "") {
            return { success: false, error: TESTIMONIAL_TEXTS.textRequired };
        }

        const testimonialData: TablesUpdate<"testimonials"> = {
            name: data.name,
            text: data.text,
            type: data.type.id
        };

        const supabase = await createClientFromRequest();
        const { error: testimonialError } = await supabase.from("testimonials").update(testimonialData).eq("id", id);
        if (testimonialError) throw testimonialError;

        return { success: true };
    } catch (error) {
        console.error("Error updating testimonial:", error);
        return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_UPDATE_ERROR };
    }
}

export async function deleteTestimonial(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("testimonials").delete().eq("id", id);
        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error("Error deleting testimonial:", error);
        return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_DELETE_ERROR };
    }
}

export async function getAllTestimonials(): Promise<{
    success: boolean;
    data?: Tables<"testimonials">[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("testimonials")
            .select("*")
            .order("created_at", { ascending: false });

        if (error) throw error;

        return { success: true, data: data || [] };
    } catch (error) {
        console.error("Error fetching testimonials:", error);
        return { success: false, error: TESTIMONIAL_TEXTS.TESTIMONIAL_FETCH_ERROR };
    }
}

export async function getTestimonials(ids?: string[]): Promise<{
    success: boolean;
    data?: Tables<"testimonials">[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        let finalItems: Tables<"testimonials">[] = [];

        if (ids && ids.length > 0) {
            const { data, error } = await supabase
                .from("testimonials")
                .select("*")
                .in("id", ids)
                .order("created_at", { ascending: false });

            if (error) throw error;

            if (data) {
                const itemsMap = new Map(data.map((item) => [item.id, item]));
                finalItems = ids
                    .map((id) => itemsMap.get(id))
                    .filter((item): item is Tables<"testimonials"> => item !== undefined);

                if (finalItems.length < 6) {
                    const remainingCount = 6 - finalItems.length;
                    const existingIds = finalItems.map((item) => item.id);

                    const { data: additionalData, error: additionalError } = await supabase
                        .from("testimonials")
                        .select("*")
                        .not("id", "in", `(${existingIds.join(",")})`)
                        .limit(remainingCount);

                    if (additionalError) throw additionalError;
                    if (additionalData) {
                        finalItems = [...finalItems, ...additionalData];
                    }
                }
            }
        } else {
            const { data, error } = await supabase.from("testimonials").select("*").limit(6);

            if (error) throw error;
            if (data) finalItems = data;
        }

        return { success: true, data: finalItems };
    } catch (error) {
        console.error("Error fetching testimonials:", error);
        return { success: false, error: "Failed to fetch testimonials" };
    }
}
