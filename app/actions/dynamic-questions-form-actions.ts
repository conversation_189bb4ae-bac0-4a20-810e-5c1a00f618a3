"use server";

import { auth } from "@clerk/nextjs/server";

import { sendCollaborationData } from "@/app/actions/collaboration-send-actions";
import { isAdminFromSessionClaims } from "@/lib/org-role";
import type { Database } from "@/types/database.types";
import { createClerkSupabaseClient } from "@/utils/supabase/server";

interface QuestionMetadata {
    label: string;
    required?: boolean;
    placeholder?: string;
    tooltip?: string;
    options?: Array<{ label: string; value: string } | string>;
    description?: string;
    max?: number;
    min?: number;
    max_date?: string;
    min_date?: string;
    showSearch?: boolean;
}

type ShapedQuestion = Omit<Question, "metadata"> & { metadata: QuestionMetadata };
type ShapedCondition = Omit<Condition, "value"> & { value: Record<string, unknown> };

interface UpsertAnswerResult {
    success: boolean;
    error?: string;
}

type Answer = Database["public"]["Tables"]["answers"]["Row"];
type Question = Database["public"]["Tables"]["questions"]["Row"];
type QuestionGroup = Database["public"]["Tables"]["groups_question"]["Row"];
type QuestionConditionLink = Database["public"]["Tables"]["link_question_to_condition"]["Row"];
type Condition = Database["public"]["Tables"]["conditions"]["Row"];

interface QuestionsDataResult {
    success: boolean;
    data?: {
        questions: ShapedQuestion[];
        questionGroups: Record<string, QuestionGroup>;
        conditions: ShapedCondition[];
        questionConditionLinks: QuestionConditionLink[];
        answers: Answer[];
    };
    error?: string;
}

export async function upsertAnswers(
    userId: string,
    answers: Array<{
        question_id: string;
        user_id: string;
        answer: string;
    }>,
    hiddenQuestionIds: string[] = []
): Promise<UpsertAnswerResult> {
    try {
        const supabase = await createClerkSupabaseClient();

        if (hiddenQuestionIds.length > 0) {
            const { error: deleteError } = await supabase
                .from("answers")
                .delete()
                .eq("user_id", userId)
                .in("question_id", hiddenQuestionIds);

            if (deleteError) {
                console.error("Error deleting hidden question answers:", deleteError);
                return { success: false, error: deleteError.message };
            }
        }

        if (answers.length > 0) {
            const { error } = await supabase.from("answers").upsert(answers, {
                onConflict: "question_id,user_id"
            });

            if (error) {
                console.error("Error upserting answers:", error);
                return { success: false, error: error.message };
            }
        }

        try {
            await sendCollaborationData(userId);
        } catch (collaborationError) {
            console.error("Error sending data to collaborations:", collaborationError);
        }

        return { success: true };
    } catch (error) {
        console.error("Error in upsertAnswers:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}

export async function fetchQuestionsData(
    sections: Database["public"]["Enums"]["question_section"][],
    userId: string,
    scholarshipId?: string
): Promise<QuestionsDataResult> {
    try {
        const { userId: sessionUserId, sessionClaims } = await auth();
        if (!sessionUserId) {
            return { success: false, error: "User not authenticated." };
        }
        if (userId !== sessionUserId && !isAdminFromSessionClaims(sessionClaims)) {
            return { success: false, error: "נדרשות הרשאות מנהל לביצוע פעולה זו." };
        }
        const supabase = await createClerkSupabaseClient();

        let questionIdData: { id: string; group_id: string }[] = [];
        let idError = null;

        if (scholarshipId) {
            const { data, error } = await supabase
                .from("link_question_to_scholarship")
                .select("question_id, questions!inner(id, group_id, section)")
                .eq("scholarship_id", scholarshipId);

            if (error) {
                idError = error;
            } else {
                questionIdData = (data ?? [])
                    .map((row) => row.questions)
                    .flat()
                    .filter((q) => sections.includes(q.section))
                    .map((q) => ({ id: q.id, group_id: q.group_id }));
            }
        } else {
            const { data, error } = await supabase
                .from("questions")
                .select("id, group_id, section")
                .in("section", sections);

            if (error) {
                idError = error;
            } else {
                questionIdData = data || [];
            }
        }

        if (idError) {
            throw idError;
        }

        if (!questionIdData || questionIdData.length === 0) {
            return {
                success: true,
                data: {
                    questions: [],
                    questionGroups: {},
                    conditions: [],
                    questionConditionLinks: [],
                    answers: []
                }
            };
        }

        const questionIds = Array.from(new Set(questionIdData.map((q) => q.id)));
        const groupIds = Array.from(new Set(questionIdData.map((q) => q.group_id)));

        const [questionsResponse, groupsResponse, answersResponse, linksResponse] = await Promise.all([
            supabase.from("questions").select("*").in("id", questionIds).order("created_at"),
            supabase.from("groups_question").select("*").in("id", groupIds).order("created_at"),
            supabase.from("answers").select("*").in("question_id", questionIds).eq("user_id", userId),
            supabase.from("link_question_to_condition").select("*").in("question_id", questionIds)
        ]);

        if (questionsResponse.error) throw questionsResponse.error;
        if (groupsResponse.error) throw groupsResponse.error;
        if (answersResponse.error) throw answersResponse.error;
        if (linksResponse.error) throw linksResponse.error;

        const links = linksResponse.data;

        let conditionsData: Condition[] = [];
        if (links.length > 0) {
            const conditionIds = Array.from(new Set(links.map((link) => link.condition_id)));
            const { data: conditionsResponse, error: conditionsError } = await supabase
                .from("conditions")
                .select("*")
                .in("id", conditionIds);

            if (conditionsError) throw conditionsError;
            conditionsData = conditionsResponse as Condition[];
        }

        const groupsMap = (groupsResponse.data || []).reduce((acc: Record<string, QuestionGroup>, group) => {
            acc[group.id] = group;
            return acc;
        }, {});

        const typedQuestions: ShapedQuestion[] = (questionsResponse.data || []).map((q) => ({
            ...q,
            metadata:
                q.metadata && typeof q.metadata === "object" && !Array.isArray(q.metadata)
                    ? { label: (q.metadata as Partial<QuestionMetadata>).label ?? "", ...q.metadata }
                    : { label: "" }
        }));

        const typedConditions: ShapedCondition[] = (conditionsData || []).map((c) => {
            let processedValue;
            if (Array.isArray(c.value)) {
                processedValue = c.value;
            } else if (c.value && typeof c.value === "object") {
                processedValue = c.value;
            } else {
                processedValue = {};
            }
            return {
                ...c,
                value: processedValue as Record<string, unknown>
            };
        });

        return {
            success: true,
            data: {
                questions: typedQuestions,
                questionGroups: groupsMap,
                conditions: typedConditions,
                questionConditionLinks: links as QuestionConditionLink[],
                answers: answersResponse.data as Answer[]
            }
        };
    } catch (error) {
        console.error("Error in fetchQuestionsData:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}

export type { ShapedCondition, ShapedQuestion };
