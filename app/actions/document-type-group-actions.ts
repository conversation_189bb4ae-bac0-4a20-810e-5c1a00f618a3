"use server";

import {
    type DocumentTypeGroupFormValues,
    type DocumentTypeGroupInsert,
    type DocumentTypeGroupUpdate,
    TEXTS
} from "@/lib/document-type-group-constants";
import { type Tables } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

export type DocumentTypeGroupWithCount = Tables<"groups_document_type"> & {
    document_types_count: number;
};

type GroupWithCountQuery = Tables<"groups_document_type"> & {
    document_types_count:
        | {
              count: number;
          }[]
        | null;
};

export async function getDocumentTypeGroups(): Promise<{
    success: boolean;
    data?: DocumentTypeGroupWithCount[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("groups_document_type")
            .select("*, document_types_count:document_types(count)")
            .order("name", { ascending: true });

        if (error) {
            throw error;
        }

        const groups = (data as GroupWithCountQuery[]).map((group) => {
            const count = group.document_types_count?.[0]?.count ?? 0;

            const { ...rest } = group;

            return {
                ...rest,
                document_types_count: count
            };
        });

        return { success: true, data: groups || [] };
    } catch (e: unknown) {
        console.error("Error in getDocumentTypeGroups:", e);
        return { success: false, error: TEXTS.FETCH_GROUPS_ERROR };
    }
}

export async function getDocumentTypeGroup(
    groupId: string
): Promise<{ success: boolean; data?: Tables<"groups_document_type">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("groups_document_type").select("*").eq("id", groupId).single();

        if (error) {
            throw error;
        }

        return { success: true, data };
    } catch (e: unknown) {
        console.error(`Error in getDocumentTypeGroup for id ${groupId}:`, e);
        return { success: false, error: TEXTS.FETCH_GROUP_ERROR };
    }
}

export async function createDocumentTypeGroup(
    data: DocumentTypeGroupFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
        const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";

        if (!sanitizedName?.trim()) {
            return { success: false, error: TEXTS.NAME_REQUIRED };
        }

        const sanitizedData: DocumentTypeGroupInsert = {
            name: sanitizedName.trim(),
            description: sanitizedDescription.trim() || null
        };

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("groups_document_type").insert(sanitizedData);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error("Error creating document type group:", error);
        return { success: false, error: TEXTS.CREATE_ERROR };
    }
}

export async function updateDocumentTypeGroup(
    groupId: string,
    data: DocumentTypeGroupFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
        const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";

        if (!sanitizedName?.trim()) {
            return { success: false, error: TEXTS.NAME_REQUIRED };
        }

        const sanitizedData: DocumentTypeGroupUpdate = {
            name: sanitizedName.trim(),
            description: sanitizedDescription.trim() || null
        };

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("groups_document_type").update(sanitizedData).eq("id", groupId);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error("Error updating document type group:", error);
        return { success: false, error: TEXTS.UPDATE_ERROR };
    }
}
