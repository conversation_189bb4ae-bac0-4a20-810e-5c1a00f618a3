"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { PRICING_PLANS } from "@/config/subscriptions";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";
import { TEXTS } from "@/lib/user-constants";
import { sanitizeEmail, sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

interface UserSearchResult {
    success: boolean;
    user?: {
        id: string;
        email: string;
        user_id: string;
    };
    error?: string;
}

const userDetailsRateLimit: Record<string, { count: number; lastReset: number }> = {};
const RATE_LIMIT_MAX = 10;
const RATE_LIMIT_WINDOW_MS = 60 * 1000;

export async function searchUserByEmail(email: string): Promise<UserSearchResult> {
    try {
        const sanitizedEmail = sanitizeEmail(email);
        if (!sanitizedEmail) {
            return { success: false, error: TEXTS.emailRequired };
        }

        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("user_claims")
            .select("id, user_id, claim_value")
            .eq("claim_key", "user_email")
            .eq("claim_value", `"${sanitizedEmail}"`)
            .order("updated_at", { ascending: false })
            .limit(1)
            .single();

        if (error) {
            console.error("Error searching user:", error);
            return { success: false, error: TEXTS.userNotFound };
        }

        if (!data) {
            return { success: false, error: TEXTS.userNotFound };
        }

        return {
            success: true,
            user: {
                id: data.id,
                email: data.claim_value as string,
                user_id: data.user_id
            }
        };
    } catch (error) {
        console.error("Error in searchUserByEmail:", error);
        return { success: false, error: TEXTS.userSearchError };
    }
}

export async function getUserDetailsByUserId(userId: string): Promise<UserSearchResult> {
    try {
        const { userId: requesterId } = await auth();
        if (!requesterId) {
            return { success: false, error: TEXTS.genericError };
        }
        const now = Date.now();
        const rl = userDetailsRateLimit[requesterId] || { count: 0, lastReset: now };
        if (now - rl.lastReset > RATE_LIMIT_WINDOW_MS) {
            rl.count = 0;
            rl.lastReset = now;
        }
        rl.count++;
        userDetailsRateLimit[requesterId] = rl;
        if (rl.count > RATE_LIMIT_MAX) {
            return { success: false, error: TEXTS.genericError };
        }
        const sanitizedUserId = sanitizeText(userId, 100);
        if (!sanitizedUserId) {
            return { success: false, error: TEXTS.userNotFound };
        }
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("user_claims")
            .select("id, user_id, claim_value")
            .eq("claim_key", "user_email")
            .eq("user_id", sanitizedUserId)
            .order("updated_at", { ascending: false })
            .limit(1)
            .single();
        if (error) {
            console.error("Error fetching user details:", error);
            return { success: false, error: TEXTS.userNotFound };
        }
        if (!data) {
            return { success: false, error: TEXTS.userNotFound };
        }

        return {
            success: true,
            user: {
                id: data.id,
                email: data.claim_value as string,
                user_id: data.user_id
            }
        };
    } catch (error) {
        console.error("Error in getUserDetailsByUserId:", error);
        return { success: false, error: TEXTS.userSearchError };
    }
}

export async function getUserSubscriptionByUserId(
    userId: string
): Promise<{ success: boolean; subscription?: UserSubscriptionWithPlan | null; error?: string }> {
    try {
        if (!userId?.trim()) {
            return { success: false, error: "User ID is required" };
        }

        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("user_subscriptions")
            .select("*")
            .eq("user_id", userId)
            .eq("is_active", true)
            .or(`expiration_date.gte.${new Date().toISOString()},expiration_date.is.null`)
            .order("created_at", { ascending: false })
            .limit(1);

        if (error) {
            console.error("Error fetching user subscription:", error);
            return { success: false, error: "Failed to fetch user subscription" };
        }

        if (!data || data.length === 0) {
            return { success: true, subscription: null };
        }

        const subscription = data[0];
        const planDetails = PRICING_PLANS.find((plan) => plan.id === subscription.plan_id);

        const subscriptionWithPlan: UserSubscriptionWithPlan = {
            ...subscription,
            planType: planDetails?.planType || "free"
        };

        return { success: true, subscription: subscriptionWithPlan };
    } catch (error) {
        console.error("Error in getUserSubscriptionByUserId:", error);
        return { success: false, error: "An unexpected error occurred" };
    }
}

export async function updateUserPlanByAdmin(
    adminUserId: string,
    targetUserId: string,
    planId: string
): Promise<{ success: boolean; error?: string }> {
    try {
        if (!adminUserId?.trim() || !targetUserId?.trim() || !planId?.trim()) {
            return { success: false, error: "Missing required parameters" };
        }

        const plan = PRICING_PLANS.find((p) => p.id === planId);
        if (!plan) {
            return { success: false, error: `Plan not found: ${planId}` };
        }

        const supabase = await createClientFromRequest();

        const now = new Date();
        let expirationDate: Date | null = null;
        if (plan.duration_days) {
            expirationDate = new Date(now);
            expirationDate.setDate(expirationDate.getDate() + plan.duration_days);
        }

        const subscriptionData = {
            user_id: targetUserId,
            plan_id: planId,
            start_date: now.toISOString(),
            expiration_date: expirationDate ? expirationDate.toISOString() : null,
            is_active: true,
            plan_price: 0,
            paid_amount: 0,
            payment_details: null,
            transaction_id: `admin_${adminUserId}_${Date.now()}`,
            order_id: `admin_update_${Date.now()}`
        };

        await supabase
            .from("user_subscriptions")
            .update({ is_active: false })
            .eq("user_id", targetUserId)
            .eq("is_active", true);

        const { error } = await supabase.from("user_subscriptions").insert(subscriptionData);

        if (error) {
            console.error("Error updating user subscription:", error);
            return { success: false, error: "Failed to update user subscription" };
        }

        revalidatePath(`/admin/users/${targetUserId}`);
        return { success: true };
    } catch (error) {
        console.error("Error in updateUserPlanByAdmin:", error);
        return { success: false, error: "An unexpected error occurred" };
    }
}
