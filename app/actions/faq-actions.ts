"use server";

import { cache } from "react";

import { type FaqFormValues, sanitizeFaqData, TEXTS, validateFaqData } from "@/lib/faq-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export async function createFaq(data: FaqFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        const validation = validateFaqData(data);
        if (!validation.valid) {
            return { success: false, error: validation.error };
        }

        const supabase = await createClientFromRequest();
        const { data: maxOrderData, error: maxOrderError } = await supabase
            .from("faq")
            .select("order_index")
            .order("order_index", { ascending: false })
            .limit(1);

        if (maxOrderError) throw maxOrderError;

        const nextOrderIndex = maxOrderData && maxOrderData.length > 0 ? maxOrderData[0].order_index + 1 : 1;

        const sanitizedData: TablesInsert<"faq"> = {
            ...sanitizeFaqData(data),
            order_index: nextOrderIndex
        };

        const { error } = await supabase.from("faq").insert(sanitizedData);

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error("Error creating FAQ:", error);
        return { success: false, error: TEXTS.FAQ_CREATE_ERROR };
    }
}

export async function updateFaq(id: string, data: FaqFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        const validation = validateFaqData(data);
        if (!validation.valid) {
            return { success: false, error: validation.error };
        }

        const sanitizedData: TablesUpdate<"faq"> = sanitizeFaqData(data);

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("faq").update(sanitizedData).eq("id", id);

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error("Error updating FAQ:", error);
        return { success: false, error: TEXTS.FAQ_UPDATE_ERROR };
    }
}

export async function getFaq(id: string): Promise<{ success: boolean; data?: Tables<"faq">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("faq").select("*").eq("id", id).single();

        if (error) throw error;
        if (!data) return { success: false, error: TEXTS.FAQ_NOT_FOUND };

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching FAQ:", error);
        return { success: false, error: TEXTS.FAQ_FETCH_ERROR };
    }
}

export async function getAllFaqs(): Promise<{ success: boolean; data?: Tables<"faq">[]; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("faq").select("*").order("order_index", { ascending: true });

        if (error) throw error;

        return { success: true, data: data || [] };
    } catch (error) {
        console.error("Error fetching FAQs:", error);
        return { success: false, error: TEXTS.FAQ_FETCH_ERROR };
    }
}

export async function deleteFaq(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("faq").delete().eq("id", id);

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error("Error deleting FAQ:", error);
        return { success: false, error: TEXTS.FAQ_DELETE_ERROR };
    }
}

export async function reorderFaqs(items: Tables<"faq">[]): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        for (const item of items) {
            const { error } = await supabase
                .from("faq")
                .update({
                    order_index: -Math.abs(item.order_index) - 1000
                })
                .eq("id", item.id);

            if (error) throw error;
        }

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const { error } = await supabase
                .from("faq")
                .update({
                    order_index: i + 1
                })
                .eq("id", item.id);

            if (error) throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error reordering FAQs:", error);
        return { success: false, error: TEXTS.FAQ_REORDER_ERROR };
    }
}

export const getPublicFaqs = cache(async () => {
    const result = await getAllFaqs();
    return {
        items: result.success ? result.data || [] : [],
        error: result.success ? null : result.error
    };
});
