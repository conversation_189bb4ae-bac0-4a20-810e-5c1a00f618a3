"use server";

import { revalidatePath } from "next/cache";

import { type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { sanitizeEmail } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

const TEXTS = {
    invalidEmail: "יש להזין כתובת אימייל תקינה",
    createError: "שגיאה ביצירת פרטי יצירת קשר",
    updateError: "שגיאה בעדכון פרטי יצירת קשר",
    fetchError: "שגיאה בטעינת פרטי יצירת קשר"
};

export async function getContacts() {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("contact").select("*").order("created_at", { ascending: false });

        if (error) {
            throw error;
        }

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching contacts:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}

export async function getContactById(id: string) {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("contact").select("*").eq("id", id).single();

        if (error) {
            throw error;
        }

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching contact by ID:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}

export async function deleteContact(id: string) {
    try {
        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("contact").delete().eq("id", id);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/contact");
        return { success: true };
    } catch (error) {
        console.error("Error deleting contact:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error occurred"
        };
    }
}

export async function createContact(data: { email: string }) {
    try {
        const sanitizedEmail = sanitizeEmail(data.email);
        if (!sanitizedEmail) {
            return {
                success: false,
                error: TEXTS.invalidEmail
            };
        }
        const supabase = await createClientFromRequest();
        const insertData: TablesInsert<"contact"> = { email: sanitizedEmail };
        const { error } = await supabase.from("contact").insert([insertData]);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/contact");
        return { success: true };
    } catch (error) {
        console.error("Error creating contact:", error);
        return {
            success: false,
            error: TEXTS.createError
        };
    }
}

export async function updateContact(id: string, data: { email: string }) {
    try {
        const sanitizedEmail = sanitizeEmail(data.email);
        if (!sanitizedEmail) {
            return {
                success: false,
                error: TEXTS.invalidEmail
            };
        }
        const supabase = await createClientFromRequest();
        const updateData: TablesUpdate<"contact"> = { email: sanitizedEmail };
        const { error } = await supabase.from("contact").update(updateData).eq("id", id);

        if (error) {
            throw error;
        }

        revalidatePath("/admin/contact");
        revalidatePath(`/admin/contact/${id}`);
        return { success: true };
    } catch (error) {
        console.error("Error updating contact:", error);
        return {
            success: false,
            error: TEXTS.updateError
        };
    }
}
