"use server";

import {
    BannerAudienceLabel,
    bannerColors,
    type BannerFormValues,
    bannerIdToColorMap,
    ColorSchemeOption
} from "@/lib/banner-constants";
import { type Database, type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

const TEXTS = {
    BANNER_DELETE_ERROR: "שגיאה במחיקת הבאנר. אנא נסה שנית.",
    BANNER_DELETE_SUCCESS: "הבאנר נמחק בהצלחה.",
    BANNER_FETCH_ERROR: "שגיאה בטעינת הבאנר",
    BANNER_NOT_FOUND: "הבאנר לא נמצא",
    BANNER_UPDATE_ERROR: "שגיאה בעדכון הבאנר",
    BANNER_CREATE_ERROR: "שגיאה ביצירת הבאנר",
    INVALID_COLOR_SCHEME: "סכמת צבעים לא חוקית"
};

type Audience = "Guest" | "User" | "Subscriber";

async function determineAudience(supabase: Awaited<ReturnType<typeof createClientFromRequest>>): Promise<Audience> {
    const {
        data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
        return "Guest";
    }

    const { data, error } = await supabase
        .from("user_subscriptions")
        .select("plan_id, is_active")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .maybeSingle();

    if (error || !data || !data.is_active || !data.plan_id) {
        return "User";
    }

    if (data.plan_id === "milgapo-basic") {
        return "User";
    }

    return "Subscriber";
}

export async function getActiveBanners(): Promise<{
    success: boolean;
    data?: Tables<"banners">[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const audienceType = await determineAudience(supabase);

        const { data, error } = await supabase
            .from("banners")
            .select(
                "id, title, text, background_color, text_color, icon, cta_text, cta_link, days_to_live, seconds_before_show, enable_dismiss, enabled, audience, created_at, updated_at"
            )
            .eq("enabled", true)
            .eq("audience", audienceType)
            .order("created_at", { ascending: false });

        if (error) throw error;

        return { success: true, data: data ?? [] };
    } catch (error) {
        console.error("Error fetching active banners:", error);
        return { success: false, error: TEXTS.BANNER_FETCH_ERROR };
    }
}

export async function getBanners(): Promise<{
    success: boolean;
    data?: Tables<"banners">[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("banners").select("*").order("created_at", { ascending: false });

        if (error) throw error;

        return { success: true, data: data ?? [] };
    } catch (error) {
        console.error("Error fetching banners:", error);
        return { success: false, error: TEXTS.BANNER_FETCH_ERROR };
    }
}

export async function getBanner(id: string): Promise<{ success: boolean; data?: Tables<"banners">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("banners").select("*").eq("id", id).single();

        if (error) throw error;

        if (!data) {
            return { success: false, error: TEXTS.BANNER_NOT_FOUND };
        }
        return { success: true, data };
    } catch (error) {
        console.error("Error fetching banner:", error);
        return { success: false, error: TEXTS.BANNER_FETCH_ERROR };
    }
}

export async function createBanner(data: BannerFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        const selectedColorScheme = bannerColors.find((c) => c.id === data.color_scheme?.id) as ColorSchemeOption;
        const colorScheme = bannerIdToColorMap[selectedColorScheme.id];

        const bannerData: TablesInsert<"banners"> = {
            title: data.title as string,
            text: data.text as string,
            audience: data.audience?.id as BannerAudienceLabel,
            background_color: colorScheme?.background,
            text_color: colorScheme?.text,
            icon: data.icon?.id,
            cta_text: data.cta_text || null,
            cta_link: data.cta_link || null,
            days_to_live: data.days_to_live,
            seconds_before_show: data.seconds_before_show,
            enable_dismiss: data.enable_dismiss,
            enabled: data.enabled
        };

        const supabase = await createClientFromRequest();
        const { error: bannerError } = await supabase.from("banners").insert(bannerData);

        if (bannerError) throw bannerError;

        return { success: true };
    } catch (error) {
        console.error("Error creating banner:", error);
        return { success: false, error: TEXTS.BANNER_CREATE_ERROR };
    }
}

export async function updateBanner(id: string, data: BannerFormValues): Promise<{ success: boolean; error?: string }> {
    try {
        const selectedColorScheme = bannerColors.find((c) => c.id === data.color_scheme?.id);
        if (!selectedColorScheme) {
            return { success: false, error: TEXTS.INVALID_COLOR_SCHEME };
        }

        const bannerData: TablesUpdate<"banners"> = {
            title: data.title || undefined,
            text: data.text || undefined,
            audience: data.audience?.id as Database["public"]["Enums"]["banners_audience"],
            background_color: data.color_scheme ? bannerIdToColorMap[data.color_scheme.id].background : "",
            text_color: data.color_scheme ? bannerIdToColorMap[data.color_scheme.id].text : "",
            icon: data.icon?.id || "",
            cta_text: data.cta_text || null,
            cta_link: data.cta_link || null,
            days_to_live: data.days_to_live,
            seconds_before_show: data.seconds_before_show,
            enable_dismiss: data.enable_dismiss,
            enabled: data.enabled
        };

        const supabase = await createClientFromRequest();
        const { error: bannerError } = await supabase.from("banners").update(bannerData).eq("id", id);

        if (bannerError) throw bannerError;

        return { success: true };
    } catch (error) {
        console.error("Error updating banner:", error);
        return { success: false, error: TEXTS.BANNER_UPDATE_ERROR };
    }
}

export async function deleteBanner(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { error: deleteError } = await supabase.from("banners").delete().eq("id", id);

        if (deleteError) throw deleteError;

        return { success: true };
    } catch (error) {
        console.error("Error deleting banner:", error);
        return { success: false, error: TEXTS.BANNER_DELETE_ERROR };
    }
}
