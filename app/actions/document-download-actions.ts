/* eslint-disable */
// @ts-nocheck
"use server";

import { auth } from "@clerk/nextjs/server";
import { type SupabaseClient } from "@supabase/supabase-js";
import JSZip from "jszip";
import { PageSizes, PDFDocument } from "pdf-lib";

import { type Option } from "@/components/forms/fields/dropdown-base";
import {
    type DocumentDownloadFormValues,
    MAX_FOLDER_NAME_LENGTH,
    sanitizeFolderName,
    TEXTS,
    validateEmail,
    validateFolderName
} from "@/lib/document-download-constants";
import { rateLimit } from "@/lib/rate-limit";
import { type Tables } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

export async function fetchAvailableDocuments(): Promise<{ success: boolean; documents?: Option[]; error?: string }> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.authError };
        }

        const rateLimitResult = rateLimit(userId);
        if (!rateLimitResult.success) {
            return { success: false, error: TEXTS.rateLimitError };
        }

        const supabase = await createClientFromRequest();
        const { data: documents, error } = await supabase.from("document_types").select("id, name").order("name");

        if (error) {
            console.error("Supabase error fetching documents:", error);
            throw error;
        }

        const documentOptions: Option[] =
            documents?.map((doc) => ({
                id: doc.id,
                label: doc.name || "מסמך ללא שם"
            })) || [];

        if (documentOptions.length === 0) {
            console.warn("No documents found in database - check if document_types table has data");
            return { success: true, documents: [] };
        }

        return { success: true, documents: documentOptions };
    } catch (error) {
        console.error("Error fetching documents:", error);
        return {
            success: false,
            error: `${TEXTS.loadingDocuments}: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}

type UserClaimData = {
    user_id: string;
    email: string;
};

type AnswerWithQuestion = Pick<Tables<"answers">, "user_id" | "question_id" | "answer"> & {
    questions: { question_text: string };
};

type UserData = UserClaimData & {
    answers: Array<{
        question_id: string;
        answer: string;
        questions: Array<{ question_text: string }>;
    }>;
};

export async function fetchUsers(emails: string[]): Promise<{ success: boolean; users?: UserData[]; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const validEmails = emails.filter(validateEmail);
        if (validEmails.length === 0) {
            return { success: false, error: TEXTS.userListRequired };
        }

        const { data: userClaimsData, error: claimsError } = await supabase
            .from("user_claims")
            .select("user_id, email:claim_value")
            .eq("claim_key", "user_email");

        if (claimsError) throw claimsError;

        const userClaims = userClaimsData as unknown as Array<{ user_id: string; email: string }>;

        const filteredUserClaims = userClaims.filter((claim) => validEmails.includes(claim.email));

        if (!filteredUserClaims.length) {
            return { success: false, error: TEXTS.noUsersFound };
        }

        const userIds = filteredUserClaims.map((claim) => claim.user_id);

        const { data: answersDataRaw, error: answersError } = await supabase
            .from("answers")
            .select("user_id, question_id, answer, questions(question_text)")
            .in("user_id", userIds);

        if (answersError) throw answersError;

        const answersData = answersDataRaw as unknown as AnswerWithQuestion[];

        const users: UserData[] = filteredUserClaims.map((claim) => ({
            user_id: claim.user_id,
            email: claim.email,
            answers: answersData
                .filter((answer) => answer.user_id === claim.user_id)
                .map((answer) => ({
                    question_id: answer.question_id,
                    answer: answer.answer,
                    questions: answer.questions ? [answer.questions] : []
                }))
        }));

        return { success: true, users };
    } catch (error) {
        console.error("Error fetching users:", error);
        return { success: false, error: TEXTS.loadingUsers };
    }
}

export async function generateDocumentDownload(
    formData: DocumentDownloadFormValues
): Promise<{ success: boolean; error?: string }> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.authError };
        }

        const rateLimitResult = rateLimit(userId, { maxRequests: 5, windowMs: 60 * 1000 }); // 5 requests per minute
        if (!rateLimitResult.success) {
            return { success: false, error: TEXTS.rateLimitError };
        }

        const supabase = await createClientFromRequest();

        const sanitizedRootFolderName = sanitizeText(formData.rootFolderName, MAX_FOLDER_NAME_LENGTH);
        if (!validateFolderName(sanitizedRootFolderName)) {
            return { success: false, error: TEXTS.rootFolderInvalid };
        }

        const validEmails = formData.userEmails.filter(validateEmail);
        if (validEmails.length === 0) {
            return { success: false, error: TEXTS.userListRequired };
        }

        const usersResult = await fetchUsers(validEmails);
        if (!usersResult.success || !usersResult.users) {
            return { success: false, error: usersResult.error };
        }

        const documentIds = [
            ...formData.individualDocuments.map((d) => d.id),
            ...formData.documentGroups.flatMap((g) => g.documents.map((d) => d.id))
        ];

        const { data: documents, error: documentsError } = await supabase
            .from("dynamic_document_uploads")
            .select(
                `
                id,
                user_id,
                document_type_id,
                file_path,
                file_name,
                file_size,
                mime_type,
                document_types:document_types(name)
            `
            )
            .in("document_type_id", documentIds)
            .in(
                "user_id",
                usersResult.users.map((u) => u.user_id)
            );

        if (documentsError) throw documentsError;

        if (!documents?.length) {
            return { success: false, error: TEXTS.noDocumentsFound };
        }

        const zip = new JSZip();
        const rootFolder = zip.folder(sanitizedRootFolderName);

        for (const user of usersResult.users) {
            const userDocuments = documents.filter((d) => d.user_id === user.user_id);
            if (userDocuments.length === 0) continue;

            const userFolderName = generateUserFolderName(user, formData.fieldSelection);
            const userFolder = rootFolder?.folder(sanitizeFolderName(userFolderName));

            await processSeparatedDocuments(
                supabase,
                userFolder,
                userDocuments.filter((d) => formData.individualDocuments.some((sd) => sd.id === d.document_type_id))
            );

            for (const group of formData.documentGroups) {
                await processCombinedDocuments(
                    supabase,
                    userFolder,
                    userDocuments.filter((d) => group.documents.some((gd) => gd.id === d.document_type_id)),
                    `${userFolderName} – ${group.name}`
                );
            }
        }

        await zip.generateAsync({ type: "blob" });

        return { success: true };
    } catch (error) {
        console.error("Error generating document download:", error);
        return { success: false, error: TEXTS.downloadError };
    }
}

function generateUserFolderName(user: UserData, fieldSelection: Option[]): string {
    const values: string[] = [];

    for (const field of fieldSelection) {
        if (field.id === "email") {
            values.push(user.email);
        } else if (field.id === "user_id") {
            values.push(user.user_id);
        } else {
            const answer = user.answers?.find((a) => a.question_id === field.id);
            if (answer?.answer) {
                values.push(answer.answer);
            }
        }
    }

    return values.join(" – ");
}

type DynamicDocumentUpload = {
    id: string;
    user_id: string;
    document_type_id: string;
    file_path: string;
    file_name: string | null;
    file_size: number | null;
    mime_type: string | null;
};

type DocumentUploadWithType = DynamicDocumentUpload & {
    document_types?: Pick<Tables<"document_types">, "name">[] | null;
};

async function processSeparatedDocuments(
    supabase: SupabaseClient,
    userFolder: JSZip | null | undefined,
    documents: DocumentUploadWithType[]
): Promise<void> {
    for (const doc of documents) {
        try {
            const { data: fileData, error } = await supabase.storage.from("dynamic-documents").download(doc.file_path);

            if (error) throw error;

            const fileName = doc.file_name || `${doc.document_types?.[0]?.name || "document"}.pdf`;
            userFolder?.file(fileName, fileData);
        } catch (error) {
            console.error(`Error processing document ${doc.id}:`, error);
        }
    }
}

async function processCombinedDocuments(
    supabase: SupabaseClient,
    userFolder: JSZip | null | undefined,
    documents: DocumentUploadWithType[],
    fileName: string
): Promise<void> {
    if (documents.length === 0) return;

    try {
        const pdfDoc = await PDFDocument.create();
        let hasContent = false;

        for (const doc of documents) {
            try {
                const { data: fileData, error } = await supabase.storage
                    .from("dynamic-documents")
                    .download(doc.file_path);

                if (error) throw error;

                const arrayBuffer = await fileData.arrayBuffer();

                if (doc.mime_type?.startsWith("image/")) {
                    try {
                        const page = pdfDoc.addPage(PageSizes.A4);
                        let image;

                        switch (doc.mime_type) {
                            case "image/png":
                                image = await pdfDoc.embedPng(arrayBuffer);
                                break;
                            case "image/jpeg":
                            case "image/jpg":
                                image = await pdfDoc.embedJpg(arrayBuffer);
                                break;
                            default:
                                console.warn(
                                    `Unsupported image format: ${doc.mime_type} for document ${doc.id}. Skipping image.`
                                );
                                continue;
                        }

                        const { width, height } = image.scale(1);
                        const pageWidth = page.getWidth();
                        const pageHeight = page.getHeight();

                        const scale = Math.min(pageWidth / width, pageHeight / height);
                        const scaledWidth = width * scale;
                        const scaledHeight = height * scale;

                        page.drawImage(image, {
                            x: (pageWidth - scaledWidth) / 2,
                            y: (pageHeight - scaledHeight) / 2,
                            width: scaledWidth,
                            height: scaledHeight
                        });
                        hasContent = true;
                    } catch (imageError) {
                        console.error(`Error embedding image for document ${doc.id}:`, imageError);
                        console.warn(`Failed to process ${doc.mime_type} image. Skipping.`);
                    }
                } else if (doc.mime_type === "application/pdf") {
                    const existingPdf = await PDFDocument.load(arrayBuffer);
                    const copiedPages = await pdfDoc.copyPages(existingPdf, existingPdf.getPageIndices());
                    copiedPages.forEach((page) => pdfDoc.addPage(page));
                    hasContent = true;
                }
            } catch (error) {
                console.error(`Error processing document ${doc.id}:`, error);
            }
        }

        if (hasContent) {
            const pdfBytes = await pdfDoc.save();
            userFolder?.file(`${fileName}.pdf`, pdfBytes);
        }
    } catch (error) {
        console.error("Error creating combined PDF:", error);
    }
}
