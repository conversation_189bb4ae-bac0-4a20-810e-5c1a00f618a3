"use server";

import { TEXTS } from "@/lib/scholarship-group-constants";
import { ScholarshipGroup } from "@/lib/scholarship-group-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export interface ScholarshipGroupFormData {
    title: string;
    slug: string;
    description: string;
    icon: string;
    image_url?: string | null;
}

export async function getScholarshipGroup(
    id: string
): Promise<{ success: boolean; data?: Tables<"groups_scholarship">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("groups_scholarship").select("*").eq("id", id).single();

        if (error) throw error;

        if (!data) {
            return { success: false, error: TEXTS.editNotFoundMessage };
        }
        return { success: true, data };
    } catch (error) {
        console.error("Error fetching scholarship group:", error);
        return { success: false, error: TEXTS.apiErrorGeneral };
    }
}

export async function createScholarshipGroup(
    data: ScholarshipGroupFormData
): Promise<{ success: boolean; data?: Tables<"groups_scholarship">; error?: string }> {
    try {
        if (!data.title || data.title.trim().length < 2) {
            return { success: false, error: "Title is required and must be at least 2 characters" };
        }

        if (!data.slug || !/^[a-zA-Z-]+$/.test(data.slug)) {
            return { success: false, error: "Slug is required and must contain only English letters and hyphens" };
        }

        if (!data.description || data.description.trim().length < 10) {
            return { success: false, error: "Description is required and must be at least 10 characters" };
        }

        if (!data.icon) {
            return { success: false, error: "Icon is required" };
        }

        const supabase = await createClientFromRequest();
        const { data: existingGroup } = await supabase
            .from("groups_scholarship")
            .select("id")
            .eq("slug", data.slug)
            .single();

        if (existingGroup) {
            return { success: false, error: TEXTS.slugExistsError };
        }

        const sanitizedData: TablesInsert<"groups_scholarship"> = {
            title: data.title.trim().replace(/[<>]/g, ""),
            slug: data.slug.trim().toLowerCase(),
            description: data.description.trim().replace(/[<>]/g, ""),
            icon: data.icon,
            image_url: data.image_url || null
        };

        const { data: newGroup, error } = await supabase
            .from("groups_scholarship")
            .insert(sanitizedData)
            .select()
            .single();

        if (error) throw error;

        return { success: true, data: newGroup };
    } catch (error) {
        console.error("Error creating scholarship group:", error);
        return { success: false, error: TEXTS.createErrorMessage };
    }
}

export async function updateScholarshipGroup(
    id: string,
    data: Partial<Omit<ScholarshipGroupFormData, "image_url">>
): Promise<{ success: boolean; error?: string }> {
    try {
        if (data.title !== undefined && (!data.title || data.title.trim().length < 2)) {
            return { success: false, error: "Title is required and must be at least 2 characters" };
        }

        if (data.slug !== undefined && (!data.slug || !/^[a-zA-Z-]+$/.test(data.slug))) {
            return { success: false, error: "Slug is required and must contain only English letters and hyphens" };
        }

        if (data.description !== undefined && (!data.description || data.description.trim().length < 10)) {
            return { success: false, error: "Description is required and must be at least 10 characters" };
        }

        if (data.icon !== undefined && !data.icon) {
            return { success: false, error: "Icon is required" };
        }

        const supabase = await createClientFromRequest();

        if (data.slug) {
            const { data: existingGroup } = await supabase
                .from("groups_scholarship")
                .select("id")
                .eq("slug", data.slug)
                .neq("id", id)
                .single();

            if (existingGroup) {
                return { success: false, error: TEXTS.slugExistsError };
            }
        }

        const sanitizedData: TablesUpdate<"groups_scholarship"> = {};

        if (data.title !== undefined) {
            sanitizedData.title = data.title.trim().replace(/[<>]/g, "");
        }
        if (data.slug !== undefined) {
            sanitizedData.slug = data.slug.trim().toLowerCase();
        }
        if (data.description !== undefined) {
            sanitizedData.description = data.description.trim().replace(/[<>]/g, "");
        }
        if (data.icon !== undefined) {
            sanitizedData.icon = data.icon;
        }

        const { error } = await supabase.from("groups_scholarship").update(sanitizedData).eq("id", id);

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error("Error updating scholarship group:", error);
        return { success: false, error: TEXTS.editErrorMessage };
    }
}

export async function deleteScholarshipGroup(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { data: associations, error: checkError } = await supabase
            .from("link_scholarship_to_scholarship_groups")
            .select("scholarship_id")
            .eq("scholarship_group_id", id)
            .limit(1);

        if (checkError) {
            console.error("Error checking scholarship group associations:", checkError);
            return { success: false, error: TEXTS.deleteErrorMessage };
        }

        if (associations && associations.length > 0) {
            return {
                success: false,
                error: TEXTS.deleteAssociationError
            };
        }

        const { error: deleteError } = await supabase.from("groups_scholarship").delete().eq("id", id);

        if (deleteError) {
            console.error("Error deleting scholarship group:", deleteError);
            return { success: false, error: TEXTS.deleteErrorMessage };
        }

        return { success: true };
    } catch (error) {
        console.error("Error deleting scholarship group:", error);
        return {
            success: false,
            error: TEXTS.deleteErrorMessage
        };
    }
}

export async function uploadScholarshipGroupImage(
    groupId: string,
    file: File
): Promise<{ success: boolean; imageUrl?: string; error?: string }> {
    try {
        if (file.type !== "image/webp") {
            return { success: false, error: TEXTS.webpOnlyError };
        }

        const maxSizeInBytes = 5 * 1024 * 1024;
        if (file.size > maxSizeInBytes) {
            return { success: false, error: TEXTS.fileSizeExceededError };
        }

        const fileExtension = "webp";
        const fileName = `${groupId}.${fileExtension}`;
        const filePath = fileName;

        const supabase = await createClientFromRequest();
        const { error: uploadError } = await supabase.storage.from("groups_scholarship").upload(filePath, file, {
            cacheControl: "3600",
            upsert: true
        });

        if (uploadError) {
            console.error("Error uploading to storage:", uploadError);
            return { success: false, error: TEXTS.apiErrorUpload };
        }

        const { data: publicUrlData } = supabase.storage.from("groups_scholarship").getPublicUrl(filePath);

        if (!publicUrlData?.publicUrl) {
            return { success: false, error: TEXTS.imageUrlGenerationError };
        }

        const { error: updateError } = await supabase
            .from("groups_scholarship")
            .update({ image_url: publicUrlData.publicUrl })
            .eq("id", groupId);

        if (updateError) {
            console.error("Error updating scholarship group with image URL:", updateError);
            return { success: false, error: TEXTS.imageUrlUpdateError };
        }

        return {
            success: true,
            imageUrl: publicUrlData.publicUrl
        };
    } catch (error) {
        console.error("Error uploading scholarship group image:", error);
        return { success: false, error: TEXTS.apiErrorUpload };
    }
}

export async function getScholarshipGroupsWithCounts(): Promise<{
    success: boolean;
    data?: ScholarshipGroup[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data: groups, error: groupsError } = await supabase
            .from("groups_scholarship")
            .select("*")
            .order("title");

        if (groupsError) {
            throw groupsError;
        }

        const { data: counts, error: countsError } = await supabase
            .from("link_scholarship_to_scholarship_groups")
            .select("scholarship_group_id");

        if (countsError) {
            throw countsError;
        }

        const countMap: Record<string, number> = {};
        (counts || []).forEach((item: { scholarship_group_id: string }) => {
            const groupId = item.scholarship_group_id;
            countMap[groupId] = (countMap[groupId] || 0) + 1;
        });

        const allGroups = (groups || []).map((group) => ({
            ...group,
            scholarships_count: countMap[group.id] || 0
        }));

        return { success: true, data: allGroups as ScholarshipGroup[] };
    } catch (error) {
        console.error("Error fetching scholarship groups with counts:", error);
        return { success: false, error: TEXTS.apiErrorGeneral };
    }
}
