"use server";

import {
    applyCoupon,
    calculateDiscount,
    incrementCouponUsage,
    validateAndApplyCoupon,
    validateCoupon
} from "@/app/actions/subscriptions-actions";
import { type Coupon } from "@/lib/coupon-constants";
import { CouponResult, ValidatedCoupon } from "@/lib/subscription-constants";
import { type Database, type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export { applyCoupon, calculateDiscount, incrementCouponUsage, validateAndApplyCoupon, validateCoupon };

export type { CouponResult, ValidatedCoupon };

const TEXTS = {
    COUPON_DELETE_ERROR: "שגיאה במחיקת הקופון. אנא נסה שנית.",
    COUPON_DELETE_SUCCESS: "הקופון נמחק בהצלחה.",
    COUPON_FETCH_ERROR: "שגיאה בטעינת הקופון",
    COUPON_NOT_FOUND: "הקופון לא נמצא",
    COUPON_UPDATE_ERROR: "שגיאה בעדכון הקופון",
    COUPON_CREATE_ERROR: "שגיאה ביצירת הקופון/ים",
    COUPON_CODE_EXISTS: "קוד קופון אחד או יותר כבר קיים במערכת. נסה שוב.",
    COUPON_CODE_GENERATION_ERROR: "לא ניתן היה לייצר מספיק קודים ייחודיים. אנא נסה שוב או הקטן את הכמות.",
    COUPON_GROUPS_FETCH_ERROR: "שגיאה בטעינת קבוצות הקופונים",
    COUPON_CODE_REQUIRED: "קוד הקופון הוא שדה חובה",
    QUANTITY_MUST_BE_POSITIVE: "יש להזין כמות גדולה מ-0."
};

type CouponGroup = {
    id: string;
    label: string;
};

export type CreationMode = "single" | "multiple";
export type CouponType = Database["public"]["Enums"]["coupon_type_enum"];

export interface Option {
    id: string;
    label: string;
}

export interface CouponFormValues {
    creation_mode: CreationMode;
    coupon_code: string | null;
    quantity: number;
    coupon_type: { id: CouponType; label: string };
    discount_value: number | null;
    expiration_date: Date | null;
    usage_limit: number | null;
    coupon_group_id: null | Option;
}

type CreateCouponData = {
    creation_mode: CreationMode;
    coupon_code: string | null;
    quantity: number;
    coupon_type: CouponType;
    discount_value: number;
    expiration_date: Date | null;
    usage_limit: number | null;
    coupon_group_id: string | null;
};

function generateRandomCode(length: number): string {
    let code = "";
    const characters = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";
    for (let i = 0; i < length; i++) {
        if (i > 0 && i % 4 === 0) {
            code += "-";
        }
        code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return code;
}

export const getCouponGroups = async (): Promise<{ success: boolean; data?: CouponGroup[]; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();
        const { data: groups, error } = await supabase.from("groups_coupon").select("id, name").order("name");

        if (error) throw error;

        const couponGroups: CouponGroup[] =
            groups?.map((group) => ({
                id: group.id,
                label: group.name
            })) || [];

        return { success: true, data: couponGroups };
    } catch (error) {
        console.error("Error fetching coupon groups:", error);
        return { success: false, error: TEXTS.COUPON_GROUPS_FETCH_ERROR };
    }
};

export const getCoupon = async (
    id: string
): Promise<{ success: boolean; data?: Tables<"coupons">; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("coupons").select("*").eq("id", id).single();

        if (error) throw error;

        if (!data) {
            return { success: false, error: TEXTS.COUPON_NOT_FOUND };
        }

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching coupon:", error);
        return { success: false, error: TEXTS.COUPON_FETCH_ERROR };
    }
};

export const createCoupons = async (
    data: CreateCouponData
): Promise<{ success: boolean; count?: number; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();
        let couponsToInsert: TablesInsert<"coupons">[] = [];

        if (data.creation_mode === "single") {
            if (!data.coupon_code) {
                return { success: false, error: TEXTS.COUPON_CODE_REQUIRED };
            }
            couponsToInsert.push({
                coupon_code: data.coupon_code,
                coupon_type: data.coupon_type,
                discount_value: data.discount_value,
                expiration_date: data.expiration_date ? data.expiration_date.toISOString() : null,
                usage_limit: data.usage_limit || 1,
                used_count: 0,
                coupon_group_id: data.coupon_group_id || null
            });
        } else {
            if (data.quantity <= 0) {
                return { success: false, error: TEXTS.QUANTITY_MUST_BE_POSITIVE };
            }

            const generatedCodes = new Set<string>();
            let attempts = 0;
            const maxAttempts = data.quantity * 20;

            while (generatedCodes.size < data.quantity && attempts < maxAttempts) {
                const batchSize = data.quantity - generatedCodes.size;
                const candidateCodes = Array.from({ length: batchSize }, () => generateRandomCode(12));

                const { data: existingCodes, error } = await supabase
                    .from("coupons")
                    .select("coupon_code")
                    .in("coupon_code", candidateCodes);

                if (error) {
                    console.error("Error checking existing coupon codes:", error);
                    throw error;
                }

                const existingCodeSet = new Set(existingCodes?.map((c) => c.coupon_code));
                const uniqueNewCodes = candidateCodes.filter((code) => !existingCodeSet.has(code));

                uniqueNewCodes.forEach((code) => {
                    if (generatedCodes.size < data.quantity) {
                        generatedCodes.add(code);
                    }
                });

                attempts++;
            }

            if (generatedCodes.size < data.quantity) {
                return {
                    success: false,
                    error: TEXTS.COUPON_CODE_GENERATION_ERROR
                };
            }

            couponsToInsert = Array.from(generatedCodes).map((code) => ({
                coupon_code: code,
                coupon_type: data.coupon_type,
                discount_value: data.discount_value,
                expiration_date: data.expiration_date ? data.expiration_date.toISOString() : null,
                usage_limit: data.usage_limit || 1,
                used_count: 0,
                coupon_group_id: data.coupon_group_id || null
            }));
        }

        const { error: couponError } = await supabase.from("coupons").insert(couponsToInsert);

        if (couponError) {
            if (couponError.code === "23505") {
                return { success: false, error: TEXTS.COUPON_CODE_EXISTS };
            }
            throw couponError;
        }

        return { success: true, count: couponsToInsert.length };
    } catch (error) {
        console.error("Error creating coupon(s):", error);
        return { success: false, error: TEXTS.COUPON_CREATE_ERROR };
    }
};

export const updateCoupon = async (
    id: string,
    data: Partial<TablesUpdate<"coupons">>
): Promise<{ success: boolean; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        const { error: couponError } = await supabase.from("coupons").update(data).eq("id", id);

        if (couponError) throw couponError;

        return { success: true };
    } catch (error) {
        console.error("Error updating coupon:", error);
        return { success: false, error: TEXTS.COUPON_UPDATE_ERROR };
    }
};

export const deleteCoupon = async (id: string): Promise<{ success: boolean; error?: string }> => {
    const supabase = await createClientFromRequest();

    const { error: deleteError } = await supabase.from("coupons").delete().eq("id", id);

    if (deleteError) {
        console.error("Error deleting coupon:", deleteError);
        return { success: false, error: TEXTS.COUPON_DELETE_ERROR };
    }

    return { success: true };
};

export const getCoupons = async (): Promise<{
    success: boolean;
    data?: Coupon[];
    error?: string;
}> => {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("coupons")
            .select(`*, groups_coupon:coupon_group_id (name)`)
            .order("coupon_group_id", { ascending: true, nullsFirst: true })
            .order("created_at", { ascending: false });

        if (error) throw error;

        type RawCoupon = Tables<"coupons"> & { groups_coupon?: { name: string } | null };
        const rows = (data || []) as RawCoupon[];
        const transformedData = rows.map((coupon) => ({
            ...coupon,
            group_name: coupon.groups_coupon?.name || null
        }));

        return { success: true, data: transformedData };
    } catch (error) {
        console.error("Error fetching coupons:", error);
        return { success: false, error: TEXTS.COUPON_FETCH_ERROR };
    }
};
