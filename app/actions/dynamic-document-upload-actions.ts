"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { TEXTS } from "@/lib/dynamic-document-upload-constants";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest, createServiceRoleClient } from "@/utils/supabase/server";

export interface DocumentUploadResult {
    success: boolean;
    error?: string;
    documentUrl?: string;
    uploadedFileName?: string;
}

export async function uploadDocument(
    documentTypeId: string,
    file: File,
    overrideUserId?: string
): Promise<DocumentUploadResult> {
    try {
        const { userId } = await auth();

        // Use override user ID if provided (for admin), otherwise use current user
        const targetUserId = overrideUserId || userId;

        if (!targetUserId) {
            return { success: false, error: TEXTS.userNotAuthenticatedError };
        }

        if (!file || file.size === 0) {
            return { success: false, error: TEXTS.noValidFileSelectedError };
        }

        const sanitizedDocumentTypeId = sanitizeText(documentTypeId, 100);
        if (!sanitizedDocumentTypeId) {
            return { success: false, error: TEXTS.invalidDocumentTypeIdError };
        }

        const supabase = await createClientFromRequest();
        const supabaseServiceRole = createServiceRoleClient();

        const { data: documentType, error: docTypeError } = await supabase
            .from("document_types")
            .select("id, name, allowed_mime_types, max_file_size_mb")
            .eq("id", sanitizedDocumentTypeId)
            .single();

        if (docTypeError || !documentType) {
            return { success: false, error: TEXTS.documentTypeNotFoundError };
        }

        const allowedMimes = (
            Array.isArray(documentType.allowed_mime_types)
                ? documentType.allowed_mime_types.filter((item): item is string => typeof item === "string")
                : []
        ) as string[];

        if (allowedMimes.length > 0 && !allowedMimes.includes(file.type)) {
            return {
                success: false,
                error: TEXTS.fileTypeNotSupportedError(file.type, allowedMimes.join(", "))
            };
        }

        const maxSizeInMB = typeof documentType.max_file_size_mb === "number" ? documentType.max_file_size_mb : 10;
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

        if (file.size > maxSizeInBytes) {
            return {
                success: false,
                error: TEXTS.fileTooLargeError(maxSizeInMB)
            };
        }

        const fileExtension = file.name.split(".").pop();
        if (!fileExtension || fileExtension === file.name) {
            return { success: false, error: TEXTS.fileMissingExtensionError };
        }

        const fileName = `${sanitizedDocumentTypeId}.${fileExtension}`;
        const filePath = `${targetUserId}/${sanitizedDocumentTypeId}/${fileName}`;

        const { error: uploadError } = await supabaseServiceRole.storage.from("user_documents").upload(filePath, file, {
            cacheControl: "3600",
            upsert: true
        });

        if (uploadError) {
            console.error("Storage upload error:", uploadError);
            return { success: false, error: TEXTS.fileUploadStorageError };
        }

        const { data: signedUrlData, error: signedUrlError } = await supabaseServiceRole.storage
            .from("user_documents")
            .createSignedUrl(filePath, 60);

        let documentUrl: string | undefined = undefined;
        if (signedUrlError) {
            console.error("Error creating signed URL:", signedUrlError);
        } else {
            documentUrl = signedUrlData.signedUrl;
        }

        revalidatePath("/dashboard/documents-upload");

        return {
            success: true,
            documentUrl,
            uploadedFileName: fileName
        };
    } catch (error) {
        console.error("Document upload error:", error);
        return { success: false, error: TEXTS.generalUploadError };
    }
}

export async function getRequiredDocuments(): Promise<{
    success: boolean;
    documents?: unknown[];
    error?: string;
}> {
    try {
        const { userId } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.userNotAuthenticatedError };
        }

        const supabase = await createClientFromRequest();

        const { data: documentTypes, error: docTypesError } = await supabase
            .from("document_types")
            .select(
                `
                id, 
                name, 
                description, 
                allowed_mime_types, 
                max_file_size_mb,
                link_url,
                example_file_path,
                group_id, 
                groups_document_type (id, name)
            `
            )
            .order("name");

        if (docTypesError) {
            console.error("Error fetching document types:", docTypesError);
            return { success: false, error: TEXTS.errorLoadingDocumentTypesError };
        }

        return { success: true, documents: documentTypes || [] };
    } catch (error) {
        console.error("Get required documents error:", error);
        return { success: false, error: TEXTS.generalLoadingDocumentsError };
    }
}

export async function checkUserDocumentStatus(
    userId: string,
    documentTypeId: string
): Promise<{
    isUploaded: boolean;
    uploadedFileName?: string;
    uploadedFileUrl?: string;
}> {
    try {
        const supabaseServiceRole = createServiceRoleClient();
        const userDocumentPath = `${userId}/${documentTypeId}`;

        const { data: existingFiles, error: listError } = await supabaseServiceRole.storage
            .from("user_documents")
            .list(userDocumentPath);

        if (listError) {
            console.warn(`Error listing files for ${userDocumentPath}: ${listError.message}`);
            return { isUploaded: false };
        }

        const targetFileNamePrefix = documentTypeId;
        const uploadedFile = existingFiles?.find((file) => file.name.startsWith(targetFileNamePrefix + "."));

        if (!uploadedFile) {
            return { isUploaded: false };
        }

        const { data: signedUrlData, error: signedUrlError } = await supabaseServiceRole.storage
            .from("user_documents")
            .createSignedUrl(`${userDocumentPath}/${uploadedFile.name}`, 60);

        let uploadedFileUrl: string | undefined = undefined;
        if (signedUrlError) {
            console.error("Error creating signed URL:", signedUrlError);
        } else {
            uploadedFileUrl = signedUrlData.signedUrl;
        }

        return {
            isUploaded: true,
            uploadedFileName: uploadedFile.name,
            uploadedFileUrl
        };
    } catch (error) {
        console.error("Check document status error:", error);
        return { isUploaded: false };
    }
}
