"use server";

import {
    ConditionDependency,
    ConditionQuestion,
    ConditionType,
    ConditionValue
} from "@/components/forms/fields/condition-selector";
import { Database, Json } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export interface ActionResult<T> {
    success: boolean;
    data?: T;
    error?: string;
}

export async function getAllQuestionsForCollaboration(): Promise<ActionResult<ConditionQuestion[]>> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("questions").select(`
                id,
                type,
                metadata,
                groups_question ( id, name )
            `);

        if (error) throw error;

        interface QuestionDataFromDb {
            id: string;
            type: string;
            metadata: Json;
            groups_question: { id: string; name: string } | { id: string; name: string }[] | null;
        }

        const formattedData = data.map((q: QuestionDataFromDb): ConditionQuestion => {
            let questionGroup = undefined;
            if (q.groups_question) {
                if (Array.isArray(q.groups_question)) {
                    if (q.groups_question.length > 0) {
                        questionGroup = q.groups_question[0];
                    }
                } else {
                    questionGroup = q.groups_question;
                }
            }

            const metadata = q.metadata as {
                label?: string;
                placeholder?: string;
                options?: string | Array<{ value: string; label: string }> | Array<{ id: string; label: string }>;
            } | null;

            return {
                id: q.id,
                type: q.type as Database["public"]["Enums"]["question_type"],
                metadata: {
                    label: metadata?.label || undefined,
                    placeholder: metadata?.placeholder || undefined,
                    options: metadata?.options || undefined
                },
                groups_question: questionGroup
            };
        });

        return { success: true, data: formattedData };
    } catch (error) {
        console.error("Error fetching questions for collaboration:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch questions"
        };
    }
}

export async function getCollaborationConditions(
    collaborationId: string
): Promise<ActionResult<ConditionDependency[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data: links, error: linkError } = await supabase
            .from("link_collaboration_to_condition")
            .select("condition_id")
            .eq("collaboration_id", collaborationId);

        if (linkError) throw linkError;

        if (!links || links.length === 0) {
            return { success: true, data: [] };
        }

        const conditionIds = links.map((link) => link.condition_id);

        const { data: conditions, error: conditionsError } = await supabase
            .from("conditions")
            .select("id, question_id, type, value")
            .in("id", conditionIds);

        if (conditionsError) throw conditionsError;

        if (!conditions || conditions.length === 0) {
            return { success: true, data: [] };
        }

        const questionIds = conditions.map((c) => c.question_id);
        const { data: questions, error: questionsError } = await supabase
            .from("questions")
            .select("id, type")
            .in("id", questionIds);

        if (questionsError) throw questionsError;

        const questionTypeMap = new Map<string, Database["public"]["Enums"]["question_type"]>();

        type QuestionRecord = {
            id: string;
            type: Database["public"]["Enums"]["question_type"];
        };

        (questions as QuestionRecord[])?.forEach((q) => {
            if (q.id && q.type) {
                questionTypeMap.set(q.id, q.type);
            }
        });

        const formattedData = conditions.map((condition) => {
            const questionType = questionTypeMap.get(condition.question_id);

            let correctConditionType: ConditionType = condition.type as ConditionType;
            if (questionType) {
                if (questionType === "number_input") {
                    correctConditionType = "range";
                } else if (questionType === "date_picker") {
                    correctConditionType = "date_range";
                } else if (["single_select", "multi_select"].includes(questionType)) {
                    correctConditionType = "in";
                }
            }

            let formattedValue: ConditionValue;
            switch (correctConditionType) {
                case "range":
                    if (condition.value && typeof condition.value === "object" && !Array.isArray(condition.value)) {
                        formattedValue = condition.value as ConditionValue;
                    } else {
                        formattedValue = { min: undefined, max: undefined };
                    }
                    break;
                case "date_range":
                    if (condition.value && typeof condition.value === "object" && !Array.isArray(condition.value)) {
                        formattedValue = condition.value as ConditionValue;
                    } else {
                        formattedValue = { operator: "greater_than", days_from_today: 0 };
                    }
                    break;
                case "in":
                default:
                    formattedValue = Array.isArray(condition.value) ? (condition.value as ConditionValue) : [];
                    break;
            }

            return {
                id: condition.id,
                question_id: { id: condition.question_id, label: condition.question_id },
                condition_type: correctConditionType,
                condition_value: formattedValue
            } as ConditionDependency & { id: string };
        });

        return { success: true, data: formattedData };
    } catch (error) {
        console.error("Error fetching collaboration conditions:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch conditions"
        };
    }
}

export async function getCollaborationQuestions(collaborationId: string): Promise<ActionResult<string[]>> {
    try {
        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("link_question_to_collaboration")
            .select("question_id")
            .eq("collaboration_id", collaborationId);

        if (error) throw error;

        const questionIds = data ? data.map((item) => item.question_id) : [];
        return { success: true, data: questionIds };
    } catch (error) {
        console.error("Error fetching collaboration questions:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Failed to fetch collaboration questions"
        };
    }
}
