"use server";

import { revalidatePath } from "next/cache";

import { type QuestionGroupFormValues, TEXTS } from "@/lib/question-group-constants";
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

type QuestionGroupFormData = QuestionGroupFormValues;

function validateQuestionGroupName(name: string): { valid: boolean; error?: string } {
    if (!name || name.trim().length < 1) {
        return { valid: false, error: TEXTS.NAME_REQUIRED };
    }
    const sanitizedName = name ? sanitizeText(name, 100) : "";
    if (!sanitizedName?.trim()) {
        return { valid: false, error: TEXTS.GROUP_CREATE_ERROR };
    }
    return { valid: true };
}

export async function createQuestionGroup(data: QuestionGroupFormData): Promise<{ success: boolean; error?: string }> {
    try {
        const validation = validateQuestionGroupName(data.name);
        if (!validation.valid) {
            return { success: false, error: validation.error };
        }
        const sanitizedName = sanitizeText(data.name, 100);
        const sanitizedData: TablesInsert<"groups_question"> = {
            name: sanitizedName.trim()
        };

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("groups_question").insert(sanitizedData);

        if (error) throw error;

        revalidatePath("/admin/questions");
        return { success: true };
    } catch (error) {
        console.error("Error creating question group:", error);
        return { success: false, error: TEXTS.GROUP_CREATE_ERROR };
    }
}

export async function updateQuestionGroup(
    id: string,
    data: QuestionGroupFormData
): Promise<{ success: boolean; error?: string }> {
    try {
        const validation = validateQuestionGroupName(data.name);
        if (!validation.valid) {
            return { success: false, error: validation.error };
        }
        const sanitizedName = sanitizeText(data.name, 100);
        const sanitizedData: TablesUpdate<"groups_question"> = {
            name: sanitizedName.trim()
        };

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("groups_question").update(sanitizedData).eq("id", id);

        if (error) throw error;

        revalidatePath("/admin/questions");
        return { success: true };
    } catch (error) {
        console.error("Error updating question group:", error);
        return { success: false, error: TEXTS.GROUP_UPDATE_ERROR };
    }
}

export async function getQuestionGroup(
    id: string
): Promise<{ success: boolean; data?: Tables<"groups_question">; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("groups_question").select("*").eq("id", id).single();

        if (error) throw error;
        if (!data) return { success: false, error: TEXTS.GROUP_NOT_FOUND };

        return { success: true, data };
    } catch (error) {
        console.error("Error fetching question group:", error);
        return { success: false, error: TEXTS.GROUP_FETCH_ERROR };
    }
}

export const deleteQuestionGroup = async (id: string): Promise<{ success: boolean; error?: string }> => {
    try {
        const supabase = await createClientFromRequest();

        const { count, error: countError } = await supabase
            .from("questions")
            .select("id", { count: "exact", head: true })
            .eq("group_id", id);

        if (countError) throw countError;

        if (count && count > 0) {
            return {
                success: false,
                error: `${TEXTS.DELETION_IN_USE_PREFIX}${count}${TEXTS.DELETION_IN_USE_SUFFIX}`
            };
        }

        const { error: deleteError } = await supabase.from("groups_question").delete().eq("id", id);

        if (deleteError) throw deleteError;

        revalidatePath("/admin/questions");
        return { success: true };
    } catch (err) {
        console.error("Error deleting question group:", err);
        return {
            success: false,
            error: err instanceof Error ? err.message : TEXTS.GROUP_DELETE_ERROR
        };
    }
};

export const getQuestionGroups = async () => {
    try {
        const supabase = await createClientFromRequest();

        const { data: groupsData, error: groupsError } = await supabase
            .from("groups_question")
            .select("id, name, created_at, updated_at")
            .order("created_at", { ascending: false });

        if (groupsError) throw groupsError;

        let countData: Array<{ group_id: string; count: string }> = [];

        const { data, error } = await supabase.from("questions").select("group_id");

        if (error) throw error;

        const groupCounts: Record<string, number> = {};
        data?.forEach((item) => {
            const groupId = item.group_id;
            if (groupId) {
                groupCounts[groupId] = (groupCounts[groupId] || 0) + 1;
            }
        });

        countData = Object.entries(groupCounts).map(([group_id, count]) => ({
            group_id,
            count: String(count)
        }));

        const countMap = Object.fromEntries(countData.map((c) => [c.group_id, Number(c.count)]));

        const groupsWithCount = (groupsData || []).map((g) => ({
            ...g,
            questions_count: countMap[g.id] ?? 0
        }));

        return {
            success: true,
            data: groupsWithCount
        };
    } catch (error) {
        console.error("Error fetching question groups:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : TEXTS.FETCH_QUESTION_GROUPS_ERROR
        };
    }
};
