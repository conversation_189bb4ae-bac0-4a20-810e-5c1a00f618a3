"use server";

import { auth } from "@clerk/nextjs/server";

import {
    type DocumentTypeFormData,
    type DocumentTypeInsert,
    type DocumentTypeUpdate,
    TEXTS
} from "@/lib/document-type-constants";
import { isAdminFromSessionClaims } from "@/lib/org-role";
import { Database } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

type DocumentType = Database["public"]["Tables"]["document_types"]["Row"];

export type DocumentTypeWithUrls = Database["public"]["Tables"]["document_types"]["Row"] & {
    example_file_url?: string;
};

export async function createDocumentType(
    data: DocumentTypeFormData
): Promise<{ success: boolean; error?: string; id?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 200) : "";
        const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";
        const sanitizedLinkUrl = data.link_url ? sanitizeText(data.link_url, 500) : "";

        if (!sanitizedName?.trim()) {
            return { success: false, error: TEXTS.NAME_REQUIRED };
        }

        const groupId = typeof data.group_id === "object" ? data.group_id.id : data.group_id;
        if (!groupId) {
            return { success: false, error: TEXTS.GROUP_REQUIRED };
        }

        if (!data.allowed_mime_types || data.allowed_mime_types.length === 0) {
            return { success: false, error: TEXTS.MIME_TYPE_REQUIRED };
        }

        const sanitizedData: DocumentTypeInsert = {
            name: sanitizedName.trim(),
            description: sanitizedDescription.trim() || null,
            example_file_path: data.example_file_path,
            link_url: sanitizedLinkUrl.trim() || null,
            allowed_mime_types: data.allowed_mime_types,
            group_id: groupId,
            max_file_size_mb: data.max_file_size_mb ? Math.max(1, Number(data.max_file_size_mb)) : 10
        };

        const supabase = await createClientFromRequest();
        const { data: newDocumentType, error } = await supabase
            .from("document_types")
            .insert(sanitizedData)
            .select("id")
            .single();

        if (error) {
            throw error;
        }

        if (data.example_file_path) {
            try {
                const filePath = data.example_file_path;

                if (filePath.startsWith("temp_")) {
                    const fileExt = filePath.split(".").pop();
                    const newFilePath = `${newDocumentType.id}.${fileExt}`;

                    const { error: copyError } = await supabase.storage
                        .from("document_examples")
                        .copy(filePath, newFilePath);

                    if (copyError) {
                        console.error("Error copying file:", copyError);
                    } else {
                        await supabase.storage.from("document_examples").remove([filePath]);

                        await supabase
                            .from("document_types")
                            .update({ example_file_path: newFilePath })
                            .eq("id", newDocumentType.id);
                    }
                }
            } catch (fileError) {
                console.error("Error handling file rename:", fileError);
            }
        }

        return { success: true, id: newDocumentType.id };
    } catch (error) {
        console.error("Error creating document type:", error);
        return { success: false, error: TEXTS.DOCUMENT_TYPE_CREATE_ERROR };
    }
}

export async function updateDocumentType(
    id: string,
    data: DocumentTypeFormData
): Promise<{ success: boolean; error?: string }> {
    try {
        const sanitizedName = data.name ? sanitizeText(data.name, 200) : "";
        const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";
        const sanitizedLinkUrl = data.link_url ? sanitizeText(data.link_url, 500) : "";

        if (!sanitizedName?.trim()) {
            return { success: false, error: TEXTS.NAME_REQUIRED };
        }

        const groupId = typeof data.group_id === "object" ? data.group_id.id : data.group_id;
        if (!groupId) {
            return { success: false, error: TEXTS.GROUP_REQUIRED };
        }

        if (!data.allowed_mime_types || data.allowed_mime_types.length === 0) {
            return { success: false, error: TEXTS.MIME_TYPE_REQUIRED };
        }

        const sanitizedData: DocumentTypeUpdate = {
            name: sanitizedName.trim(),
            description: sanitizedDescription.trim() || null,
            example_file_path: data.example_file_path,
            link_url: sanitizedLinkUrl.trim() || null,
            allowed_mime_types: data.allowed_mime_types,
            group_id: groupId,
            max_file_size_mb: data.max_file_size_mb ? Math.max(1, Number(data.max_file_size_mb)) : 10,
            updated_at: new Date().toISOString()
        };

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("document_types").update(sanitizedData).eq("id", id);

        if (error) {
            throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error updating document type:", error);
        return { success: false, error: TEXTS.DOCUMENT_TYPE_UPDATE_ERROR };
    }
}

export async function deleteDocumentType(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { data: documentType, error: fetchError } = await supabase
            .from("document_types")
            .select("example_file_path")
            .eq("id", id)
            .single();

        if (fetchError) {
            throw fetchError;
        }

        if (documentType?.example_file_path) {
            const { error: storageError } = await supabase.storage
                .from("document_examples")
                .remove([documentType.example_file_path]);

            if (storageError) {
                console.error("Error deleting example file from storage:", storageError);
            }
        }

        const { error } = await supabase.from("document_types").delete().eq("id", id);

        if (error) {
            throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error deleting document type:", error);
        return { success: false, error: TEXTS.DOCUMENT_TYPE_DELETE_ERROR };
    }
}

export async function getDocumentType(id: string): Promise<{ data?: DocumentType; error?: string }> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase.from("document_types").select("*").eq("id", id).single();

        if (error) {
            throw error;
        }

        return { data: data as DocumentType };
    } catch (error) {
        console.error("Error fetching document type:", error);
        return { error: TEXTS.DOCUMENT_TYPE_NOT_FOUND };
    }
}

export async function linkDocumentTypeToScholarship(
    documentTypeId: string,
    scholarshipId: string
): Promise<{ success: boolean; error?: string }> {
    try {
        const { userId, sessionClaims } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.AUTH_REQUIRED };
        }

        if (!isAdminFromSessionClaims(sessionClaims)) {
            return { success: false, error: TEXTS.ADMIN_REQUIRED };
        }

        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("link_scholarship_to_document_type").insert({
            document_type_id: documentTypeId,
            scholarship_id: scholarshipId
        });

        if (error) {
            throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error linking document type to scholarship:", error);
        return { success: false, error: TEXTS.DOCUMENT_TYPE_LINK_ERROR };
    }
}

export async function unlinkDocumentTypeFromScholarship(
    documentTypeId: string,
    scholarshipId: string
): Promise<{ success: boolean; error?: string }> {
    try {
        const { userId, sessionClaims } = await auth();
        if (!userId) {
            return { success: false, error: TEXTS.AUTH_REQUIRED };
        }

        if (!isAdminFromSessionClaims(sessionClaims)) {
            return { success: false, error: TEXTS.ADMIN_REQUIRED };
        }

        const supabase = await createClientFromRequest();
        const { error } = await supabase
            .from("link_scholarship_to_document_type")
            .delete()
            .eq("document_type_id", documentTypeId)
            .eq("scholarship_id", scholarshipId);

        if (error) {
            throw error;
        }

        return { success: true };
    } catch (error) {
        console.error("Error unlinking document type from scholarship:", error);
        return { success: false, error: TEXTS.DOCUMENT_TYPE_UNLINK_ERROR };
    }
}

export async function getDocumentTypesForScholarship(
    scholarshipId: string
): Promise<{ data?: DocumentType[]; error?: string }> {
    try {
        const supabase = await createClientFromRequest();

        const { data: links, error: linksError } = await supabase
            .from("link_scholarship_to_document_type")
            .select("document_type_id")
            .eq("scholarship_id", scholarshipId);

        if (linksError) {
            throw linksError;
        }

        if (!links || links.length === 0) {
            return { data: [] };
        }

        const documentTypeIds = links.map((link) => link.document_type_id);

        const { data: documentTypes, error: typesError } = await supabase
            .from("document_types")
            .select("*")
            .in("id", documentTypeIds);

        if (typesError) {
            throw typesError;
        }

        return { data: documentTypes as DocumentType[] };
    } catch (error) {
        console.error("Error fetching document types for scholarship:", error);
        return { error: TEXTS.DOCUMENT_TYPES_FETCH_ERROR };
    }
}

export async function getDocumentTypes(): Promise<{
    success: boolean;
    data?: DocumentTypeWithUrls[];
    error?: string;
}> {
    try {
        const supabase = await createClientFromRequest();
        const { data, error } = await supabase
            .from("document_types")
            .select("*, group:groups_document_type(name)")
            .order("created_at", { ascending: false });

        if (error) {
            throw error;
        }

        const processedData = data.map((item) => {
            const { data: publicUrlData } = item.example_file_path
                ? supabase.storage.from("document_examples").getPublicUrl(item.example_file_path)
                : { data: { publicUrl: undefined } };

            return {
                ...item,
                example_file_url: publicUrlData.publicUrl
            };
        });

        return { success: true, data: processedData };
    } catch (error) {
        console.error("Error in getDocumentTypes:", error);
        return { success: false, error: TEXTS.FETCH_ERROR };
    }
}
