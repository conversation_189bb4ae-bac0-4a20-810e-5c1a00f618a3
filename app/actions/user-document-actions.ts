"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { checkScholarshipEligibility } from "@/app/services/scholarship-eligibility";
import { isAdminFromSessionClaims } from "@/lib/org-role";
import { Tables } from "@/types/database.types";
import { createClientFromRequest, createServiceRoleClient } from "@/utils/supabase/server";

const TEXTS = {
    adminRequired: "נדרשות הרשאות מנהל לביצוע פעולה זו."
};

export interface UserDocumentStatus {
    documentTypeId: string;
    documentTypeName: string;
    documentTypeDescription: string | null;
    documentGroupId: string | null;
    documentGroupName: string | null;
    link_url: string | null;
    allowedMimeTypes: string[];
    maxFileSizeInMB: number | null;
    example_file_path?: string | null;
    exampleFileUrl?: string;

    isUploaded: boolean;
    uploadedFileName: string | null;
    uploadedFileUrl?: string;

    acceptedFileTypeDescription: string;
}

export async function getRequiredAndUploadedDocuments(overrideUserId?: string): Promise<{
    success: boolean;
    documents: UserDocumentStatus[];
    error?: string;
}> {
    const { userId: clerkUserId } = await auth();

    const targetUserId = overrideUserId || clerkUserId;

    if (!targetUserId) {
        return { success: false, documents: [], error: "User not authenticated." };
    }

    const supabaseServiceRole = createServiceRoleClient();

    try {
        const supabaseUserClient = await createClientFromRequest();
        const eligibilityResult = await checkScholarshipEligibility(targetUserId);
        if (eligibilityResult.error || !eligibilityResult.data) {
            return {
                success: false,
                documents: [],
                error: `Failed to fetch eligible scholarships: ${eligibilityResult.error || "No data"}`
            };
        }

        const eligibleScholarships = eligibilityResult.data.filter((s) => s.isEligible);
        if (eligibleScholarships.length === 0) {
            return { success: true, documents: [], error: undefined };
        }

        const eligibleScholarshipIds = eligibleScholarships.map((s) => s.scholarshipId);

        const { data: docLinks, error: docLinksError } = await supabaseUserClient
            .from("link_scholarship_to_document_type")
            .select("document_type_id")
            .in("scholarship_id", eligibleScholarshipIds);

        if (docLinksError) {
            return { success: false, documents: [], error: `Failed to fetch document links: ${docLinksError.message}` };
        }
        if (!docLinks || docLinks.length === 0) {
            return { success: true, documents: [] };
        }

        const distinctDocumentTypeIdsSet = new Set(docLinks.map((link) => link.document_type_id));
        const distinctDocumentTypeIdsArray = Array.from(distinctDocumentTypeIdsSet);

        const { data: documentTypesData, error: docTypesError } = await supabaseUserClient
            .from("document_types")
            .select(
                `
                id, 
                name, 
                description, 
                allowed_mime_types, 
                max_file_size_mb,
                link_url,
                example_file_path,
                group_id, 
                groups_document_type (id, name)
            `
            )
            .in("id", distinctDocumentTypeIdsArray);

        if (docTypesError) {
            return { success: false, documents: [], error: `Failed to fetch document types: ${docTypesError.message}` };
        }
        if (!documentTypesData) {
            return { success: false, documents: [], error: "No document type data found." };
        }

        const documentStatuses: UserDocumentStatus[] = [];

        for (const docType of documentTypesData) {
            const groupData = docType.groups_document_type;
            let documentGroupId: string | null = null;
            let documentGroupName: string | null = "Uncategorized";

            const group = Array.isArray(groupData) ? groupData[0] : groupData;

            if (group && typeof group === "object" && group !== null) {
                documentGroupId = typeof group.id === "string" ? group.id : null;
                documentGroupName = typeof group.name === "string" ? group.name : "Uncategorized";
            }

            const userDocumentPath = `${targetUserId}/${docType.id}`;

            const { data: existingFiles, error: listError } = await supabaseServiceRole.storage
                .from("user_documents")
                .list(userDocumentPath);

            let isUploaded = false;
            let uploadedFileName: string | null = null;
            let uploadedFileUrl: string | undefined = undefined;

            if (listError) {
                console.warn(`Error listing files for ${userDocumentPath}: ${listError.message}`);
            }

            const targetFileNamePrefix = docType.id;
            const uploadedFile = existingFiles?.find((file) => file.name.startsWith(targetFileNamePrefix + "."));

            if (uploadedFile) {
                isUploaded = true;
                uploadedFileName = uploadedFile.name;

                const { data: signedUrlData, error: signedUrlError } = await supabaseServiceRole.storage
                    .from("user_documents")
                    .createSignedUrl(`${userDocumentPath}/${uploadedFile.name}`, 60);
                if (signedUrlError) {
                    console.error(
                        `Error creating signed URL for ${userDocumentPath}/${uploadedFile.name}:`,
                        signedUrlError
                    );
                    uploadedFileUrl = undefined;
                } else {
                    uploadedFileUrl = signedUrlData.signedUrl;
                }
            }

            const allowedMimes = (
                Array.isArray(docType.allowed_mime_types)
                    ? docType.allowed_mime_types.filter((item): item is string => typeof item === "string")
                    : []
            ) as string[];

            let exampleFileUrl: string | undefined = undefined;
            const exampleFilePath = docType.example_file_path;
            if (exampleFilePath) {
                const { data: exampleSignedUrlData, error: exampleSignedUrlError } = await supabaseServiceRole.storage
                    .from("document_examples")
                    .createSignedUrl(exampleFilePath, 60);
                if (!exampleSignedUrlError && exampleSignedUrlData?.signedUrl) {
                    exampleFileUrl = exampleSignedUrlData.signedUrl;
                }
            }

            documentStatuses.push({
                documentTypeId: docType.id,
                documentTypeName: docType.name,
                documentTypeDescription: docType.description ?? null,
                documentGroupId: documentGroupId,
                documentGroupName: documentGroupName,
                link_url: docType.link_url,
                allowedMimeTypes: allowedMimes,
                maxFileSizeInMB: typeof docType.max_file_size_mb === "number" ? docType.max_file_size_mb : 10,
                example_file_path: exampleFilePath,
                exampleFileUrl,

                isUploaded,
                uploadedFileName,
                uploadedFileUrl: uploadedFileUrl,
                acceptedFileTypeDescription: `Accepted formats: ${allowedMimes.join(", ")}.`
            });
        }

        return { success: true, documents: documentStatuses };
    } catch (error) {
        console.error("Error in getRequiredAndUploadedDocuments:", error);
        return {
            success: false,
            documents: [],
            error: error instanceof Error ? error.message : "An unknown error occurred"
        };
    }
}

function getAllowedExtensionsFromMimeTypes(mimeTypes: string[]): string[] {
    const mimeToExt: Record<string, string[]> = {
        "application/pdf": ["pdf"],
        "image/jpeg": ["jpg", "jpeg"],
        "image/png": ["png"],
        "image/webp": ["webp"]
    };
    const exts = new Set<string>();
    for (const mime of mimeTypes) {
        const mapped = mimeToExt[mime];
        if (mapped) {
            mapped.forEach((ext) => exts.add(ext));
        }
    }
    return Array.from(exts);
}

export async function uploadUserDocument(
    documentTypeId: string,
    file: File,
    overrideUserId?: string
): Promise<{ success: boolean; error?: string; uploadedFileName?: string; uploadedFileUrl?: string | undefined }> {
    const { userId: clerkUserId, sessionClaims } = await auth();

    if (overrideUserId && overrideUserId !== clerkUserId) {
        if (!isAdminFromSessionClaims(sessionClaims)) {
            return { success: false, error: TEXTS.adminRequired };
        }
    }

    const targetUserId = overrideUserId || clerkUserId;

    if (!targetUserId) {
        return { success: false, error: "User not authenticated." };
    }

    const supabaseServiceRole = createServiceRoleClient();

    type SelectedDocumentTypeFields = Pick<
        Tables<"document_types">,
        "id" | "name" | "description" | "allowed_mime_types" | "max_file_size_mb"
    >;

    try {
        const supabaseUserClient = await createClientFromRequest();
        const { data: docTypeData, error: docTypeError } = await supabaseUserClient
            .from("document_types")
            .select("id, name, description, allowed_mime_types, max_file_size_mb")
            .eq("id", documentTypeId)
            .single<SelectedDocumentTypeFields>();

        if (docTypeError || !docTypeData) {
            return {
                success: false,
                error: `Document type ${documentTypeId} not found or error fetching: ${docTypeError?.message}`
            };
        }

        const currentDocType = docTypeData;

        const allowedMimes = (
            Array.isArray(currentDocType.allowed_mime_types)
                ? currentDocType.allowed_mime_types.filter((item): item is string => typeof item === "string")
                : []
        ) as string[];
        if (allowedMimes.length > 0 && !allowedMimes.includes(file.type)) {
            return {
                success: false,
                error: `File type ${file.type} is not allowed for this document. Allowed: ${allowedMimes.join(", ")}`
            };
        }

        const maxSizeInMB = typeof currentDocType.max_file_size_mb === "number" ? currentDocType.max_file_size_mb : 10;
        const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

        if (file.size > maxSizeInBytes) {
            return { success: false, error: `File size exceeds the maximum limit of ${maxSizeInMB}MB.` };
        }

        const fileExtension = file.name.split(".").pop();
        if (!fileExtension) {
            return { success: false, error: "File has no extension." };
        }

        const allowedExtensions = getAllowedExtensionsFromMimeTypes(allowedMimes);
        if (
            allowedExtensions.length > 0 &&
            !allowedExtensions.map((ext) => ext.toLowerCase()).includes(fileExtension.toLowerCase())
        ) {
            return {
                success: false,
                error: `File extension .${fileExtension} is not allowed for this document. Allowed: ${allowedExtensions
                    .map((e) => "." + e)
                    .join(", ")}`
            };
        }

        const newFileName = `${documentTypeId}.${fileExtension}`;

        const filePath = `${targetUserId}/${documentTypeId}/${newFileName}`;

        const { error: uploadError } = await supabaseServiceRole.storage.from("user_documents").upload(filePath, file, {
            cacheControl: "3600",
            upsert: true
        });

        if (uploadError) {
            return { success: false, error: `Failed to upload document: ${uploadError.message}` };
        }

        const { data: signedUrlData, error: signedUrlError } = await supabaseServiceRole.storage
            .from("user_documents")
            .createSignedUrl(filePath, 60);

        let newFileUrl: string | undefined = undefined;
        if (signedUrlError) {
            console.error(`Error creating signed URL for ${filePath} after upload:`, signedUrlError);
        } else {
            newFileUrl = signedUrlData.signedUrl;
        }

        revalidatePath("/dashboard/documents-upload");

        return {
            success: true,
            uploadedFileName: newFileName,
            uploadedFileUrl: newFileUrl
        };
    } catch (error) {
        console.error("Error in uploadUserDocument:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "An unknown server error occurred during upload."
        };
    }
}
