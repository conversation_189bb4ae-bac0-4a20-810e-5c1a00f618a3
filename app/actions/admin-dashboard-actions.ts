"use server";

import { clerkClient } from "@clerk/nextjs/server";

import { PRICING_PLANS } from "@/config/subscriptions";
import { SubscriptionStat, UserStat } from "@/lib/admin-dashboard-constants";
import { createClientFromRequest } from "@/utils/supabase/server";

interface SubscriptionStatsResponse {
    success: boolean;
    data?: SubscriptionStat[];
    error?: string;
}

interface UserStatsResponse {
    success: boolean;
    data?: UserStat;
    error?: string;
}

export async function getSubscriptionStats(): Promise<SubscriptionStatsResponse> {
    const supabase = await createClientFromRequest();
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    const { data: activeSubsData, error: activeSubsError } = await supabase
        .from("user_subscriptions")
        .select("plan_id")
        .eq("is_active", true);

    if (activeSubsError) {
        console.error("Error fetching active subscriptions:", activeSubsError);
        return { success: false, error: "Failed to fetch active subscriptions" };
    }

    const { data: newSubsData, error: newSubsError } = await supabase
        .from("user_subscriptions")
        .select("plan_id")
        .gte("created_at", twentyFourHoursAgo);

    if (newSubsError) {
        console.error("Error fetching new subscriptions:", newSubsError);
        return { success: false, error: "Failed to fetch new subscriptions" };
    }

    const activePlanCounts = new Map<string, number>();
    PRICING_PLANS.forEach((plan) => activePlanCounts.set(plan.id, 0));
    (activeSubsData || []).forEach((item: { plan_id: string }) => {
        activePlanCounts.set(item.plan_id, (activePlanCounts.get(item.plan_id) || 0) + 1);
    });

    const totalActive = Array.from(activePlanCounts.values()).reduce((sum, count) => sum + count, 0);

    const newSubsMap = new Map<string, number>();
    PRICING_PLANS.forEach((plan) => newSubsMap.set(plan.id, 0));
    (newSubsData || []).forEach((item: { plan_id: string }) => {
        newSubsMap.set(item.plan_id, (newSubsMap.get(item.plan_id) || 0) + 1);
    });

    const subscriptionStats = PRICING_PLANS.map((plan) => {
        const activeCount = activePlanCounts.get(plan.id) || 0;
        return {
            planId: plan.id,
            planName: plan.title,
            count: activeCount,
            percentage: totalActive > 0 ? Math.round((activeCount / totalActive) * 100) : 0,
            newLast24h: newSubsMap.get(plan.id) || 0
        };
    });

    return { success: true, data: subscriptionStats };
}

export async function getUserStats(): Promise<UserStatsResponse> {
    try {
        const client = await clerkClient();
        const totalUsers = await client.users.getCount();
        return { success: true, data: { total: totalUsers } };
    } catch (error) {
        console.error("Error fetching user stats:", error);
        return { success: false, error: "Failed to fetch user stats" };
    }
}
