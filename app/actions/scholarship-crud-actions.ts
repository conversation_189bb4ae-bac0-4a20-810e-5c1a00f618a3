"use server";

import { Tables, TablesInsert, TablesUpdate } from "@/types/database.types";
import { createServiceRoleClient } from "@/utils/supabase/server";

const TEXTS = {
    SCHOLARSHIP_DELETE_ERROR: "שגיאה במחיקת המלגה. אנא נסה שנית.",
    SCHOLARSHIP_CREATE_ERROR: "שגיאה ביצירת המלגה. אנא נסה שנית.",
    SCHOLARSHIP_UPDATE_ERROR: "שגיאה בעדכון המלגה. אנא נסה שנית.",
    SLUG_EXISTS_ERROR: "קידומת זו כבר קיימת במערכת. אנא בחר קידומת אחרת.",
    SCHOLARSHIP_NOT_FOUND: "המלגה לא נמצאה.",
    UNSUPPORTED_FILE_TYPE: "רק קבצי WebP נתמכים"
};

export const createScholarship = async (
    data: Omit<TablesInsert<"scholarships">, "id" | "created_at" | "updated_at">
): Promise<{ success: boolean; data?: Tables<"scholarships">; error?: string }> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Creating scholarship with data:", data);

        const { data: existingScholarships, error: checkError } = await supabase
            .from("scholarships")
            .select("id")
            .eq("slug", data.slug);

        if (checkError) {
            console.error("Error checking existing slug:", checkError);
            return { success: false, error: TEXTS.SCHOLARSHIP_CREATE_ERROR };
        }

        if (existingScholarships && existingScholarships.length > 0) {
            console.log("Slug already exists:", data.slug);
            return { success: false, error: TEXTS.SLUG_EXISTS_ERROR };
        }

        console.log("Inserting scholarship into database...");
        const { data: scholarship, error: createError } = await supabase
            .from("scholarships")
            .insert([data])
            .select()
            .single();

        if (createError) {
            console.error("Error creating scholarship:", {
                message: createError.message,
                details: createError.details,
                hint: createError.hint,
                code: createError.code
            });
            return { success: false, error: TEXTS.SCHOLARSHIP_CREATE_ERROR };
        }

        console.log("Scholarship created successfully:", scholarship);
        return { success: true, data: scholarship };
    } catch (error) {
        console.error("Unexpected error creating scholarship:", error);
        return { success: false, error: TEXTS.SCHOLARSHIP_CREATE_ERROR };
    }
};

export const updateScholarship = async (
    id: string,
    data: TablesUpdate<"scholarships">
): Promise<{ success: boolean; error?: string }> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Updating scholarship with data:", { id, data });

        if (data.slug) {
            const { data: existingScholarships, error: checkError } = await supabase
                .from("scholarships")
                .select("id")
                .eq("slug", data.slug)
                .not("id", "eq", id);

            if (checkError) {
                console.error("Error checking existing slug:", checkError);
                return { success: false, error: TEXTS.SCHOLARSHIP_UPDATE_ERROR };
            }

            if (existingScholarships && existingScholarships.length > 0) {
                console.log("Slug already exists:", data.slug);
                return { success: false, error: TEXTS.SLUG_EXISTS_ERROR };
            }
        }

        const { data: updatedScholarship, error: updateError } = await supabase
            .from("scholarships")
            .update(data)
            .eq("id", id)
            .select("id")
            .single();

        if (updateError) {
            console.error("Error updating scholarship:", {
                message: updateError.message,
                details: updateError.details,
                hint: updateError.hint,
                code: updateError.code
            });
            return { success: false, error: TEXTS.SCHOLARSHIP_UPDATE_ERROR };
        }

        if (!updatedScholarship) {
            console.error("No scholarship found with ID:", id);
            return { success: false, error: TEXTS.SCHOLARSHIP_NOT_FOUND };
        }

        console.log("Scholarship updated successfully");
        return { success: true };
    } catch (error) {
        console.error("Unexpected error updating scholarship:", error);
        return { success: false, error: TEXTS.SCHOLARSHIP_UPDATE_ERROR };
    }
};

export const getScholarship = async (
    id: string
): Promise<{
    success: boolean;
    data?: Tables<"scholarships"> & {
        link_scholarship_to_testimonial: { testimonial_id: string }[];
        link_scholarship_to_scholarship_groups: { scholarship_group_id: string }[];
    };
    error?: string;
}> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Fetching scholarship:", id);

        const { data: scholarship, error } = await supabase
            .from("scholarships")
            .select(
                `
                *,
                link_scholarship_to_testimonial(testimonial_id),
                link_scholarship_to_scholarship_groups(scholarship_group_id)
            `
            )
            .eq("id", id)
            .maybeSingle();

        if (error) {
            console.error("Error fetching scholarship:", error);
            return { success: false, error: TEXTS.SCHOLARSHIP_NOT_FOUND };
        }

        if (!scholarship) {
            return { success: false, error: TEXTS.SCHOLARSHIP_NOT_FOUND };
        }

        console.log("Scholarship fetched successfully");
        return {
            success: true,
            data: scholarship as Tables<"scholarships"> & {
                link_scholarship_to_testimonial: { testimonial_id: string }[];
                link_scholarship_to_scholarship_groups: { scholarship_group_id: string }[];
            }
        };
    } catch (error) {
        console.error("Unexpected error fetching scholarship:", error);
        return { success: false, error: TEXTS.SCHOLARSHIP_NOT_FOUND };
    }
};

export const uploadScholarshipImage = async (
    scholarshipId: string,
    file: File
): Promise<{ success: boolean; imageUrl?: string; error?: string }> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Starting upload for scholarship:", scholarshipId);
        console.log("File details:", {
            name: file.name,
            size: file.size,
            type: file.type
        });

        if (file.type !== "image/webp") {
            console.error("Unsupported file type:", file.type);
            return { success: false, error: TEXTS.UNSUPPORTED_FILE_TYPE };
        }

        const filePath = `${scholarshipId}.webp`;

        console.log("Uploading to path:", filePath);

        const { data: uploadData, error: uploadError } = await supabase.storage
            .from("scholarships")
            .upload(filePath, file, { upsert: true });

        if (uploadError) {
            console.error("Upload error details:", {
                message: uploadError.message,
                error: uploadError
            });
            return { success: false, error: `שגיאה בהעלאת התמונה: ${uploadError.message}` };
        }

        console.log("Upload successful:", uploadData);

        const {
            data: { publicUrl }
        } = supabase.storage.from("scholarships").getPublicUrl(filePath);

        console.log("Public URL generated:", publicUrl);

        const { error: updateError } = await supabase
            .from("scholarships")
            .update({ image_url: publicUrl })
            .eq("id", scholarshipId);

        if (updateError) {
            console.error("Database update error:", updateError);
            await supabase.storage.from("scholarships").remove([filePath]);
            return { success: false, error: "שגיאה בעדכון התמונה" };
        }

        console.log("Image upload and database update completed successfully");
        return { success: true, imageUrl: publicUrl };
    } catch (error) {
        console.error("Unexpected error uploading scholarship image:", error);
        return { success: false, error: "שגיאה בהעלאת התמונה" };
    }
};

export const linkScholarshipTestimonials = async (
    scholarshipId: string,
    testimonialIds: string[]
): Promise<{ success: boolean; error?: string }> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Linking testimonials:", { scholarshipId, testimonialIds });

        const { error: deleteError } = await supabase
            .from("link_scholarship_to_testimonial")
            .delete()
            .eq("scholarship_id", scholarshipId);

        if (deleteError) {
            console.error("Error deleting existing testimonial links:", deleteError);
            return { success: false, error: "שגיאה בעדכון חוות דעת" };
        }

        if (testimonialIds.length > 0) {
            const connections = testimonialIds.map((testimonial_id) => ({
                scholarship_id: scholarshipId,
                testimonial_id
            }));

            const { error: insertError } = await supabase.from("link_scholarship_to_testimonial").insert(connections);

            if (insertError) {
                console.error("Error inserting testimonial links:", insertError);
                return { success: false, error: "שגיאה בעדכון חוות דעת" };
            }
        }

        console.log("Testimonial links updated successfully");
        return { success: true };
    } catch (error) {
        console.error("Unexpected error linking testimonials:", error);
        return { success: false, error: "שגיאה בעדכון חוות דעת" };
    }
};

export const linkScholarshipGroups = async (
    scholarshipId: string,
    groupIds: string[]
): Promise<{ success: boolean; error?: string }> => {
    const supabase = createServiceRoleClient();

    try {
        console.log("Linking scholarship groups:", { scholarshipId, groupIds });

        const { error: deleteError } = await supabase
            .from("link_scholarship_to_scholarship_groups")
            .delete()
            .eq("scholarship_id", scholarshipId);

        if (deleteError) {
            console.error("Error deleting existing group links:", deleteError);
            return { success: false, error: "שגיאה בעדכון קבוצות מלגה" };
        }

        if (groupIds.length > 0) {
            const connections = groupIds.map((scholarship_group_id) => ({
                scholarship_id: scholarshipId,
                scholarship_group_id
            }));

            const { error: insertError } = await supabase
                .from("link_scholarship_to_scholarship_groups")
                .insert(connections);

            if (insertError) {
                console.error("Error inserting group links:", insertError);
                return { success: false, error: "שגיאה בעדכון קבוצות מלגה" };
            }
        }

        console.log("Scholarship group links updated successfully");
        return { success: true };
    } catch (error) {
        console.error("Unexpected error linking scholarship groups:", error);
        return { success: false, error: "שגיאה בעדכון קבוצות מלגה" };
    }
};
