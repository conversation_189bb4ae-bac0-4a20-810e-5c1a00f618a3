"use server";

import { auth, clerkClient } from "@clerk/nextjs/server";

export const completeOnboarding = async () => {
    const { userId } = await auth();

    if (!userId) {
        return { message: "No Logged In User" };
    }

    const client = await clerkClient();

    try {
        const res = await client.users.updateUser(userId, {
            publicMetadata: {
                onboardingComplete: true
            }
        });
        return { message: res.publicMetadata };
    } catch (err) {
        console.error("Error updating user metadata in completeOnboarding:", err);
        return {
            message: `There was an error updating the user metadata. ${err instanceof Error ? err.message : String(err)}`
        };
    }
};
