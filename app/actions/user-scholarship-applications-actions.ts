"use server";

import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

import { type TablesInsert } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

const TEXTS = {
    fetchError: "שגיאה בטעינת סטטוס בקשות למלגה",
    updateError: "שגיאה בעדכון סטטוס בקשה למלגה",
    authError: "נדרשת התחברות"
};

export async function getUserScholarshipApplications(scholarshipIds: string[]) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return { success: false, error: TEXTS.authError };
        }

        const supabase = await createClientFromRequest();

        const { data, error } = await supabase
            .from("user_scholarship_applications")
            .select("scholarship_id, should_apply")
            .in("scholarship_id", scholarshipIds)
            .eq("user_id", userId);

        if (error) {
            throw new Error(error.message);
        }

        return { success: true, applications: data };
    } catch (e: unknown) {
        const message = e instanceof Error ? e.message : TEXTS.fetchError;
        return { success: false, error: message };
    }
}

export async function updateUserScholarshipApplication(scholarshipId: string, shouldApply: boolean) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return { success: false, error: TEXTS.authError };
        }

        const supabase = await createClientFromRequest();

        const applicationData: TablesInsert<"user_scholarship_applications"> = {
            user_id: userId,
            scholarship_id: scholarshipId,
            should_apply: shouldApply
        };

        const { error } = await supabase.from("user_scholarship_applications").upsert(applicationData, {
            onConflict: "user_id,scholarship_id"
        });

        if (error) {
            throw error;
        }

        revalidatePath("/dashboard/eligible-scholarships");
        return { success: true };
    } catch (e: unknown) {
        const message = e instanceof Error ? e.message : TEXTS.updateError;
        return { success: false, error: message };
    }
}
