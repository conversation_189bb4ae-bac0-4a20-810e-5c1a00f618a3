import { AuthenticateWithRedirectCallback } from "@clerk/nextjs";

import { LoadingIcon } from "@/components/common/loading-icon";
import { TEXTS } from "@/lib/auth-constants";

export default function SSOCallback() {
    return (
        <div className="flex justify-center items-center h-screen">
            <LoadingIcon text={TEXTS.loading} />
            <AuthenticateWithRedirectCallback />
        </div>
    );
}
