import { Metadata } from "next";

import Contact from "@/components/home/<USER>";
import FAQ from "@/components/home/<USER>";

const TEXTS = {
    title: "צור קשר",
    description: "צור קשר עם מילגאפו - שאלות, הצעות, או כל דבר אחר",
    keywords: "צור קשר, מילגאפו, סיוע, שאלות נפוצות",
    contactTitle: "צור קשר",
    contactSubtitle: "יש לך שאלה? אנחנו כאן לעזור",
    faq: {
        title: "שאלות נפוצות",
        subtitle: "שאלות נפוצות ותשובות"
    }
};

export const metadata: Metadata = {
    title: TEXTS.title,
    description: TEXTS.description,
    keywords: TEXTS.keywords
};

export default function ContactPage() {
    return (
        <main className="px-4 md:px-0 space-y-12 md:space-y-16 py-8">
            <section id="contact" className="scroll-mt-20 max-w-4xl mx-auto">
                <Contact title={TEXTS.contactTitle} subtitle={TEXTS.contactSubtitle} />
            </section>

            <section id="faq" className="scroll-mt-20">
                <FAQ title={TEXTS.faq.title} subtitle={TEXTS.faq.subtitle} />
            </section>
        </main>
    );
}
