import { notFound } from "next/navigation";

import ScholarshipGroupContent from "@/components/scholarship/scholarship-group-content";
import type { Tables } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

interface RouteParams {
    scholarshipGroupSlug: string;
}

interface PageProps {
    params: Promise<RouteParams>;
}

type TestimonialLink = Tables<"link_scholarship_groups_to_testimonial">;
type ScholarshipGroup = Pick<Tables<"groups_scholarship">, "id" | "title" | "description" | "image_url" | "slug">;

export default async function ScholarshipGroupPage({ params }: PageProps) {
    const resolvedParams = await params;
    const supabase = await createClientFromRequest();

    const groupResult = await supabase
        .from("groups_scholarship")
        .select("id, title, description, image_url, slug")
        .eq("slug", resolvedParams.scholarshipGroupSlug)
        .single<ScholarshipGroup>();

    if (!groupResult.data) {
        return notFound();
    }

    const testimonialsResult = await supabase
        .from("link_scholarship_groups_to_testimonial")
        .select("testimonial_id")
        .eq("scholarship_group_id", groupResult.data.id);

    if (testimonialsResult.error) {
        console.error("Error fetching testimonials:", testimonialsResult.error);
        return notFound();
    }

    const testimonialIds =
        (testimonialsResult.data as Pick<TestimonialLink, "testimonial_id">[])?.map((item) => item.testimonial_id) ||
        [];

    const groupData = {
        ...groupResult.data,
        image_url: groupResult.data.image_url || undefined
    };

    return <ScholarshipGroupContent group={groupData} testimonialIds={testimonialIds} />;
}
