import { Metadata } from "next";

import ScholarshipGroups from "@/components/home/<USER>";
import Scholarships from "@/components/home/<USER>";

const TEXTS = {
    title: "מילגאפו",
    description: "מילגאפו",
    keywords: "מילגאפו",
    ScholarshipGroupsTitle: "מלגות לפי נושא",
    press: {
        title: "כותבים עלינו"
    }
};

export const metadata: Metadata = {
    title: TEXTS.title,
    description: TEXTS.description,
    keywords: TEXTS.keywords
};

export default async function Home() {
    return (
        <main className="px-4 py-8 md:px-0 space-y-12 md:space-y-16">
            <section id="scholarship-groups" className="scroll-mt-20">
                <div className="max-w-4xl mx-auto">
                    <div className="my-8">
                        <Scholarships />
                    </div>
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-2xl md:text-3xl font-bold">{TEXTS.ScholarshipGroupsTitle}</h2>
                    </div>
                    <div className="my-8">
                        <ScholarshipGroups />
                    </div>
                </div>
            </section>
        </main>
    );
}
