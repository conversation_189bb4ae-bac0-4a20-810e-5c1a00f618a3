import { Metadata } from "next";
import { notFound } from "next/navigation";
import { cache, Suspense } from "react";

import ScholarshipDetail from "@/components/scholarship/scholarship-detail";
import ScholarshipDetailSkeleton from "@/components/scholarship/scholarship-detail-skeleton";
import { createClientFromRequest } from "@/utils/supabase/server";

const getScholarshipBySlug = cache(async (slug: string) => {
    const supabase = await createClientFromRequest();
    const { data: scholarship, error } = await supabase
        .from("scholarships")
        .select(
            "id, slug, title, description, short_description, volunteer_hours, min_amount, max_amount, start_date, end_date, requirements, benefits, target_audience, image_url, url, is_public, scholarship_type, created_at, updated_at, contact_email, contact_person, contact_phone, internal_notes, is_active, response_date"
        )
        .eq("slug", slug)
        .single();

    if (error) {
        console.error("Error fetching scholarship:", error);
        return notFound();
    }

    if (!scholarship || !scholarship.is_public) {
        return notFound();
    }

    return scholarship;
});

interface RouteParams {
    scholarshipSlug: string;
}

interface PageProps {
    params: Promise<RouteParams>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
    const resolvedParams = await params;
    const scholarship = await getScholarshipBySlug(resolvedParams.scholarshipSlug);

    return {
        title: scholarship.title,
        description: scholarship.description
    };
}

export default async function ScholarshipPage({ params }: PageProps) {
    const resolvedParams = await params;
    const scholarship = await getScholarshipBySlug(resolvedParams.scholarshipSlug);
    return (
        <Suspense fallback={<ScholarshipDetailSkeleton />}>
            <ScholarshipDetail scholarshipData={scholarship} />
        </Suspense>
    );
}
