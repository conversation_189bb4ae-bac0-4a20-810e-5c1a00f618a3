import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";

export default function ScholarshipNotFound() {
    return (
        <div className="flex min-h-[calc(100vh-80px)] flex-col items-center justify-center space-y-6 text-center">
            <div className="space-y-2">
                <h1 className="text-4xl font-extrabold tracking-tighter sm:text-5xl">לא נמצאה מלגה</h1>
                <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl">
                    מצטערים, לא מצאנו את המלגה שחיפשת. ייתכן שהקישור שגוי או שהמלגה כבר לא זמינה.
                </p>
            </div>
            <Button asChild>
                <Link href="/scholarships">חזרה לרשימת המלגות</Link>
            </Button>
        </div>
    );
}
