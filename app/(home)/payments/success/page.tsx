"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

import { PaymentStatusCard } from "@/components/payments/payment-status-card";

const TEXTS = {
    title: "אימות תשלום",
    loading: "מאמתים את פרטי התשלום שלך...",
    successTitle: "התשלום אושר!",
    successDescription: "התשלום שלך עבר בהצלחה והחבילה שלך עודכנה.",
    successDetailsPrefix: "חבילת",
    successTransactionPrefix: "מספר עסקה:",
    errorTitle: "שגיאה באימות התשלום",
    errorDescription: "אירעה שגיאה במהלך אימות התשלום שלך.",
    errorStatus: "סטטוס שגיאה",
    redirectingToDashboard: "מועבר לחשבון אישי בעוד",
    redirectingToSubscriptions: "מועבר לעמוד החבילות בעוד",
    seconds: "שניות"
};

function PaymentSuccessContent() {
    const searchParams = useSearchParams();
    const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
    const [error, setError] = useState<string | null>(null);
    const [countdown, setCountdown] = useState(5);
    const [verificationData, setVerificationData] = useState<{
        orderId?: string;
        transactionId?: string;
        message?: string;
    } | null>(null);

    const router = useRouter();

    // Handle API verification
    useEffect(() => {
        const verifyPayment = async () => {
            setStatus("loading");
            setError(null);
            setVerificationData(null);
            setCountdown(5);

            const params: Record<string, string> = {};
            searchParams.forEach((value, key) => {
                params[key] = value;
            });

            try {
                // Use the API endpoint which now uses our shared library
                const response = await fetch("/api/verify-checkout", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(params)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    setVerificationData({
                        orderId: result.orderId,
                        transactionId: result.transactionId,
                        message: result.message
                    });
                    setStatus("success");
                } else {
                    setError(result.error || TEXTS.errorDescription);
                    setStatus("error");
                }
            } catch (err) {
                console.error("Error calling verification API:", err);
                setError(err instanceof Error ? err.message : TEXTS.errorDescription);
                setStatus("error");
            }
        };

        verifyPayment();
    }, [searchParams]);

    // Handle countdown
    useEffect(() => {
        if (status === "loading") return;

        const countdownInterval = setInterval(() => {
            setCountdown((prev) => Math.max(prev - 1, 0));
        }, 1000);

        return () => clearInterval(countdownInterval);
    }, [status]);

    // Handle redirect when countdown reaches 0
    useEffect(() => {
        if (countdown === 0) {
            if (status === "success") {
                router.push("/dashboard");
            } else if (status === "error") {
                router.push("/subscriptions");
            }
        }
    }, [countdown, router, status]);

    // Create redirect message based on status
    const getRedirectMessage = () => {
        if (status === "success") {
            return `${TEXTS.redirectingToDashboard} ${countdown} ${TEXTS.seconds}`;
        } else if (status === "error") {
            return `${TEXTS.redirectingToSubscriptions} ${countdown} ${TEXTS.seconds}`;
        }
        return "";
    };

    return (
        <PaymentStatusCard
            title={TEXTS.title}
            status={status}
            loadingText={TEXTS.loading}
            successTitle={TEXTS.successTitle}
            successDescription={`${verificationData?.message || TEXTS.successDescription} ${getRedirectMessage()}`}
            successDetailsPrefix={TEXTS.successDetailsPrefix}
            successTransactionPrefix={TEXTS.successTransactionPrefix}
            errorTitle={TEXTS.errorTitle}
            errorDescription={`${error ? `${TEXTS.errorStatus} ${error}` : TEXTS.errorDescription} ${getRedirectMessage()}`}
            details={{
                orderId: verificationData?.orderId,
                transactionId: verificationData?.transactionId
            }}
            actions={[]}
        />
    );
}

export default function PaymentSuccessPage() {
    return (
        <Suspense
            fallback={
                <PaymentStatusCard
                    title={TEXTS.title}
                    status="loading"
                    loadingText={TEXTS.loading}
                    successTitle=""
                    successDescription=""
                    errorTitle=""
                    errorDescription=""
                />
            }
        >
            <PaymentSuccessContent />
        </Suspense>
    );
}
