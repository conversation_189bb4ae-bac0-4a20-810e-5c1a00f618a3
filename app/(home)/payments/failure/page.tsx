"use client";

import { useRouter } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

import { PaymentStatusCard } from "@/components/payments/payment-status-card";

const TEXTS = {
    title: "שגיאה בתשלום",
    errorTitle: "התשלום נכשל",
    errorDescriptionGeneric: "אירעה שגיאה במהלך ניסיון התשלום. לא בוצע חיוב.",
    redirectingToSubscriptions: "מועבר לעמוד החבילות בעוד",
    seconds: "שניות",
    loading: "טוען..."
};

function PaymentFailureContent() {
    const router = useRouter();
    const [countdown, setCountdown] = useState(5);
    const errorMessage = `${TEXTS.errorDescriptionGeneric} ${TEXTS.redirectingToSubscriptions} ${countdown} ${TEXTS.seconds}`;

    // Handle countdown
    useEffect(() => {
        const countdownInterval = setInterval(() => {
            setCountdown((prev) => Math.max(prev - 1, 0));
        }, 1000);

        return () => clearInterval(countdownInterval);
    }, []);

    // Handle redirect when countdown reaches 0
    useEffect(() => {
        if (countdown === 0) {
            router.push("/subscriptions");
        }
    }, [countdown, router]);

    return (
        <PaymentStatusCard
            title={TEXTS.title}
            status="error"
            errorTitle={TEXTS.errorTitle}
            errorDescription={errorMessage}
            actions={[]}
            loadingText=""
            successTitle=""
            successDescription=""
        />
    );
}

export default function PaymentFailurePage() {
    return (
        <Suspense
            fallback={
                <PaymentStatusCard
                    title={TEXTS.title}
                    status="loading"
                    loadingText={TEXTS.loading}
                    successTitle=""
                    successDescription=""
                    errorTitle=""
                    errorDescription=""
                />
            }
        >
            <PaymentFailureContent />
        </Suspense>
    );
}
