import "../globals.css";

import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

import { Navbar } from "@/components/layout/navbar";
import { SiteFooter } from "@/components/layout/site-footer";
import { Banner } from "@/components/ui/banner";

export const metadata = {
    dir: "rtl"
};

export default function HomeLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <div className="min-h-screen bg-background font-sans antialiased" dir="rtl">
            <div className="flex min-h-screen flex-col">
                <div className="flex flex-col">
                    <Banner />
                    <div className="relative">
                        <Navbar />
                    </div>
                </div>
                <main className="flex-1 pt-16">{children}</main>
                <span className="mt-12">
                    <SiteFooter />
                </span>
                <Analytics />
                <SpeedInsights />
            </div>
        </div>
    );
}
