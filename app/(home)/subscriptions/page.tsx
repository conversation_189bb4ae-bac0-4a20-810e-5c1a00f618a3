"use client";

import React, { useEffect, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { SubscriptionsSkeleton } from "@/components/subscriptions/skeleton/subscriptions-skeleton";
import { SubscriptionsTable } from "@/components/subscriptions/subscriptions-table";
import { Card } from "@/components/ui/card";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

const TEXTS = {
    TITLE: "תוכניות מחיר",
    SUBTITLE: "בחר את התוכנית המתאימה לך",
    LOADING: "טוען נתוני מנוי..."
};

export default function SubscriptionsPage() {
    const [currentSubscription, setCurrentSubscription] = useState<UserSubscriptionWithPlan | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        async function fetchSubscription() {
            try {
                const subscription = await getCurrentUserSubscription();
                setCurrentSubscription(subscription);
            } catch (error) {
                console.error("Error fetching subscription:", error);
            } finally {
                setIsLoading(false);
            }
        }

        fetchSubscription();
    }, []);

    return (
        <div className="container mx-auto px-4 md:px-6">
            <Card className="md:p-10 shadow-md">
                <div className="text-center mb-12" dir="rtl">
                    <h1 className="text-3xl md:text-4xl font-bold mb-4">{TEXTS.TITLE}</h1>
                    <p className="text-xl text-muted-foreground">{TEXTS.SUBTITLE}</p>
                </div>
                {isLoading ? (
                    <SubscriptionsSkeleton />
                ) : (
                    <SubscriptionsTable showFeaturesTable={true} currentSubscription={currentSubscription} />
                )}
            </Card>
        </div>
    );
}
