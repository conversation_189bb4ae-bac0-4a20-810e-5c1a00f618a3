import { Metadata } from "next";

import ContentWithImage from "@/components/home/<USER>";
import FinalCTA from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import Press from "@/components/home/<USER>";
import ScholarshipGroups from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";

const TEXTS = {
    title: "מילגאפו",
    description: "מילגאפו",
    keywords: "מילגאפו",
    ScholarshipGroupsTitle: "כל המלגות",
    press: {
        title: "כותבים עלינו"
    }
};

export const metadata: Metadata = {
    title: TEXTS.title,
    description: TEXTS.description,
    keywords: TEXTS.keywords
};

export default async function Home() {
    return (
        <main className="px-4 md:px-0 space-y-12 md:space-y-16">
            <section id="hero" className="scroll-mt-20">
                <Hero />
            </section>

            <section id="scholarship-groups" className="scroll-mt-20">
                <div className="max-w-4xl mx-auto">
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-2xl md:text-3xl font-bold">{TEXTS.ScholarshipGroupsTitle}</h2>
                    </div>
                    <ScholarshipGroups />
                </div>
            </section>

            <section id="about" className="scroll-mt-20">
                <ContentWithImage />
            </section>

            <section id="press" className="scroll-mt-20">
                <Press title={TEXTS.press.title} />
            </section>

            <section id="testimonials" className="scroll-mt-20">
                <Testimonials />
            </section>

            <section id="final-cta" className="scroll-mt-20 max-w-4xl mx-auto">
                <FinalCTA />
            </section>
        </main>
    );
}
