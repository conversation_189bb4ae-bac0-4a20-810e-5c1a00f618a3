import { NextResponse } from "next/server";

const DATA_PARAMS = {
    resource_id: "1c5bc716-8210-4ec7-85be-92e6271955c2",
    distinct: "true",
    plain: "true",
    limit: "10000",
    include_total: "true",
    records_format: "objects"
};

const DATA_URL =
    "https://data.gov.il/api/3/action/datastore_search?" +
    Object.entries(DATA_PARAMS)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join("&");

type BankBranchRecord = {
    Bank_Code: number;
    Bank_Name: string;
    Branch_Code: number;
    Branch_Name: string;
};

type Bank = {
    bankCode: number;
    bankName: string;
    branches: {
        branchCode: number;
        branchName: string;
    }[];
};

export const config = {
    revalidate: 86400
};

export async function GET() {
    try {
        const res = await fetch(DATA_URL, { next: { revalidate: config.revalidate } });
        if (!res.ok) {
            return NextResponse.json({ error: "Failed to fetch bank data" }, { status: 500 });
        }
        const data = await res.json();
        const records: BankBranchRecord[] = data?.result?.records || [];
        const bankMap = new Map<number, Bank>();

        for (const rec of records) {
            if (!rec.Bank_Code || !rec.Bank_Name || !rec.Branch_Code || !rec.Branch_Name) continue;
            if (!bankMap.has(rec.Bank_Code)) {
                bankMap.set(rec.Bank_Code, {
                    bankCode: rec.Bank_Code,
                    bankName: rec.Bank_Name,
                    branches: []
                });
            }
            bankMap.get(rec.Bank_Code)!.branches.push({
                branchCode: rec.Branch_Code,
                branchName: rec.Branch_Name
            });
        }

        const banks: Bank[] = Array.from(bankMap.values()).map((bank) => ({
            ...bank,
            branches: bank.branches.sort(
                (a, b) => a.branchName.localeCompare(b.branchName, "he") || a.branchCode - b.branchCode
            )
        }));

        banks.sort((a, b) => a.bankName.localeCompare(b.bankName, "he") || a.bankCode - b.bankCode);

        return NextResponse.json({ banks });
    } catch (e) {
        console.error("Error fetching bank data:", e);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}
