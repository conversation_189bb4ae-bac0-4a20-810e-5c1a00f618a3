import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { checkScholarshipEligibility } from "@/app/services/scholarship-eligibility";

export const maxDuration = 60;

export async function GET() {
    const { userId } = await auth();

    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
        const result = await checkScholarshipEligibility(userId);

        if (result.error) {
            console.error("Error checking scholarship eligibility:", result.error);
            return NextResponse.json({ error: result.error }, { status: 500 });
        }

        return NextResponse.json({ eligibility: result.data });
    } catch (error) {
        console.error("Error checking scholarship eligibility:", error);
        return NextResponse.json({ error: "Failed to check eligibility" }, { status: 500 });
    }
}
