import { verifyWebhook } from "@clerk/nextjs/webhooks";
import { NextRequest } from "next/server";

import { createServiceRoleClient } from "@/utils/supabase/server";
import { setUserClaim, UserClaim<PERSON>ey } from "@/utils/user-claims-client";

export async function POST(req: NextRequest) {
    try {
        const evt = await verifyWebhook(req);

        const eventType = evt.type;

        if (eventType === "user.created" || eventType === "user.updated") {
            const userId = evt.data?.id;
            if (!userId) {
                console.warn("No user ID found in webhook event.");
                return new Response("No user ID found in webhook event.", { status: 400 });
            }

            const supabase = createServiceRoleClient();

            const emailAddresses = evt.data?.email_addresses;
            if (Array.isArray(emailAddresses) && emailAddresses.length > 0) {
                for (const email of emailAddresses) {
                    if (email?.email_address) {
                        try {
                            await setUserClaim(supabase, userId, UserClaimKey.USER_EMAIL, email.email_address);
                        } catch (error) {
                            console.error("Error inserting user claim:", error);
                            return new Response("Error inserting user claim", { status: 500 });
                        }
                    }
                }
            } else {
                console.warn(`No email addresses found for user ${userId}. Skipping claim setting.`);
            }
        }

        return new Response("Webhook received", { status: 200 });
    } catch (err) {
        console.error("Error verifying webhook:", err);
        return new Response("Error verifying webhook", { status: 400 });
    }
}
