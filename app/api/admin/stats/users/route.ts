import { clerkClient } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const client = await clerkClient();
        const totalUsers = await client.users.getCount();

        return NextResponse.json({
            total: totalUsers
        });
    } catch (error) {
        console.error("Error fetching Clerk user stats:", error);
        return new NextResponse("Internal Server Error", { status: 500 });
    }
}
