import { NextResponse } from "next/server";

import { ContactFormTemplate } from "@/components/email/contact-form-template";
import { resend } from "@/lib/resend";
import { createClientFromRequest } from "@/utils/supabase/server";

const TEXTS = {
    invalidEmail: "כתובת אימייל אינה תקינה",
    invalidEmailFormat: "פורמט כתובת אימייל אינו תקין",
    invalidEmailDomain: "שם הדומיין של האימייל אינו קיים",
    disposableEmail: "אימיילים זמניים אינם מומלצים",
    emailSent: "אימייל נשלח בהצלחה",
    emailSubmissionFailed: "שגיאה בשליחת האימייל"
};

const MAILEROO_API_KEY = process.env.MAILEROO_API_KEY;

export async function POST(req: Request) {
    const supabase = await createClientFromRequest();
    const { data, error } = await supabase.from("contact").select("email");

    if (error) {
        console.error("Error fetching contact email:", error);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    const contactEmails = data.map((contact) => contact.email);

    try {
        const body = await req.json();
        const { email, subject, message } = body;

        try {
            const verificationResponse = await fetch("https://verify.maileroo.net/check", {
                method: "POST",
                body: JSON.stringify({ email_address: email, api_key: MAILEROO_API_KEY })
            });

            const verificationData = await verificationResponse.json();

            if (!verificationData.data.format_valid || !verificationData.data.mx_found) {
                return NextResponse.json(
                    {
                        error: TEXTS.invalidEmail,
                        details: !verificationData.data.format_valid
                            ? TEXTS.invalidEmailFormat
                            : TEXTS.invalidEmailDomain
                    },
                    { status: 400 }
                );
            }

            if (verificationData.disposable) {
                return NextResponse.json(
                    {
                        error: TEXTS.invalidEmail,
                        details: TEXTS.disposableEmail
                    },
                    { status: 400 }
                );
            }
        } catch (verificationError) {
            console.error("Email verification failed:", verificationError);
        }

        const res = await resend.emails.send({
            from: "טופס יצירת קשר <<EMAIL>>",
            to: contactEmails,
            subject: subject as string,
            react: ContactFormTemplate({ email, subject, message }) as React.ReactNode,
            replyTo: email
        });

        if (res?.error) {
            return NextResponse.json({ error: res.error.message }, { status: 500 });
        }

        return NextResponse.json({ success: true, message: TEXTS.emailSent }, { status: 200 });
    } catch (error) {
        console.error("Error processing email submission:", error);
        return NextResponse.json({ error: TEXTS.emailSubmissionFailed }, { status: 500 });
    }
}
