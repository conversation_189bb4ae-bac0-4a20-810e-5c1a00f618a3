import { type NextRequest, NextResponse } from "next/server";

import { verifyPaymentAndUpdateSubscription } from "@/app/actions/subscriptions-actions";

export async function POST(request: NextRequest) {
    try {
        const paymentParams = await request.json();
        const verificationResult = await verifyPaymentAndUpdateSubscription(paymentParams);

        if (verificationResult.success) {
            return NextResponse.json(
                {
                    success: true,
                    message: verificationResult.message,
                    orderId: verificationResult.orderId,
                    transactionId: verificationResult.transactionId,
                    verificationDetails: verificationResult.verificationDetails
                },
                { status: 200 }
            );
        } else {
            return NextResponse.json(
                {
                    success: false,
                    error: verificationResult.error,
                    details: verificationResult.verificationDetails
                },
                { status: 400 }
            );
        }
    } catch (error) {
        console.error("Payment verification API error:", error);
        const errorMessage = error instanceof Error ? error.message : "אירעה שגיאה פנימית באימות התשלום.";
        if (error instanceof SyntaxError) {
            return NextResponse.json({ success: false, error: "שגיאת פורמט בבקשה." }, { status: 400 });
        }
        return NextResponse.json({ success: false, error: errorMessage }, { status: 500 });
    }
}
