import { type NextRequest, NextResponse } from "next/server";

import { validateAndApplyCoupon } from "@/app/actions/subscriptions-actions";
import { getPaymentErrorMessage } from "@/utils/payment-errors";

interface ValidateCouponRequest {
    coupon_code: string;
    total_amount: number;
}

export async function POST(request: NextRequest) {
    try {
        const body: ValidateCouponRequest = await request.json();
        const { coupon_code, total_amount } = body;

        if (!coupon_code || typeof total_amount !== "number" || total_amount < 0) {
            return NextResponse.json({ success: false, error: "Invalid input parameters." }, { status: 400 });
        }

        const result = await validateAndApplyCoupon(coupon_code, total_amount);

        if (!result.success) {
            const friendlyError = getPaymentErrorMessage(result.error);
            return NextResponse.json({ success: false, error: friendlyError }, { status: 400 });
        }

        return NextResponse.json(
            {
                success: true,
                discountValue: result.discountValue,
                couponType: result.couponType,
                discountApplied: result.discountApplied,
                finalAmount: result.finalAmount,
                couponCode: result.couponCode
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Coupon validation API error:", error);
        if (error instanceof SyntaxError) {
            return NextResponse.json({ success: false, error: "Invalid JSON format." }, { status: 400 });
        }

        return NextResponse.json({ success: false, error: getPaymentErrorMessage(undefined) }, { status: 500 });
    }
}
