import { type NextRequest, NextResponse } from "next/server";

import { extractCCode, getPaymentErrorMessage } from "@/utils/payment-errors";

const PAYMENT_GATEWAY_MERCHANT_ID = process.env.PAYMENT_GATEWAY_MERCHANT_ID;
const PAYMENT_GATEWAY_API_KEY = process.env.PAYMENT_GATEWAY_API_KEY;
const PAYMENT_GATEWAY_PASSP = process.env.PAYMENT_GATEWAY_PASSP;
const PAYMENT_GATEWAY_SIGN_URL = process.env.PAYMENT_GATEWAY_SIGN_URL;

interface GenerateCheckoutUrlRequest {
    total_amount: number;
    order_id: string;
    coupon_code?: string;
    planName: string;
    customer_info: {
        name?: string;
        email?: string;
        phone?: string;
        lastName?: string;
        city?: string;
        street?: string;
        zip?: string;
    };
}

export async function POST(request: NextRequest) {
    const missingVars: string[] = [];
    if (!PAYMENT_GATEWAY_MERCHANT_ID) missingVars.push("PAYMENT_GATEWAY_MERCHANT_ID");
    if (!PAYMENT_GATEWAY_API_KEY) missingVars.push("PAYMENT_GATEWAY_API_KEY");
    if (!PAYMENT_GATEWAY_PASSP) missingVars.push("PAYMENT_GATEWAY_PASSP");
    if (!PAYMENT_GATEWAY_SIGN_URL) missingVars.push("PAYMENT_GATEWAY_SIGN_URL");

    if (missingVars.length > 0) {
        const errorMessage = `Missing Payment Gateway environment variables: ${missingVars.join(", ")}.`;
        console.error(errorMessage);
        return NextResponse.json({ success: false, error: "שגיאת תצורה בשער התשלומים." }, { status: 500 });
    }

    try {
        const body: GenerateCheckoutUrlRequest = await request.json();
        const { total_amount, order_id, coupon_code, planName, customer_info } = body;

        if (!order_id || !planName || typeof total_amount !== "number" || total_amount <= 0) {
            return NextResponse.json(
                { success: false, error: "פרמטרים לא חוקיים (חסר order_id, planName, או סכום לא חוקי)." },
                { status: 400 }
            );
        }

        const gatewayOrderId = coupon_code ? `${order_id}_COUPON_${coupon_code}` : order_id;

        const signParams: Record<string, string | number | boolean> = {
            action: "APISign",
            What: "SIGN",
            Masof: PAYMENT_GATEWAY_MERCHANT_ID!,
            KEY: PAYMENT_GATEWAY_API_KEY!,
            PassP: PAYMENT_GATEWAY_PASSP!,
            Amount: total_amount.toFixed(2),
            Order: gatewayOrderId,
            ClientName: customer_info.name || "",
            ClientLName: customer_info.lastName || "",
            email: customer_info.email || "",
            UserId: "",
            phone: customer_info.phone || "",
            city: customer_info.city || "",
            street: customer_info.street || "",
            zip: customer_info.zip || "",
            Info: planName,
            UTF8: true,
            UTF8out: true,
            Sign: "True",
            MoreData: true,
            SendHesh: true,
            sendemail: true,
            Pritim: true,
            heshDesc: `[0~${planName}~1~${total_amount.toFixed(2)}]`,
            tmp: 5
        };

        const signUrl = new URL(PAYMENT_GATEWAY_SIGN_URL!);
        Object.entries(signParams).forEach(([key, value]) => {
            signUrl.searchParams.append(key, String(value));
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        let signResponse: Response;
        let responseText: string;

        try {
            signResponse = await fetch(signUrl.toString(), {
                method: "GET",
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            responseText = await signResponse.text();
        } catch (fetchError) {
            clearTimeout(timeoutId);

            if (fetchError instanceof Error && fetchError.name === "AbortError") {
                console.error("Payment gateway request timed out after 10 seconds");
                throw new Error("פג זמן בקשה לשירות התשלומים. אנא נסה שוב מאוחר יותר.");
            }

            console.error("Payment gateway fetch error:", fetchError);
            throw new Error("נכשל בהתחברות לשירות התשלומים. אנא נסה שוב מאוחר יותר.");
        }

        if (!signResponse!.ok || !responseText || !responseText.includes("signature=")) {
            const errorCode = extractCCode(responseText);
            const friendlyError = getPaymentErrorMessage(errorCode);
            console.error(
                `Invalid signing response. CCode: ${errorCode || "N/A"}. Gateway response: ${responseText.substring(
                    0,
                    500
                )}`
            );

            throw new Error(friendlyError);
        }

        const finalPaymentUrl = `${PAYMENT_GATEWAY_SIGN_URL!}?${responseText}`;

        return NextResponse.json({ success: true, signedUrl: finalPaymentUrl }, { status: 200 });
    } catch (error) {
        console.error("Generate checkout URL API error:", error);

        const errorMessage = error instanceof Error ? error.message : "אירעה שגיאת שרת פנימית.";

        if (error instanceof SyntaxError) {
            return NextResponse.json({ success: false, error: "פורמט JSON לא חוקי בבקשה." }, { status: 400 });
        }
        return NextResponse.json({ success: false, error: errorMessage }, { status: 500 });
    }
}
