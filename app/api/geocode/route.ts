import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams;
        const query = searchParams.get("query");
        const language = searchParams.get("language") || "en";
        const country = searchParams.get("country");
        const types = searchParams.get("types");
        const limit = searchParams.get("limit") || "5";

        if (!query) {
            return NextResponse.json({ error: "Query parameter is required" }, { status: 400 });
        }

        const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
        if (!mapboxToken) {
            console.error("Mapbox access token is not defined in environment variables");
            return NextResponse.json({ error: "Geocoding service configuration error" }, { status: 500 });
        }

        const url = new URL("https://api.mapbox.com/geocoding/v5/mapbox.places/" + encodeURIComponent(query) + ".json");

        url.searchParams.append("access_token", mapboxToken);
        url.searchParams.append("language", language);
        url.searchParams.append("limit", limit);

        if (country) {
            url.searchParams.append("country", country);
        }

        if (types) {
            url.searchParams.append("types", types);
        }

        url.searchParams.append("autocomplete", "true");

        const response = await fetch(url.toString());

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Mapbox API error: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Mapbox API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        return NextResponse.json(data);
    } catch (error) {
        console.error("Error in geocode API:", error);
        return NextResponse.json({ error: "Failed to fetch geocoding results" }, { status: 500 });
    }
}
