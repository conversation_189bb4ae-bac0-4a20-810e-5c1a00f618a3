@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add smooth scrolling behavior for the entire page */
html {
    scroll-behavior: smooth;
}

/* Add smooth anchor transitions */
@layer base {
    section[id] {
        scroll-margin-top: 80px;
    }
    :root {
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
    .dark {
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@keyframes blob {
    0% {
        transform: translate(0, 0) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0, 0) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

@layer base {
    :root {
        --background: 210 40% 98%;
        --foreground: 222 47% 11%;
        --card: 0 0% 100%;
        --card-foreground: 222 47% 11%;
        --popover: 0 0% 100%;
        --popover-foreground: 222 47% 11%;
        --primary: 222 85% 47%;
        --primary-foreground: 0 0% 100%;
        --secondary: 200 85% 92%;
        --secondary-foreground: 222 47% 11%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215 16% 40%;
        --accent: 200 85% 92%;
        --accent-foreground: 222 47% 11%;
        --destructive: 0 84% 55%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 85%;
        --input: 214.3 31.8% 85%;
        --ring: 221 83% 53%;
        --radius: 1rem;

        --font-sans: var(--font-rubik);

        --foreground-rgb: 0, 0, 0;
        --background-start-rgb: 214, 219, 220;
        --background-end-rgb: 255, 255, 255;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
        font-family: var(--font-sans);
        font-weight: 300;
        font-feature-settings:
            "rlig" 1,
            "calt" 1;
        background: linear-gradient(135deg, hsl(var(--background)), hsl(var(--secondary)), hsl(var(--accent)));
        min-height: 100vh;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-weight: 400;
    }
    /* Always ensure placeholders are left-to-right */
    ::placeholder {
        direction: ltr;
        text-align: left;
    }
    /* Hide number input spinners */
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    input[type="number"] {
        -moz-appearance: textfield;
    }
}

@layer utilities {
    /* RTL support */
    .rtl {
        direction: rtl;
    }

    /* Shadow utilities */
    .shadow-subtle {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Focus styles */
    .focus-ring {
        @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-2;
    }

    /* Error message */
    .error-message {
        color: hsl(var(--destructive));
        background-color: hsl(var(--destructive) / 0.1);
        border: 1px solid hsl(var(--destructive) / 0.2);
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-size: 0.875rem;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .error-message::before {
        content: "⚠️";
        flex-shrink: 0;
    }
}

/* React Phone Number Input Resets */
.PhoneInputCountryIcon--border {
    box-shadow: none !important;
    background: none !important;
}

/* Force the dropdown to be on the left side, even in RTL layouts */
.rtl .PhoneInput {
    display: flex !important;
    flex-direction: row !important;
}

/* RTL support for country dropdown */
.rtl .cmd-input {
    text-align: right;
}

/* Fix RTL placeholder for command input */
.rtl .cmd-empty {
    text-align: right;
}

/* RTL command input placeholder */
.rtl [cmdk-input]::placeholder {
    direction: rtl;
    text-align: right;
}

/* Prevent page transition flash */
html {
    background-color: hsl(var(--background));
}

body,
#__next,
.layout-wrapper {
    background-color: hsl(var(--background));
    min-height: 100vh;
}

/* Ensure content always fills the screen */
.content-wrapper {
    min-height: 100vh;
    position: relative;
}

/* Prevent horizontal scrolling */
body {
    overflow-x: hidden;
}
