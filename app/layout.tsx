import "./globals.css";

import { heIL } from "@clerk/localizations";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { PostHogProvider } from "@/components/PostHogProvider";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import { StagewiseProvider } from "@/lib/stage-wise";

const rubik = Rubik({
    subsets: ["latin", "hebrew"],
    weight: ["400", "500", "600", "700"],
    display: "swap",
    variable: "--font-rubik"
});

export const metadata = {
    title: "Milgapo",
    description: "Search and aggregate scholarships"
};

export default function RootLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                <link rel="icon" href="/favicons/favicon.svg" type="image/svg+xml" />
                <link rel="apple-touch-icon" href="/favicons/apple-touch-icon.png" />
                <link rel="manifest" href="/favicons/site.webmanifest" />
            </head>
            <body className={`${rubik.className}`} suppressHydrationWarning>
                <StagewiseProvider />
                <PostHogProvider>
                    <ClerkProvider
                        localization={{
                            ...heIL,
                            formFieldInputPlaceholder__emailAddress: 'הזן כתובת דוא"ל',
                            formFieldLabel__emailAddress: 'כתובת דוא"ל'
                        }}
                        signInFallbackRedirectUrl="/dashboard"
                        signUpFallbackRedirectUrl="/onboarding"
                    >
                        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
                            {children}

                            <SpeedInsights />
                            <Analytics />
                            <Toaster richColors dir="rtl" />
                        </ThemeProvider>
                    </ClerkProvider>
                </PostHogProvider>
            </body>
        </html>
    );
}
