"use client";

import { ArrowR<PERSON> } from "lucide-react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import animationData from "@/public/errors/404-animation.json";

const TEXTS = {
    title: "העמוד שחיפשת לא נמצא",
    description: "אולי לא מצאנו את העמוד שחיפשת אבל אנחנו יכולים לעזור לך למצוא מלגה",
    homeButton: "חזרה לדף הבית",
    scholarshipsButton: "חיפוש מלגות",
    backButton: "חזרה"
};

function BackButton() {
    const router = useRouter();

    return (
        <Button
            type="button"
            onClick={() => router.back()}
            variant="ghost"
            size="icon"
            className="h-8 w-8 sm:h-7 sm:w-7 rounded-full hover:bg-secondary/80"
            aria-label={TEXTS.backButton}
        >
            <ArrowRight className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
        </Button>
    );
}

function AnimationSkeleton() {
    return <Skeleton className="h-48 w-full sm:h-56 md:h-64 rounded-xl" />;
}

const Lottie = dynamic(() => import("lottie-react"), {
    ssr: false,
    loading: () => <AnimationSkeleton />
});

export default function NotFound() {
    return (
        <div dir="rtl" className="container flex min-h-[calc(100vh-4rem)] items-center justify-center py-8">
            <Card className="w-full max-w-2xl relative">
                <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
                    <BackButton />
                </div>
                <CardHeader className="pb-0">
                    <div className="w-full max-w-md mx-auto">
                        <Lottie animationData={animationData} loop={true} className="h-48 w-full sm:h-56 md:h-64" />
                    </div>
                </CardHeader>
                <CardContent className="space-y-4 text-center pt-6">
                    <h1 className="text-3xl sm:text-4xl scroll-m-20 tracking-tight">{TEXTS.title}</h1>
                    <p className="text-base text-muted-foreground sm:text-lg leading-7 [&:not(:first-child)]:mt-6">
                        {TEXTS.description}
                    </p>
                </CardContent>
            </Card>
        </div>
    );
}
