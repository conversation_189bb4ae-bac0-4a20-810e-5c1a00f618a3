"use client";

import { Menu } from "lucide-react";

import { AppSidebar } from "@/components/layout/sidebar/app-sidebar";
import { SiteLogo } from "@/components/layout/site-logo";
import { Banner } from "@/components/ui/banner";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { sidebarData } from "@/config/admin";
import { StagewiseProvider } from "@/lib/stage-wise";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const adminNavGroups = [];
    if (sidebarData.generalManagement && sidebarData.generalManagement.items.length > 0) {
        adminNavGroups.push(sidebarData.generalManagement);
    }
    if (sidebarData.scholarshipManagement && sidebarData.scholarshipManagement.items.length > 0) {
        adminNavGroups.push(sidebarData.scholarshipManagement);
    }
    if (sidebarData.contentManagement && sidebarData.contentManagement.items.length > 0) {
        adminNavGroups.push(sidebarData.contentManagement);
    }

    return (
        <SidebarProvider>
            <StagewiseProvider />
            <div className="fixed inset-0 flex flex-row-reverse">
                <AppSidebar
                    navMain={sidebarData.navMain}
                    navGroups={adminNavGroups}
                    navSecondary={sidebarData.navSecondary}
                />
                <main className="flex-1 overflow-auto bg-gradient-to-br from-background to-background/80">
                    <Banner />
                    <div className="sticky top-0 z-10 flex items-center justify-between p-4 md:hidden bg-background/80 backdrop-blur-sm border-b">
                        <div className="flex-1"></div>
                        <div className="absolute inset-x-0 flex justify-center items-center pointer-events-none">
                            <div className="pointer-events-auto">
                                <SiteLogo href="/admin" size="lg" />
                            </div>
                        </div>
                        <SidebarTrigger className="z-20 relative">
                            <Menu className="h-5 w-5" />
                        </SidebarTrigger>
                    </div>
                    <div className="pb-safe">{children}</div>
                </main>
            </div>
        </SidebarProvider>
    );
}
