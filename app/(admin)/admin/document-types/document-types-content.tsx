"use client";

import { FileText } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { deleteDocumentType } from "@/app/actions/document-type-actions";
import { type DocumentTypeWithUrls as DocumentType } from "@/app/actions/document-type-actions";
import { type DocumentTypeGroupWithCount } from "@/app/actions/document-type-group-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useDocumentTypeGroups } from "@/hooks/use-document-type-groups";
import { useDocumentTypes } from "@/hooks/use-document-types";
import { TEXTS as AdminDocumentTypesTexts } from "@/lib/document-type-constants";
import { ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS } from "@/lib/document-type-group-constants";
import { getSupabaseClient } from "@/utils/supabase/client";

const TEXTS = { ...AdminDocumentTypesTexts, ...ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS };

const MIME_TYPE_DISPLAY: Record<string, string> = {
    "application/pdf": "PDF",
    "image/jpeg": "JPEG",
    "image/png": "PNG",
    "image/webp": "WEBP"
};

const BADGE_COLORS: Record<string, string> = {
    "application/pdf": "bg-red-100 text-red-800 hover:bg-red-200",
    "image/jpeg": "bg-blue-100 text-blue-800 hover:bg-blue-200",
    "image/png": "bg-green-100 text-green-800 hover:bg-green-200",
    "image/webp": "bg-purple-100 text-purple-800 hover:bg-purple-200"
};

type TabValue = "document-types" | "groups";

export function DocumentTypesContent() {
    const searchParams = useSearchParams();
    const tabParam = searchParams.get("tab");
    const [activeTab, setActiveTab] = useState<TabValue>(tabParam === "groups" ? "groups" : "document-types");

    useEffect(() => {
        if (tabParam === "groups") {
            setActiveTab("groups");
        } else if (tabParam === "document-types") {
            setActiveTab("document-types");
        }
    }, [tabParam]);

    const {
        items: documentTypes,
        loading: loadingDocumentTypes,
        error: errorDocumentTypes,
        refetch: refetchDocumentTypes
    } = useDocumentTypes();

    const {
        items: groups,
        loading: loadingGroups,
        error: errorGroups,
        refetch: refetchGroups
    } = useDocumentTypeGroups();

    const handleDeleteDocumentType = async (id: string) => {
        try {
            const result = await deleteDocumentType(id);
            if (result.success) {
                await refetchDocumentTypes();
            }
            return result;
        } catch (err) {
            console.error("Error deleting document type:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        }
    };

    const handleDeleteGroup = async (id: string) => {
        try {
            const supabase = getSupabaseClient();
            const { error } = await supabase.from("groups_document_type").delete().eq("id", id);
            if (error) {
                throw error;
            }
            await refetchGroups();
            await refetchDocumentTypes();
            toast.success(TEXTS.deleteSuccessGroup);
            return { success: true };
        } catch (err) {
            console.error("Error deleting document type group:", err);
            toast.error(TEXTS.deleteErrorGroup);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteErrorGroup };
        }
    };

    const documentTypeColumns: AdminTableColumn<DocumentType>[] = [
        {
            key: "name",
            label: TEXTS.name,
            render: (item) => item.name
        },
        {
            key: "description",
            label: TEXTS.description,
            render: (item) => item.description || "-"
        },
        {
            key: "example_file",
            label: "קובץ לדוגמה",
            render: (item) => {
                if (item.example_file_url) {
                    return (
                        <a
                            href={item.example_file_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800"
                            title="צפה בקובץ"
                        >
                            <FileText className="w-5 h-5" />
                        </a>
                    );
                }
                return "-";
            }
        },
        {
            key: "allowed_mime_types",
            label: TEXTS.allowedMimeTypes,
            render: (item) => (
                <div className="flex flex-wrap gap-1.5 max-w-xs">
                    {Array.isArray(item.allowed_mime_types) && item.allowed_mime_types.length > 0 ? (
                        item.allowed_mime_types.map((mimeType, index) => {
                            const mimeTypeString =
                                typeof mimeType === "object" && mimeType !== null
                                    ? "id" in mimeType
                                        ? String((mimeType as { id: string }).id)
                                        : "value" in mimeType
                                          ? String((mimeType as { value: string }).value)
                                          : String(mimeType)
                                    : String(mimeType);
                            return (
                                <Badge
                                    key={index}
                                    className={BADGE_COLORS[mimeTypeString] || "bg-gray-100 text-gray-800"}
                                >
                                    {MIME_TYPE_DISPLAY[mimeTypeString] || mimeTypeString}
                                </Badge>
                            );
                        })
                    ) : (
                        <span>-</span>
                    )}
                </div>
            )
        }
    ];

    const groupColumns: AdminTableColumn<DocumentTypeGroupWithCount>[] = [
        {
            key: "name",
            label: TEXTS.groupName,
            render: (item) => item.name
        },
        {
            key: "document_types_count",
            label: TEXTS.documentTypesCount,
            render: (item) => item.document_types_count ?? "-"
        }
    ];

    return (
        <div className="container mx-auto max-w-7xl">
            <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as TabValue)}
                className="w-full"
                dir="rtl"
            >
                <TabsList className="w-full grid grid-cols-2 h-12 bg-muted my-2 mt-8 px-1 rounded-lg">
                    <TabsTrigger
                        value="document-types"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.documentTypesTabTitle}
                    </TabsTrigger>
                    <TabsTrigger
                        value="groups"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.groupsTabTitle}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="document-types" className="mt-6">
                    <AdminTable
                        title={TEXTS.pageTitle}
                        items={documentTypes}
                        columns={documentTypeColumns}
                        loading={loadingDocumentTypes}
                        error={errorDocumentTypes ? new Error(errorDocumentTypes) : null}
                        onDelete={handleDeleteDocumentType}
                        onRefetch={refetchDocumentTypes}
                        addButtonLabel={TEXTS.newDocumentType}
                        addButtonHref="/admin/document-types/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitle}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescription}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noDocumentTypes}
                        loadingText={TEXTS.loading}
                        errorPrefix={TEXTS.errorPrefix}
                        deleteErrorText={TEXTS.deleteError}
                        deleteSuccessText={TEXTS.deleteSuccess}
                        deletingText={TEXTS.deletingDocumentType}
                    />
                </TabsContent>

                <TabsContent value="groups" className="mt-6">
                    <AdminTable
                        title={TEXTS.groupsPageTitle}
                        items={groups}
                        columns={groupColumns}
                        loading={loadingGroups}
                        error={errorGroups ? new Error(errorGroups) : null}
                        onDelete={handleDeleteGroup}
                        onRefetch={refetchGroups}
                        addButtonLabel={TEXTS.newGroup}
                        addButtonHref="/admin/document-types/groups/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitleGroup}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescriptionGroup}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noGroups}
                        loadingText={TEXTS.loadingGroups}
                        errorPrefix={TEXTS.errorPrefixGroups}
                        deleteErrorText={TEXTS.deleteErrorGroup}
                        deleteSuccessText={TEXTS.deleteSuccessGroup}
                        deletingText={TEXTS.deletingGroup}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
}
