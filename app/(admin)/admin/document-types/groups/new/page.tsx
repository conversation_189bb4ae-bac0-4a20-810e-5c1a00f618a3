"use client";

import { DocumentTypeGroupForm } from "@/components/forms/document-type-group-form";
import { ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS } from "@/lib/document-type-group-constants";

export default function NewDocumentTypeGroupPage() {
    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">{ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS.newGroup}</h1>
                <DocumentTypeGroupForm />
            </div>
        </div>
    );
}
