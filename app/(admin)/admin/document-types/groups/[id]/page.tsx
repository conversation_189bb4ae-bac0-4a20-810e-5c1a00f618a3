"use client";

import { useParams } from "next/navigation";

import { DocumentTypeGroupForm } from "@/components/forms/document-type-group-form";

const TEXTS = {
    editPageTitle: "עריכת קבוצת מסמכים"
};

export default function EditDocumentTypeGroupPage() {
    const params = useParams();
    const idParam = Array.isArray(params.id) ? params.id[0] : params.id;

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">{TEXTS.editPageTitle}</h1>
                <DocumentTypeGroupForm groupId={idParam} />
            </div>
        </div>
    );
}
