"use client";

import { useParams } from "next/navigation";
import React from "react";

import { DocumentTypeForm } from "@/components/forms/document-type-form";
import { TEXTS } from "@/lib/document-type-constants";

export default function EditDocumentTypePage() {
    const params = useParams();
    const documentTypeId = params.id as string;

    if (!documentTypeId) {
        return <div>{TEXTS.errorNotFound}</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">{TEXTS.editTitle}</h1>
                <DocumentTypeForm documentTypeId={documentTypeId} />
            </div>
        </div>
    );
}
