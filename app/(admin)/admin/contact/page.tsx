"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useState } from "react";

import { deleteContact } from "@/app/actions/contact-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { Contact, useContacts } from "@/hooks/use-contacts";
import { ADMIN_CONTACTS_TEXTS as TEXTS } from "@/lib/contact-constants";

export default function ContactsPage() {
    const { items: contacts, loading, error, refetch } = useContacts();
    const [, setIsDeleting] = useState(false);

    const handleDelete = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteContact(id);
            return result;
        } catch (err) {
            console.error("Error deleting contact:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        } finally {
            setIsDeleting(false);
        }
    };

    const columns: AdminTableColumn<Contact>[] = [
        {
            key: "email",
            label: TEXTS.email,
            render: (item) => item.email
        },
        {
            key: "created_at",
            label: TEXTS.createdAt,
            render: (item) => format(new Date(item.created_at), "dd/MM/yyyy HH:mm", { locale: he })
        }
    ];

    return (
        <AdminTable
            title={TEXTS.pageTitle}
            items={contacts}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={refetch}
            addButtonLabel={TEXTS.newContact}
            addButtonHref="/admin/contact/new"
            deleteConfirmTitle={TEXTS.deleteConfirmTitle}
            deleteConfirmDescription={TEXTS.deleteConfirmDescription}
            confirmDeleteText={TEXTS.confirmDelete}
            noItemsText={TEXTS.noContacts}
            loadingText={TEXTS.loading}
            errorPrefix={TEXTS.errorPrefix}
            deleteErrorText={TEXTS.deleteError}
            deleteSuccessText={TEXTS.deleteSuccess}
            deletingText={TEXTS.deletingContact}
        />
    );
}
