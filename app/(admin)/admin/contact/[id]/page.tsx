"use client";

import { useParams } from "next/navigation";

import { ContactForm } from "@/components/forms/contact-form";
import { TEXTS } from "@/lib/contact-constants";

export default function EditContactPage() {
    const params = useParams();
    const contactId = params.id as string;

    if (!contactId) {
        return <p className="text-center text-red-500">{TEXTS.editErrorNotFound}</p>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6" dir="rtl">
                    {TEXTS.editPageTitle}
                </h1>
                <ContactForm contactId={contactId} />
            </div>
        </div>
    );
}
