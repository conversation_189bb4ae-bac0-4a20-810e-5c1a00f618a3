"use client";

import { useParams } from "next/navigation";

import { CollaborationForm } from "@/components/forms/collaboration-form";

const TEXTS = {
    pageTitle: "עריכת שיתוף פעולה"
};

export default function EditCollaborationPage() {
    const params = useParams();
    const collaborationId = params.id as string;

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-2xl font-semibold mb-6 text-right" dir="rtl">
                    {TEXTS.pageTitle}
                </h1>
                <CollaborationForm collaborationId={collaborationId} />
            </div>
        </div>
    );
}
