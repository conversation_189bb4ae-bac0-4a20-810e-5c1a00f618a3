"use client";

import { useState } from "react";

import { deleteCollaboration } from "@/app/actions/collaboration-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { useCollaborations } from "@/hooks/use-collaborations";
import { ADMIN_COLLABORATIONS_TEXTS as TEXTS } from "@/lib/collaboration-constants";
import { type Tables } from "@/types/database.types";

const AUTH_TYPES = {
    bearer_token: TEXTS.bearerToken,
    none: TEXTS.noAuth
};

export default function CollaborationsPage() {
    const { items: collaborations, loading, error, refetch } = useCollaborations();
    const [, setIsDeleting] = useState(false);

    const handleDelete = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteCollaboration(id);
            if (result.success) {
                await refetch();
            }
            return result;
        } catch (err) {
            console.error("Error deleting collaboration:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        } finally {
            setIsDeleting(false);
        }
    };

    const columns: AdminTableColumn<Tables<"collaborations">>[] = [
        {
            key: "name",
            label: TEXTS.name,
            render: (item: Tables<"collaborations">) => item.name
        },
        {
            key: "description",
            label: TEXTS.description,
            render: (item: Tables<"collaborations">) => item.description || "-"
        },
        {
            key: "api_endpoint",
            label: TEXTS.apiEndpoint,
            render: (item: Tables<"collaborations">) => item.api_endpoint
        },
        {
            key: "auth_type",
            label: TEXTS.authType,
            render: (item: Tables<"collaborations">) =>
                AUTH_TYPES[item.auth_type as keyof typeof AUTH_TYPES] || item.auth_type
        }
    ];

    return (
        <AdminTable
            title={TEXTS.pageTitle}
            items={collaborations}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={refetch}
            addButtonLabel={TEXTS.newCollaboration}
            addButtonHref="/admin/collaborations/new"
            deleteConfirmTitle={TEXTS.deleteConfirmTitle}
            deleteConfirmDescription={TEXTS.deleteConfirmDescription}
            confirmDeleteText={TEXTS.confirmDelete}
            noItemsText={TEXTS.noCollaborations}
            loadingText={TEXTS.loading}
            errorPrefix={TEXTS.errorPrefix}
            deleteErrorText={TEXTS.deleteError}
            deleteSuccessText={TEXTS.deleteSuccess}
            deletingText={TEXTS.deletingCollaboration}
        />
    );
}
