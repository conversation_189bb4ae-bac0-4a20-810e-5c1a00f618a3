"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { deleteCoupon } from "@/app/actions/coupon-actions";
import { deleteCouponGroup } from "@/app/actions/coupon-group-actions";
import { CopyToClipboard } from "@/components/common/copy-to-clipboard";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type CouponGroupWithCount as CouponGroup, useCouponGroups } from "@/hooks/use-coupon-groups";
import { useCoupons } from "@/hooks/use-coupons";
import { ADMIN_COUPONS_TEXTS, type Coupon } from "@/lib/coupon-constants";
import { ADMIN_COUPON_GROUPS_TEXTS } from "@/lib/coupon-group-constants";

const TEXTS = { ...ADMIN_COUPONS_TEXTS, ...ADMIN_COUPON_GROUPS_TEXTS };

type TabValue = "coupons" | "groups";

export function CouponsContent() {
    const searchParams = useSearchParams();
    const tabParam = searchParams.get("tab");
    const [activeTab, setActiveTab] = useState<TabValue>(tabParam === "groups" ? "groups" : "coupons");
    const [, setIsDeleting] = useState(false);

    useEffect(() => {
        if (tabParam === "groups") {
            setActiveTab("groups");
        } else if (tabParam === "coupons") {
            setActiveTab("coupons");
        }
    }, [tabParam]);

    const { items: coupons, loading: couponsLoading, error: couponsError, refetch: refetchCoupons } = useCoupons();

    const { items: groups, loading: groupsLoading, error: groupsError, refetch: refetchGroups } = useCouponGroups();

    const handleDeleteCoupon = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteCoupon(id);
            return result;
        } catch (err) {
            console.error("Error deleting coupon:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteGroup = async (id: string) => {
        try {
            const res = await deleteCouponGroup(id);
            if (res?.success) {
                await refetchGroups();
            }
            return res;
        } catch (err) {
            console.error("Error deleting coupon group:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteErrorGroup };
        }
    };

    const couponColumns: AdminTableColumn<Coupon>[] = [
        {
            key: "group_name",
            label: TEXTS.group,
            render: (item) => item.group_name || TEXTS.noGroup
        },
        {
            key: "coupon_code",
            label: TEXTS.couponCode,
            render: (item) => (
                <div className="flex items-center gap-2">
                    <CopyToClipboard text={item.coupon_code} onSuccessMessage={TEXTS.copySuccess} />
                    <span>{item.coupon_code}</span>
                </div>
            )
        },
        {
            key: "coupon_type",
            label: TEXTS.type,
            render: (item) => (item.coupon_type === "fixed_amount" ? TEXTS.fixedAmount : TEXTS.percentage)
        },
        {
            key: "discount_value",
            label: TEXTS.value,
            render: (item) =>
                item.coupon_type === "fixed_amount" ? `${item.discount_value}₪` : `${item.discount_value}%`
        },
        {
            key: "expiration_date",
            label: TEXTS.expirationDate,
            render: (item) =>
                item.expiration_date
                    ? format(new Date(item.expiration_date), "dd/MM/yyyy", { locale: he })
                    : TEXTS.noExpiration
        },
        {
            key: "used_count",
            label: TEXTS.uses,
            render: (item) => (
                <span dir="ltr">
                    {item.used_count} / {item.usage_limit == null || item.usage_limit === 0 ? "∞" : item.usage_limit}
                </span>
            )
        }
    ];

    const groupColumns: AdminTableColumn<CouponGroup>[] = [
        {
            key: "name",
            label: TEXTS.name,
            render: (item) => item.name
        },
        {
            key: "description",
            label: TEXTS.description,
            render: (item) => item.description || "-"
        },
        {
            key: "coupons_count",
            label: TEXTS.couponsCount,
            render: (item) => item.coupons_count
        }
    ];

    return (
        <div className="container mx-auto max-w-7xl">
            <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as TabValue)}
                className="w-full"
                dir="rtl"
            >
                <TabsList className="w-full grid grid-cols-2 h-12 bg-muted my-2 mt-8 px-1 rounded-lg">
                    <TabsTrigger
                        value="coupons"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.couponsTabTitle}
                    </TabsTrigger>
                    <TabsTrigger
                        value="groups"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.groupsTabTitle}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="coupons" className="mt-6">
                    <AdminTable
                        title={TEXTS.pageTitle}
                        items={coupons}
                        columns={couponColumns}
                        loading={couponsLoading}
                        error={couponsError}
                        onDelete={handleDeleteCoupon}
                        onRefetch={refetchCoupons}
                        addButtonLabel={TEXTS.newCoupon}
                        addButtonHref="/admin/coupons/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitle}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescription}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noCoupons}
                        loadingText={TEXTS.loading}
                        errorPrefix={TEXTS.errorPrefix}
                        deleteErrorText={TEXTS.deleteError}
                        deleteSuccessText={TEXTS.deleteSuccess}
                        deletingText={TEXTS.deletingCoupon}
                    />
                </TabsContent>

                <TabsContent value="groups" className="mt-6">
                    <AdminTable
                        title={TEXTS.groupsPageTitle}
                        items={groups}
                        columns={groupColumns}
                        loading={groupsLoading}
                        error={groupsError ? new Error(groupsError) : null}
                        onDelete={handleDeleteGroup}
                        onRefetch={refetchGroups}
                        addButtonLabel={TEXTS.newGroup}
                        addButtonHref="/admin/coupons/groups/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitleGroup}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescriptionGroup}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noGroups}
                        loadingText={TEXTS.loadingGroups}
                        errorPrefix={TEXTS.errorPrefixGroups}
                        deleteErrorText={TEXTS.deleteErrorGroup}
                        deleteSuccessText={TEXTS.deleteSuccessGroup}
                        deletingText={TEXTS.deletingGroup}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
}
