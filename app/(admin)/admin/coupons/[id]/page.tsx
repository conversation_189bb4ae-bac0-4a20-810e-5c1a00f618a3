"use client";

import { useParams } from "next/navigation";

import { CouponForm } from "@/components/forms/coupon-form";
import { TEXTS } from "@/lib/coupon-constants";

export default function EditCouponPage() {
    const params = useParams();
    const couponId = params.id as string;

    if (!couponId) {
        return <p className="text-center text-red-500">{TEXTS.editErrorNotFound}</p>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">{TEXTS.formTitleEdit}</h1>
                <CouponForm couponId={couponId} />
            </div>
        </div>
    );
}
