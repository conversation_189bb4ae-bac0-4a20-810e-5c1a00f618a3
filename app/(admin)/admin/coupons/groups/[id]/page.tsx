"use client";

import { useParams } from "next/navigation";
import React from "react";

import { CouponGroupForm } from "@/components/forms/coupon-group-form";
import { TEXTS } from "@/lib/coupon-group-constants";

export default function EditCouponGroupPage() {
    const params = useParams();
    const groupId = params.id as string;

    if (!groupId) {
        return <div>{TEXTS.couponGroupNotFound}</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">{TEXTS.editPageTitle}</h1>
                <CouponGroupForm groupId={groupId} />
            </div>
        </div>
    );
}
