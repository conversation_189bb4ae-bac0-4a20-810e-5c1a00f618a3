"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { deleteQuestion } from "@/app/actions/question-actions";
import { deleteQuestionGroup } from "@/app/actions/question-group-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { QuestionGroup, useQuestionGroups } from "@/hooks/use-question-groups";
import { Question, useQuestions } from "@/hooks/use-questions";
import { ADMIN_QUESTIONS_TEXTS } from "@/lib/question-constants";
import { ADMIN_QUESTION_GROUPS_TEXTS } from "@/lib/question-group-constants";

const TEXTS = { ...ADMIN_QUESTIONS_TEXTS, ...ADMIN_QUESTION_GROUPS_TEXTS };

const QUESTION_TYPES = {
    short_text: "טקסט קצר",
    long_text: "טקסט ארוך",
    single_select: "בחירה יחידה",
    multi_select: "בחירה מרובה",
    number_input: "מספר",
    date_picker: "תאריך",
    address_select: "כתובת",
    bank_select: "פרטי בנק"
};

const SECTIONS = {
    personal_details: "פרטים אישיים",
    data_entry: "הזנת נתונים",
    specific_scholarship: "מלגה ספציפית"
};

type TabValue = "questions" | "groups";

export function QuestionsContent() {
    const searchParams = useSearchParams();
    const tabParam = searchParams.get("tab");
    const [activeTab, setActiveTab] = useState<TabValue>(tabParam === "groups" ? "groups" : "questions");

    useEffect(() => {
        if (tabParam === "groups") {
            setActiveTab("groups");
        } else if (tabParam === "questions") {
            setActiveTab("questions");
        }
    }, [tabParam]);

    const {
        items: questions,
        loading: questionsLoading,
        error: questionsError,
        refetch: refetchQuestions
    } = useQuestions();
    const { items: groups, loading: groupsLoading, error: groupsError, refetch: refetchGroups } = useQuestionGroups();

    const handleDeleteQuestion = async (id: string) => {
        try {
            const result = await deleteQuestion(id);
            if (result.success) {
                await refetchQuestions();
            }
            return result;
        } catch (err) {
            console.error("Error deleting question:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        }
    };

    const handleDeleteGroup = async (id: string) => {
        try {
            const result = await deleteQuestionGroup(id);
            if (result.success) {
                await refetchGroups();
            }
            return result;
        } catch (err) {
            console.error("Error deleting question group:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteErrorGroup };
        }
    };

    const questionColumns: AdminTableColumn<Question>[] = [
        {
            key: "metadata_label",
            label: TEXTS.title,
            render: (item: Question) => item.metadata?.label || "ללא כותרת"
        },
        {
            key: "type",
            label: TEXTS.type,
            render: (item: Question) => QUESTION_TYPES[item.type as keyof typeof QUESTION_TYPES] || item.type
        },
        {
            key: "groups_question",
            label: TEXTS.group,
            render: (item: Question) => item.groups_question?.name || "ללא קבוצה"
        },
        {
            key: "section",
            label: TEXTS.section,
            render: (item: Question) => SECTIONS[item.section] || item.section
        },
        {
            key: "metadata_required",
            label: TEXTS.required,
            render: (item: Question) => (item.metadata?.required ? "כן" : "לא")
        }
    ];

    const groupColumns: AdminTableColumn<QuestionGroup>[] = [
        {
            key: "name",
            label: TEXTS.name,
            render: (item) => item.name
        },
        {
            key: "questions_count",
            label: TEXTS.questionsCount,
            render: (item) => item.questions_count
        }
    ];

    return (
        <div className="container mx-auto max-w-7xl">
            <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as TabValue)}
                className="w-full"
                dir="rtl"
            >
                <TabsList className="w-full grid grid-cols-2 h-12 bg-muted my-2 mt-8 px-1 rounded-lg">
                    <TabsTrigger
                        value="questions"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.questionsTabTitle}
                    </TabsTrigger>
                    <TabsTrigger
                        value="groups"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.groupsTabTitle}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="questions" className="mt-6">
                    <AdminTable
                        title={TEXTS.pageTitle}
                        items={questions}
                        columns={questionColumns}
                        loading={questionsLoading}
                        error={questionsError}
                        onDelete={handleDeleteQuestion}
                        onRefetch={refetchQuestions}
                        addButtonLabel={TEXTS.newQuestion}
                        addButtonHref="/admin/questions/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitle}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescription}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noQuestions}
                        loadingText={TEXTS.loading}
                        errorPrefix={TEXTS.errorPrefix}
                        deleteErrorText={TEXTS.deleteError}
                        deleteSuccessText={TEXTS.deleteSuccess}
                        deletingText={TEXTS.deletingQuestion}
                    />
                </TabsContent>

                <TabsContent value="groups" className="mt-6">
                    <AdminTable
                        title={TEXTS.groupsPageTitle}
                        items={groups}
                        columns={groupColumns}
                        loading={groupsLoading}
                        error={groupsError}
                        onDelete={handleDeleteGroup}
                        onRefetch={refetchGroups}
                        addButtonLabel={TEXTS.newGroup}
                        addButtonHref="/admin/questions/groups/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitleGroup}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescriptionGroup}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noGroups}
                        loadingText={TEXTS.loadingGroups}
                        errorPrefix={TEXTS.errorPrefixGroups}
                        deleteErrorText={TEXTS.deleteErrorGroup}
                        deleteSuccessText={TEXTS.deleteSuccessGroup}
                        deletingText={TEXTS.deletingGroup}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
}
