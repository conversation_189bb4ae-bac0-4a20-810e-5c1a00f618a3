"use client";

import { useParams } from "next/navigation";
import React from "react";

import { TestimonialForm } from "@/components/forms/testimonial-form";

export default function EditTestimonialPage() {
    const params = useParams();
    const testimonialId = params.id as string;

    if (!testimonialId) {
        return <div>שגיאה: מספר ביקורת לא נמצא.</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">עריכת ביקורת</h1>
                <TestimonialForm testimonialId={testimonialId} />
            </div>
        </div>
    );
}
