"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useEffect, useState } from "react";

import { deleteTestimonial, getAllTestimonials } from "@/app/actions/testimonial-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { TESTIMONIAL_TEXTS, testimonialTypeOptionsMap } from "@/lib/testimonial-constants";
import { type Tables } from "@/types/database.types";

type Testimonial = Tables<"testimonials">;

export default function TestimonialsPage() {
    const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);
    const [, setIsDeleting] = useState(false);

    const fetchTestimonials = async () => {
        try {
            setLoading(true);
            const result = await getAllTestimonials();

            if (!result.success) {
                throw new Error(result.error || "Failed to fetch testimonials");
            }

            setTestimonials(result.data || []);
            setError(null);
        } catch (err) {
            console.error("Error fetching testimonials:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTestimonials();
    }, []);

    const handleDelete = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteTestimonial(id);

            if (!result.success) {
                throw new Error(result.error);
            }

            return { success: true };
        } catch (err) {
            console.error("Error deleting testimonial:", err);
            return {
                success: false,
                error: err instanceof Error ? err.message : TESTIMONIAL_TEXTS.TESTIMONIAL_DELETE_ERROR
            };
        } finally {
            setIsDeleting(false);
        }
    };

    const columns: AdminTableColumn<Testimonial>[] = [
        {
            key: "name",
            label: TESTIMONIAL_TEXTS.nameLabel,
            render: (item) => item.name
        },
        {
            key: "text",
            label: TESTIMONIAL_TEXTS.text,
            render: (item) => item.text
        },
        {
            key: "type",
            label: TESTIMONIAL_TEXTS.type,
            render: (item) => {
                const typeLabel = testimonialTypeOptionsMap[item.type];
                return typeLabel;
            }
        },
        {
            key: "created_at",
            label: TESTIMONIAL_TEXTS.createdAt,
            render: (item) => format(new Date(item.created_at), "dd/MM/yyyy", { locale: he })
        },
        {
            key: "updated_at",
            label: TESTIMONIAL_TEXTS.updatedAt,
            render: (item) => format(new Date(item.updated_at), "dd/MM/yyyy", { locale: he })
        }
    ];

    return (
        <AdminTable
            title={TESTIMONIAL_TEXTS.pageTitle}
            items={testimonials}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={fetchTestimonials}
            addButtonLabel={TESTIMONIAL_TEXTS.newTestimonial}
            addButtonHref="/admin/testimonials/new"
            deleteConfirmTitle={TESTIMONIAL_TEXTS.deleteConfirmTitle}
            deleteConfirmDescription={TESTIMONIAL_TEXTS.deleteConfirmDescription}
            confirmDeleteText={TESTIMONIAL_TEXTS.confirmDelete}
            noItemsText={TESTIMONIAL_TEXTS.noTestimonials}
            loadingText={TESTIMONIAL_TEXTS.loading}
            errorPrefix={TESTIMONIAL_TEXTS.errorPrefix}
            deleteErrorText={TESTIMONIAL_TEXTS.TESTIMONIAL_DELETE_ERROR}
            deleteSuccessText={TESTIMONIAL_TEXTS.deleteSuccess}
            deletingText={TESTIMONIAL_TEXTS.deletingTestimonial}
        />
    );
}
