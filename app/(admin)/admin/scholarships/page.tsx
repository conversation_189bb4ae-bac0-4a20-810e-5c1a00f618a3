"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useEffect, useState } from "react";

import { deleteScholarship } from "@/app/actions/scholarship-actions";
import { deleteScholarshipGroup } from "@/app/actions/scholarship-group-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn, FilterValue } from "@/components/table/types";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { useAdminScholarships } from "@/hooks/use-admin-scholarships";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";
import { type ScholarshipWithGroups } from "@/lib/scholarship-constants";
import { type ScholarshipGroup } from "@/lib/scholarship-group-constants";
import { getSupabaseClient } from "@/utils/supabase/client";

const TEXTS = {
    scholarshipsTabTitle: "מלגות",
    pageTitle: "מלגות",
    newScholarship: "מלגה חדשה",
    loading: "טוען מלגות...",
    errorPrefix: "שגיאה בטעינת המלגות:",
    noScholarships: "לא נמצאו מלגות",
    title: "שם המלגה",
    amount: "סכום",
    startDate: "תאריך פתיחה",
    endDate: "תאריך סגירה",
    group: "קבוצה",
    actions: "פעולות",
    edit: "עריכה",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק מלגה זו? לא ניתן לשחזר פעולה זו.",
    cancel: "ביטול",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת המלגה",
    deleteSuccess: "המלגה נמחקה בהצלחה",
    deletingScholarship: "מוחק מלגה...",
    previous: "הקודם",
    next: "הבא",
    filterTitle: "סנן לפי שם",
    filterAmount: "סנן לפי סכום",
    filterGroup: "סנן לפי קבוצה",

    groupsTabTitle: "קבוצות מלגות",
    groupsPageTitle: "קבוצות מלגות",
    newGroup: "קבוצת מלגות חדשה",
    loadingGroups: "טוען קבוצות מלגות...",
    errorPrefixGroups: "שגיאה בטעינת קבוצות המלגות:",
    noGroups: "לא נמצאו קבוצות מלגות",
    groupTitle: "שם",
    description: "תיאור",
    scholarshipsCount: "מספר מלגות",
    deleteConfirmTitleGroup: "אישור מחיקה",
    deleteConfirmDescriptionGroup: "האם אתה בטוח שברצונך למחוק קבוצת מלגות זו? לא ניתן לשחזר פעולה זו.",
    deleteErrorGroup: "שגיאה במחיקת קבוצת המלגות",
    deleteSuccessGroup: "קבוצת המלגות נמחקה בהצלחה",
    deletingGroup: "מוחק קבוצת מלגות..."
};

type TabValue = "scholarships" | "groups";

export default function ScholarshipsPage() {
    const [activeTab, setActiveTab] = useState<TabValue>("scholarships");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [filters, setFilters] = useState<Record<string, FilterValue>>({});
    const [allGroupOptions, setAllGroupOptions] = useState<{ id: string; label: string }[]>([]);
    const [, setIsDeleting] = useState(false);

    const {
        items: scholarships,
        loading: scholarshipsLoading,
        error: scholarshipsError,
        refetch: refetchScholarships,
        totalItems,
        totalPages
    } = useAdminScholarships({
        page: currentPage,
        pageSize,
        filters
    });

    const {
        items: groups,
        loading: groupsLoading,
        error: groupsError,
        refetch: refetchGroups
    } = useScholarshipGroups();

    useEffect(() => {
        const abort = new AbortController();
        async function fetchAllGroups() {
            try {
                const supabase = getSupabaseClient();
                const { data } = await supabase
                    .from("groups_scholarship")
                    .select("id, title")
                    .order("title", { ascending: true })
                    .abortSignal(abort.signal);

                if (data) {
                    const options = data.map((group) => ({
                        id: String(group.id),
                        label: group.title
                    }));
                    setAllGroupOptions(options);
                }
            } catch (err) {
                if (!(err instanceof Error && err.name === "AbortError")) {
                    console.error("Error fetching scholarship groups:", err);
                }
            }
        }

        fetchAllGroups();
        return () => abort.abort();
    }, []);

    const handleDeleteScholarship = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteScholarship(id);
            if (result.success) {
                await refetchScholarships();
            }
            return result;
        } catch (err) {
            console.error("Error deleting scholarship:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteGroup = async (id: string) => {
        try {
            const result = await deleteScholarshipGroup(id);
            if (result.success) {
                await refetchGroups();
            }
            return result;
        } catch (err) {
            console.error("Error deleting scholarship group:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteErrorGroup };
        }
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setCurrentPage(1);
    };

    const handleFilter = (newFilters: Record<string, FilterValue>) => {
        setFilters(newFilters);
        setCurrentPage(1);
    };

    const scholarshipColumns: AdminTableColumn<ScholarshipWithGroups>[] = [
        {
            key: "title",
            label: TEXTS.title,
            render: (item) => (
                <div className="flex items-center gap-2">
                    <span>{item.title}</span>
                    {item.internal_notes && <TooltipIcon text={item.internal_notes} />}
                </div>
            ),
            width: "40%",
            filterable: true,
            filterType: "text",
            filterPlaceholder: TEXTS.filterTitle
        },
        {
            key: "group",
            label: TEXTS.group,
            render: (item) => {
                const group = item.link_scholarship_to_scholarship_groups?.[0]?.groups_scholarship;
                return group?.title || "ללא";
            },
            width: "20%",
            filterable: true,
            filterType: "select",
            filterOptions: [...allGroupOptions],
            filterPlaceholder: TEXTS.filterGroup
        },
        {
            key: "amount",
            label: TEXTS.amount,
            render: (item) =>
                item.min_amount === item.max_amount
                    ? `${item.max_amount.toLocaleString("he-IL")} ₪`
                    : `${item.min_amount.toLocaleString("he-IL")} - ${item.max_amount.toLocaleString("he-IL")} ₪`,
            width: "10%",
            filterable: true,
            filterType: "number",
            filterPlaceholder: TEXTS.filterAmount
        },
        {
            key: "response_date",
            label: "תאריך תשובות",
            render: (item) =>
                item.response_date ? format(new Date(item.response_date), "dd/MM/yyyy", { locale: he }) : "-",
            width: "15%",
            filterable: true,
            filterType: "date"
        },
        {
            key: "is_public",
            label: "סטטוס",
            render: (item) => (item.is_public ? "פומבית" : "פרטית"),
            width: "10%"
        },
        {
            key: "start_date",
            label: TEXTS.startDate,
            render: (item) => (item.start_date ? format(new Date(item.start_date), "dd/MM/yyyy", { locale: he }) : "-"),
            width: "17.5%",
            filterable: true,
            filterType: "date"
        },
        {
            key: "end_date",
            label: TEXTS.endDate,
            render: (item) => (item.end_date ? format(new Date(item.end_date), "dd/MM/yyyy", { locale: he }) : "-"),
            width: "17.5%",
            filterable: true,
            filterType: "date"
        }
    ];

    const groupColumns: AdminTableColumn<ScholarshipGroup>[] = [
        {
            key: "title",
            label: TEXTS.groupTitle,
            render: (item) => item.title || "ללא שם"
        },
        {
            key: "description",
            label: TEXTS.description,
            render: (item) => item.description || "ללא תיאור"
        },
        {
            key: "scholarships_count",
            label: TEXTS.scholarshipsCount,
            render: (item) => `${item.scholarships_count || 0} מלגות`
        }
    ];

    return (
        <div className="container mx-auto max-w-7xl">
            <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as TabValue)}
                className="w-full"
                dir="rtl"
            >
                <TabsList className="w-full grid grid-cols-2 h-12 bg-muted my-2 mt-8 px-1 rounded-lg">
                    <TabsTrigger
                        value="scholarships"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.scholarshipsTabTitle}
                    </TabsTrigger>
                    <TabsTrigger
                        value="groups"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.groupsTabTitle}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="scholarships" className="mt-6">
                    <AdminTable
                        title={TEXTS.pageTitle}
                        items={scholarships}
                        columns={scholarshipColumns}
                        loading={scholarshipsLoading}
                        error={scholarshipsError}
                        onDelete={handleDeleteScholarship}
                        onRefetch={refetchScholarships}
                        addButtonLabel={TEXTS.newScholarship}
                        addButtonHref="/admin/scholarships/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitle}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescription}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noScholarships}
                        loadingText={TEXTS.loading}
                        errorPrefix={TEXTS.errorPrefix}
                        deleteErrorText={TEXTS.deleteError}
                        deleteSuccessText={TEXTS.deleteSuccess}
                        deletingText={TEXTS.deletingScholarship}
                        currentPage={currentPage}
                        onPageChange={handlePageChange}
                        totalItems={totalItems}
                        totalPages={totalPages}
                        pageSize={pageSize}
                        onPageSizeChange={handlePageSizeChange}
                        onFilter={handleFilter}
                        activeFilters={filters}
                    />
                </TabsContent>

                <TabsContent value="groups" className="mt-6">
                    <AdminTable
                        title={TEXTS.groupsPageTitle}
                        items={groups}
                        columns={groupColumns}
                        loading={groupsLoading}
                        error={groupsError}
                        onDelete={handleDeleteGroup}
                        onRefetch={refetchGroups}
                        addButtonLabel={TEXTS.newGroup}
                        addButtonHref="/admin/scholarships/groups/new"
                        deleteConfirmTitle={TEXTS.deleteConfirmTitleGroup}
                        deleteConfirmDescription={TEXTS.deleteConfirmDescriptionGroup}
                        confirmDeleteText={TEXTS.confirmDelete}
                        noItemsText={TEXTS.noGroups}
                        loadingText={TEXTS.loadingGroups}
                        errorPrefix={TEXTS.errorPrefixGroups}
                        deleteErrorText={TEXTS.deleteErrorGroup}
                        deleteSuccessText={TEXTS.deleteSuccessGroup}
                        deletingText={TEXTS.deletingGroup}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
}
