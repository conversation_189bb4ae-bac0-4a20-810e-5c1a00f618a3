"use client";

import { use } from "react";

import { ScholarshipGroupForm } from "@/components/forms/scholarship-group-form";

const TEXTS = {
    pageTitle: "עריכת קבוצת מלגות"
};

export default function EditScholarshipGroupPage({ params }: { params: Promise<{ id: string }> }) {
    const resolvedParams = use(params);

    return (
        <div className="container py-8 mx-auto max-w-7xl" dir="rtl">
            <h1 className="text-2xl font-semibold mb-6">{TEXTS.pageTitle}</h1>
            <div className="bg-white rounded-lg shadow p-6">
                <ScholarshipGroupForm groupId={resolvedParams.id} />
            </div>
        </div>
    );
}
