"use client";

import { use } from "react";

import { ScholarshipForm } from "@/components/forms/scholarship-form";

const TEXTS = {
    pageTitle: "עריכת מלגה"
};

export default function EditScholarshipPage({ params }: { params: Promise<{ id: string }> }) {
    const resolvedParams = use(params);

    return (
        <div className="container py-8 mx-auto max-w-7xl" dir="rtl">
            <h1 className="text-2xl font-semibold mb-6">{TEXTS.pageTitle}</h1>
            <div className="bg-white rounded-lg shadow p-6">
                <ScholarshipForm scholarshipId={resolvedParams.id} />
            </div>
        </div>
    );
}
