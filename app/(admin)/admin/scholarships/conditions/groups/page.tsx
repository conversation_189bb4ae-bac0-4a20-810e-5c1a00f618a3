"use client";

import { useEffect } from "react";
import { toast } from "sonner";

import { deleteScholarshipConditionGroup } from "@/app/actions/scholarship-condition-group-actions";
import { AdminTable } from "@/components/table/admin-table";
import { ScholarshipConditionGroup, useScholarshipConditionGroups } from "@/hooks/use-scholarship-condition-groups";

const TEXTS = {
    pageTitle: "קבוצות תנאים למלגות",
    newGroup: "קבוצת תנאים חדשה",
    loading: "טוען קבוצות תנאים...",
    errorPrefix: "שגיאה בטעינת קבוצות התנאים:",
    noGroups: "לא נמצאו קבוצות תנאים",
    name: "שם",
    conditionsCount: "מספר תנאים",
    actions: "פעולות",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק קבוצת תנאים זו? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת קבוצת התנאים",
    deleteSuccess: "קבוצת התנאים נמחקה בהצלחה",
    deletingGroup: "מוחק קבוצת תנאים..."
};

export default function ScholarshipConditionGroupsPage() {
    const { items: groups, loading, error, refetch } = useScholarshipConditionGroups();

    useEffect(() => {
        if (error) {
            toast.error(TEXTS.errorPrefix, {
                description: error instanceof Error ? error.message : TEXTS.errorPrefix
            });
        }
    }, [error]);

    const handleDelete = async (id: string) => {
        try {
            const result = await deleteScholarshipConditionGroup(id);
            if (result.success) {
                return { success: true };
            } else {
                return { success: false, error: result.error || TEXTS.deleteError };
            }
        } catch (err) {
            console.error("Error deleting condition group:", err);
            return {
                success: false,
                error: err instanceof Error ? err.message : TEXTS.deleteError
            };
        }
    };

    const columns = [
        {
            key: "name",
            label: TEXTS.name,
            render: (group: ScholarshipConditionGroup) => (
                <span className="font-medium text-gray-900">{group.name}</span>
            )
        },
        {
            key: "conditions_count",
            label: TEXTS.conditionsCount,
            render: (group: ScholarshipConditionGroup) => group.conditions_count
        }
    ];

    return (
        <AdminTable
            title={TEXTS.pageTitle}
            items={groups}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={refetch}
            addButtonLabel={TEXTS.newGroup}
            addButtonHref="/admin/scholarships/conditions/groups/new"
            deleteConfirmTitle={TEXTS.deleteConfirmTitle}
            deleteConfirmDescription={TEXTS.deleteConfirmDescription}
            confirmDeleteText={TEXTS.confirmDelete}
            noItemsText={TEXTS.noGroups}
            loadingText={TEXTS.loading}
            errorPrefix={TEXTS.errorPrefix}
            deleteErrorText={TEXTS.deleteError}
            deleteSuccessText={TEXTS.deleteSuccess}
            deletingText={TEXTS.deletingGroup}
        />
    );
}
