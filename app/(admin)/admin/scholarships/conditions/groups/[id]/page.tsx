"use client";

import { useParams } from "next/navigation";
import React from "react";

import { ConditionGroupForm } from "@/components/forms/condition-group-form";

export default function EditScholarshipConditionGroupPage() {
    const params = useParams();
    const groupId = params.id as string;

    if (!groupId) {
        return <div>Error: Condition Group ID not found.</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <ConditionGroupForm groupId={groupId} />
            </div>
        </div>
    );
}
