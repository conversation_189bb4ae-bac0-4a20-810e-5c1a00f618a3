"use client";

import { useParams } from "next/navigation";
import React from "react";

import { BannerForm } from "@/components/forms/banner-form";

export default function EditBannerPage() {
    const params = useParams();
    const bannerId = params.id as string;

    if (!bannerId) {
        return <div>שגיאה: מספר באנר לא נמצא.</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">עריכת באנר</h1>
                <BannerForm bannerId={bannerId} />
            </div>
        </div>
    );
}
