"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import * as LucideIcons from "lucide-react";
import { LucideIcon } from "lucide-react";

import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { useAdminBanners } from "@/hooks/use-admin-banners";
import { ADMIN_BANNERS_TEXTS as TEXTS } from "@/lib/banner-constants";
import { type Tables } from "@/types/database.types";

type Banner = Tables<"banners">;

export default function BannersPage() {
    const { items: banners, loading, error, handleDelete, refetch } = useAdminBanners();

    const columns: AdminTableColumn<Banner>[] = [
        {
            key: "title",
            label: TEXTS.title,
            render: (item) => item.title || "-"
        },
        {
            key: "text",
            label: TEXTS.text,
            render: (item) => item.text
        },
        {
            key: "icon",
            label: TEXTS.icon,
            render: (item) => {
                const IconComponent =
                    (LucideIcons as unknown as Record<string, LucideIcon>)[item.icon] || LucideIcons.Info;
                return (
                    <div className="flex items-center justify-center">
                        <IconComponent className="h-5 w-5" />
                    </div>
                );
            }
        },
        {
            key: "audience",
            label: TEXTS.audience,
            render: (item) => {
                switch (item.audience) {
                    case "Guest":
                        return TEXTS.audienceGuest;
                    case "User":
                        return TEXTS.audienceUser;
                    case "Subscriber":
                        return TEXTS.audienceSubscriber;
                    default:
                        return item.audience;
                }
            }
        },
        {
            key: "colors",
            label: TEXTS.backgroundAndTextColor,
            render: (item) => (
                <div className="flex items-center">
                    <div
                        className="w-6 h-6 mr-2 flex items-center justify-center text-xs font-bold"
                        style={{
                            backgroundColor: item.background_color,
                            color: item.text_color,
                            border: "1px solid rgba(0,0,0,0.1)",
                            borderRadius: "10px"
                        }}
                    >
                        T
                    </div>
                </div>
            )
        },
        {
            key: "cta",
            label: TEXTS.ctaText,
            render: (item) => item.cta_text || "-"
        },
        {
            key: "days_to_live",
            label: TEXTS.daysToLive,
            render: (item) => item.days_to_live
        },
        {
            key: "enabled",
            label: TEXTS.enabled,
            render: (item) => (item.enabled ? "כן" : "לא")
        },
        {
            key: "created_at",
            label: TEXTS.createdAt,
            render: (item) => format(new Date(item.created_at), "dd/MM/yyyy", { locale: he })
        }
    ];

    return (
        <AdminTable
            title={TEXTS.pageTitle}
            items={banners}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={refetch}
            addButtonLabel={TEXTS.newBanner}
            addButtonHref="/admin/banners/new"
            deleteConfirmTitle={TEXTS.deleteConfirmTitle}
            deleteConfirmDescription={TEXTS.deleteConfirmDescription}
            confirmDeleteText={TEXTS.confirmDelete}
            noItemsText={TEXTS.noBanners}
            loadingText={TEXTS.loading}
            errorPrefix={TEXTS.errorPrefix}
            deleteErrorText={TEXTS.deleteError}
            deleteSuccessText={TEXTS.deleteSuccess}
            deletingText={TEXTS.deletingBanner}
        />
    );
}
