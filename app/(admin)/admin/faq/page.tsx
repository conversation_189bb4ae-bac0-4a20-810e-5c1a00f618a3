"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { deleteFaq, getAllFaqs, reorderFaqs } from "@/app/actions/faq-actions";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { TEXTS } from "@/lib/faq-constants";
import { type Tables } from "@/types/database.types";

type Faq = Tables<"faq">;

export default function FaqPage() {
    const [faqs, setFaqs] = useState<Faq[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);
    const [, setIsDeleting] = useState(false);

    const fetchFaqs = async () => {
        try {
            setLoading(true);
            const result = await getAllFaqs();

            if (!result.success) {
                throw new Error(result.error);
            }

            setFaqs(result.data || []);
            setError(null);
        } catch (err) {
            console.error("Error fetching FAQs:", err);
            setError(err instanceof Error ? err : new Error("Unknown error occurred"));
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchFaqs();
    }, []);

    const handleDelete = async (id: string) => {
        setIsDeleting(true);
        try {
            const result = await deleteFaq(id);

            if (!result.success) {
                throw new Error(result.error);
            }

            return { success: true };
        } catch (err) {
            console.error("Error deleting FAQ:", err);
            return {
                success: false,
                error: err instanceof Error ? err.message : TEXTS.DELETE_ERROR
            };
        } finally {
            setIsDeleting(false);
        }
    };

    const handleReorder = async (reorderedItems: Faq[]) => {
        try {
            setLoading(true);
            const result = await reorderFaqs(reorderedItems);

            if (!result.success) {
                throw new Error(result.error);
            }

            await fetchFaqs();
        } catch (err) {
            console.error("Error reordering FAQs:", err);
            toast.error(TEXTS.FAQ_REORDER_ERROR);
            await fetchFaqs();
        } finally {
            setLoading(false);
        }
    };

    const columns: AdminTableColumn<Faq>[] = [
        {
            key: "order_index",
            label: TEXTS.INDEX,
            render: (item) => item.order_index
        },
        {
            key: "question",
            label: TEXTS.QUESTION,
            render: (item) => item.question
        },
        {
            key: "answer",
            label: TEXTS.ANSWER,
            render: (item) => item.answer
        },
        {
            key: "created_at",
            label: TEXTS.CREATED_AT,
            render: (item) => format(new Date(item.created_at), "dd/MM/yyyy", { locale: he })
        },
        {
            key: "updated_at",
            label: TEXTS.UPDATED_AT,
            render: (item) => format(new Date(item.updated_at), "dd/MM/yyyy", { locale: he })
        }
    ];

    return (
        <AdminTable
            title={TEXTS.PAGE_TITLE}
            items={faqs}
            columns={columns}
            loading={loading}
            error={error}
            onDelete={handleDelete}
            onRefetch={fetchFaqs}
            addButtonLabel={TEXTS.NEW_FAQ}
            addButtonHref="/admin/faq/new"
            deleteConfirmTitle={TEXTS.DELETE_CONFIRM_TITLE}
            deleteConfirmDescription={TEXTS.DELETE_CONFIRM_DESCRIPTION}
            confirmDeleteText={TEXTS.CONFIRM_DELETE}
            noItemsText={TEXTS.NO_FAQS}
            loadingText={TEXTS.LOADING}
            errorPrefix={TEXTS.ERROR_PREFIX}
            deleteErrorText={TEXTS.DELETE_ERROR}
            deleteSuccessText={TEXTS.DELETE_SUCCESS}
            deletingText={TEXTS.DELETING_FAQ}
            reorderable={true}
            onReorder={handleReorder}
        />
    );
}
