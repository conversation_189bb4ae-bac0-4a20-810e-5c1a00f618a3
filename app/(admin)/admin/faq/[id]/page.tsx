"use client";

import { useParams } from "next/navigation";
import React from "react";

import { FaqForm } from "@/components/forms/faq-form";

export default function EditFaqPage() {
    const params = useParams();
    const faqId = params.id as string;

    if (!faqId) {
        return <div>שגיאה: מספר שאלה לא נמצא.</div>;
    }

    return (
        <div className="container py-8 mx-auto max-w-5xl">
            <div className="bg-white rounded-lg shadow p-6">
                <h1 className="text-right text-2xl font-semibold mb-6">עריכת שאלה</h1>
                <FaqForm faqId={faqId} />
            </div>
        </div>
    );
}
