import { DynamicQuestionsForm } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

const TEXTS = {
    questionsTab: "שאלות"
};

interface UserQuestionsTabProps {
    userId: string;
}

export function UserQuestionsTab({ userId }: UserQuestionsTabProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>{TEXTS.questionsTab}</CardTitle>
            </CardHeader>
            <CardContent>
                <DynamicQuestionsForm
                    sections={["personal_details", "data_entry", "specific_scholarship"]}
                    overrideUserId={userId}
                    hideTitle={false}
                    columns={2}
                    suppressSuccessToast={false}
                    onSubmitEnd={(success) => {
                        if (success) {
                            console.log("User answers updated successfully for user:", userId);
                        }
                    }}
                />
            </CardContent>
        </Card>
    );
}
