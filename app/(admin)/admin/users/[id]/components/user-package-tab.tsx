"use client";

import { useEffect, useState } from "react";

import { getUserSubscriptionByUserId } from "@/app/actions/user-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { AdminSubscriptionsTable } from "@/components/subscriptions/admin-subscriptions-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

const TEXTS = {
    packageTab: "חבילה",
    loadingSubscriptionDetails: "טוען פרטי תוכנית..."
};

interface UserPackageTabProps {
    userId: string;
}

export function UserPackageTab({ userId }: UserPackageTabProps) {
    const [subscription, setSubscription] = useState<UserSubscriptionWithPlan | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        async function fetchSubscription() {
            try {
                setLoading(true);
                const result = await getUserSubscriptionByUserId(userId);

                if (result.success) {
                    setSubscription(result.subscription || null);
                    setError(null);
                } else {
                    setError(result.error || "Failed to fetch subscription");
                    setSubscription(null);
                }
            } catch (err) {
                console.error("Error fetching user subscription:", err);
                setError("An unexpected error occurred");
                setSubscription(null);
            } finally {
                setLoading(false);
            }
        }

        if (userId) {
            fetchSubscription();
        }
    }, [userId]);

    if (loading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>{TEXTS.packageTab}</CardTitle>
                </CardHeader>
                <CardContent>
                    <LoadingIcon text={TEXTS.loadingSubscriptionDetails} />
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>{TEXTS.packageTab}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-red-500">{error}</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>{TEXTS.packageTab}</CardTitle>
            </CardHeader>
            <CardContent>
                <AdminSubscriptionsTable targetUserId={userId} currentSubscription={subscription} />
            </CardContent>
        </Card>
    );
}
