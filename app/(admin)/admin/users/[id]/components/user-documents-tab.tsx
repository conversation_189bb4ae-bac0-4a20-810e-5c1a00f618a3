import { DynamicDocumentUploadForm } from "@/components/forms/dynamic-document-upload-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TEXTS } from "@/lib/user-constants";

interface UserDocumentsTabProps {
    userId: string;
}

export function UserDocumentsTab({ userId }: UserDocumentsTabProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>{TEXTS.documentsTab}</CardTitle>
            </CardHeader>
            <CardContent>
                <DynamicDocumentUploadForm
                    overrideUserId={userId}
                    onUploadComplete={() => {
                        console.log("Document uploaded successfully for user:", userId);
                    }}
                />
            </CardContent>
        </Card>
    );
}
