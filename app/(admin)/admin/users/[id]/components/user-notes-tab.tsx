"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";

import { deleteUserNote, type UserNoteWithReporter } from "@/app/actions/user-notes-actions";
import { EmptyState } from "@/components/common/empty-state";
import { UserNoteForm } from "@/components/forms/user-note-form";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useUserNotes } from "@/hooks/use-user-notes";

const TEXTS = {
    pageTitle: "הערות משתמש",
    newNote: "הערה חדשה",
    loading: "טוען הערות...",
    errorPrefix: "שגיאה בטעינת הערות:",
    noNotes: "לא נמצאו הערות",

    noteLabel: "הערה",
    notePlaceholder: "הזן הערה...",
    noteRequired: "יש להזין הערה",
    reporterLabel: "מדווח",
    timeLabel: "זמן דיווח",

    addButton: "הוסף הערה",
    cancel: "ביטול",
    confirmDelete: "מחק",

    deleteConfirmTitle: "אישור מחיקת הערה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק הערה זו? לא ניתן לשחזר פעולה זו.",

    deleteError: "שגיאה במחיקת הערה",
    deleteSuccess: "הערה נמחקה בהצלחה",
    createError: "שגיאה ביצירת הערה",
    createSuccess: "הערה נוספה בהצלחה",
    deleting: "מוחק...",
    creating: "יוצר..."
};

interface UserNotesTabProps {
    userId: string;
}

export function UserNotesTab({ userId }: UserNotesTabProps) {
    const { notes, loading, error, refetch } = useUserNotes(userId);

    const handleDelete = async (id: string) => {
        try {
            const result = await deleteUserNote(id);
            return result;
        } catch (err) {
            console.error("Error deleting note:", err);
            return { success: false, error: err instanceof Error ? err.message : TEXTS.deleteError };
        }
    };

    const columns: AdminTableColumn<UserNoteWithReporter>[] = [
        {
            key: "reporter_email",
            label: TEXTS.reporterLabel,
            render: (item) => item.reporter_email || item.user_id
        },
        {
            key: "note",
            label: TEXTS.noteLabel,
            render: (item) => (
                <div className="max-w-md truncate" title={item.note}>
                    {item.note}
                </div>
            )
        },
        {
            key: "created_at",
            label: TEXTS.timeLabel,
            render: (item) => format(new Date(item.created_at), "dd/MM/yyyy HH:mm", { locale: he })
        }
    ];

    return (
        <Card>
            <CardHeader>
                <CardTitle>{TEXTS.pageTitle}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div>
                    <UserNoteForm reportedUserId={userId} onSuccess={refetch} />
                </div>

                <Separator />

                {!loading && !error && notes.length === 0 ? (
                    <EmptyState message={TEXTS.noNotes} />
                ) : (
                    <div className="[&_.container]:!p-0 [&_.container]:!max-w-none [&_.container]:!mx-0">
                        <AdminTable
                            title=""
                            items={notes}
                            columns={columns}
                            loading={loading}
                            error={error}
                            onDelete={handleDelete}
                            onRefetch={refetch}
                            deleteConfirmTitle={TEXTS.deleteConfirmTitle}
                            deleteConfirmDescription={TEXTS.deleteConfirmDescription}
                            confirmDeleteText={TEXTS.confirmDelete}
                            noItemsText={TEXTS.noNotes}
                            loadingText={TEXTS.loading}
                            errorPrefix={TEXTS.errorPrefix}
                            deleteErrorText={TEXTS.deleteError}
                            deleteSuccessText={TEXTS.deleteSuccess}
                            deletingText={TEXTS.deleting}
                        />
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
