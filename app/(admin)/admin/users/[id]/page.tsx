"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

import { UserDocumentsTab } from "@/app/(admin)/admin/users/[id]/components/user-documents-tab";
import { UserNotesTab } from "@/app/(admin)/admin/users/[id]/components/user-notes-tab";
import { UserPackageTab } from "@/app/(admin)/admin/users/[id]/components/user-package-tab";
import { UserQuestionsTab } from "@/app/(admin)/admin/users/[id]/components/user-questions-tab";
import { getUserDetailsByUserId } from "@/app/actions/user-actions";
import { CopyToClipboard } from "@/components/common/copy-to-clipboard";
import { EmptyState } from "@/components/common/empty-state";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { TEXTS } from "@/lib/user-constants";

type TabValue = "questions" | "documents" | "package" | "notes";

interface UserDetails {
    id: string;
    email: string;
    user_id: string;
}

const isValidUserId = (id: unknown) => typeof id === "string" && id.trim().length > 0;

export default function UserDetailPage() {
    const params = useParams();
    const userId = params.id as string;

    const [activeTab, setActiveTab] = useState<TabValue>("questions");
    const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const isUserIdValid = isValidUserId(userId);

    useEffect(() => {
        if (!isUserIdValid) return;
        async function fetchUserDetails() {
            try {
                setLoading(true);
                const result = await getUserDetailsByUserId(userId);

                if (result.success && result.user) {
                    setUserDetails(result.user);
                    setError(null);
                } else {
                    setError(result.error || TEXTS.userNotFound);
                    setUserDetails(null);
                }
            } catch (err) {
                console.error("Error fetching user details:", err);
                setError(TEXTS.genericError);
                setUserDetails(null);
            } finally {
                setLoading(false);
            }
        }

        if (userId) {
            fetchUserDetails();
        }
    }, [userId, isUserIdValid]);

    if (!isUserIdValid) {
        return (
            <div className="container mx-auto max-w-7xl p-8" dir="rtl">
                <EmptyState message={TEXTS.userIdInvalid} variant="error" />
            </div>
        );
    }

    if (loading) {
        return (
            <div className="container mx-auto max-w-7xl p-8" dir="rtl">
                <Card className="mb-6">
                    <CardHeader>
                        <Skeleton className="h-8 w-48 mb-4" />
                        <div className="space-y-2">
                            <Skeleton className="h-6 w-64" />
                            <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-32" />
                                <Skeleton className="h-6 w-6" />
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                <div className="w-full" dir="rtl">
                    <div className="w-full grid grid-cols-4 h-12 bg-muted my-2 px-1 rounded-lg">
                        {[1, 2, 3, 4].map((i) => (
                            <Skeleton key={i} className="h-10 mx-1 rounded-md" />
                        ))}
                    </div>
                    <div className="mt-6">
                        <Skeleton className="h-96 w-full rounded-lg" />
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto max-w-7xl p-8" dir="rtl">
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="text-2xl font-bold">{TEXTS.userDetailsTitle}</CardTitle>
                        <p className="text-red-500">{error}</p>
                    </CardHeader>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto max-w-7xl p-8" dir="rtl">
            <Card className="mb-6">
                <CardHeader>
                    <CardTitle className="text-2xl font-bold">{TEXTS.userDetailsTitle}</CardTitle>
                    {userDetails && (
                        <div className="space-y-2">
                            <p className="text-lg font-medium text-primary">{userDetails.email}</p>
                            <div className="flex items-center gap-2">
                                <p className="text-muted-foreground">
                                    {TEXTS.userIdLabel}: {userId}
                                </p>
                                <CopyToClipboard text={userId} onSuccessMessage={TEXTS.userIdCopySuccess} size="sm" />
                            </div>
                        </div>
                    )}
                </CardHeader>
            </Card>

            <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as TabValue)}
                className="w-full"
                dir="rtl"
            >
                <TabsList className="w-full grid grid-cols-4 h-12 bg-muted my-2 px-1 rounded-lg">
                    <TabsTrigger
                        value="questions"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.questionsTab}
                    </TabsTrigger>
                    <TabsTrigger
                        value="documents"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.documentsTab}
                    </TabsTrigger>
                    <TabsTrigger
                        value="package"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.packageTab}
                    </TabsTrigger>
                    <TabsTrigger
                        value="notes"
                        className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                    >
                        {TEXTS.notesTab}
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="questions" className="mt-6">
                    <UserQuestionsTab userId={userId} />
                </TabsContent>

                <TabsContent value="documents" className="mt-6">
                    <UserDocumentsTab userId={userId} />
                </TabsContent>

                <TabsContent value="package" className="mt-6">
                    <UserPackageTab userId={userId} />
                </TabsContent>

                <TabsContent value="notes" className="mt-6">
                    <UserNotesTab userId={userId} />
                </TabsContent>
            </Tabs>
        </div>
    );
}
