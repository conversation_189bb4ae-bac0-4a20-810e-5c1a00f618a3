"use client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { useUserOrgRole } from "@/hooks/use-user-org-role";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const { role, isLoaded } = useUserOrgRole();
    const router = useRouter();

    useEffect(() => {
        if (!isLoaded) return;
        if (role !== "admin" && role !== "employee") {
            router.replace("/dashboard");
        }
    }, [isLoaded, role, router]);

    return <>{children}</>;
}
