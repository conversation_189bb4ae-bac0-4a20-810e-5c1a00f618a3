import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { isAdminFromSessionClaims } from "@/lib/org-role";

const VERCEL_DEPLOY_URL = process.env.NEXT_PUBLIC_VERCEL_URL || process.env.VERCEL_URL;
const BASE_URL = VERCEL_DEPLOY_URL ? `https://${VERCEL_DEPLOY_URL}` : "http://localhost:3000";

export async function GET() {
    const { userId, sessionClaims } = await auth();

    if (!userId) {
        return NextResponse.redirect(`${BASE_URL}/login`);
    }

    const isAdmin = isAdminFromSessionClaims(sessionClaims);

    const subscription = await getCurrentUserSubscription();
    const isFreePlan = !subscription || subscription.planType === "free";

    const defaultRedirect = isAdmin ? "/admin" : "/dashboard";

    if (isFreePlan && !isAdmin) {
        return NextResponse.redirect(`${BASE_URL}/subscriptions`);
    }

    return NextResponse.redirect(`${BASE_URL}${defaultRedirect}`);
}
