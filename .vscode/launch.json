{"version": "0.2.0", "configurations": [{"command": "yarn dev", "name": "Debug", "request": "launch", "type": "node-terminal"}, {"command": "yarn build", "name": "Build", "request": "launch", "type": "node-terminal"}, {"command": "yarn lint", "name": "<PERSON><PERSON>", "request": "launch", "type": "node-terminal"}, {"command": "yarn format:all", "name": "Format", "request": "launch", "type": "node-terminal"}, {"command": "supabase start", "name": "Start Supabase", "request": "launch", "type": "node-terminal"}, {"command": "supabase stop", "name": "Stop Supabase", "request": "launch", "type": "node-terminal"}, {"command": "supabase stop && supabase start", "name": "Restart <PERSON>", "request": "launch", "type": "node-terminal"}]}