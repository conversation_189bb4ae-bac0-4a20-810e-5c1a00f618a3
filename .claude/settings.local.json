{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(grep:*)", "Bash(yarn test:*)", "Bash(node:*)", "Bash(yarn build)", "Bash(yarn lint)", "Bash(yarn format:all)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(yarn lint:*)", "Bash(yarn generate:types:*)", "Bash(yarn build:*)", "Bash(yarn typecheck)", "Bash(rm:*)", "Bash(yarn tsc:*)", "Bash(git restore:*)", "Bash(git checkout:*)", "mcp__Sequential_thinking__sequentialthinking", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "mcp__supabase__list_extensions", "mcp__supabase__list_migrations", "mcp__supabase__execute_sql", "mcp__supabase__get_advisors"], "deny": []}}