import DOMPurify from "dompurify";

export interface SanitizationOptions {
    allowedTags?: string[];
    allowedAttributes?: string[];
    stripTags?: boolean;
    maxLength?: number;
}

export const DEFAULT_SANITIZATION_OPTIONS: SanitizationOptions = {
    allowedTags: [],
    allowedAttributes: [],
    stripTags: true,
    maxLength: 10000
};

export const BASIC_HTML_OPTIONS: SanitizationOptions = {
    allowedTags: ["p", "br", "strong", "em", "u", "ul", "ol", "li"],
    allowedAttributes: [],
    stripTags: false,
    maxLength: 10000
};

export const RICH_TEXT_OPTIONS: SanitizationOptions = {
    allowedTags: [
        "p",
        "br",
        "strong",
        "em",
        "u",
        "ul",
        "ol",
        "li",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "blockquote",
        "a"
    ],
    allowedAttributes: ["href", "title", "target"],
    stripTags: false,
    maxLength: 50000
};

function sanitizeServerSide(input: string, maxLength: number): string {
    if (!input || typeof input !== "string") {
        return "";
    }

    const trimmedInput = input.trim();
    if (trimmedInput.length === 0) {
        return "";
    }

    let sanitized = trimmedInput.replace(/<[^>]*>/g, "");

    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

    sanitized = sanitized
        .replace(/javascript:/gi, "")
        .replace(/data:/gi, "")
        .replace(/vbscript:/gi, "")
        .replace(/on\w+\s*=/gi, "");

    if (sanitized.length > maxLength) {
        sanitized = sanitized.substring(0, maxLength);
    }

    return sanitized.trim();
}

function isServerEnvironment(): boolean {
    return typeof window === "undefined";
}

export function sanitizeHtml(input: string, options: SanitizationOptions = DEFAULT_SANITIZATION_OPTIONS): string {
    if (!input || typeof input !== "string") {
        return "";
    }

    const trimmedInput = input.trim();

    if (trimmedInput.length === 0) {
        return "";
    }

    const maxLength = options.maxLength || DEFAULT_SANITIZATION_OPTIONS.maxLength!;
    const truncatedInput = trimmedInput.length > maxLength ? trimmedInput.substring(0, maxLength) : trimmedInput;

    if (isServerEnvironment()) {
        return sanitizeServerSide(truncatedInput, maxLength);
    }

    try {
        const sanitizerOptions: any = {};

        if (options.stripTags) {
            sanitizerOptions.ALLOWED_TAGS = [];
            sanitizerOptions.ALLOWED_ATTR = [];
            sanitizerOptions.KEEP_CONTENT = true;
        } else {
            if (options.allowedTags && options.allowedTags.length > 0) {
                sanitizerOptions.ALLOWED_TAGS = options.allowedTags;
            }
            if (options.allowedAttributes && options.allowedAttributes.length > 0) {
                sanitizerOptions.ALLOWED_ATTR = options.allowedAttributes;
            }
        }

        sanitizerOptions.FORCE_BODY = false;
        sanitizerOptions.RETURN_DOM = false;
        sanitizerOptions.RETURN_DOM_FRAGMENT = false;
        sanitizerOptions.SANITIZE_DOM = true;
        sanitizerOptions.SANITIZE_NAMED_PROPS = true;
        sanitizerOptions.SAFE_FOR_TEMPLATES = true;

        const sanitized = DOMPurify.sanitize(truncatedInput, sanitizerOptions);

        return String(sanitized).trim();
    } catch (error) {
        console.warn("DOMPurify sanitization fai\led, falling back to server-safe sanitization:", error);
        return sanitizeServerSide(truncatedInput, maxLength);
    }
}

export function sanitizeText(input: string, maxLength: number = 1000): string {
    return sanitizeHtml(input, {
        ...DEFAULT_SANITIZATION_OPTIONS,
        maxLength
    });
}

export function sanitizeBasicHtml(input: string, maxLength: number = 5000): string {
    return sanitizeHtml(input, {
        ...BASIC_HTML_OPTIONS,
        maxLength
    });
}

export function sanitizeRichText(input: string, maxLength: number = 50000): string {
    return sanitizeHtml(input, {
        ...RICH_TEXT_OPTIONS,
        maxLength
    });
}

export function sanitizeFormData<T extends Record<string, any>>(
    data: T,
    fieldOptions: Partial<Record<keyof T, SanitizationOptions>> = {}
): T {
    const sanitizedData = { ...data };

    for (const [key, value] of Object.entries(data)) {
        if (typeof value === "string") {
            const options = fieldOptions[key as keyof T] || DEFAULT_SANITIZATION_OPTIONS;
            sanitizedData[key as keyof T] = sanitizeHtml(value, options) as T[keyof T];
        }
    }

    return sanitizedData;
}

export function sanitizeUrls(input: string): string {
    const sanitized = sanitizeText(input);

    const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/gi;
    return sanitized.replace(urlRegex, (url) => {
        try {
            const urlObj = new URL(url);
            if (urlObj.protocol === "http:" || urlObj.protocol === "https:") {
                return url;
            }
            return "";
        } catch {
            return "";
        }
    });
}

export function sanitizeEmail(input: string): string {
    const sanitized = sanitizeText(input, 100);
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(sanitized) ? sanitized.toLowerCase() : "";
}

export function sanitizePhoneNumber(input: string): string {
    const sanitized = sanitizeText(input, 20);
    return sanitized.replace(/[^\d\s\-\+\(\)]/g, "");
}
