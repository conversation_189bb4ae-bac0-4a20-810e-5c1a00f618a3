/**
 * Converts a text string into an array by splitting on newlines,
 * trimming whitespace, and removing empty lines.
 *
 * @param text - The text to convert into an array
 * @returns An array of non-empty, trimmed strings
 */
export function textToArray(text: string): string[] {
    return text
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0);
}

/**
 * Formats an Israeli phone number to E.164 format
 *
 * Converts a phone number like "05XXXXXXXX" to "+972XXXXXXXX"
 *
 * @param phone - Phone number in local Israeli format (05XXXXXXXX)
 * @returns E.164 formatted phone number (+972XXXXXXXX)
 */
export function formatPhoneToE164(phone: string): string {
    const digitsOnly = phone.replace(/\D/g, "");

    if (digitsOnly.startsWith("05")) {
        return `+972${digitsOnly.substring(1)}`;
    }

    if (digitsOnly.startsWith("972")) {
        return `+${digitsOnly}`;
    }

    if (phone.startsWith("+")) {
        return phone;
    }

    return digitsOnly;
}
