const YAADPAY_ERROR_CODES: Record<string, string> = {
    "901": "מסוף לא מורשה לעבודה בשיטה זו: אין הרשאה למסוף",
    "902": "שגיאת אימות: הפניה למסוף שונה משיטת האימות המוגדרת",
    "903": "חריגה ממספר התשלומים שמוגדר במסוף: יש לפנות לתמיכה לצורך שינוי",
    "999": "שגיאת תקשורת - יעד סליקה",
    "998": "עסקה בוטלה - יעד סליקה",
    "997": "טוקן לא תקין",
    "996": "מסוף לא מורשה לשימוש בטוקן",
    "800": "חיוב דחוי",
    "700": "אישור ללא חיוב: שיריון מסגרת ללא הפקדה",
    "600": "קבלת פרטי עסקה (J2): בדיקת פרטי כרטיס -בדיקת תקינות מספר הכרטיס ללא בדיקת מסגרת",
    "990": "פרטי הכרטיס לא נקראו בצורה מלאה, נא להעביר את הכרטיס שנית",
    "400": "סכום הפריטים אינו תואם לסכום לחיוב: פרמטר סכום וסכימה של הפרטים אינם שויים",
    "401": "חובה להזין שם פרטי או משפחה: יש לשלוח פרמטר ClientName or ClientLName",
    "402": "חובה להזין תיאור עסקה: יש לשלוח פרמטר Info",
    "33": "ניתן לזכות את כל העסקה או סכום קטן מסכום העסקה בלבד",
    "001": "הכרטיס חסום.",
    "002": "הכרטיס גנוב.",
    "003": "יש להתקשר לחברת האשראי.",
    "004": "העסקה לא אושרה על ידי חברת האשראי.",
    "005": "כרטיס מזוייף.",
    "006": "CVV שגוי.",
    "015": "תוקף הכרטיס פג.",
    "447": "מספר הכרטיס שגוי."
};

const DEFAULT_ERROR_MESSAGE = "אירעה שגיאה לא צפויה בתהליך התשלום.";

export function getPaymentErrorMessage(errorCode: string | number | undefined | null): string {
    if (errorCode === undefined || errorCode === null) {
        return DEFAULT_ERROR_MESSAGE;
    }
    const codeStr = String(errorCode);
    return YAADPAY_ERROR_CODES[codeStr] || codeStr;
}

export function extractCCode(responseText: string | undefined): string | undefined {
    if (!responseText) return undefined;
    const match = responseText.match(/[?&]CCode=(\d+)/);
    return match ? match[1] : undefined;
}
