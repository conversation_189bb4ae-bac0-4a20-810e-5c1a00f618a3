import type { <PERSON><PERSON> } from "@/types/database.types";

export enum UserClaimKey {
    USER_EMAIL = "user_email",
    ONBOARDING_STAGE = "onboarding_stage",
    SUBSCRIBED_TO_UPDATES = "subscribed_to_updates",
    ACCEPTED_TERMS = "accepted_terms"
}

// Use a generic constraint that describes what we need from the client
type SupabaseClientLike = {
    from(table: string): any;
};

export async function setUserClaim<T extends SupabaseClientLike>(
    supabase: T,
    userId: string,
    claimKey: UserClaimKey,
    claimValue: Json
) {
    if (!userId) throw new Error("No user ID provided");

    const { error } = await supabase
        .from("user_claims")
        .upsert([{ user_id: userId, claim_key: claimKey, claim_value: claimValue }], {
            onConflict: "user_id,claim_key"
        });
    if (error) throw error;
    return;
}

export async function getUserClaim<T extends SupabaseClientLike>(
    supabase: T,
    userId: string,
    claimKey: UserClaim<PERSON>ey
): Promise<Json | null> {
    if (!userId) throw new Error("No user ID provided");

    const { data, error } = await supabase
        .from("user_claims")
        .select("claim_value")
        .eq("user_id", userId)
        .eq("claim_key", claimKey)
        .maybeSingle();
    if (error) {
        console.error("[user-claims] getUserClaim failed:", error);
        throw error;
    }
    return data?.claim_value ?? null;
}
