import { createServerClient } from "@supabase/ssr";
import { createClient } from "@supabase/supabase-js";
import { auth } from "@clerk/nextjs/server";
import { Database } from "@/types/database.types";

// Create an authenticated Supabase client using Clerk JWT
export const createClerkSupabaseClient = async () => {
    const { cookies } = await import("next/headers");
    const cookieStore = await cookies();

    // Get the Clerk token for Supabase template
    const { getToken } = await auth();
    const token = await getToken({ template: "supabase" });

    const headers: Record<string, string> = {
        "Cache-Control": "no-store"
    };

    if (token) {
        headers.Authorization = `Bearer ${token}`;
    }

    return createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            global: {
                headers
            },
            cookies: {
                getAll() {
                    return cookieStore.getAll();
                },
                setAll(cookiesToSet: Array<{ name: string; value: string; options?: any }>) {
                    try {
                        cookiesToSet.forEach(({ name, value, options }) => {
                            cookieStore.set(name, value, options);
                        });
                    } catch (error) {
                        console.error("Failed to set cookies:", error);
                        throw error;
                    }
                }
            }
        }
    );
};

// Legacy function for backward compatibility - now uses Clerk authentication
export const createClientFromRequest = createClerkSupabaseClient;

// Service role client for admin operations (unchanged)
export const createServiceRoleClient = () => {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
        throw new Error("Missing Supabase URL or Service Role Key for service role client.");
    }

    return createClient<Database>(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!, {
        auth: {
            autoRefreshToken: false,
            persistSession: false,
            detectSessionInUrl: false
        }
    });
};
