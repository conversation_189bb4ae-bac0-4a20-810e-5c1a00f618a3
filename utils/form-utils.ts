import { Option } from "@/components/forms/fields/dropdown-base";

/**
 * Maps an array of string IDs to Option objects by finding corresponding items
 * and extracting labels from them.
 */
export function mapIdsToOptions<T>(
    ids: string[],
    sourceItems: T[],
    getIdFromItem: (item: T) => string,
    getLabelFromItem: (item: T) => string | undefined,
    getFallbackLabel: (id: string) => string = (id) => `ID: ${id}`
): Option[] {
    return ids.map((id) => {
        const sourceItem = sourceItems.find((item) => getIdFromItem(item) === id);
        const label = sourceItem ? getLabelFromItem(sourceItem) : undefined;

        return {
            id,
            label: label || getFallbackLabel(id)
        };
    });
}

/**
 * Maps a single string ID to an Option object by finding the corresponding item
 * and extracting a label from it.
 */
export function mapIdToOption<T>(
    id: string,
    sourceItems: T[],
    getIdFromItem: (item: T) => string,
    getLabelFromItem: (item: T) => string | undefined,
    getFallbackLabel: (id: string) => string = (id) => `ID: ${id}`
): Option {
    const sourceItem = sourceItems.find((item) => getIdFromItem(item) === id);
    const label = sourceItem ? getLabelFromItem(sourceItem) : undefined;

    return {
        id,
        label: label || getFallbackLabel(id)
    };
}

/**
 * Convenience function specifically for mapping question IDs to Options
 * using question metadata for labels.
 */
export function mapQuestionIdsToOptions(
    questionIds: string[],
    availableQuestions: Array<{
        id: string;
        metadata?: {
            label?: string;
            placeholder?: string;
        };
    }>
): Option[] {
    return mapIdsToOptions(
        questionIds,
        availableQuestions,
        (question) => question.id,
        (question) => question.metadata?.label || question.metadata?.placeholder,
        (id) => `Question ID: ${id}`
    );
}
