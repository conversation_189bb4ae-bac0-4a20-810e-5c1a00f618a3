import { GemIcon, StarIcon, TrendingUpIcon } from "lucide-react";
import type { LucideIcon } from "lucide-react";
import { Feature, PlanColors, PlanFeature, PlanType, SubscriptionsItem, TEXTS } from "@/lib/subscription-constants";

export const PLAN_IDS: Record<PlanType, string> = {
    free: "basic",
    milgapro: "pro",
    elite: "elite",
    vip: "vip"
} as const;

export const PLAN_ICONS: Record<PlanType, LucideIcon | null> = {
    free: null,
    milgapro: TrendingUpIcon,
    elite: StarIcon,
    vip: GemIcon
} as const;

export const FEATURES: Record<string, Feature> = {
    personal_dashboard: {
        id: "personal_dashboard",
        name: "התאמת מלגות אישית"
    },
    independent_submissions: {
        id: "independent_submissions",
        name: "עדכונים על הגשות עצמאיות"
    },
    auto_submit: {
        id: "auto_submit",
        name: "מנגנון הגשות אוטומטיות"
    },
    no_volunteer: {
        id: "no_volunteer",
        name: "הגשה למלגות ללא התנדבות"
    },
    new_scholarships: {
        id: "new_scholarships",
        name: "עדכונים על מלגות חדשות"
    },
    account_documents: {
        id: "account_documents",
        name: "חדש: צירוף מסמכים בחשבון"
    },
    annual_data: {
        id: "annual_data",
        name: "חדש: עדכון שמירת שנתי"
    }
};

export const PLAN_COLORS: Record<PlanType, PlanColors> = {
    free: {
        text: "text-gray-400",
        bg: "bg-gray-50",
        bg10: "bg-gray-50/10",
        bar: "",
        cardGradient: "bg-white"
    },
    milgapro: {
        text: "text-blue-500",
        bg: "bg-blue-50",
        bg10: "bg-blue-50/10",
        bar: "bg-blue-500",
        cardGradient: "bg-gradient-to-br from-blue-50/70 to-white"
    },
    elite: {
        text: "text-amber-500",
        bg: "bg-amber-50",
        bg10: "bg-amber-50/10",
        bar: "bg-amber-500",
        cardGradient: "bg-gradient-to-br from-amber-50/70 to-white"
    },
    vip: {
        text: "text-purple-600",
        bg: "bg-purple-50",
        bg10: "bg-purple-50/10",
        bar: "bg-purple-600",
        cardGradient: "bg-gradient-to-br from-purple-50/70 to-white"
    }
};

export const PLAN_BADGE_COLORS: Record<Exclude<PlanType, "free">, string> = {
    milgapro: "text-blue-500 bg-blue-50",
    elite: "text-amber-500 bg-amber-50",
    vip: "text-purple-600 bg-purple-50"
} as const;

export const PLAN_FEATURE_MAPPINGS: Record<PlanType, Record<string, boolean>> = {
    free: {
        personal_dashboard: true,
        independent_submissions: true,
        auto_submit: false,
        no_volunteer: false,
        new_scholarships: false,
        account_documents: false,
        annual_data: false
    },
    milgapro: {
        personal_dashboard: true,
        independent_submissions: true,
        auto_submit: true,
        no_volunteer: true,
        new_scholarships: true,
        account_documents: false,
        annual_data: false
    },
    elite: {
        personal_dashboard: true,
        independent_submissions: true,
        auto_submit: true,
        no_volunteer: true,
        new_scholarships: true,
        account_documents: true,
        annual_data: false
    },
    vip: {
        personal_dashboard: true,
        independent_submissions: true,
        auto_submit: true,
        no_volunteer: true,
        new_scholarships: true,
        account_documents: true,
        annual_data: true
    }
};

export const PLAN_FEATURES: Record<PlanType, readonly string[]> = {
    free: ["personal_dashboard", "independent_submissions"],
    milgapro: ["personal_dashboard", "independent_submissions", "auto_submit", "no_volunteer", "new_scholarships"],
    elite: [
        "personal_dashboard",
        "independent_submissions",
        "auto_submit",
        "no_volunteer",
        "new_scholarships",
        "account_documents"
    ],
    vip: [
        "personal_dashboard",
        "independent_submissions",
        "auto_submit",
        "no_volunteer",
        "new_scholarships",
        "account_documents",
        "annual_data"
    ]
} as const;

export const PLAN_HIERARCHY: Record<PlanType, number> = {
    free: 0,
    milgapro: 1,
    elite: 2,
    vip: 3
} as const;

export const getPlanTextColor = (plan: PlanType) => PLAN_COLORS[plan]?.text || "text-gray-400";
export const getPlanBgColor = (plan: PlanType) => PLAN_COLORS[plan]?.bg || "bg-gray-50";
export const getPlanBgColor10 = (plan: PlanType) => PLAN_COLORS[plan]?.bg10 || "bg-gray-50/10";
export const getPlanBarColor = (plan: PlanType) => PLAN_COLORS[plan]?.bar || "";

export function hasAccessToPlan(userPlan: PlanType, requiredPlan: PlanType): boolean {
    return PLAN_HIERARCHY[userPlan] >= PLAN_HIERARCHY[requiredPlan];
}

export function isFeatureAvailableInPlan(featureId: string, plan: PlanType): boolean {
    return PLAN_FEATURES[plan]?.includes(featureId) || false;
}

export function getRequiredPlanForFeature(featureId: string): PlanType {
    const planOrder: PlanType[] = ["vip", "elite", "milgapro", "free"];

    for (const plan of planOrder) {
        if (PLAN_FEATURES[plan].includes(featureId)) {
            return plan;
        }
    }

    console.warn(`Unknown feature ID: ${featureId}`);
    return "free";
}

export const createPlanFeatures = (planType: PlanType): PlanFeature[] => {
    const featureMapping = PLAN_FEATURE_MAPPINGS[planType];

    return Object.keys(featureMapping).map((featureId) => ({
        featureId,
        isApplicable: featureMapping[featureId]
    }));
};

export const PRICING_PLANS: SubscriptionsItem[] = [
    {
        id: "milgapo-basic",
        title: "חבילת Basic",
        description: "גישה בסיסית להתאמת מלגות אישית ועדכונים על הגשות עצמאיות.",
        price: 0,
        isFeatured: false,
        features: createPlanFeatures("free"),
        duration_days: null,
        planType: "free",
        colors: PLAN_COLORS.free
    },
    {
        id: "milgapro",
        title: "Milga-Pro",
        description: "חבילה מתקדמת הכוללת מנגנון הגשות אוטומטיות ועדכונים על מלגות חדשות.",
        price: 280,
        isFeatured: false,
        features: createPlanFeatures("milgapro"),
        duration_days: 365,
        planType: "milgapro",
        colors: PLAN_COLORS.milgapro
    },
    {
        id: "milgapo-elite",
        title: "Elite Student",
        description: "חבילה יוקרתית עם כל הפיצ׳רים, כולל מנהל חשבון אישי.",
        price: 350,
        isFeatured: true,
        features: createPlanFeatures("elite"),
        duration_days: 365,
        planType: "elite",
        colors: PLAN_COLORS.elite
    },
    {
        id: "milgapo-vip",
        title: "VIP",
        description: "חבילת פרימיום הכוללת את כל הפיצ׳רים עם תמיכה שנתית.",
        price: 400,
        isFeatured: false,
        features: createPlanFeatures("vip"),
        duration_days: 365,
        planType: "vip",
        colors: PLAN_COLORS.vip
    }
];

export const subscriptions = {
    plans: PRICING_PLANS,
    features: FEATURES,
    planIds: PLAN_IDS,
    planIcons: PLAN_ICONS,
    planColors: PLAN_COLORS,
    planBadgeColors: PLAN_BADGE_COLORS,
    planFeatureMappings: PLAN_FEATURE_MAPPINGS,
    planFeatures: PLAN_FEATURES,
    planHierarchy: PLAN_HIERARCHY,
    texts: TEXTS,
    utils: {
        getPlanTextColor,
        getPlanBgColor,
        getPlanBgColor10,
        getPlanBarColor,
        hasAccessToPlan,
        isFeatureAvailableInPlan,
        getRequiredPlanForFeature,
        createPlanFeatures
    }
};

export default subscriptions;
