import {
    GraduationCapIcon,
    BookCheckIcon,
    ListChecksIcon,
    UploadCloudIcon,
    UserIcon,
    type LucideIcon
} from "lucide-react";

import { PlanType } from "@/lib/subscription-constants";

export const TEXTS = {
    subscribeBanner: "רוצה שנגיש אותך בצורה אוטומטית?",
    personalInfo: "מידע אישי",
    myScholarships: "המלגות שלי",
    settings: "הגדרות",
    dashboard: "המלגות שלי",
    personalDataEntry: "פרטים אישיים",
    documentsUpload: "העלאת מסמכים",
    eligibleScholarships: "בחירת מלגות",
    scholarshipsInProgress: "מלגות בתהליך",
    groups: {
        personalDataGroup: "הזנת פרטים והגשה",
        scholarshipTracking: "מעקב מלגות"
    },
    premium: {
        milgapro: "Pro",
        elite: "Elite",
        vip: "VIP"
    }
};

type NavItem = {
    title: string;
    url: string;
    icon?: LucideIcon;
};

type GroupNavItem = {
    name: string;
    url: string;
    icon?: LucideIcon;
    requiredPlan: PlanType;
};

type SidebarGroup = {
    title: string;
    icon?: LucideIcon;
    items: GroupNavItem[];
};

export const sidebarData: {
    navMain: NavItem[];
    navSecondary: NavItem[];
    personalDataGroup: SidebarGroup;
    scholarshipTrackingGroup: SidebarGroup;
} = {
    navMain: [
        {
            title: TEXTS.dashboard,
            url: "/dashboard",
            icon: GraduationCapIcon
        }
    ],

    personalDataGroup: {
        title: TEXTS.groups.personalDataGroup,
        icon: undefined,
        items: [
            {
                name: TEXTS.personalDataEntry,
                url: "/dashboard/personal-details",
                icon: UserIcon,
                requiredPlan: "free" as PlanType
            },

            {
                name: TEXTS.documentsUpload,
                url: "/dashboard/documents-upload",
                icon: UploadCloudIcon,
                requiredPlan: "milgapro" as PlanType
            }
        ]
    },

    scholarshipTrackingGroup: {
        title: TEXTS.groups.scholarshipTracking,
        icon: undefined,
        items: [
            {
                name: TEXTS.eligibleScholarships,
                url: "/dashboard/eligible-scholarships",
                icon: BookCheckIcon,
                requiredPlan: "milgapro" as PlanType
            },

            {
                name: TEXTS.scholarshipsInProgress,
                url: "/dashboard/scholarships-in-progress",
                icon: ListChecksIcon,
                requiredPlan: "milgapro" as PlanType
            }
        ]
    },
    navSecondary: []
};
