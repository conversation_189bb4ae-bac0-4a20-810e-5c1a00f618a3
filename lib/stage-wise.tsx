import { StagewiseToolbar } from "@stagewise/toolbar-next";
import React from "react";

import { isDevMode } from "@/lib/utils";

export function StagewiseProvider(): React.ReactElement | null {
    if (!isDevMode()) {
        return null;
    }

    try {
        return (
            <StagewiseToolbar
                config={{
                    plugins: []
                }}
            />
        );
    } catch (error) {
        console.error("Failed to render StagewiseToolbar:", error);
        return null;
    }
}
