export const TEXTS = {
    emailLabel: "כתובת אימייל",
    emailPlaceholder: "הזן כתובת אימייל",
    submit: "שמור",
    submitting: "שומר...",
    submitSuccess: "נשמר בהצלחה",
    createError: "שגיאה ביצירת פרטי יצירת קשר",
    updateError: "שגיאה בעדכון פרטי יצירת קשר",
    loadingError: "שגיאה בטעינת פרטי יצירת קשר",
    cancel: "ביטול",
    emailError: "יש להזין כתובת אימייל תקינה",
    emailRequired: "יש להזין כתובת אימייל",
    editPageTitle: "עריכת פנייה",
    editErrorNotFound: "שגיאה: מספר פנייה לא נמצא."
};

export const ADMIN_CONTACTS_TEXTS = {
    pageTitle: "פניות",
    loading: "טוען פניות...",
    errorPrefix: "שגיאה בטעינת הפניות:",
    noContacts: "לא נמצאו פניות",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק פנייה זו? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת הפנייה",
    deleteSuccess: "הפנייה נמחקה בהצלחה",
    deletingContact: "מוחק פנייה...",
    newContact: "פנייה חדשה",
    email: "כתובת אימייל",
    createdAt: "תאריך יצירה",
    actions: "פעולות",
    edit: "עריכה",
    cancel: "ביטול"
};

export interface ContactFormValues {
    email: string;
}

export const defaultValues: ContactFormValues = {
    email: ""
};
