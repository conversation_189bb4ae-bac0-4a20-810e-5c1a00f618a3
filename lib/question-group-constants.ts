export const TEXTS = {
    // Form UI texts
    nameLabel: "שם קבוצת השאלות",
    nameDescription: "שם המזהה את הקבוצה",
    namePlaceholder: "לדוגמה: שאלות בסיס, שאלות אקדמיות, שאלות כלליות",
    nameRequired: "שם קבוצת השאלות נדרש",
    NAME_REQUIRED: "שם קבוצת השאלות נדרש",
    createButtonText: "צור קבוצת שאלות",
    updateButtonText: "עדכן קבוצת שאלות",
    cancelButtonText: "בטל",
    createSuccessMessage: "קבוצת השאלות נוצרה בהצלחה",
    editSuccessMessage: "קבוצת השאלות עודכנה בהצלחה",
    editNotFoundMessage: "קבוצת השאלות לא נמצאה",
    editErrorMessage: "שגיאה בטעינת קבוצת השאלות",
    loadingMessage: "טוען קבוצת שאלות...",
    unknownError: "שגיאה לא ידועה",

    // Server action texts
    GROUP_DELETE_ERROR: "שגיאה במחיקת קבוצת השאלות. אנא נסה שנית.",
    GROUP_DELETE_SUCCESS: "קבוצת השאלות נמחקה בהצלחה.",
    GROUP_IN_USE_ERROR: "לא ניתן למחוק קבוצת שאלות זו כיוון שהיא בשימוש בשאלות.",
    GROUP_CREATE_ERROR: "שגיאה ביצירת קבוצת השאלות",
    GROUP_UPDATE_ERROR: "שגיאה בעדכון קבוצת השאלות",
    GROUP_FETCH_ERROR: "שגיאה בטעינת קבוצת השאלות",
    GROUP_NOT_FOUND: "קבוצת השאלות לא נמצאה",
    DELETION_IN_USE_PREFIX: "לא ניתן למחוק קבוצת שאלות זו כיוון שהיא בשימוש ב-",
    DELETION_IN_USE_SUFFIX: " שאלות.",
    FETCH_QUESTION_GROUPS_ERROR: "שגיאה בטעינת קבוצות השאלות",
    formTitle: "קבוצת שאלות",
    submitButton: "שמור",
    submitting: "שומר...",
    successMessage: "קבוצת השאלות נשמרה בהצלחה",
    errorMessage: "שגיאה בשמירת קבוצת השאלות"
};

export interface QuestionGroupFormValues {
    name: string;
}

export const defaultValues: QuestionGroupFormValues = {
    name: ""
};

export const ADMIN_QUESTION_GROUPS_TEXTS = {
    groupsTabTitle: "קבוצות שאלות",
    groupsPageTitle: "קבוצות שאלות",
    newGroup: "קבוצת שאלות חדשה",
    loadingGroups: "טוען קבוצות שאלות...",
    errorPrefixGroups: "שגיאה בטעינת קבוצות השאלות:",
    noGroups: "לא נמצאו קבוצות שאלות",
    name: "שם",
    questionsCount: "מספר שאלות",
    deleteConfirmTitleGroup: "אישור מחיקה",
    deleteConfirmDescriptionGroup: "האם אתה בטוח שברצונך למחוק קבוצת שאלות זו? לא ניתן לשחזר פעולה זו.",
    deleteErrorGroup: "שגיאה במחיקת קבוצת השאלות",
    deleteSuccessGroup: "קבוצת השאלות נמחקה בהצלחה",
    deletingGroup: "מוחק קבוצת שאלות..."
};
