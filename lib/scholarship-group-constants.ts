import {
    Award,
    BookOpen,
    Briefcase,
    Building2,
    Calculator,
    Code,
    Globe,
    GraduationCap,
    Heart,
    Library,
    LucideIcon,
    MapPin,
    Medal,
    Microscope,
    Music,
    Palette,
    Pencil,
    School,
    Shield,
    Star,
    Trophy,
    Users
} from "lucide-react";

import { Option } from "@/components/forms/fields/dropdown-base";

export interface ScholarshipGroupFormValues {
    title: string;
    slug: string;
    description: string;
    icon: Option;
    image_file: File | null;
}

export const ICON_MAP: Record<string, LucideIcon> = {
    "graduation-cap": GraduationCap,
    certificate: Medal,
    award: Award,
    shield: Shield,
    books: BookOpen,
    building: Building2,
    map: MapPin,
    star: Star,
    school: School,
    library: Library,
    "book-open": BookOpen,
    users: Users,
    trophy: Trophy,
    medal: Medal,
    heart: Heart,
    globe: Globe,
    briefcase: Briefcase,
    microscope: Microscope,
    pencil: Pencil,
    calculator: Calculator,
    music: Music,
    palette: Palette,
    code: Code
};

export const iconOptions = [
    { id: "graduation-cap", label: "כובע סיום" },
    { id: "certificate", label: "תעודה" },
    { id: "award", label: "פרס" },
    { id: "shield", label: "מגן" },
    { id: "books", label: "ספרים" },
    { id: "building", label: "בניין" },
    { id: "map", label: "מפה" },
    { id: "star", label: "כוכב" },
    { id: "school", label: "בית ספר" },
    { id: "library", label: "ספרייה" },
    { id: "book-open", label: "ספר פתוח" },
    { id: "users", label: "קבוצת סטודנטים" },
    { id: "trophy", label: "גביע" },
    { id: "medal", label: "מדליה" },
    { id: "heart", label: "לב" },
    { id: "globe", label: "גלובוס" },
    { id: "briefcase", label: "תיק עבודה" },
    { id: "microscope", label: "מיקרוסקופ" },
    { id: "pencil", label: "עיפרון" },
    { id: "calculator", label: "מחשבון" },
    { id: "music", label: "מוזיקה" },
    { id: "palette", label: "פלטה" },
    { id: "code", label: "קוד" }
];

export const defaultScholarshipGroupFormValues: ScholarshipGroupFormValues = {
    title: "",
    slug: "",
    description: "",
    icon: iconOptions[0],
    image_file: null
};

export const TEXTS = {
    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "קבוצת המלגות עודכנה בהצלחה",
    editErrorMessage: "שגיאה בעדכון קבוצת המלגות",
    editLoadingMessage: "טוען קבוצת מלגות...",
    editNotFoundMessage: "קבוצת המלגות לא נמצאה",
    updateButtonText: "עדכן קבוצת מלגות",
    cancelButtonText: "ביטול",

    createSuccessMessage: "קבוצת המלגות נוצרה בהצלחה",
    createErrorMessage: "שגיאה ביצירת קבוצת המלגות",
    createButtonText: "צור קבוצת מלגות",

    deleteErrorMessage: "שגיאה במחיקת קבוצת המלגות. אנא נסה שנית.",
    deleteAssociationError: "לא ניתן למחוק קבוצת מלגות המקושרת למלגות.",

    groupNameLabel: "שם הקבוצה",
    groupNamePlaceholder: "הזן את שם קבוצת המלגות",
    groupNameRequired: "שם הקבוצה הוא שדה חובה",

    groupSlugLabel: "קידומת הקבוצה",
    groupSlugPlaceholder: "הזן את קידומת קבוצת המלגות",
    groupSlugPattern: "כתובת הקבוצה יכולה להכיל רק אותיות באנגלית ומקפים",
    groupSlugRequired: "קידומת הקבוצה הוא שדה חובה",

    descriptionLabel: "תיאור",
    descriptionPlaceholder: "תאר את קבוצת המלגות",
    descriptionRequired: "תיאור הקבוצה הוא שדה חובה",

    iconLabel: "אייקון",
    iconPlaceholder: "בחר אייקון לקבוצה",
    iconRequired: "יש לבחור אייקון לקבוצה",

    imageLabel: "תמונת הקבוצה",
    imageRequired: "יש להעלות תמונה לקבוצה",

    slugExistsError: "קידומת זו כבר קיימת במערכת. אנא בחר קידומת אחרת.",

    webpDescription: "WebP הוא פורמט תמונה מודרני שמספק איכות גבוהה בנפח קטן",
    fileTypeError: "ניתן להעלות קבצי WebP בלבד. אנא המר את התמונה ל-WebP לפני ההעלאה",
    webpOnlyError: "רק קבצי WebP מותרים",
    fileSizeExceededError: "גודל הקובץ חורג מהמותר (5MB)",
    imageUrlGenerationError: "שגיאה ביצירת קישור לתמונה",
    imageUrlUpdateError: "שגיאה בעדכון קישור התמונה",
    dragDropText: "גרור קובץ לכאן או לחץ לבחירה",
    selectedFileText: "קובץ נבחר",
    fileSizeError: "הקובץ גדול מדי. גודל מקסימלי הוא",
    fileFormatDesc: "קבצי WebP בלבד | גודל מקסימלי:",

    apiErrorGeneral: "שגיאה בטעינת קבוצת המלגות",
    apiErrorUpload: "שגיאה בהעלאת התמונה"
};
export interface ScholarshipGroup {
    id: string;
    title: string;
    description: string;
    slug: string;
    icon: string;
    image_url: string | null;
    created_at: string;
    updated_at: string;
    scholarships_count?: number;
    isNew?: boolean;
}
