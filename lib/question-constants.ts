import { Constants, Database, Tables } from "@/types/database.types";

export const TEXTS = {
    loading: "טוען...",
    successMessage: "השאלה נוצרה בהצלחה",
    successUpdateMessage: "השאלה עודכנה בהצלחה",
    errorMessage: "שגיאה ביצירת השאלה",
    errorUpdateMessage: "שגיאה בעדכון השאלה",
    dataFetchError: "שגיאה בטעינת הנתונים",
    titleLabel: "כותרת השאלה",
    titlePlaceholder: "הזן את כותרת השאלה",
    typeLabel: "סוג השאלה",
    typePlaceholder: "בחר את סוג השאלה",
    sectionLabel: "קטגוריית השאלה",
    sectionPlaceholder: "בחר את קטגוריית השאלה",
    sectionOptionPersonal: "פרטים אישיים",
    sectionOptionData: "הזנת נתונים",
    sectionOptionScholarship: "מלג<PERSON> ספציפית",
    tooltipLabel: "טקסט מציין (Tooltip)",
    tooltipPlaceholder: "הזן טקסט מציין",
    groupLabel: "קבוצת שאלות",
    groupPlaceholder: "בחר קבוצת שאלות",
    isRequiredLabel: "האם שדה חובה?",
    placeholderLabel: "טקסט מרמז (Placeholder)",
    placeholderPlaceholder: "הזן טקסט מרמז",
    optionsLabel: "אפשרויות (הפרד באמצעות ירידת שורה)",
    optionsPlaceholder: "הכנס אפשרויות, כל אחת בשורה חדשה",
    minValueLabel: "ערך מינימלי",
    minValuePlaceholder: "הכנס ערך מינימלי",
    maxValueLabel: "ערך מקסימלי",
    maxValuePlaceholder: "הכנס ערך מקסימלי",
    allowFutureDateLabel: "האם לאפשר תאריך עתידי?",
    allowPastDateLabel: "האם לאפשר תאריך עבר?",
    enableSearchLabel: "האם לאפשר חיפוש באפשרויות?",
    selectScholarshipsLabel: "בחר מלגות",
    selectScholarshipsPlaceholder: "בחר מלגות",
    enableDependenciesLabel: "האם השאלה תלויה בשאלות אחרות?",
    dependenciesLabel: "תלויות בשאלות אחרות",
    createQuestion: "צור שאלה",
    updateQuestion: "עדכן שאלה",
    cancelButton: "ביטול",
    submitting: "שולח...",
    typeShortText: "טקסט קצר",
    typeLongText: "טקסט ארוך",
    typeSingleSelect: "בחירה יחידה",
    typeMultiSelect: "בחירה מרובה",
    typeNumberInput: "מספר",
    typeDatePicker: "תאריך",
    typeAddressSelect: "כתובת",
    typeBankSelect: "בנק",
    optionRequired: "חובה להוסיף לפחות אפשרות אחת",
    optionValidLabelRequired: "חובה להוסיף לפחות אפשרות אחת עם תווית תקינה",
    optionEmptyLabelsFound: "נמצאו",
    optionEmptyLabelsError: "אפשרויות עם תווית ריקה או לא תקינה. יש למלא את כל התוויות",
    optionDuplicateLabelsError: "נמצאו תוויות כפולות. כל אפשרות חייבת להיות עם תווית ייחודית",
    patternLabel: "תבנית תיקוף (Regex)",
    patternPlaceholder: "הכנס תבנית תיקוף",
    patternMessageLabel: "הודעת שגיאה לתבנית",
    patternMessagePlaceholder: "הכנס הודעת שגיאה שתוצג כאשר התבנית לא תקינה"
};

export const ADMIN_QUESTIONS_TEXTS = {
    pageTitle: "שאלות",
    newQuestion: "שאלה חדשה",
    loading: "טוען שאלות...",
    errorPrefix: "שגיאה בטעינת השאלות:",
    noQuestions: "לא נמצאו שאלות",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק שאלה זו? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת השאלה",
    deleteSuccess: "השאלה נמחקה בהצלחה",
    deletingQuestion: "מוחק שאלה...",
    questionsTabTitle: "שאלות",
    title: "כותרת",
    type: "סוג",
    group: "קבוצה",
    section: "שלב",
    required: "חובה",
    actions: "פעולות",
    edit: "עריכה",
    cancel: "ביטול"
};

// Database types
export type QuestionType = Database["public"]["Enums"]["question_type"];
export type QuestionSection = Database["public"]["Enums"]["question_section"];

export const PREDEFINED_TYPES = Constants.public.Enums.question_type;

export const EXCLUDED_DEPENDENCY_TYPES: QuestionType[] = ["short_text", "long_text", "address_select", "bank_select"];

export const TYPE_TRANSLATION_KEYS: Record<QuestionType, keyof typeof TEXTS> = {
    short_text: "typeShortText",
    long_text: "typeLongText",
    single_select: "typeSingleSelect",
    multi_select: "typeMultiSelect",
    number_input: "typeNumberInput",
    date_picker: "typeDatePicker",
    address_select: "typeAddressSelect",
    bank_select: "typeBankSelect"
};

export const SECTION_OPTIONS = [
    { id: "personal_details", label: TEXTS.sectionOptionPersonal },
    { id: "data_entry", label: TEXTS.sectionOptionData },
    { id: "specific_scholarship", label: TEXTS.sectionOptionScholarship }
];

// Option type for form selects
export type Option = { id: string; label: string };

// Database types for questions
export type Scholarship = Pick<Tables<"scholarships">, "id" | "title">;
export type QuestionFromDB = Tables<"questions">;

// Form types
export type QuestionMetadataFromDB = {
    label?: string;
    required?: boolean;
    tooltip?: string;
    placeholder?: string;
    options?: Array<Option>;
    min?: number;
    max?: number;
    isFutureAllowed?: boolean;
    allowPast?: boolean;
    showSearch?: boolean;
    pattern?: string;
    patternMessage?: string;
};

export type ConditionValue =
    | Option[]
    | number
    | { min?: number; max?: number }
    | { operator: "greater_than" | "less_than"; days_from_today: number };

export interface DependencyFormData {
    question_id: Option;
    condition_type?: string;
    condition_value?: ConditionValue;
}

export interface QuestionFormValues {
    title: string;
    type: Option;
    tooltip: string;
    group_id: Option;
    is_required: boolean;
    section: Option;
    scholarship_ids: Array<Option>;
    enable_dependencies: boolean;
    dependencies: DependencyFormData[];
    metadata: {
        placeholder?: string;
        options?: Array<Option>;
        min?: number;
        max?: number;
        isFutureAllowed?: boolean;
        allowPast?: boolean;
        showSearch?: boolean;
        pattern?: string;
        patternMessage?: string;
    };
}

export type DependencyFromDB = {
    question_id: string;
    condition_type?: string;
    condition_value?: ConditionValue;
};

// Default form values
export const defaultValues: QuestionFormValues = {
    title: "",
    type: { id: "short_text", label: TEXTS.typeShortText },
    tooltip: "",
    group_id: { id: "", label: "" },
    is_required: true,
    section: { id: "data_entry", label: TEXTS.sectionOptionData },
    scholarship_ids: [],
    enable_dependencies: false,
    dependencies: [],
    metadata: {
        isFutureAllowed: false,
        allowPast: true,
        showSearch: false
    }
};

// Utility functions
export const extractId = (value: Option | string): string => (typeof value === "object" ? value.id : value);

export const getSectionLabel = (section: QuestionSection): string => {
    return SECTION_OPTIONS.find((option) => option.id === section)?.label || TEXTS.sectionOptionData;
};

export const createSelectOption = (id: string, label: string): Option => ({ id, label });

export const mapScholarshipToOption = (scholarship: Scholarship): Option =>
    createSelectOption(scholarship.id, scholarship.title);

export const isValidOption = (opt: unknown): opt is Option => {
    if (!opt || typeof opt !== "object") return false;
    if (!("label" in opt)) return false;
    const label = (opt as { label: unknown }).label;
    if (label === null || label === undefined) return false;
    if (typeof label !== "string" && typeof label !== "number") return false;
    return label.toString().trim() !== "";
};

export const validateSelectOptions = (data: QuestionFormValues): string | null => {
    const { type, metadata } = data;

    if (type.id !== "single_select" && type.id !== "multi_select") {
        return null;
    }

    if (!metadata.options || !Array.isArray(metadata.options)) {
        return TEXTS.optionRequired;
    }

    if (metadata.options.length === 0) {
        return TEXTS.optionRequired;
    }

    const validOptions = metadata.options.filter(isValidOption);

    if (validOptions.length === 0) {
        return TEXTS.optionValidLabelRequired;
    }

    const invalidOptions = metadata.options.filter((opt) => !isValidOption(opt));

    if (invalidOptions.length > 0) {
        return `${TEXTS.optionEmptyLabelsFound} ${invalidOptions.length} ${TEXTS.optionEmptyLabelsError}`;
    }

    const labels = validOptions.map((opt) => opt.label.toString().trim().toLowerCase());
    const duplicateLabels = labels.filter((label, index) => labels.indexOf(label) !== index);

    if (duplicateLabels.length > 0) {
        return TEXTS.optionDuplicateLabelsError;
    }

    return null;
};

// Type options for forms
export const typeOptions = PREDEFINED_TYPES.map((type) => createSelectOption(type, TEXTS[TYPE_TRANSLATION_KEYS[type]]));
