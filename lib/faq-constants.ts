import { type Tables } from "@/types/database.types";
import { sanitizeText } from "@/utils/sanitization";

export type Faq = Tables<"faq">;

export interface FaqFormValues {
    question: string;
    answer: string;
}

export const defaultValues: FaqFormValues = {
    question: "",
    answer: ""
};

export const TEXTS = {
    FAQ_DELETE_ERROR: "שגיאה במחיקת השאלה. אנא נסה שנית.",
    FAQ_FETCH_ERROR: "שגיאה בטעינת השאלה",
    FAQ_NOT_FOUND: "השאלה לא נמצאה",
    FAQ_UPDATE_ERROR: "שגיאה בעדכון השאלה",
    FAQ_CREATE_ERROR: "שגיאה ביצירת השאלה",
    FAQ_REORDER_ERROR: "שגיאה בעדכון סדר השאלות",

    QUESTION_REQUIRED: "שאלה היא שדה חובה",
    ANSWER_REQUIRED: "תשובה היא שדה חובה",

    EDIT_SUCCESS_MESSAGE: "השאלה עודכנה בהצלחה",
    EDIT_ERROR_MESSAGE: "שגיאה בעדכון השאלה",
    EDIT_LOADING_MESSAGE: "טוען שאלה...",
    EDIT_NOT_FOUND_MESSAGE: "השאלה לא נמצאה",
    UPDATE_BUTTON_TEXT: "עדכן שאלה",
    CANCEL_BUTTON_TEXT: "ביטול",
    CREATE_SUCCESS_MESSAGE: "השאלה נוצרה בהצלחה",
    CREATE_ERROR_MESSAGE: "שגיאה ביצירת השאלה",
    CREATE_BUTTON_TEXT: "צור שאלה",
    QUESTION_LABEL: "שאלה",
    QUESTION_PLACEHOLDER: "הזן שאלה",
    ANSWER_LABEL: "תשובה",
    ANSWER_PLACEHOLDER: "הזן תשובה",

    PAGE_TITLE: "שאלות ותשובות",
    NEW_FAQ: "שאלה חדשה",
    LOADING: "טוען שאלות ותשובות...",
    ERROR_PREFIX: "שגיאה בטעינת השאלות:",
    NO_FAQS: "לא נמצאו שאלות ותשובות",
    QUESTION: "שאלה",
    ANSWER: "תשובה",
    INDEX: "מס'",
    CREATED_AT: "תאריך יצירה",
    UPDATED_AT: "תאריך עדכון",
    ACTIONS: "פעולות",
    EDIT: "עריכה",
    DELETE_CONFIRM_TITLE: "אישור מחיקה",
    DELETE_CONFIRM_DESCRIPTION: "האם אתה בטוח שברצונך למחוק שאלה זו? לא ניתן לשחזר פעולה זו.",
    CANCEL: "ביטול",
    CONFIRM_DELETE: "מחק",
    DELETE_ERROR: "שגיאה במחיקת השאלה",
    DELETE_SUCCESS: "השאלה נמחקה בהצלחה",
    DELETING_FAQ: "מוחק שאלה..."
};

export function validateFaqData(data: FaqFormValues): { valid: boolean; error?: string } {
    if (!data.question || data.question.trim().length < 1) {
        return { valid: false, error: TEXTS.QUESTION_REQUIRED };
    }

    if (!data.answer || data.answer.trim().length < 1) {
        return { valid: false, error: TEXTS.ANSWER_REQUIRED };
    }

    return { valid: true };
}

export function sanitizeFaqData(data: FaqFormValues) {
    return {
        question: sanitizeText(data.question, 500),
        answer: sanitizeText(data.answer, 2000)
    };
}
