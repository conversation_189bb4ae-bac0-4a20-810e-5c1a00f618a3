export const TEXTS = {
    loadingDocuments: "טוען רשימת מסמכים...",
    errorLoadingDocuments: "שגיאה בטעינת רשימת המסמכים",
    noDocumentsRequired: "לא נדרשים מסמכים כרגע עבור המלגות הזמינות לך.",
    allDocumentsUploaded: "כל הכבוד! כל המסמכים הנדרשים הועלו בהצלחה.",
    documentStatusUploaded: "הועלה",
    documentStatusUploading: "מעלה...",
    documentStatusPending: "ממתין להעלאה",
    viewUploadedDocument: "צפה במסמך",
    uploadNewLabel: "העלה מסמך",
    replaceLabelPrefix: "החלף את",
    dragDropText: "גרור קובץ לכאן או לחץ לבחירה",
    fileFormatDescriptionDefault: (maxSize: number) => `מקסימום ${maxSize}MB.`,
    selectedFileText: "קובץ נבחר",
    fileTypeErrorMessage: "סוג קובץ לא נתמך.",
    defaultFileSizeErrorMessage: (maxSize: number) => `הקובץ גדול מדי. גודל מקסימלי: ${maxSize}MB.`,
    retryButton: "נסה שנית",
    allDocumentsUploadedSubtitle: "תוכל עדיין להחליף מסמכים קיימים אם תרצה.",
    fileSizeErrorMessageSpecific: (maxSize: number) => `הקובץ גדול מדי. גודל מקסימלי למסמך זה: ${maxSize}MB.`,
    canUploadNewVersionPrompt: "ניתן להעלות גרסה חדשה למטה כדי להחליף את הקובץ הקיים.",
    fileFormatDescriptionWithMimeType: (mimeTypeDescription: string, maxSize: number) =>
        `${mimeTypeDescription} עד ${maxSize}MB.`,
    uncategorizedGroupName: "כללי",
    defaultAcceptedFileTypesDescription: "PDF, JPG, PNG",
    uploadNewVersionLabel: "העלה גרסה חדשה",
    viewExampleFile: "צפה בקובץ לדוגמה",
    exampleDocumentLabel: "מסמך לדוגמא",
    viewExampleLinkLabel: "צפה בקישור לדוגמה",
    documentsUploadedProgress: (uploaded: number, total: number) => `${uploaded}/${total}`,
    completionPercentage: (percentage: number) => `השלמת ${percentage}% מהמסמכים הנדרשים`,
    invalidUrlError: "הקישור אינו תקין או אינו בטוח לפתיחה",
    uploading: "מעלה קובץ...",
    invalidDocumentIdError: "מזהה המסמך לא תקין",
    uploadSuccessMessage: "המסמך הועלה בהצלחה",
    uploadErrorMessage: "שגיאה בהעלאת המסמך",
    fallbackFileName: "קובץ",

    // Server action error messages
    userNotAuthenticatedError: "משתמש לא מחובר",
    noValidFileSelectedError: "לא נבחר קובץ תקין",
    invalidDocumentTypeIdError: "מזהה סוג המסמך לא תקין",
    documentTypeNotFoundError: "סוג המסמך לא נמצא",
    fileTypeNotSupportedError: (fileType: string, allowedTypes: string) =>
        `סוג הקובץ ${fileType} לא נתמך. סוגים מותרים: ${allowedTypes}`,
    fileTooLargeError: (maxSize: number) => `הקובץ גדול מדי. גודל מקסימלי: ${maxSize}MB`,
    fileMissingExtensionError: "הקובץ חסר סיומת",
    fileUploadStorageError: "שגיאה בהעלאת הקובץ",
    generalUploadError: "שגיאה כללית בהעלאת המסמך",
    errorLoadingDocumentTypesError: "שגיאה בטעינת סוגי המסמכים",
    generalLoadingDocumentsError: "שגיאה כללית בטעינת המסמכים"
};

export interface DocumentUploadFormData {
    [key: string]: File | null;
}

export interface UserDocumentStatus {
    documentTypeId: string;
    documentTypeName: string;
    documentTypeDescription?: string | null;
    documentGroupName?: string | null;
    isUploaded: boolean;
    allowedMimeTypes?: string[] | null;
    maxFileSizeInMB?: number | null;
    uploadedFileUrl?: string | null;
    exampleFileUrl?: string | null;
    link_url?: string | null;
}

export const MIME_TYPE_FRIENDLY_NAMES: Record<string, string> = {
    "application/pdf": "PDF",
    "image/jpeg": "JPEG",
    "image/jpg": "JPG",
    "image/png": "PNG",
    "application/msword": "Word (DOC)",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "Word (DOCX)"
};

export function getFriendlyFileTypeNames(acceptedFileTypes?: string[] | null): string {
    if (!acceptedFileTypes || acceptedFileTypes.length === 0) {
        return TEXTS.defaultAcceptedFileTypesDescription;
    }

    const friendlyNames = acceptedFileTypes
        .map((type) => MIME_TYPE_FRIENDLY_NAMES[type] || type.split("/").pop()?.toUpperCase() || type)
        .filter((name) => name);

    if (friendlyNames.length === 0) {
        return TEXTS.defaultAcceptedFileTypesDescription;
    }
    return friendlyNames.join(", ");
}

export function getUploadStatusBadge(uploadedCount: number, totalCount: number) {
    if (uploadedCount === 0) {
        return {
            variant: "destructive" as const,
            className: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800"
        };
    } else if (uploadedCount === totalCount) {
        return {
            variant: "default" as const,
            className:
                "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800"
        };
    } else {
        return {
            variant: "secondary" as const,
            className:
                "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800"
        };
    }
}

export function isValidAndSafeUrl(url: string): boolean {
    if (!url || typeof url !== "string") {
        return false;
    }

    try {
        const parsedUrl = new URL(url.trim());

        if (!["http:", "https:"].includes(parsedUrl.protocol)) {
            return false;
        }

        if (!parsedUrl.hostname || parsedUrl.hostname.length === 0) {
            return false;
        }

        const hostname = parsedUrl.hostname.toLowerCase();

        if (
            hostname === "127.0.0.1" ||
            hostname === "0.0.0.0" ||
            hostname.startsWith("192.168.") ||
            hostname.startsWith("10.") ||
            hostname.startsWith("172.16.") ||
            hostname.includes("..")
        ) {
            return false;
        }

        const fullUrl = parsedUrl.href.toLowerCase();
        const suspiciousPatterns = [
            "javascript:",
            "data:",
            "blob:",
            "vbscript:",
            "file:",
            "ftp:",
            "<script",
            "javascript%3a",
            "data%3a",
            "%3cscript"
        ];

        return !suspiciousPatterns.some((pattern) => fullUrl.includes(pattern));
    } catch (error) {
        console.error("Error parsing URL:", error);
        return false;
    }
}
