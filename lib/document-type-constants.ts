import { type TablesInsert, type TablesUpdate } from "@/types/database.types";

export const TEXTS = {
    // Server action error messages
    DOCUMENT_TYPE_UPDATE_ERROR: "שגיאה בעדכון סוג המסמך. אנא נסה שנית.",
    DOCUMENT_TYPE_NOT_FOUND: "סוג המסמך לא נמצא.",
    DOCUMENT_TYPE_DELETE_ERROR: "שגיאה במחיקת סוג המסמך. אנא נסה שנית.",
    DOCUMENT_TYPE_CREATE_ERROR: "שגיאה ביצירת סוג המסמך. אנא נסה שנית.",
    DOCUMENT_TYPE_LINK_ERROR: "שגיאה בקישור סוג המסמך למלגה. אנא נסה שנית.",
    DOCUMENT_TYPE_UNLINK_ERROR: "שגיאה בביטול קישור סוג המסמך מהמלגה. אנא נסה שנית.",
    FETCH_ERROR: "שגיאה בטעינת סוגי מסמכים",
    DOCUMENT_TYPES_FETCH_ERROR: "שגיאה בטעינת סוגי המסמכים",

    // Authentication and authorization error messages
    AUTH_REQUIRED: "נדרשת הזדהות למערכת לביצוע פעולה זו.",
    ADMIN_REQUIRED: "נדרשות הרשאות מנהל לביצוע פעולה זו.",

    // Form validation messages
    GROUP_REQUIRED: "יש לבחור קבוצה",
    NAME_REQUIRED: "שם סוג המסמך הוא שדה חובה",
    MIME_TYPE_REQUIRED: "יש לבחור לפחות סוג קובץ אחד",
    MAX_FILE_SIZE_REQUIRED: "יש להזין גודל קובץ מקסימלי",
    LINK_URL_INVALID: "כתובת הקישור אינה תקינה",

    // Success messages
    CREATE_SUCCESS: "סוג המסמך נוצר בהצלחה",
    UPDATE_SUCCESS: "סוג המסמך עודכן בהצלחה",
    FILE_UPLOAD_SUCCESS: "הקובץ הועלה בהצלחה",

    // Form labels
    GROUP_LABEL: "קבוצת מסמכים",
    GROUP_PLACEHOLDER: "בחר קבוצה",
    NAME_LABEL: "שם סוג המסמך",
    NAME_PLACEHOLDER: "הזן את שם סוג המסמך",
    DESCRIPTION_LABEL: "תיאור",
    DESCRIPTION_PLACEHOLDER: "הזן תיאור לסוג המסמך",
    LINK_URL_LABEL: "קישור",
    LINK_URL_PLACEHOLDER: "הזן קישור לדוגמה או תבנית",
    EXAMPLE_FILE_LABEL: "קובץ לדוגמה",
    ALLOWED_MIME_TYPES_LABEL: "סוגי קבצים מותרים",
    ALLOWED_MIME_TYPES_PLACEHOLDER: "בחר סוגי קבצים מותרים",
    MAX_FILE_SIZE_LABEL: "גודל קובץ מקסימלי (MB)",
    MAX_FILE_SIZE_PLACEHOLDER: "הזן גודל קובץ מקסימלי במגה-בייט",

    // Button texts
    CREATE_BUTTON: "צור סוג מסמך",
    UPDATE_BUTTON: "עדכן סוג מסמך",
    CANCEL_BUTTON: "ביטול",

    // Loading states
    LOADING_DOCUMENT_TYPE: "טוען סוג מסמך...",
    UPLOADING_FILE: "מעלה קובץ...",

    // File upload texts
    DRAG_DROP_TEXT: "גרור קובץ לכאן או לחץ לבחירת קובץ",
    SELECTED_FILE_TEXT: "קובץ נבחר",
    PDF_FILE_TYPE_DESCRIPTION: "קובץ PDF בלבד",
    FILE_TYPE_ERROR: "סוג הקובץ אינו נתמך",
    UPLOAD_ERROR: "שגיאה בהעלאת הקובץ",

    // Functions for dynamic text
    FILE_FORMAT_DESCRIPTION: (maxSize: number) => `גודל מקסימלי: ${maxSize}MB`,
    FILE_SIZE_ERROR: (maxSize: number) => `גודל הקובץ חורג מהמותר (${maxSize}MB)`,

    // Error messages
    ERROR_MESSAGE: "שגיאה בפעולה",
    DOCUMENT_TYPE_NOT_FOUND_MESSAGE: "סוג המסמך לא נמצא",

    editTitle: "עריכת סוג מסמך",
    newDocumentTypeTitle: "סוג מסמך חדש",
    errorNotFound: "שגיאה: מספר סוג מסמך לא נמצא.",

    // Page and table texts
    pageTitle: "סוגי מסמכים",
    newDocumentType: "סוג מסמך חדש",
    loading: "טוען סוגי מסמכים...",
    errorPrefix: "שגיאה בטעינת סוגי המסמכים:",
    noDocumentTypes: "לא נמצאו סוגי מסמכים",
    name: "שם",
    description: "תיאור",
    allowedMimeTypes: "סוגי קבצים מותרים",
    actions: "פעולות",
    edit: "עריכה",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק סוג מסמך זה? לא ניתן לשחזר פעולה זו.",
    cancel: "ביטול",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת סוג המסמך",
    deleteSuccess: "סוג המסמך נמחק בהצלחה",
    deletingDocumentType: "מוחק סוג מסמך...",
    documentTypesTabTitle: "סוגי מסמכים"
};

export interface DocumentTypeFormValues {
    name: string;
    description: string;
    link_url: string;
    example_file_path: string | null;
    allowed_mime_types: string[];
    group_id: { id: string; label: string } | null;
    max_file_size_mb: number;
}

export const defaultValues: DocumentTypeFormValues = {
    group_id: null,
    name: "",
    description: "",
    link_url: "",
    example_file_path: null,
    allowed_mime_types: [],
    max_file_size_mb: 10
};

// MIME type options for the form
export const MIME_TYPE_OPTIONS = [
    { id: "application/pdf", label: "PDF" },
    { id: "image/jpeg", label: "תמונת JPEG" },
    { id: "image/png", label: "תמונת PNG" },
    { id: "image/webp", label: "תמונת WEBP" }
];

// Helper types for server actions
export type DocumentTypeInsert = TablesInsert<"document_types">;
export type DocumentTypeUpdate = TablesUpdate<"document_types">;

// Form data type for server actions (with group_id handling)
export interface DocumentTypeFormData {
    name: string;
    description: string;
    example_file_path: string | null;
    link_url: string | null;
    allowed_mime_types: string[];
    group_id: { id: string; label: string } | string;
    max_file_size_mb?: number;
}
