import { type Tables } from "@/types/database.types";

type ScholarshipRow = Tables<"scholarships">;

export type ScholarshipWithGroups = ScholarshipRow & {
    link_scholarship_to_scholarship_groups?: Array<{
        scholarship_group_id: string;
        scholarship_id: string;
        created_at: string | null;
        groups_scholarship: {
            title: string;
        };
    }>;
    is_public: boolean;
};

export const TEXTS = {
    SCHOLARSHIP_DELETE_ERROR: "שגיאה במחיקת המלגה. אנא נסה שנית.",
    SCHOLARSHIP_DELETE_SUCCESS: "המלגה נמחקה בהצלחה.",
    SCHOLARSHIPS_FETCH_ERROR: "שגיאה בטעינת המלגות.",
    ENTITY_FETCH_ERROR: "שגיאה בטעינת הישויות",
    formTitleEdit: "עריכת מלגה",
    formSubmitCreate: "הוספה",
    formSubmitUpdate: "עדכו<PERSON>",
    newScholarshipPageTitle: "מלגה חדשה"
};

export const ADMIN_SCHOLARSHIPS_TEXTS = {
    pageTitle: "מלגות",
    newScholarship: "מלגה חדשה",
    loading: "טוען מלגות...",
    errorPrefix: "שגיאה בטעינת המלגות:",
    noScholarships: "לא נמצאו מלגות",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק מלגה זו? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת המלגה",
    deleteSuccess: "המלגה נמחקה בהצלחה",
    deletingScholarship: "מוחק מלגה..."
};
