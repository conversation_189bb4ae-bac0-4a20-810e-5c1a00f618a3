export const TEXTS = {
    // Form labels - Signup
    createAccount: "יצירת חשבון",
    email: "אימיי<PERSON>",
    emailPlaceholder: "הזן כתובת אימייל",
    signUp: "הרשמה",
    signUpWithGoogle: "הרש<PERSON><PERSON> עם Google",
    signUpWithFacebook: "הרשמה עם Facebook",
    signingUp: "טוען...",
    loading: "טוען...",
    // Form labels - Login
    signInTitle: "התחבר לחשבון",
    signInWithGoogle: "התחבר עם Google",
    signInWithFacebook: "התחבר עם Facebook",
    signingIn: "מתחבר...",
    // Checkboxes
    termsLabel: {
        before: "אני מסכים ל",
        termsText: "תנאי השירות",
        termsLink: "/terms-of-use",
        middle: " ול",
        privacyText: "מדיניות הפרטיות",
        privacyLink: "/privacy-policy"
    },
    newsletterLabel: "אני מסכימ/ה לקבל עדכונים באימייל",
    termsRequired: "יש להסכים לתנאי השירות",

    // Verification
    checkEmail: "בדקו את האימייל שלכם",
    emailCode: "קוד אימות",
    emailCodeDescription: "הזן את קוד האימות שנשלח לאימייל שלך",
    emailOtpLabel: "קוד אימות אימייל",
    verify: "אימות",
    resendCode: "לא קיבלת קוד? שלח שוב",
    resendCodeCountdown: "לא קיבלת קוד? שלח שוב",

    // Continue step
    fillMissingFields: "השלימו פרטים חסרים",
    continue: "המשך",

    // Links and navigation
    alreadyHaveAccount: "יש לך כבר חשבון?",
    noAccount: "אין לך חשבון?",
    signIn: "התחבר",
    signUpLink: "הרשם",
    orDivider: "או",

    // Auth footer
    termsAgreement: {
        before: "בהתחברות אני מסכים/ה ל",
        text: "תנאי השימוש",
        link: "/terms-of-use",
        middle: " ול",
        after: ""
    },
    privacyLink: {
        text: "מדיניות הפרטיות",
        link: "/privacy-policy"
    },
    copyright: "כל הזכויות שמורות Milgapo בע״מ",

    // Navbar
    account: "החשבון שלי",
    signup: "הרשמה",
    toggleAccount: "פתח חשבון",
    toggleSignup: "הרשמה לאתר",

    // Messages
    preferencesSuccess: "ההעדפות נשמרו בהצלחה",
    unauthorizedError: "משתמש לא מורשה",
    savePreferencesError: "שגיאה בשמירת ההעדפות",
    invalidDataError: "נתונים לא תקינים",
    emailExistsError: "האימייל הזה כבר רשום במערכת. אנא התחבר במקום",
    emailNotFound: "האימייל לא נמצא במערכת. אנא הרשם תחילה",
    invalidPassword: "סיסמה שגויה. אנא נסה שוב",
    signInError: "שגיאה בהתחברות. אנא נסה שוב",
    accountLocked: "החשבון נחסם זמנית. אנא נסה שוב מאוחר יותר",
    invalidCode: "קוד האימות שגוי. אנא נסה שוב",
    expiredCode: "קוד האימות פג תוקף. אנא בקש קוד חדש",

    // Sign-up validation errors (OTP only)
    invalidEmailFormat: "כתובת האימייל אינה תקינה. אנא בדוק שנית",
    signUpValidationError: "שגיאה בבדיקת הנתונים. אנא בדוק את הפרטים שוב",
    signUpError: "שגיאה בהרשמה. אנא נסה שוב",
    tooManyRequests: "יותר מדי בקשות. אנא המתן מעט ונסה שוב",
    requiredFieldMissing: "נא למלא את כל השדות הנדרשים"
};

export const CLERK_APPEARANCE = {
    layout: {
        unsafe_disableDevelopmentModeWarnings: true
    },
    elements: {
        card: "shadow bg-white/90 rounded-2xl p-6 w-full max-w-sm mx-auto z-10",
        cardBox: "shadow-none",
        logoBox: "hidden",
        headerTitle: "hidden",
        headerSubtitle: "hidden",
        socialButtonsBlockButton:
            "flex items-center justify-center gap-2 py-2 px-4 rounded-lg border font-semibold transition bg-white hover:bg-gray-50 focus:ring-2 focus:ring-primary/40",
        socialButtonsBlockButtonText: "text-base font-bold",
        socialButtonsBlockButtonIcon: "w-5 h-5 mr-2",
        dividerRow: "my-4",
        dividerText: "text-muted-foreground text-sm",
        formFieldLabel: "text-right text-sm font-medium mb-1",
        formFieldInput:
            "w-full rounded-lg border border-gray-300 px-4 py-2 text-base focus:border-primary focus:ring-2 focus:ring-primary/20 transition placeholder:text-gray-400 text-right direction-rtl",
        formButtonPrimary:
            "w-full bg-primary text-white rounded-lg py-2 mt-2 font-bold shadow-lg hover:bg-primary/90 transition",
        footer: "text-xs text-muted-foreground text-center mt-4"
    },
    variables: {
        colorPrimary: "#1a73e8",
        colorText: "#222",
        colorBackground: "#f8fafc",
        colorInputBackground: "#fff",
        colorInputText: "#222",
        colorDanger: "#e53e3e"
    }
};

export const AUTH_ROUTES = {
    signup: "/signup",
    login: "/login",
    onboarding: "/onboarding"
} as const;

export const getHebrewErrorMessage = (englishErrorText: string, isSignUp: boolean = false): string | null => {
    const text = englishErrorText.toLowerCase();

    if (isSignUp) {
        if (
            text.includes("email address is taken") ||
            text.includes("that email address is taken") ||
            text.includes("identifier_exists")
        ) {
            return TEXTS.emailExistsError;
        }
        if (
            text.includes("invalid email") ||
            text.includes("email format") ||
            text.includes("form_email_invalid") ||
            text.includes("must be a valid email address")
        ) {
            return TEXTS.invalidEmailFormat;
        }
        if (text.includes("required") || text.includes("missing") || text.includes("form_param_missing")) {
            return TEXTS.requiredFieldMissing;
        }
        if (text.includes("rate limit") || text.includes("too many requests") || text.includes("too_many_requests")) {
            return TEXTS.tooManyRequests;
        }
        if (text.includes("validation") || text.includes("form_validation_failed")) {
            return TEXTS.signUpValidationError;
        }
    } else {
        // Sign-in specific errors
        if (
            text.includes("not found") ||
            text.includes("doesn't exist") ||
            text.includes("identifier_not_found") ||
            text.includes("couldn't find your account")
        ) {
            return TEXTS.emailNotFound;
        }
        if (text.includes("password") || text.includes("credentials") || text.includes("credential_invalid")) {
            return TEXTS.invalidPassword;
        }
        if (text.includes("locked") || text.includes("too many") || text.includes("requests")) {
            return TEXTS.accountLocked;
        }
        if (text.includes("expired")) {
            return TEXTS.expiredCode;
        }
        if (
            text.includes("invalid email") ||
            text.includes("email format") ||
            text.includes("form_email_invalid") ||
            text.includes("must be a valid email address")
        ) {
            return TEXTS.invalidEmailFormat;
        }
        if (text.includes("incorrect") || text.includes("invalid") || text.includes("code")) {
            return TEXTS.invalidCode;
        }
    }

    return null;
};
