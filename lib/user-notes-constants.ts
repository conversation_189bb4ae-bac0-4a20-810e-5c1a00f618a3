import { type UserNoteWithReporter } from "@/app/actions/user-notes-actions";

export const TEXTS = {
    pageTitle: "הערות משתמש",
    newNote: "הערה חדשה",
    loading: "טוען הערות...",
    errorPrefix: "שגיאה בטעינת הערות:",
    noNotes: "לא נמצאו הערות",

    noteLabel: "הערה",
    notePlaceholder: "הזן הערה...",
    noteRequired: "יש להזין הערה",
    reporterLabel: "מדווח",
    timeLabel: "זמן דיווח",

    addButton: "הוסף הערה",
    cancel: "ביטול",
    confirmDelete: "מחק",

    deleteConfirmTitle: "אישור מחיקת הערה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק הערה זו? לא ניתן לשחזר פעולה זו.",

    deleteError: "שגיאה במחיקת הערה",
    deleteSuccess: "הערה נמחקה בהצלחה",
    createError: "שגיאה ביצירת הערה",
    createSuccess: "הערה נוספה בהצלחה",
    deleting: "מוחק...",
    creating: "יוצר..."
};

export interface UserNoteFormData {
    note: string;
}

export const defaultValues: UserNoteFormData = {
    note: ""
};

export type UserNote = UserNoteWithReporter;
