export type UserOrgRole = "admin" | "employee" | "user";

export function getUserOrgRole(sessionClaims?: unknown): UserOrgRole {
    const orgRoles = (sessionClaims as Record<string, unknown> | undefined)?.organizations ?? {};
    const firstEntry = Object.entries(orgRoles)[0] as [string, unknown] | undefined;
    const roleString = firstEntry ? String(firstEntry[1]) : undefined;

    switch (roleString) {
        case "org:admin":
            return "admin";
        case "org:employee":
            return "employee";
        default:
            return "user";
    }
}

export function isAdminRole(role: UserOrgRole): boolean {
    return role === "admin" || role === "employee";
}

export function isAdminFromSessionClaims(sessionClaims?: unknown): boolean {
    return isAdminRole(getUserOrgRole(sessionClaims));
}
