import { Option } from "@/components/forms/fields/dropdown-base";
import { type Tables } from "@/types/database.types";

export const TEXTS = {
    amountLabel: "סכום המלגה:",
    dateRangeLabel: "תאריכי הרשמה:",
    volunteerHoursLabel: "שעות התנדבות",

    externalLinkLabel: "אתר המלגה",
    completeDetailsButton: "השלם פרטים",
    contactInfoLabel: "פרטי קשר",

    contactPersonLabel: "איש קשר:",
    contactEmailLabel: "מייל ליצירת קשר:",
    contactPhoneLabel: "נייד ליצירת קשר:",

    applyQuestionSubmission: "האם תרצה שנגיש אותך למלגה זו?",
    applyQuestionGuidance: "האם תרצה שנדריך אותך למלגה זו?",
    applicationStatusPlaceholder: "בחר סטטוס הגשה",
    applicationStatusYes: "כן, אני מעוניין",
    applicationStatusNo: "לא, תודה",

    scholarshipTypeGuidance: "הדרכה",
    scholarshipTypeSubmission: "הגשה",

    missingDataLabel: "חסרים נתונים או שאינך עומד בקריטריונים.",
    missingFieldsLabel: "חסרים {count} שדות נדרשים"
};

export type UserPlanVariant = "free" | "premium";

export type QuestionFromDB = Tables<"questions">;

export interface ApplicationQuestionMetadata {
    label?: string;
    placeholder?: string;
    options?: Option[];
    required?: boolean;
    tooltip?: string;
}

export const DEFAULT_APPLICATION_OPTIONS: Option[] = [
    { id: "yes", label: TEXTS.applicationStatusYes },
    { id: "no", label: TEXTS.applicationStatusNo }
];

export const getApplyText = (type: string): string => {
    return type === "guidance" ? TEXTS.applyQuestionGuidance : TEXTS.applyQuestionSubmission;
};

export const isPositiveAnswer = (optionId: string, options: Option[]): boolean => {
    if (options.length === 0) return false;
    if (optionId === "yes" && options.some((opt) => opt.id === "yes")) return true;
    if (options.length > 0 && optionId === options[0].id) return true;
    return false;
};
