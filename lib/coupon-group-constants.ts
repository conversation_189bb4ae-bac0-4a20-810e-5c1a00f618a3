export const TEXTS = {
    couponGroupDeleteError: "שגיאה במחיקת קבוצת הקופונים. אנא נסה שנית.",
    couponGroupDeleteSuccess: "קבוצת הקופונים נמחקה בהצלחה.",
    couponGroupFetchError: "שגיאה בטעינת קבוצת הקופונים",
    couponGroupNotFound: "קבוצת הקופונים לא נמצאה",
    couponGroupUpdateError: "שגיאה בעדכון קבוצת הקופונים",
    couponGroupCreateError: "שגיאה ביצירת קבוצת הקופונים",
    couponGroupsFetchError: "שגיאה בטעינת קבוצות הקופונים",
    nameRequired: "שם קבוצת הקופונים הוא שדה חובה",
    formTitle: "קבוצת קופונים",
    nameLabel: "שם הקבוצה",
    namePlaceholder: "הכנ<PERSON> שם לקבוצה",
    descriptionLabel: "תיאור",
    descriptionPlaceholder: "הכנס תיאור לקבוצה",
    submitButton: "שמור",
    submitting: "שומר...",
    successMessage: "קבוצת הקופונים נשמרה בהצלחה",
    errorMessage: "שגיאה בשמירת קבוצת הקופונים",
    editPageTitle: "עריכת קבוצת קופונים"
};

export const ADMIN_COUPON_GROUPS_TEXTS = {
    groupsTabTitle: "קבוצות קופונים",
    groupsPageTitle: "קבוצות קופונים",
    newGroup: "קבוצת קופונים חדשה",
    loadingGroups: "טוען קבוצות קופונים...",
    errorPrefixGroups: "שגיאה בטעינת קבוצות הקופונים:",
    noGroups: "לא נמצאו קבוצות קופונים",
    name: "שם",
    description: "תיאור",
    couponsCount: "מספר קופונים",
    deleteConfirmTitleGroup: "אישור מחיקה",
    deleteConfirmDescriptionGroup: "האם אתה בטוח שברצונך למחוק קבוצת קופונים זו? לא ניתן לשחזר פעולה זו.",
    deleteErrorGroup: "שגיאה במחיקת קבוצת הקופונים",
    deleteSuccessGroup: "קבוצת הקופונים נמחקה בהצלחה",
    deletingGroup: "מוחק קבוצת קופונים..."
};
