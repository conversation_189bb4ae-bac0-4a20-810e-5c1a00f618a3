import { type Database, type Tables } from "@/types/database.types";

export interface CouponResult {
    success: boolean;
    error?: string;
    couponCode?: string;
    couponType?: Database["public"]["Enums"]["coupon_type_enum"];
    discountValue?: number;
    discountApplied?: number;
    finalAmount?: number;
    verificationDetails?: Record<string, string | number | boolean | undefined>;
}

export type CouponAppliedInfo = {
    couponCode: string;
    couponType: Database["public"]["Enums"]["coupon_type_enum"];
    discountValue: number;
    discountApplied: number;
    finalAmount: number;
};

export type CouponType = Database["public"]["Enums"]["coupon_type_enum"];

export type ValidatedCoupon = Pick<Tables<"coupons">, "coupon_code" | "coupon_type" | "discount_value">;

export interface PaymentDetails {
    orderId: string;
    transactionId: string;
    amount: number;

    cCode?: string | number;
    aCode?: string;
    sign?: string;
    fild1?: string;
    fild2?: string;
    fild3?: string;

    rawParams?: Record<string, string | number | undefined>;
}

export interface PaymentParams {
    Order?: string;
    Id?: string;
    CCode?: string | number;
    Amount?: string | number;
    ACode?: string;
    Sign?: string;
    Fild1?: string;
    Fild2?: string;
    Fild3?: string;
    [key: string]: string | number | undefined;
}

export interface VerificationResult {
    success: boolean;
    error?: string;
    message?: string;
    orderId?: string;
    transactionId?: string;
    verificationDetails?: Record<string, string | number | boolean | undefined>;
}

export const TEXTS = {
    COUPON_APPLIED_SUCCESS: (couponCode: string) => `קופון ${couponCode} נכנס למערכת.`,
    COUPON_UPDATE_ERROR: "שגיאה בעדכון הקופון. אנא נסה שנית.",
    COUPON_NOT_FOUND: "קוד קופון לא נמצא.",
    COUPON_EXPIRED: "תוקף הקופון פג.",
    COUPON_USAGE_LIMIT_REACHED: "הקופון הגיע למגבלת השימוש שלו.",
    PAYMENT_VERIFIED_SUCCESS: "התשלום אומת בהצלחה.",
    PAYMENT_GATEWAY_CONFIG_ERROR: "שגיאת תצורה בשער התשלומים.",
    PAYMENT_VERIFICATION_ERROR: "שגיאה באימות התשלום.",
    PAYMENT_MISSING_PARAMS: "פרטי אישור תשלום חסרים מהמענה של שירות התשלומים.",
    GET_STARTED: "להתחיל",
    PAYMENT: "לתשלום",
    REGISTER: "הרשמה",
    MOST_POPULAR: "תוכנית מומלצת",
    ONE_TIME_PAYMENT: " תשלום חד פעמי למשך",
    DAYS: "ימים",
    FEATURES_COMPARISON: "השוואת תכונות",
    FEATURE: "תכונה",
    BASIC: "בסיסי",
    PRO: "מקצועי",
    ENTERPRISE: "עסקי",
    UNLIMITED: "ללא הגבלה",
    YES: "כן",
    NO: "לא",
    COUPON_PLACEHOLDER: "הזינו קוד קופון",
    APPLY_COUPON: "החלת קופון",
    COUPON_TITLE: "יש לכם קוד קופון? הזינו כאן:",
    ENTER_COUPON_CODE: "אנא הזינו קוד קופון.",
    COUPON_APPLIED_SUCCESS_MSG: (code: string) => `קופון '${code}' הוחל בהצלחה!`,
    COUPON_INCREMENT_FAILED_BUT_FREE: "שגיאה בעדכון הקופון, אך התוכנית נוספה בחינם.",
    PLAN_ADDED_FREE_SUCCESS: (title: string) => `התוכנית ${title} נוספה לחשבונך בחינם באמצעות קופון!`,
    FREE_PLAN_ADDED_SUCCESS: (title: string) => `התוכנית החינמית ${title} נוספה לחשבונך!`,
    FREE_PLAN_ADD_ERROR: "שגיאה בהוספת התוכנית החינמית",
    CHECKOUT_INITIATION_FAILED: "אירעה שגיאה בתהליך התשלום. אנא נסה שנית.",
    CHECKOUT_UNEXPECTED_ERROR: "אירעה שגיאה לא צפויה בתהליך התשלום.",
    FEATURE_COMPARISON: "השוואת תכונות",
    ACTIVE_PLAN: "תוכנית פעילה",
    INCLUDED_IN_CURRENT_PLAN: "מוכל בתוכנית קיימת",
    CURRENT_SUBSCRIPTION: {
        CURRENT_PLAN: "התוכנית הנוכחית שלך:",
        EXPIRES_ON: "בתוקף עד:",
        PLAN_PRICE: "מחיר התוכנית:",
        PAID_AMOUNT: "סכום ששולם:",
        NO_ACTIVE_SUBSCRIPTION: "אין לך מנוי פעיל כרגע.",
        YOUR_CURRENT_PLAN_IS: "תוכניתך הנוכחית היא",
        UNTIL_DATE: "עד לתאריך"
    }
};

export type PlanType = "free" | "milgapro" | "elite" | "vip";

export interface Feature {
    id: string;
    name: string;
}

export interface PlanFeature {
    featureId: string;
    isApplicable: boolean;
    value?: string;
}

export interface PlanColors {
    text: string;
    bg: string;
    bg10: string;
    bar: string;
    cardGradient: string;
}

export interface SubscriptionsItem {
    id: string;
    title: string;
    description: string;
    price: number;
    originalPrice?: number;
    features: PlanFeature[];
    isFeatured: boolean;
    duration_days: number | null;
    planType: PlanType;
    colors: PlanColors;
}

export interface FeatureComparison {
    name: string;
    plans: {
        [planName: string]: string | boolean | number;
    };
}

export type UserSubscriptionWithPlan = Tables<"user_subscriptions"> & {
    planType?: PlanType;
};
