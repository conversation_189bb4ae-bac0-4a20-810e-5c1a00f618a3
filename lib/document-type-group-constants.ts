import { type TablesInsert, type TablesUpdate } from "@/types/database.types";

export const TEXTS = {
    formTitle: "קבוצת מסמכים",
    nameLabel: "שם הקבוצה",
    namePlaceholder: "הכ<PERSON><PERSON> שם לקבוצה",
    nameRequired: "שם הקבוצה הוא שדה חובה",
    documentTypesLabel: "סוגי מסמכים",
    documentTypesPlaceholder: "בחר סוגי מסמכים",
    submitButton: "שמור",
    submitting: "שומר...",
    successMessage: "קבוצת המסמכים נשמרה בהצלחה",
    errorMessage: "שגיאה בשמירת קבוצת המסמכים",
    FETCH_GROUPS_ERROR: "שגיאה בטעינת קבוצות סוגי מסמכים",
    FETCH_GROUP_ERROR: "שגיאה בטעינת קבוצת סוגי מסמכים",

    NAME_REQUIRED: "שם קבוצת המסמכים הוא שדה חובה",
    CREATE_ERROR: "שגיאה ביצירת קבוצת המסמכים",
    UPDATE_ERROR: "שגיאה בעדכון קבוצת המסמכים",

    CREATE_SUCCESS: "קבוצת המסמכים נוצרה בהצלחה",
    UPDATE_SUCCESS: "קבוצת המסמכים עודכנה בהצלחה",

    NAME_LABEL: "שם",
    NAME_PLACEHOLDER: "הזן שם לקבוצת המסמכים",
    DESCRIPTION_LABEL: "תיאור",
    DESCRIPTION_PLACEHOLDER: "הזן תיאור לקבוצת המסמכים",

    CREATE_BUTTON: "צור קבוצת מסמכים",
    UPDATE_BUTTON: "עדכן קבוצת מסמכים",
    CANCEL_BUTTON: "ביטול",

    LOADING_GROUP: "טוען קבוצת מסמכים...",
    GROUP_NOT_FOUND: "קבוצת המסמכים לא נמצאה"
};

export const ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS = {
    groupsTabTitle: "קבוצות מסמכים",
    groupsPageTitle: "קבוצות מסמכים",
    newGroup: "קבוצת מסמכים חדשה",
    loadingGroups: "טוען קבוצות מסמכים...",
    errorPrefixGroups: "שגיאה בטעינת קבוצות המסמכים:",
    noGroups: "לא נמצאו קבוצות מסמכים",
    groupName: "שם",
    documentTypesCount: "מספר סוגי מסמכים",
    deleteConfirmTitleGroup: "אישור מחיקה",
    deleteConfirmDescriptionGroup: "האם אתה בטוח שברצונך למחוק קבוצת מסמכים זו? לא ניתן לשחזר פעולה זו.",
    deleteErrorGroup: "שגיאה במחיקת קבוצת המסמכים",
    deleteSuccessGroup: "קבוצת המסמכים נמחקה בהצלחה",
    deletingGroup: "מוחק קבוצת מסמכים..."
};

export interface DocumentTypeGroupFormValues {
    name: string;
    description: string;
}

export const defaultValues: DocumentTypeGroupFormValues = {
    name: "",
    description: ""
};

export type DocumentTypeGroupInsert = TablesInsert<"groups_document_type">;
export type DocumentTypeGroupUpdate = TablesUpdate<"groups_document_type">;
