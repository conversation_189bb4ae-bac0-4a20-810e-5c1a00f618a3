import { Option } from "@/components/forms/fields/dropdown-base";
import { type Tables } from "@/types/database.types";

export type CouponType = "fixed_amount" | "percentage";
export type CreationMode = "single" | "multiple";

export interface CouponFormValues {
    creation_mode: CreationMode;
    coupon_code: string | null;
    quantity: number;
    coupon_type: Option;
    discount_value: number | null;
    expiration_date: Date | null;
    usage_limit: number | null;
    coupon_group_id: Option | null;
}

export const couponTypeOptions = [
    { id: "fixed_amount" as CouponType, label: "סכום קבוע", subtitle: "סכום קבוע בשקלים" },
    { id: "percentage" as CouponType, label: "אחוזים", subtitle: "אחוזים מהסכום" }
];

export const defaultCouponFormValues: CouponFormValues = {
    creation_mode: "single",
    coupon_code: null,
    quantity: 10,
    coupon_type: couponTypeOptions[0],
    discount_value: null,
    expiration_date: null,
    usage_limit: null,
    coupon_group_id: null
};

export const TEXTS = {
    COUPON_DELETE_ERROR: "שגיאה במחיקת הקופון. אנא נסה שנית.",
    COUPON_DELETE_SUCCESS: "הקופון נמחק בהצלחה.",
    COUPON_FETCH_ERROR: "שגיאה בטעינת הקופון",
    COUPON_NOT_FOUND: "הקופון לא נמצא",
    COUPON_UPDATE_ERROR: "שגיאה בעדכון הקופון",
    COUPON_CREATE_ERROR: "שגיאה ביצירת הקופון/ים",
    COUPON_CODE_EXISTS: "קוד קופון אחד או יותר כבר קיים במערכת. נסה שוב.",
    COUPON_CODE_GENERATION_ERROR: "לא ניתן היה לייצר מספיק קודים ייחודיים. אנא נסה שוב או הקטן את הכמות.",
    COUPON_GROUPS_FETCH_ERROR: "שגיאה בטעינת קבוצות הקופונים",
    COUPON_CODE_REQUIRED: "קוד הקופון הוא שדה חובה",
    QUANTITY_MUST_BE_POSITIVE: "יש להזין כמות גדולה מ-0.",
    DISCOUNT_VALUE_MUST_BE_POSITIVE: "ערך ההנחה חייב להיות חיובי",
    EXPIRATION_DATE_MUST_BE_FUTURE: "תאריך התפוגה חייב להיות עתידי",
    USAGE_LIMIT_MUST_BE_POSITIVE: "מספר השימושים חייב להיות חיובי",

    // Form UI text
    errorMessage: "שגיאה בפעולה",
    editSuccessMessage: "הקופון עודכן בהצלחה",
    editErrorMessage: "שגיאה בעדכון הקופון",
    editLoadingMessage: "טוען קופון...",
    editNotFoundMessage: "הקופון לא נמצא",
    updateButtonText: "עדכן קופון",
    cancelButtonText: "ביטול",
    createSuccessMessageSingle: "הקופון נוצר בהצלחה",
    createSuccessMessageMultiple: (count: number) => `${count} קופונים נוצרו בהצלחה`,
    createErrorMessage: "שגיאה ביצירת הקופון/ים",
    generateButtonText: "צור קופונים",
    createButtonText: "צור קופון",
    singleCouponLabel: "קופון יחיד",
    multipleCouponLabel: "מספר קופונים",
    couponCodeRequired: "קוד הקופון הוא שדה חובה",
    couponCodeExists: "קוד קופון אחד או יותר כבר קיים במערכת. נסה שוב.",
    quantityRequired: "כמות הקופונים היא שדה חובה",
    quantityMin: "יש להזין כמות גדולה מ-0.",
    couponCodeLabel: "קוד קופון",
    couponCodePlaceholder: "הזן את קוד הקופון",
    quantityLabel: "כמות קופונים ליצירה",
    quantityPlaceholder: "הזן כמות",
    couponTypeLabel: "סוג הקופון",
    couponTypePlaceholder: "בחר סוג קופון",
    discountValueLabel: "ערך ההנחה",
    discountValuePlaceholder: "הזן את ערך ההנחה",
    expirationDateLabel: "תאריך תפוגה (אופציונאלי)",
    usageLimitLabel: "מספר שימושים מקסימלי",
    usageLimitPlaceholder: "הזן את מספר השימושים המקסימלי",
    couponGroupLabel: "קבוצת קופונים",
    couponGroupPlaceholder: "בחר קבוצת קופונים",
    couponGroupRequired: "קבוצת קופונים היא שדה חובה",
    fieldRequiredSuffix: "הוא שדה חובה",
    formTitleEdit: "עריכת קופון",
    formSubmitCreate: "הוספה",
    formSubmitUpdate: "עדכון",
    editErrorNotFound: "שגיאה: מספר קופון לא נמצא."
};

export const ADMIN_COUPONS_TEXTS = {
    pageTitle: "קופונים",
    newCoupon: "קופון חדש",
    loading: "טוען קופונים...",
    errorPrefix: "שגיאה בטעינת הקופונים:",
    noCoupons: "לא נמצאו קופונים",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק קופון זה? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת הקופון",
    deleteSuccess: "הקופון נמחק בהצלחה",
    deletingCoupon: "מוחק קופון...",
    couponsTabTitle: "קופונים",
    group: "קבוצה",
    noGroup: "ללא קבוצה",
    couponCode: "קוד קופון",
    type: "סוג",
    value: "ערך",
    expirationDate: "תאריך תפוגה",
    uses: "שימושים",
    actions: "פעולות",
    fixedAmount: "סכום קבוע",
    percentage: "אחוזים",
    noExpiration: "ללא תפוגה",
    edit: "עריכה",
    cancel: "ביטול",
    copySuccess: "קוד הקופון הועתק בהצלחה"
};

export type Coupon = Tables<"coupons"> & { group_name?: string | null };
