export const TEXTS = {
    // Main page texts
    pageTitle: "ניהול משתמשים",
    emailLabel: "כתובת אימייל של המשתמש",
    emailPlaceholder: "הזן כתובת אימייל",
    emailRequired: "יש להזין כתובת אימייל",
    emailError: "יש להזין כתובת אימייל תקינה",
    searchButton: "חפש משתמש",
    searching: "מחפש...",

    // User detail page texts
    userDetailsTitle: "פרטי משתמש",
    userIdLabel: "מזהה משתמש",
    userNotFound: "משתמש לא נמצא",
    loadingUserDetails: "טוען פרטי משתמש...",
    userIdInvalid: "מזהה משתמש לא תקין",

    // Tab titles
    questionsTab: "שאלות",
    documentsTab: "מסמכים",
    packageTab: "חבילה",
    notesTab: "הערות",

    // Tab content placeholders
    questionsContent: "תוכן שאלות יוצג כאן",
    documentsContent: "תוכן מסמכים יוצג כאן",
    packageContent: "תוכן חבילה יוצג כאן",
    notesContent: "תוכן הערות יוצג כאן",

    // Error messages
    userSearchError: "שגיאה בחיפוש משתמש",
    genericError: "אירעה שגיאה לא צפויה",

    // Copy messages
    userIdCopySuccess: "מזהה משתמש הועתק בהצלחה"
};

export interface UserFormData {
    email: string;
}

export const defaultValues: UserFormData = {
    email: ""
};

export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
