export const TEXTS = {
    CONDITION_GROUP_DELETE_ERROR: "שגיאה במחיקת קבוצת התנאים. אנא נסה שנית.",
    CONDITION_GROUP_DELETE_SUCCESS: "קבוצת התנאים נמחקה בהצלחה.",
    CONDITION_GROUP_FETCH_ERROR: "שגיאה בטעינת קבוצת התנאים",
    CONDITION_GROUP_NOT_FOUND: "קבוצת התנאים לא נמצאה",
    CONDITION_GROUP_UPDATE_ERROR: "שגיאה בעדכון קבוצת התנאים",
    CONDITION_GROUP_CREATE_ERROR: "שגיאה ביצירת קבוצת התנאים",
    CONDITION_GROUPS_FETCH_ERROR: "שגיאה בטעינת קבוצות התנאים",
    QUESTIONS_FETCH_ERROR: "שגיאה בטעינת השאלות",

    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "קבוצת התנאים עודכנה בהצלחה",
    editErrorMessage: "שגיאה בעדכון קבוצת התנאים",
    editLoadingMessage: "טוען קבוצת תנאים...",
    editNotFoundMessage: "קבוצת התנאים לא נמצאה",
    updateButtonText: "שמור שינויים",
    cancelButtonText: "ביטול",

    createSuccessMessage: "קבוצת התנאים נוצרה בהצלחה",
    createErrorMessage: "שגיאה ביצירת קבוצת התנאים",
    createButtonText: "צור קבוצת תנאים",

    nameLabel: "שם הקבוצה",
    namePlaceholder: "הזן שם לקבוצת התנאים",
    nameRequired: "שם קבוצת התנאים הוא שדה חובה",

    conditionsHeading: "תנאי קבוצה",
    conditionsDescription: "הגדר את התנאים לקבוצה זו. הסטודנט צריך לענות על כל התנאים בקבוצה כדי להיות זכאי.",

    generalTabLabel: "פרטים כלליים",
    conditionsTabLabel: "תנאי קבוצה",
    formTitle: "עריכת קבוצת תנאים"
};

export interface ConditionGroupFormValues {
    name: string;
    dependencies: ConditionGroupDependency[];
}

export interface ConditionGroupDependency {
    id?: string;
    question_id: { id: string; label: string };
    condition_type?: string;
    condition_value?: unknown;
}

export interface ConditionQuestion {
    id: string;
    type: string;
    metadata: Record<string, unknown>;
    groups_question?: {
        id: string;
        name: string;
    };
}

export const defaultConditionGroupValues: ConditionGroupFormValues = {
    name: "",
    dependencies: []
};
