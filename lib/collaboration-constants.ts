import { Option } from "@/components/forms/fields/dropdown-base";
import { type Database } from "@/types/database.types";

export type AuthType = Database["public"]["Enums"]["auth_type"];

export const URL_REGEX = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;

export const TEXTS = {
    // Action error messages
    COLLABORATION_CREATE_ERROR: "שגיאה ביצירת שיתוף הפעולה. אנא נסה שנית.",
    COLLABORATION_UPDATE_ERROR: "שגיאה בעדכון שיתוף הפעולה. אנא נסה שנית.",
    COLLABORATION_DELETE_ERROR: "שגיאה במחיקת שיתוף הפעולה. אנא נסה שנית.",
    COLLABORATION_FETCH_ERROR: "שגיאה בטעינת שיתוף הפעולה",
    COLLABORATION_NOT_FOUND: "שיתוף הפעולה לא נמצא",
    INVALID_URL_FORMAT: "נא להזין כתובת URL תקינה",
    REQUIRED_FIELD_NAME: "נא להזין שם",
    REQUIRED_FIELD_API_ENDPOINT: "נא להזין כתובת URL",
    INVALID_DATA: "נתונים לא תקינים",
    INVALID_AUTH_TYPE: "סוג אימות לא תקין",

    // Form UI text
    pageTitle: {
        new: "שיתוף פעולה חדש",
        edit: "עריכת שיתוף פעולה"
    },
    nameLabel: "שם",
    namePlaceholder: "הזן שם שיתוף פעולה",
    nameRequired: "נא להזין שם",
    descriptionLabel: "תיאור",
    descriptionPlaceholder: "הזן תיאור (אופציונלי)",
    apiEndpointLabel: "נקודת קצה API",
    apiEndpointPlaceholder: "הזן כתובת URL",
    apiEndpointRequired: "נא להזין כתובת URL",
    authTypeLabel: "סוג אימות",
    authTypePlaceholder: "בחר סוג אימות",
    authValueLabel: "ערך אימות",
    authValuePlaceholder: "הזן את ערך האימות",
    authValueRequired: "נא להזין ערך אימות",
    saveButtonText: "שמור",
    cancelButtonText: "ביטול",
    createButtonText: "צור שיתוף פעולה",
    updateButtonText: "עדכן שיתוף פעולה",
    createSuccessMessage: "שיתוף הפעולה נוצר בהצלחה",
    updateSuccessMessage: "שיתוף הפעולה עודכן בהצלחה",
    createErrorMessage: "אירעה שגיאה ביצירת שיתוף הפעולה",
    updateErrorMessage: "אירעה שגיאה בעדכון שיתוף הפעולה",
    loadingText: "טוען טופס שיתוף פעולה...",
    notFoundText: "שיתוף הפעולה לא נמצא",
    bearerToken: "Bearer Token",
    noAuth: "ללא אימות",
    conditionsLabel: "תנאים להפעלת שיתוף פעולה",
    conditionsDescription: "הגדרת תנאים שכאשר מתקיימים, המערכת תשלח נתונים לשיתוף הפעולה",
    questionsLabel: "שאלות לשליחה",
    questionsPlaceholder: "בחר שאלות",
    questionsDescription: "בחירת השאלות שהתשובות עליהן יישלחו לשיתוף הפעולה כאשר התנאים מתקיימים",
    questionsRequired: "יש לבחור לפחות שאלה אחת",
    apiEndpointError: "נא להזין כתובת URL תקינה",
    fetchQuestionsError: "שגיאה בטעינת השאלות",
    fetchCollaborationError: "שגיאה בטעינת שיתוף הפעולה"
};

export const ADMIN_COLLABORATIONS_TEXTS = {
    pageTitle: "שיתופי פעולה",
    newCollaboration: "שיתוף פעולה חדש",
    loading: "טוען שיתופי פעולה...",
    errorPrefix: "שגיאה בטעינת שיתופי הפעולה:",
    noCollaborations: "לא נמצאו שיתופי פעולה",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק שיתוף פעולה זה? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת שיתוף הפעולה",
    deleteSuccess: "שיתוף הפעולה נמחק בהצלחה",
    deletingCollaboration: "מוחק שיתוף פעולה...",
    name: "שם",
    description: "תיאור",
    apiEndpoint: "נקודת קצה API",
    authType: "סוג אימות",
    actions: "פעולות",
    edit: "עריכה",
    bearerToken: "Bearer Token",
    noAuth: "ללא אימות"
};

export const AUTH_TYPE_OPTIONS: Record<AuthType, Option> = {
    bearer_token: { id: "bearer_token", label: TEXTS.bearerToken },
    none: { id: "none", label: TEXTS.noAuth }
} as const;

export const authTypeOptions: Option[] = Object.values(AUTH_TYPE_OPTIONS);

export const authTypeToOption = (authType: AuthType): Option => AUTH_TYPE_OPTIONS[authType];
export const optionToAuthType = (option: Option): AuthType => option.id as AuthType;
