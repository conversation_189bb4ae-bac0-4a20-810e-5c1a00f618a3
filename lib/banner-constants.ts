import { type Option } from "@/components/forms/fields/dropdown-base";
import { type Database } from "@/types/database.types";

export type BannerAudienceLabel = Database["public"]["Enums"]["banners_audience"];
export type BannerAudience = { id: BannerAudienceLabel; label: string };

export interface ColorSchemeOption {
    id: string;
    label: string;
}

export const bannerColors: ColorSchemeOption[] = [
    { id: "info", label: "כחול בהיר - מידע" },
    { id: "success", label: "ירוק בהיר - הצלחה" },
    { id: "warning", label: "צהוב בהיר - אזהרה" },
    { id: "notice", label: "כתום בהיר - הודעה" },
    { id: "alert", label: "אדום בהיר - התראה" },
    { id: "event", label: "סגול בהיר - אירוע" },
    { id: "important", label: "שחור - חשוב מאוד" },
    { id: "dark-blue", label: "כחול כהה" },
    { id: "dark-green", label: "ירוק כהה" },
    { id: "dark-red", label: "אדום כהה" }
];

export const bannerIdToColorMap: Record<string, { background: string; text: string }> = {
    info: { background: "#BAE1FF", text: "#000000" },
    success: { background: "#BAFFC9", text: "#000000" },
    warning: { background: "#FFFFBA", text: "#000000" },
    notice: { background: "#FFDFBA", text: "#000000" },
    alert: { background: "#FFB3BA", text: "#000000" },
    event: { background: "#E2BAFF", text: "#000000" },
    important: { background: "#222222", text: "#FFFFFF" },
    "dark-blue": { background: "#1E40AF", text: "#FFFFFF" },
    "dark-green": { background: "#166534", text: "#FFFFFF" },
    "dark-red": { background: "#991B1B", text: "#FFFFFF" }
};

export const getColorSchemeFromBackgroundColor = (backgroundColor: string): ColorSchemeOption | null => {
    const colorSchemeEntry = Object.entries(bannerIdToColorMap).find(
        ([, colors]) => colors.background === backgroundColor
    );

    if (!colorSchemeEntry) return null;

    const colorSchemeId = colorSchemeEntry[0];
    return bannerColors.find((color) => color.id === colorSchemeId) || null;
};

export interface BannerFormValues {
    title: string | null;
    text: string | null;
    audience: Option | null;
    color_scheme: Option | null;
    icon: Option | null;
    cta_text: string | null;
    cta_link: string | null;
    days_to_live: number;
    seconds_before_show: number;
    enable_dismiss: boolean;
    enabled: boolean;
}

export const defaultValues: BannerFormValues = {
    title: null,
    text: null,
    audience: null,
    color_scheme: null,
    icon: null,
    cta_text: null,
    cta_link: null,
    days_to_live: 0,
    seconds_before_show: 0,
    enable_dismiss: true,
    enabled: true
};

export const TEXTS = {
    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "הבאנר עודכן בהצלחה",
    editErrorMessage: "שגיאה בעדכון הבאנר",
    editLoadingMessage: "טוען באנר...",
    editNotFoundMessage: "הבאנר לא נמצא",
    updateButtonText: "עדכן באנר",
    cancelButtonText: "ביטול",

    createSuccessMessage: "הבאנר נוצר בהצלחה",
    createErrorMessage: "שגיאה ביצירת הבאנר",
    createButtonText: "צור באנר",

    titleLabel: "כותרת",
    titlePlaceholder: "הזן כותרת",
    titleRequired: "כותרת היא שדה חובה",

    pageTitle: "באנר חדש",

    textLabel: "טקסט",
    textPlaceholder: "הזן טקסט",
    textRequired: "טקסט הוא שדה חובה",

    audienceLabel: "קהל יעד",
    audiencePlaceholder: "בחר קהל יעד",
    audienceRequired: "קהל יעד הוא שדה חובה",
    audienceGuest: "אורח",
    audienceUser: "משתמש",
    audienceSubscriber: "מנוי",

    colorSchemeLabel: "סכמת צבעים",
    colorSchemePlaceholder: "בחר סכמת צבעים",
    colorSchemeRequired: "סכמת צבעים היא שדה חובה",

    iconLabel: "אייקון",
    iconPlaceholder: "בחר אייקון",
    iconRequired: "אייקון הוא שדה חובה",

    ctaTextLabel: "טקסט כפתור פעולה",
    ctaTextPlaceholder: "הזן טקסט לכפתור (אופציונלי)",

    ctaLinkLabel: "קישור כפתור פעולה",
    ctaLinkPlaceholder: "הזן קישור לכפתור (אופציונלי)",

    daysToLiveLabel: "ימים לפני הצגה חוזרת",
    daysToLiveDescription: "מספר הימים שיעברו לפני שהבאנר יוצג שוב למשתמש שסגר אותו",
    daysToLiveRequired: "ימים לפני הצגה חוזרת הוא שדה חובה",

    secondsBeforeShowLabel: "שניות לפני הצגה",
    secondsBeforeShowDescription: "מספר השניות שיעברו לפני שהבאנר יוצג למשתמש",
    secondsBeforeShowRequired: "שניות לפני הצגה הוא שדה חובה",

    enableDismissLabel: "אפשר סגירה",
    enableDismissDescription: "האם לאפשר למשתמש לסגור את הבאנר",

    enabledLabel: "האם פעיל",
    enabledDescription: "האם הבאנר יוצג למשתמשים",

    ctaLinkPatternMessage: "יש להזין כתובת URL חוקית"
};

export const ADMIN_BANNERS_TEXTS = {
    pageTitle: "באנרים",
    newBanner: "באנר חדש",
    loading: "טוען באנרים...",
    errorPrefix: "שגיאה בטעינת הבאנרים:",
    noBanners: "לא נמצאו באנרים",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק באנר זה? לא ניתן לשחזר פעולה זו.",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקת הבאנר",
    deleteSuccess: "הבאנר נמחק בהצלחה",
    deletingBanner: "מוחק באנר...",
    title: "כותרת",
    text: "טקסט",
    icon: "אייקון",
    backgroundAndTextColor: "צבע רקע וטקסט",
    ctaText: "כפתור פעולה",
    daysToLive: "ימים לפני הצגה חוזרת",
    secondsBeforeShow: "שניות לפני הצגה",
    enableDismiss: "אפשר סגירה",
    enabled: "האם פעיל",
    audience: "קהל יעד",
    audienceGuest: "אורח",
    audienceUser: "משתמש",
    audienceSubscriber: "מנוי",
    createdAt: "תאריך יצירה",
    updatedAt: "תאריך עדכון",
    actions: "פעולות",
    edit: "עריכה",
    cancel: "ביטול"
};

export const iconOptions = [
    { id: "Info", label: "מידע" },
    { id: "AlertCircle", label: "התראה" },
    { id: "AlertTriangle", label: "אזהרה" },
    { id: "Bell", label: "התראה" },
    { id: "Megaphone", label: "הכרזה" },
    { id: "Gift", label: "מבצע" },
    { id: "Tag", label: "תג מחיר" },
    { id: "Calendar", label: "אירוע" },
    { id: "Star", label: "חדש" },
    { id: "CheckCircle", label: "הצלחה" },
    { id: "Clock", label: "זמני" },
    { id: "Heart", label: "אהבה" }
];

export const audienceOptions = [
    { id: "Guest", label: TEXTS.audienceGuest },
    { id: "User", label: TEXTS.audienceUser },
    { id: "Subscriber", label: TEXTS.audienceSubscriber }
];
