import { type Tables } from "@/types/database.types";

export interface Option<T = string> {
    id: T;
    label: string;
    subtitle?: string;
}

export type TestimonialTypeEnum = "personal" | "institution";

export interface TestimonialFormValues {
    name: string;
    text: string;
    type: { id: TestimonialTypeEnum; label: string };
}

export const testimonialTypeOptions: Option<TestimonialTypeEnum>[] = [
    { id: "personal", label: "אישי" },
    { id: "institution", label: "מוסדי" }
];

export const testimonialTypeOptionsMap: Record<TestimonialTypeEnum, string> = {
    personal: "אישי",
    institution: "מוסדי"
};

export const TESTIMONIAL_TEXTS = {
    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "הביקורת עודכן בהצלחה",
    editErrorMessage: "שגיאה בעדכון הביקורת",
    editLoadingMessage: "טוען ביקורת...",
    editNotFoundMessage: "הביקורת לא נמצא",
    updateButtonText: "עדכן ביקורת",
    cancelButtonText: "ביטול",

    createSuccessMessage: "הטעם נוצר בהצלחה",
    createErrorMessage: "שגיאה ביצירת הטעם",
    createButtonText: "צור טעם",

    nameLabel: "שם",
    namePlaceholder: "הזן שם",
    nameRequired: "שם הוא שדה חובה",

    textLabel: "טקסט",
    textPlaceholder: "הזן טקסט",
    textRequired: "טקסט הוא שדה חובה",

    typeLabel: "סוג ביקורת",
    typePlaceholder: "בחר סוג ביקורת",
    typeRequired: "סוג ביקורת הוא שדה חובה",

    TESTIMONIAL_FETCH_ERROR: "שגיאה בטעינת הביקורת",
    TESTIMONIAL_NOT_FOUND: "הביקורת לא נמצא",
    TESTIMONIAL_UPDATE_ERROR: "שגיאה בעדכון הביקורת",
    TESTIMONIAL_CREATE_ERROR: "שגיאה ביצירת הביקורת",
    TESTIMONIAL_DELETE_ERROR: "שגיאה במחיקת חוות הדעת",

    pageTitle: "חוות דעת",
    newTestimonial: "חוות דעת חדשה",
    newTestimonialPageTitle: "חוות דעת חדשה",
    loading: "טוען חוות דעת...",
    errorPrefix: "שגיאה בטעינת חוות הדעת:",
    noTestimonials: "לא נמצאו חוות דעת",
    text: "תוכן",
    type: "סוג",
    createdAt: "תאריך יצירה",
    updatedAt: "תאריך עדכון",
    actions: "פעולות",
    edit: "עריכה",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק חוות דעת זו? לא ניתן לשחזר פעולה זו.",
    cancel: "ביטול",
    confirmDelete: "מחק",
    deleteSuccess: "חוות הדעת נמחקה בהצלחה",
    deletingTestimonial: "מוחק חוות דעת...",
    formTitleEdit: "עריכת המלצה",
    formSubmitCreate: "הוספה",
    formSubmitUpdate: "עדכון"
};

export const defaultTestimonialValues: TestimonialFormValues = {
    name: "",
    text: "",
    type: { id: "personal", label: "אישי" }
};

export type Testimonial = Tables<"testimonials">;
export type TestimonialType = "personal" | "institution";
