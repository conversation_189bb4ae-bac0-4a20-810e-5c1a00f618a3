# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Milgapo is a Hebrew-language scholarship application platform built with Next.js 15, Supabase, and Clerk authentication. It features a subscription-based access model, dynamic form system, and comprehensive admin dashboard.

## Development Commands

### Core Development

- `yarn dev` - Start development server with Turbopack
- `yarn build` - Build production application
- `yarn start` - Start production server

### Code Quality

- `yarn lint` - Run ESLint with auto-fix
- `yarn format:all` - Format all files with Prettier
- `yarn format:staged` - Format only staged files

### Testing

- `yarn test` - Run Jest test suite
- `yarn test:coverage` - Run tests with coverage report
- `yarn test:ci` - Run tests for CI environment

### Database

- `yarn generate:types` - Generate TypeScript types from Supabase schema

## Architecture Overview

### Technology Stack

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL), Server Actions
- **Auth**: Clerk with Hebrew localization and JWT integration
- **Forms**: React Hook Form with dynamic field system
- **Testing**: Jest with Testing Library (95%+ coverage)

### Route Structure

```
/(home)         - Public marketing pages, scholarship browsing
/(auth)         - Authentication flows (login/signup)
/(dashboard)    - User dashboard (subscription-gated features)
/(admin)        - Admin interface (role-protected)
/onboarding     - Multi-step user registration
```

### Key Directories

- `/app/actions/` - Server actions organized by domain
- `/components/forms/` - Dynamic form system and field types
- `/hooks/` - Custom hooks for data fetching and state management
- `/utils/supabase/` - Database client configuration
- `/supabase/migrations/` - Database schema and RLS policies

## Authentication & Security

### Clerk Integration

- Hebrew localization with custom translations
- JWT tokens integrated with Supabase Row Level Security (RLS)
- Role-based access control (admin vs regular users)
- Middleware protection for all routes

### Database Security

- Comprehensive RLS policies protect all data
- Clerk user ID extraction from JWT claims
- Admin role checking via org_role claim
- User-specific data isolation

## Code Conventions

### Import Paths

- **MUST** use `@/` alias for all internal imports
- Never use relative imports (`../../../`)
- External packages use standard names

### TypeScript

- **Never use `any` type** - use `unknown` with type narrowing
- **Use database types** from `types/database.types.ts` (auto-generated)
- Use `Tables<"table_name">` for SELECT operations
- Use `TablesInsert<"table_name">` for INSERT operations
- Use `TablesUpdate<"table_name">` for UPDATE operations
- Use database enums: `Database["public"]["Enums"]["enum_name"]`

### Comments

- **Minimal comments** - prefer self-documenting code
- Only comment complex logic that needs "why" explanation
- No function documentation or JSDoc

## Form System Architecture

### Dynamic Questions System

The platform features a sophisticated dynamic form system that renders forms based on database configuration:

- **Field Types**: Text, number, date, file upload, multi-select, address, bank selection
- **Conditional Logic**: Questions appear based on previous answers
- **Real-time Validation**: React Hook Form with TypeScript validation
- **Server Actions**: All form submissions use Server Actions

### Form Patterns

- Use `dynamic-questions-form` component for scholarship applications
- Use individual form components for admin CRUD operations
- All forms follow React Hook Form patterns with proper validation

## Subscription Model

The platform has a tiered subscription system:

- **Free** - Basic scholarship browsing
- **Milga-Pro (₪280)** - Auto-submissions, premium features
- **Elite (₪350)** - Document management, account manager
- **VIP (₪400)** - Full feature access, annual support

Access control is implemented at multiple levels:

- Component-level guards using `PlanAccessGuard`
- Database-level validation in RLS policies
- Navigation shows locked features for lower tiers

## Testing Approach

- **95%+ test coverage** across actions, components, hooks
- Jest with jsdom environment and Testing Library
- Mocked dependencies (Clerk, Supabase, external APIs)
- Tests located in `/tests/` directory mirroring source structure

### Test Types

- Unit tests for utility functions and constants
- Integration tests for server actions
- Component tests with user interaction simulation
- API route testing for all endpoints

## Admin Dashboard

Comprehensive content management system with:

- **Scholarships** - Full CRUD with conditions and document requirements
- **Questions** - Dynamic form builder for applications
- **Users** - User management with subscription tracking
- **Content** - FAQ, testimonials, banners management
- **Financial** - Coupons, collaborations, payment tracking

Admin access is role-protected and includes analytics dashboard with user statistics.

## Common Tasks

### Adding New Database Table

1. Create migration in `/supabase/migrations/`
2. Add RLS policies for security
3. Run `yarn generate:types` to update TypeScript definitions
4. Create server actions in `/app/actions/`
5. Add relevant hooks in `/hooks/`
6. Write comprehensive tests

### Creating New Form

1. Use existing field types from `/components/forms/fields/`
2. Follow React Hook Form patterns
3. Implement server action for submission
4. Add proper TypeScript validation
5. Include loading states and error handling

### Adding Admin Feature

1. Create page in `/app/(admin)/admin/`
2. Use `AdminTable` component for data display
3. Implement CRUD server actions
4. Add proper admin role checking
5. Include comprehensive test coverage
