--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.13 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: _migration_scholarships_slugs; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: groups_question; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.groups_question VALUES ('1dc9863c-764f-4e37-9241-40b200de1e42', 'פרטים אישיים', '2025-06-15 19:50:32.232857+00', '2025-06-15 19:50:32.232857+00');
INSERT INTO public.groups_question VALUES ('d77e7b5e-9fc9-4a59-8217-aecaadfdb555', 'משפחה וילדים', '2025-06-15 19:50:40.907029+00', '2025-06-15 19:50:40.907029+00');
INSERT INTO public.groups_question VALUES ('9eb96a8a-c61e-4416-a13d-f35fadd9717e', 'כללי', '2025-06-15 21:02:11.015289+00', '2025-06-15 21:02:11.015289+00');


--
-- Data for Name: questions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.questions VALUES ('90b8af85-078e-4cf4-83e1-93f2e56546a7', 'address_select', 'personal_details', '{"label": "איפה נולדת?", "pattern": ".*", "tooltip": "כתובת מלאה", "required": true, "showSearch": false, "placeholder": "", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 20:00:05.641491+00', '2025-06-15 20:00:05.641491+00');
INSERT INTO public.questions VALUES ('9fb2d06c-ba02-491d-8da4-0b1dd0c41ae8', 'long_text', 'data_entry', '{"label": "רשום פסקה קצרה על עצמך", "pattern": ".*", "tooltip": "פסקה קצרה עליך מהילדות ועד היום", "required": false, "showSearch": false, "placeholder": "הכל התחיל לפני...", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:53:49.093044+00', '2025-06-15 19:53:49.093044+00');
INSERT INTO public.questions VALUES ('b9908df9-c2a5-4553-b877-9fd310a71bac', 'date_picker', 'personal_details', '{"label": "מה תאריך הלידה שלך?", "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:58:49.596527+00', '2025-06-15 19:58:49.596527+00');
INSERT INTO public.questions VALUES ('bb432f59-6152-4ca8-865a-d4589728be7c', 'bank_select', 'personal_details', '{"label": "מה הבנק שלך?", "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 20:00:28.634285+00', '2025-06-15 20:00:28.634285+00');
INSERT INTO public.questions VALUES ('e4164a0b-1731-49a2-a878-6b4495102f14', 'short_text', 'data_entry', '{"label": "במה אתה עובד?", "pattern": ".*", "tooltip": "לדוגמא: רואה חשבון", "required": true, "showSearch": false, "placeholder": "מה המקצוע העיקרי שלך", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:51:41.914418+00', '2025-06-15 19:51:41.914418+00');
INSERT INTO public.questions VALUES ('e1a4dad0-726d-4dac-9b92-5ecce9310e0f', 'single_select', 'personal_details', '{"label": "מגדר", "options": [{"id": "c73d4ad3-b172-41e4-b71e-bf5d768aa19f", "label": "זכר"}, {"id": "20547062-e810-424e-8431-e1c4821f1eac", "label": "נקבה"}], "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "הזן את המגדר שלך", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:54:30.424233+00', '2025-06-17 17:18:28.669737+00');
INSERT INTO public.questions VALUES ('b0c46cec-c3cf-49c4-910a-61c489405379', 'multi_select', 'personal_details', '{"label": "איפה אתה מתעניין ללמוד?", "options": [{"id": "8e39e75b-456f-4eb0-be9e-5e9a480fe4fb", "label": "תל אביב"}, {"id": "9fed659b-4e4c-404e-959c-c3d5be999df4", "label": "האקדמית יפו"}, {"id": "ea75f292-9348-4e48-945f-8429e28aa348", "label": "רייכמן"}, {"id": "a406d55c-a40c-4593-87ee-e04e7974062f", "label": "טכניון"}], "pattern": ".*", "tooltip": "מוסד לימודי אחד או יותר", "required": true, "showSearch": false, "placeholder": "בחר מוסד", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:56:42.388643+00', '2025-06-17 17:18:45.23664+00');
INSERT INTO public.questions VALUES ('ba1258ea-9bd0-4d6a-b4b0-4c78319ecf89', 'number_input', 'data_entry', '{"max": 115, "min": 14, "label": "בן כמה אתה?", "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "הזן את הגיל שלך", "patternMessage": "", "isFutureAllowed": false}', '1dc9863c-764f-4e37-9241-40b200de1e42', '2025-06-15 19:57:38.52171+00', '2025-06-17 17:20:06.834356+00');
INSERT INTO public.questions VALUES ('94d1b0af-5d4a-477c-b933-6d0987ee6b8c', 'single_select', 'personal_details', '{"label": "האם יש לך ילדים?", "options": [{"id": "574f1683-4a01-4794-aa1a-599b8c839c3d", "label": "כן"}, {"id": "f6ca8cd7-f454-48fc-8ac5-51146f5f7dd3", "label": "לא"}], "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "בחר תשובה", "patternMessage": "", "isFutureAllowed": false}', 'd77e7b5e-9fc9-4a59-8217-aecaadfdb555', '2025-06-15 20:01:03.269653+00', '2025-06-17 17:20:48.4111+00');
INSERT INTO public.questions VALUES ('5c90d9e1-8db8-430e-b799-3f48c288603d', 'single_select', 'personal_details', '{"label": "האם תרצה שנשלח את הפרטים שלך לחברות השמה?", "options": [{"id": "8a5ffaed-5dc4-4954-b55d-0d570f828986", "label": "כן"}, {"id": "9fed8981-93ca-42dc-9d45-ebcb1572e7ed", "label": "לא"}], "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "בחר תשובה", "patternMessage": "", "isFutureAllowed": false}', '9eb96a8a-c61e-4416-a13d-f35fadd9717e', '2025-06-15 21:02:46.889867+00', '2025-06-17 17:21:45.35054+00');
INSERT INTO public.questions VALUES ('35a4f610-137e-4efc-bef3-9e70cc037022', 'number_input', 'personal_details', '{"max": 10, "min": 1, "label": "כמה ילדים יש לך?", "pattern": ".*", "tooltip": "", "required": true, "showSearch": false, "placeholder": "הזן מספר", "patternMessage": "", "isFutureAllowed": false}', 'd77e7b5e-9fc9-4a59-8217-aecaadfdb555', '2025-06-15 20:02:26.37094+00', '2025-06-17 17:55:38.962507+00');


--
-- Data for Name: answers; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: banners; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.banners VALUES ('427f2ad3-cdf8-4882-a4eb-caebe5c7bd44', 'הכרזה לאורחים בלבד - משתמשים רשומים לא יראו אותה', 'ניתן להוסיף טקסט לגוף הבאנר', '#BAFFC9', '#000000', 'AlertCircle', 'לינק לגוגל', 'http://www.google.com', 0, 0, true, false, 'Guest', '2025-06-15 20:45:50.949693+00', '2025-06-21 21:35:06.547175+00');


--
-- Data for Name: collaborations; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.collaborations VALUES ('5def139b-72f9-48a3-b455-b99caf68a06e', 'שיתוף פעולה לדוגמא - All Jobs', 'תיאור לשיתוף הפעולה, לשימוש פנימי בלבד', 'http://www.google.com', 'bearer_token', 'BEARER_TOKEN_EXAMPLE', '2025-06-15 21:05:06.925422+00', '2025-06-22 19:31:57.869674+00');


--
-- Data for Name: collaborations_history; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: groups_condition; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.groups_condition VALUES ('a876a488-abef-4170-97a7-41994ebdb060', 'קבוצה לדגומא', '2025-06-17 17:38:01.874613+00', '2025-06-24 22:08:04.067624+00');


--
-- Data for Name: conditions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.conditions VALUES ('efe97c87-3525-4d87-990f-33067e2faa9a', NULL, '94d1b0af-5d4a-477c-b933-6d0987ee6b8c', 'in', '["574f1683-4a01-4794-aa1a-599b8c839c3d"]', '2025-06-16 09:55:34.33141+00', '2025-06-16 09:55:34.33141+00');
INSERT INTO public.conditions VALUES ('cdb69761-72c4-4d6b-90f6-e2967da303ac', NULL, '94d1b0af-5d4a-477c-b933-6d0987ee6b8c', 'in', '["574f1683-4a01-4794-aa1a-599b8c839c3d"]', '2025-06-17 17:55:38.981455+00', '2025-06-18 17:09:40.659998+00');
INSERT INTO public.conditions VALUES ('71cb6299-0033-4fc7-ab06-7addd6739050', NULL, '5c90d9e1-8db8-430e-b799-3f48c288603d', 'in', '[{"id": "8a5ffaed-5dc4-4954-b55d-0d570f828986", "label": "כן"}, {"id": "9fed8981-93ca-42dc-9d45-ebcb1572e7ed", "label": "לא"}]', '2025-06-15 21:05:06.934613+00', '2025-06-22 19:31:57.869674+00');
INSERT INTO public.conditions VALUES ('67468058-61e6-4735-90e3-9158706f27b7', 'a876a488-abef-4170-97a7-41994ebdb060', '5c90d9e1-8db8-430e-b799-3f48c288603d', 'in', '[{"id": "8a5ffaed-5dc4-4954-b55d-0d570f828986", "label": "כן"}]', '2025-06-24 22:08:04.097707+00', '2025-06-24 22:08:04.097707+00');
INSERT INTO public.conditions VALUES ('3ed40147-5119-4820-9ee5-95e75df36bbe', 'a876a488-abef-4170-97a7-41994ebdb060', '94d1b0af-5d4a-477c-b933-6d0987ee6b8c', 'in', '[{"id": "f6ca8cd7-f454-48fc-8ac5-51146f5f7dd3", "label": "לא"}]', '2025-06-24 22:08:04.097707+00', '2025-06-24 22:08:04.097707+00');
INSERT INTO public.conditions VALUES ('e8f5db09-29db-4522-a40f-5b2432a970e7', NULL, 'e1a4dad0-726d-4dac-9b92-5ecce9310e0f', 'in', '["c73d4ad3-b172-41e4-b71e-bf5d768aa19f"]', '2025-06-30 12:23:52.532144+00', '2025-06-30 12:23:52.532144+00');


--
-- Data for Name: contact; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.contact VALUES ('837fd4f1-bf1b-4a03-b658-d63ff5b3ccdc', '<EMAIL>', '2025-06-15 21:09:38.287336+00', '2025-06-15 21:09:38.287336+00');
INSERT INTO public.contact VALUES ('e47ff144-b4fd-4784-ba9e-b9d3b0348dfc', '<EMAIL>', '2025-06-15 21:09:43.564213+00', '2025-06-15 21:09:43.564213+00');


--
-- Data for Name: groups_coupon; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.groups_coupon VALUES ('2bc2ad83-05dd-48ac-8f76-7acf392f63e5', 'קבוצת קופונים לדוגמא', 'תיאור קבוצת קופונים', '2025-06-15 20:47:27.722287+00', '2025-06-15 20:47:27.722287+00');
INSERT INTO public.groups_coupon VALUES ('6b6acfb6-cd47-4169-ac58-80ddaa5246b9', 'קבוצת קופונים יחידים', 'קופונים שונים אחד מהשני, לדוגמא עבור יום פתוח של מוסד', '2025-06-15 20:49:57.067817+00', '2025-06-15 20:49:57.067817+00');


--
-- Data for Name: coupons; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.coupons VALUES ('abbfdf44-c57f-4e32-a44a-a316d8f7e638', 'MILGA100', 'percentage', 100, NULL, NULL, 0, '2bc2ad83-05dd-48ac-8f76-7acf392f63e5', '2025-06-15 20:49:26.318771+00', '2025-06-15 21:00:50.910052+00');
INSERT INTO public.coupons VALUES ('2d5cf325-1e44-436d-a054-3b4631c1b581', 'E9JT-QH6Y-JDRP', 'percentage', 50, NULL, 1, 0, '6b6acfb6-cd47-4169-ac58-80ddaa5246b9', '2025-06-22 08:31:26.76178+00', '2025-06-22 08:31:26.76178+00');
INSERT INTO public.coupons VALUES ('91e193a2-66f9-47a1-beec-eaa4372042f1', 'NM56-WDQ8-P8Y5', 'percentage', 50, NULL, 1, 0, '6b6acfb6-cd47-4169-ac58-80ddaa5246b9', '2025-06-22 08:31:26.76178+00', '2025-06-22 08:31:26.76178+00');
INSERT INTO public.coupons VALUES ('d9d547ab-ab50-4c59-a481-73ad0b947ef2', 'MKK8-2SDD-THMR', 'percentage', 50, NULL, 1, 0, '6b6acfb6-cd47-4169-ac58-80ddaa5246b9', '2025-06-22 08:31:26.76178+00', '2025-06-22 08:31:26.76178+00');
INSERT INTO public.coupons VALUES ('b7d24bb0-2b39-45b8-9db1-9d1f74bc96a8', '64GE-5CJP-JNWV', 'percentage', 50, NULL, 1, 0, '6b6acfb6-cd47-4169-ac58-80ddaa5246b9', '2025-06-22 08:31:26.76178+00', '2025-06-22 08:31:26.76178+00');
INSERT INTO public.coupons VALUES ('1ae96c73-ef85-436e-aeae-6388b5d1803e', 'FKN4-4ZHC-SG63', 'percentage', 50, NULL, 1, 0, '6b6acfb6-cd47-4169-ac58-80ddaa5246b9', '2025-06-22 08:31:26.76178+00', '2025-06-22 08:31:26.76178+00');


--
-- Data for Name: groups_document_type; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.groups_document_type VALUES ('d3b8839c-96d4-4983-bbdf-3b3e8a6c831e', 'קבוצת מסמכים', 'תיאור קבוצת מסמכים', '2025-06-15 19:33:49.671708+00', '2025-06-15 19:33:49.671708+00');


--
-- Data for Name: document_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.document_types VALUES ('1d347a18-f4b5-4cda-9fc7-6c81d333713b', 'מסמך לדוגמא', 'תיאור מסמך לדוגמא', NULL, NULL, '["application/pdf", "image/jpeg", "image/png", "image/webp"]', 'd3b8839c-96d4-4983-bbdf-3b3e8a6c831e', '2025-06-15 19:38:31.375603+00', '2025-06-15 19:38:31.375603+00', 10);


--
-- Data for Name: faq; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.faq VALUES ('60554d4d-7fda-457f-81e8-0c685ad893e5', 'שאלה 1', 'תושבה 1', 1, '2025-06-15 21:08:44.500868+00', '2025-06-15 21:09:18.334621+00');
INSERT INTO public.faq VALUES ('ad323f40-3924-4c3e-9353-8cc2fefc9ec7', 'שאלה 2', 'תשובה 2', 2, '2025-06-15 21:08:50.964812+00', '2025-06-15 21:09:18.33951+00');
INSERT INTO public.faq VALUES ('f2f6bcea-a1f1-41ea-8457-2f3025c4f3de', 'שאלה 3', 'תשובה 3', 3, '2025-06-15 21:08:57.821314+00', '2025-06-15 21:09:18.344471+00');


--
-- Data for Name: groups_scholarship; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.groups_scholarship VALUES ('40d5cd22-b88a-4d2f-8b04-ea5051be9ffe', 'קבוצת מלגות', 'תיאור הקבוצה', 'slug', 'graduation-cap', 'http://localhost:54321/storage/v1/object/public/groups_scholarship/40d5cd22-b88a-4d2f-8b04-ea5051be9ffe.webp', '2025-06-15 19:18:23.267722+00', '2025-06-15 19:18:23.519443+00');


--
-- Data for Name: link_collaboration_to_condition; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_collaboration_to_condition VALUES ('c83723d4-b19f-435b-b104-ec27da3cd47f', '5def139b-72f9-48a3-b455-b99caf68a06e', '71cb6299-0033-4fc7-ab06-7addd6739050', '2025-06-15 21:05:06.941299+00', '2025-06-15 21:05:06.941299+00');


--
-- Data for Name: link_question_to_collaboration; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_question_to_collaboration VALUES ('8add2534-cf43-431e-9fc1-9500b896f805', '5def139b-72f9-48a3-b455-b99caf68a06e', 'e4164a0b-1731-49a2-a878-6b4495102f14', '2025-06-22 19:31:57.869674+00', '2025-06-22 19:31:57.869674+00');
INSERT INTO public.link_question_to_collaboration VALUES ('30f6e39d-ba78-4d4a-afd1-27b5a33716db', '5def139b-72f9-48a3-b455-b99caf68a06e', '9fb2d06c-ba02-491d-8da4-0b1dd0c41ae8', '2025-06-22 19:31:57.869674+00', '2025-06-22 19:31:57.869674+00');
INSERT INTO public.link_question_to_collaboration VALUES ('381d0775-2c03-45a7-8ede-2e7ac026e4c8', '5def139b-72f9-48a3-b455-b99caf68a06e', '90b8af85-078e-4cf4-83e1-93f2e56546a7', '2025-06-22 19:31:57.869674+00', '2025-06-22 19:31:57.869674+00');


--
-- Data for Name: link_question_to_condition; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_question_to_condition VALUES ('35a4f610-137e-4efc-bef3-9e70cc037022', 'cdb69761-72c4-4d6b-90f6-e2967da303ac', '2025-06-17 17:55:38.981455+00', '2025-06-17 17:55:38.981455+00');


--
-- Data for Name: scholarships; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.scholarships VALUES ('2fc1b9ed-ed3d-4215-9bf2-dbdf13e7f174', 'public', 'מלגה פומבית לדוגמא', 'תיאור מלא וארוך למלגה הפומבית', 'תיאור קצר למלגה הפומבית', 50, 1000, 5000, '2025-06-15 21:00:00+00', '2025-06-20 21:00:00+00', '["תנאי 1", "תנאי 2", "תנאי 3"]', '["הטבה 1", "הטבה 2"]', 'כאן כותבים מי קהל היעד שאליו מיועדת המלגה', 'http://localhost:54321/storage/v1/object/public/scholarships/2fc1b9ed-ed3d-4215-9bf2-dbdf13e7f174.webp', 'https://app.milgapo.co.il/', 'הערות אלו יוצגו רק לעובדי מלגהפה', true, true, 'submission', 'אלון ברד', '<EMAIL>', '0506715060', '2025-07-04', '2025-06-16 09:55:33.712675+00', '2025-06-16 09:55:34.023549+00');
INSERT INTO public.scholarships VALUES ('3ed55fb2-5b93-4242-a4de-e97eb02b6b78', 'private', 'מלגה פרטית', 'תיאור מלא למלגה פרטית', 'תיאור קצר למלגה פרטית', 100, 10500, 25000, '2025-06-20 21:00:00+00', '2025-06-24 21:00:00+00', '["תנאי זכאות למלגה 1", "תנאי זכאות למלגה 2", "תנאי זכאות למלגה 3"]', '["הטבה 1", "הטבה 2"]', 'קהל היעד של המלגה', 'http://localhost:54321/storage/v1/object/public/scholarships/3ed55fb2-5b93-4242-a4de-e97eb02b6b78.webp', 'http://www.milgapo.co.il', 'מלגה לזכרים בלבד', false, true, 'submission', 'יפתח כהן', '<EMAIL>', '05061738596', '2025-07-04', '2025-06-16 10:26:43.883321+00', '2025-06-30 12:23:51.719125+00');


--
-- Data for Name: link_question_to_scholarship; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: testimonials; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.testimonials VALUES ('3f49fbb1-b992-47cd-80bb-19c0dc25bfbf', 'חוות דעת לדוגמא', 'טקס לדוגמא של חוות הדעת', 'personal', '2025-06-15 21:07:49.593032+00', '2025-06-15 21:07:49.593032+00');
INSERT INTO public.testimonials VALUES ('d670a3ae-aedc-4961-9ce0-54afec3941b0', 'דוגמא לחוות דעת מוסדית', 'חוות הדעת עצמה', 'institution', '2025-06-15 21:08:06.268184+00', '2025-06-15 21:08:06.268184+00');


--
-- Data for Name: link_scholarship_groups_to_testimonial; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: link_scholarship_to_condition; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_scholarship_to_condition VALUES ('2fc1b9ed-ed3d-4215-9bf2-dbdf13e7f174', 'efe97c87-3525-4d87-990f-33067e2faa9a', '2025-06-16 09:55:34.33822+00', '2025-06-16 09:55:34.33822+00');
INSERT INTO public.link_scholarship_to_condition VALUES ('3ed55fb2-5b93-4242-a4de-e97eb02b6b78', 'e8f5db09-29db-4522-a40f-5b2432a970e7', '2025-06-30 12:23:52.537348+00', '2025-06-30 12:23:52.537348+00');


--
-- Data for Name: link_scholarship_to_condition_groups; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: link_scholarship_to_document_type; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_scholarship_to_document_type VALUES ('2fc1b9ed-ed3d-4215-9bf2-dbdf13e7f174', '1d347a18-f4b5-4cda-9fc7-6c81d333713b', '2025-06-16 09:55:34.488478+00', '2025-06-16 09:55:34.488478+00');
INSERT INTO public.link_scholarship_to_document_type VALUES ('3ed55fb2-5b93-4242-a4de-e97eb02b6b78', '1d347a18-f4b5-4cda-9fc7-6c81d333713b', '2025-06-30 12:23:52.710821+00', '2025-06-30 12:23:52.710821+00');


--
-- Data for Name: link_scholarship_to_scholarship_groups; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_scholarship_to_scholarship_groups VALUES ('40d5cd22-b88a-4d2f-8b04-ea5051be9ffe', '2fc1b9ed-ed3d-4215-9bf2-dbdf13e7f174', '2025-06-16 09:55:34.180053+00');
INSERT INTO public.link_scholarship_to_scholarship_groups VALUES ('40d5cd22-b88a-4d2f-8b04-ea5051be9ffe', '3ed55fb2-5b93-4242-a4de-e97eb02b6b78', '2025-06-30 12:23:52.159644+00');


--
-- Data for Name: link_scholarship_to_testimonial; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.link_scholarship_to_testimonial VALUES ('3ed55fb2-5b93-4242-a4de-e97eb02b6b78', '3f49fbb1-b992-47cd-80bb-19c0dc25bfbf', '2025-06-30 12:23:51.954852+00', '2025-06-30 12:23:51.954852+00');


--
-- Data for Name: user_claims; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: user_scholarship_applications; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: user_subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- PostgreSQL database dump complete
--

