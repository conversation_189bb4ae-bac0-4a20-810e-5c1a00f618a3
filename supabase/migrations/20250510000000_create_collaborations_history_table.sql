-- Create collaborations_history table to track all collaboration data sent
CREATE TABLE IF NOT EXISTS collaborations_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  collaboration_id UUID NOT NULL REFERENCES collaborations(id) ON DELETE CASCADE,
  request_payload JSONB NOT NULL,
  response_status_code INTEGER,
  response_body JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add table and column comments
COMMENT ON TABLE collaborations_history IS 'Tracks history of all data sent to third-party collaborations';
COMMENT ON COLUMN collaborations_history.id IS 'Unique identifier for the history record';
COMMENT ON COLUMN collaborations_history.user_id IS 'Reference to the user whose data was sent';
COMMENT ON COLUMN collaborations_history.collaboration_id IS 'Reference to the collaboration the data was sent to';
COMMENT ON COLUMN collaborations_history.request_payload IS 'The data payload that was sent to the collaboration';
COMMENT ON COLUMN collaborations_history.response_status_code IS 'HTTP status code received from the collaboration API';
COMMENT ON COLUMN collaborations_history.response_body IS 'Response body received from the collaboration API';
COMMENT ON COLUMN collaborations_history.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN collaborations_history.updated_at IS 'Timestamp when the record was last updated';

-- Add updated_at trigger
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_collaborations_history_updated_at') THEN
        CREATE TRIGGER update_collaborations_history_updated_at
            BEFORE UPDATE ON collaborations_history
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_collaborations_history_user_id ON collaborations_history(user_id);

-- Create index on collaboration_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_collaborations_history_collaboration_id ON collaborations_history(collaboration_id);

-- Create index on created_at for faster date-based queries
CREATE INDEX IF NOT EXISTS idx_collaborations_history_created_at ON collaborations_history(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE collaborations_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users admins and service can view collaboration history" ON collaborations_history;
DROP POLICY IF EXISTS "Users admins and service can insert collaboration history" ON collaborations_history;
DROP POLICY IF EXISTS "Users admins and service can update collaboration history" ON collaborations_history;
DROP POLICY IF EXISTS "Users admins and service can delete collaboration history" ON collaborations_history;

-- Combined SELECT policy for users (own history), admins (all), and service role (all)
CREATE POLICY "Users admins and service can view collaboration history" ON collaborations_history
    FOR SELECT
    USING (
        (public.is_authenticated() AND user_id = public.requesting_user_id())
        OR public.is_admin()
        OR (SELECT current_setting('role')) = 'service_role'
    );

-- Combined INSERT policy for users (own history), admins (all), and service role (all)
CREATE POLICY "Users admins and service can insert collaboration history" ON collaborations_history
    FOR INSERT
    WITH CHECK (
        (public.is_authenticated() AND user_id = public.requesting_user_id())
        OR public.is_admin()
        OR (SELECT current_setting('role')) = 'service_role'
    );

-- Combined UPDATE policy for users (own history), admins (all), and service role (all)
CREATE POLICY "Users admins and service can update collaboration history" ON collaborations_history
    FOR UPDATE
    USING (
        (public.is_authenticated() AND user_id = public.requesting_user_id())
        OR public.is_admin()
        OR (SELECT current_setting('role')) = 'service_role'
    )
    WITH CHECK (
        (public.is_authenticated() AND user_id = public.requesting_user_id())
        OR public.is_admin()
        OR (SELECT current_setting('role')) = 'service_role'
    );

-- Combined DELETE policy for users (own history), admins (all), and service role (all)
CREATE POLICY "Users admins and service can delete collaboration history" ON collaborations_history
    FOR DELETE
    USING (
        (public.is_authenticated() AND user_id = public.requesting_user_id())
        OR public.is_admin()
        OR (SELECT current_setting('role')) = 'service_role'
    );

-- RLS Comments
COMMENT ON TABLE collaborations_history IS 'RLS enabled: Users can only access their own history, admins and service role have full access (consolidated policies)';
