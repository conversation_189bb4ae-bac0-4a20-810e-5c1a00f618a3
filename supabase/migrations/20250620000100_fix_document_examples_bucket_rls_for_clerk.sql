-- Fix RLS policies for document_examples bucket to work with Clerk authentication
-- This resolves the "new row violates row-level security policy" error when uploading files

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS public_upload_document_examples ON storage.objects;
DROP POLICY IF EXISTS public_update_document_examples ON storage.objects;
DROP POLICY IF EXISTS anon_upload_document_examples ON storage.objects;
DROP POLICY IF EXISTS anon_update_document_examples ON storage.objects;

-- Allow only admins to upload document examples
CREATE POLICY admin_upload_document_examples 
ON storage.objects 
FOR INSERT 
TO authenticated
WITH CHECK (
    bucket_id = 'document_examples' 
    AND public.is_admin()
);

-- Allow only admins to update document examples  
CREATE POLICY admin_update_document_examples 
ON storage.objects 
FOR UPDATE 
TO authenticated
USING (
    bucket_id = 'document_examples' 
    AND public.is_admin()
) 
WITH CHECK (
    bucket_id = 'document_examples' 
    AND public.is_admin()
);

-- Allow only admins to delete document examples
CREATE POLICY admin_delete_document_examples 
ON storage.objects 
FOR DELETE 
TO authenticated
USING (
    bucket_id = 'document_examples' 
    AND public.is_admin()
);

-- Allow public read access for document examples (users can view examples)
CREATE POLICY public_read_document_examples
ON storage.objects 
FOR SELECT
TO public
USING (bucket_id = 'document_examples');
