CREATE TABLE IF NOT EXISTS link_scholarship_groups_to_testimonial (
    scholarship_group_id UUID REFERENCES groups_scholarship(id) ON DELETE CASCADE,
    testimonial_id UUID REFERENCES testimonials(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_group_id, testimonial_id)
);

comment on table link_scholarship_groups_to_testimonial is 'Links between scholarship groups and testimonials';
comment on column link_scholarship_groups_to_testimonial.scholarship_group_id is 'The unique identifier for the scholarship group';
comment on column link_scholarship_groups_to_testimonial.testimonial_id is 'The unique identifier for the testimonial';
comment on column link_scholarship_groups_to_testimonial.created_at is 'The date and time when the testimonial was created';
comment on column link_scholarship_groups_to_testimonial.updated_at is 'The date and time when the testimonial was last updated';

CREATE INDEX IF NOT EXISTS idx_link_scholarship_groups_to_testimonial_group_id ON link_scholarship_groups_to_testimonial(scholarship_group_id);
CREATE INDEX IF NOT EXISTS idx_link_scholarship_groups_to_testimonial_testimonial_id ON link_scholarship_groups_to_testimonial(testimonial_id);

DROP TRIGGER IF EXISTS update_link_scholarship_groups_to_testimonial_updated_at ON link_scholarship_groups_to_testimonial;
CREATE TRIGGER update_link_scholarship_groups_to_testimonial_updated_at
    BEFORE UPDATE ON link_scholarship_groups_to_testimonial
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_scholarship_groups_to_testimonial ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial;
DROP POLICY IF EXISTS "Public read access for scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial;

-- Public read access (needed for testimonial display)
CREATE POLICY "Public read access for scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship group-testimonial links" ON link_scholarship_groups_to_testimonial
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_scholarship_groups_to_testimonial IS 'RLS enabled: Admin management, public read access for testimonial display (consolidated policies)';