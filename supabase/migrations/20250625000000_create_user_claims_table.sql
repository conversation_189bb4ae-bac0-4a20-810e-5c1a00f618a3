CREATE TABLE IF NOT EXISTS user_claims (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL DEFAULT auth.jwt()->>'sub',
    claim_key TEXT NOT NULL,
    claim_value JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE (user_id, claim_key)
);

COMMENT ON TABLE user_claims IS 'Key-value metadata for each user';
COMMENT ON COLUMN user_claims.user_id IS 'Reference to the user';
COMMENT ON COLUMN user_claims.claim_key IS 'Name of the claim';
COMMENT ON COLUMN user_claims.claim_value IS 'Value of the claim';

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_claims_updated_at') THEN
        CREATE TRIGGER update_user_claims_updated_at
            BEFORE UPDATE ON user_claims
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_user_claims_user_id ON user_claims(user_id);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE user_claims ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for user claims" ON user_claims;
DROP POLICY IF EXISTS "Consolidated INSERT access for user claims" ON user_claims;
DROP POLICY IF EXISTS "Consolidated UPDATE access for user claims" ON user_claims;
DROP POLICY IF EXISTS "Consolidated DELETE access for user claims" ON user_claims;

-- Single SELECT policy: users can view their own claims OR admins can view all
CREATE POLICY "Consolidated SELECT access for user claims" ON user_claims
    FOR SELECT
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single INSERT policy: users can insert their own claims OR admins can insert any
CREATE POLICY "Consolidated INSERT access for user claims" ON user_claims
    FOR INSERT
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single UPDATE policy: users can update their own claims OR admins can update any
CREATE POLICY "Consolidated UPDATE access for user claims" ON user_claims
    FOR UPDATE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    )
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single DELETE policy: users can delete their own claims OR admins can delete any
CREATE POLICY "Consolidated DELETE access for user claims" ON user_claims
    FOR DELETE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

COMMENT ON TABLE user_claims IS 'RLS enabled: Users can access their own claims, admins can access all (fully consolidated policies)';
