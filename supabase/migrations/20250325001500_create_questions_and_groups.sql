DO $$
BEGIN
    DROP TYPE IF EXISTS question_type CASCADE;
    DROP TYPE IF EXISTS question_section CASCADE;
    
    CREATE TYPE question_type AS ENUM (
        'single_select',
        'number_input',
        'short_text',
        'long_text',
        'multi_select',
        'date_picker',
        'address_select',
        'bank_select'
    );

    CREATE TYPE question_section AS ENUM (
        'personal_details',
        'data_entry',
        'specific_scholarship'
    );
END
$$;

CREATE TABLE IF NOT EXISTS groups_question (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

comment on table public.groups_question is 'Groups of questions for a dynamic forms.';
comment on column public.groups_question.id is 'The unique identifier for the question group.';
comment on column public.groups_question.name is 'The name of the question group.';
comment on column public.groups_question.created_at is 'The date and time when the question group was created.';
comment on column public.groups_question.updated_at is 'The date and time when the question group was last updated.';

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_groups_question_updated_at') THEN
        CREATE TRIGGER update_groups_question_updated_at
            BEFORE UPDATE ON groups_question
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type question_type NOT NULL,
    section question_section NOT NULL DEFAULT 'data_entry',
    dependencies JSONB,
    metadata JSONB,
    group_id UUID NOT NULL REFERENCES groups_question(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

comment on table public.questions is 'Questions for a dynamic forms.';
comment on column public.questions.id is 'The unique identifier for the question.';
comment on column public.questions.type is 'The type of the question.';
comment on column public.questions.section is 'The section of the question.';
comment on column public.questions.dependencies is 'The dependencies of the question.';
comment on column public.questions.metadata is 'The metadata of the question.';
comment on column public.questions.group_id is 'The group of the question.';
comment on column public.questions.created_at is 'The date and time when the question was created.';
comment on column public.questions.updated_at is 'The date and time when the question was last updated.';   

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_questions_updated_at') THEN
        CREATE TRIGGER update_questions_updated_at
            BEFORE UPDATE ON questions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;



-- Optimize ordering by group creation time
CREATE INDEX IF NOT EXISTS idx_groups_question_created_at ON groups_question(created_at);

-- Speed up question lookups by group
CREATE INDEX IF NOT EXISTS idx_questions_group_id ON questions(group_id);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS on both tables
ALTER TABLE groups_question ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for question groups" ON groups_question;
DROP POLICY IF EXISTS "Authenticated read access for question groups" ON groups_question;
DROP POLICY IF EXISTS "Admin INSERT access for question groups" ON groups_question;
DROP POLICY IF EXISTS "Admin UPDATE access for question groups" ON groups_question;
DROP POLICY IF EXISTS "Admin DELETE access for question groups" ON groups_question;
DROP POLICY IF EXISTS "Admin management access for question groups" ON groups_question;

DROP POLICY IF EXISTS "Consolidated SELECT access for questions" ON questions;
DROP POLICY IF EXISTS "Authenticated read access for questions" ON questions;
DROP POLICY IF EXISTS "Admin INSERT access for questions" ON questions;
DROP POLICY IF EXISTS "Admin UPDATE access for questions" ON questions;
DROP POLICY IF EXISTS "Admin DELETE access for questions" ON questions;
DROP POLICY IF EXISTS "Admin management access for questions" ON questions;

-- Consolidated policies to avoid multiple permissive policies performance warning

-- GROUPS_QUESTION TABLE POLICIES
-- Authenticated read access for question groups (for forms)
CREATE POLICY "Authenticated read access for question groups" ON groups_question
    FOR SELECT
    USING (public.is_authenticated());

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for question groups" ON groups_question
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for question groups" ON groups_question
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for question groups" ON groups_question
    FOR DELETE
    USING (public.is_admin());

-- QUESTIONS TABLE POLICIES
-- Authenticated read access for questions (for forms)
CREATE POLICY "Authenticated read access for questions" ON questions
    FOR SELECT
    USING (public.is_authenticated());

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for questions" ON questions
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for questions" ON questions
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for questions" ON questions
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE groups_question IS 'RLS enabled: Admin-only management, authenticated read access for forms (fully consolidated policies)';
COMMENT ON TABLE questions IS 'RLS enabled: Admin-only management, authenticated read access for forms (fully consolidated policies)';
