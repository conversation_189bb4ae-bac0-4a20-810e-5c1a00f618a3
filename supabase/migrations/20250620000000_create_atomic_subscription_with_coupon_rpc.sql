-- Create atomic subscription with coupon RPC function
-- This function handles subscription creation/update and coupon usage increment in a single transaction

CREATE OR REPLACE FUNCTION update_user_subscription_with_coupon(
    p_user_id TEXT,
    p_plan_id TEXT,
    p_subscription_data JSONB,
    p_coupon_code TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    error_message TEXT,
    subscription_id UUID
) AS $$
DECLARE
    v_coupon_id UUID;
    v_coupon_type public.coupon_type_enum;
    v_discount_value NUMERIC;
    v_expiration_date TIMESTAMP WITH TIME ZONE;
    v_usage_limit INTEGER;
    v_used_count INTEGER;
    v_plan_price NUMERIC;
    v_discount_amount NUMERIC;
    v_final_subscription_data JSONB;
    v_new_subscription_id UUID;
BEGIN
    -- Initialize discount amount
    v_discount_amount := 0;
    v_final_subscription_data := p_subscription_data;
    
    -- If coupon code is provided, validate and process it
    IF p_coupon_code IS NOT NULL AND p_coupon_code != '' THEN
        -- Get coupon details with row lock to prevent concurrent usage
        SELECT id, coupon_type, discount_value, expiration_date, usage_limit, used_count
        INTO v_coupon_id, v_coupon_type, v_discount_value, v_expiration_date, v_usage_limit, v_used_count
        FROM coupons 
        WHERE coupon_code = p_coupon_code
        FOR UPDATE;
        
        -- Check if coupon exists
        IF v_coupon_id IS NULL THEN
            RETURN QUERY SELECT false, 'קוד קופון לא נמצא.', NULL::UUID;
            RETURN;
        END IF;
        
        -- Check if coupon is expired
        IF v_expiration_date IS NOT NULL AND v_expiration_date < NOW() THEN
            RETURN QUERY SELECT false, 'תוקף הקופון פג.', NULL::UUID;
            RETURN;
        END IF;
        
        -- Usage limit will be checked atomically during increment
        
        -- Extract plan price from subscription data
        v_plan_price := COALESCE((p_subscription_data->>'plan_price')::NUMERIC, 0);
        
        -- Calculate discount
        IF v_coupon_type = 'percentage' THEN
            v_discount_amount := (v_plan_price * v_discount_value) / 100;
        ELSE
            v_discount_amount := v_discount_value;
        END IF;
        
        -- Ensure discount doesn't exceed plan price
        v_discount_amount := LEAST(v_plan_price, v_discount_amount);
        
        -- Update subscription data with coupon information
        v_final_subscription_data := v_final_subscription_data || 
            jsonb_build_object(
                'paid_amount', GREATEST(0, v_plan_price - v_discount_amount),
                'coupon_id', v_coupon_id
            );
    END IF;
    
    -- Deactivate existing subscriptions for the user
    UPDATE user_subscriptions 
    SET is_active = false 
    WHERE user_id = p_user_id AND is_active = true;
    
    -- Create new subscription
    INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        start_date,
        expiration_date,
        is_active,
        plan_price,
        paid_amount,
        transaction_id,
        order_id,
        payment_details,
        coupon_id
    )
    VALUES (
        p_user_id,
        p_plan_id,
        COALESCE((v_final_subscription_data->>'start_date')::TIMESTAMP WITH TIME ZONE, NOW()),
        (v_final_subscription_data->>'expiration_date')::TIMESTAMP WITH TIME ZONE,
        COALESCE((v_final_subscription_data->>'is_active')::BOOLEAN, true),
        COALESCE((v_final_subscription_data->>'plan_price')::NUMERIC, 0),
        COALESCE((v_final_subscription_data->>'paid_amount')::NUMERIC, 0),
        v_final_subscription_data->>'transaction_id',
        v_final_subscription_data->>'order_id',
        CASE 
            WHEN v_final_subscription_data->'payment_details' IS NOT NULL 
            THEN v_final_subscription_data->'payment_details'
            ELSE NULL
        END,
        (v_final_subscription_data->>'coupon_id')::UUID
    )
    RETURNING id INTO v_new_subscription_id;
    
    -- If coupon was used and subscription was created successfully, increment coupon usage
    IF v_coupon_id IS NOT NULL AND v_new_subscription_id IS NOT NULL THEN
        UPDATE coupons
        SET used_count = used_count + 1
        WHERE id = v_coupon_id AND (usage_limit IS NULL OR used_count < usage_limit);
        IF NOT FOUND THEN
            RAISE EXCEPTION 'הקופון הגיע למגבלת השימוש שלו במהלך ההרשמה.';
        END IF;
    END IF;
    
    -- Return success
    RETURN QUERY SELECT true, NULL::TEXT, v_new_subscription_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Return error details
        RETURN QUERY SELECT false, SQLERRM, NULL::UUID;
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Add comment explaining the function
COMMENT ON FUNCTION update_user_subscription_with_coupon(TEXT, TEXT, JSONB, TEXT) IS 
'Atomically creates/updates user subscription and increments coupon usage count if applicable. All operations are performed in a single transaction to ensure data consistency.'; 