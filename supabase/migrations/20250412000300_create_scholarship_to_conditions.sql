CREATE TABLE IF NOT EXISTS link_scholarship_to_condition (
    scholarship_id UUID NOT NULL REFERENCES scholarships(id) ON DELETE CASCADE,
    condition_id UUID NOT NULL REFERENCES conditions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_id, condition_id)
);

COMMENT ON TABLE public.link_scholarship_to_condition IS 'Directly links scholarships to specific conditions.';
COMMENT ON COLUMN public.link_scholarship_to_condition.scholarship_id IS 'Reference to the scholarship.';
COMMENT ON COLUMN public.link_scholarship_to_condition.condition_id IS 'Reference to the specific condition.';
COMMENT ON COLUMN public.link_scholarship_to_condition.created_at IS 'Date and time when the scholarship to condition link was created.';
COMMENT ON COLUMN public.link_scholarship_to_condition.updated_at IS 'Date and time when the scholarship to condition link was last updated.';

CREATE INDEX idx_link_scholarship_to_condition_scholarship_id ON link_scholarship_to_condition(scholarship_id);
CREATE INDEX idx_link_scholarship_to_condition_condition_id ON link_scholarship_to_condition(condition_id);

-- Trigger to update updated_at timestamp
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_link_scholarship_to_condition_updated_at') THEN
        CREATE TRIGGER update_link_scholarship_to_condition_updated_at
            BEFORE UPDATE ON link_scholarship_to_condition
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_scholarship_to_condition ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can access scholarship-condition links" ON link_scholarship_to_condition;
DROP POLICY IF EXISTS "Users can view scholarship-condition links" ON link_scholarship_to_condition;
DROP POLICY IF EXISTS "Consolidated SELECT access for scholarship-condition links" ON link_scholarship_to_condition;

-- Consolidated SELECT policy combining admin and authenticated user access
CREATE POLICY "Consolidated SELECT access for scholarship-condition links" ON link_scholarship_to_condition
    FOR SELECT
    USING (
        public.is_admin() 
        OR public.is_authenticated()
    );

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for scholarship-condition links" ON link_scholarship_to_condition
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship-condition links" ON link_scholarship_to_condition
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship-condition links" ON link_scholarship_to_condition
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_scholarship_to_condition IS 'RLS enabled: Admin management, authenticated read access for eligibility checking (consolidated policies)'; 