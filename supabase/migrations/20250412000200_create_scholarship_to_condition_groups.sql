CREATE TABLE IF NOT EXISTS link_scholarship_to_condition_groups (
    scholarship_id UUID NOT NULL REFERENCES scholarships(id) ON DELETE CASCADE,
    group_id UUID NOT NULL REFERENCES groups_condition(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_id, group_id)
);

COMMENT ON TABLE public.link_scholarship_to_condition_groups IS 'Links scholarships to condition groups for eligibility evaluation.';
COMMENT ON COLUMN public.link_scholarship_to_condition_groups.scholarship_id IS 'Reference to the scholarship.';
COMMENT ON COLUMN public.link_scholarship_to_condition_groups.group_id IS 'Reference to the condition group.';
COMMENT ON COLUMN public.link_scholarship_to_condition_groups.created_at IS 'Date and time when the scholarship to condition group link was created.';
COMMENT ON COLUMN public.link_scholarship_to_condition_groups.updated_at IS 'Date and time when the scholarship to condition group link was last updated.';

CREATE INDEX idx_link_scholarship_to_condition_groups_scholarship_id ON link_scholarship_to_condition_groups(scholarship_id);
CREATE INDEX idx_link_scholarship_to_condition_groups_group_id ON link_scholarship_to_condition_groups(group_id);

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_link_scholarship_to_condition_groups_updated_at') THEN
        CREATE TRIGGER update_link_scholarship_to_condition_groups_updated_at
            BEFORE UPDATE ON link_scholarship_to_condition_groups
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$; 

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_scholarship_to_condition_groups ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage scholarship-condition group links" ON link_scholarship_to_condition_groups;
DROP POLICY IF EXISTS "Public read access for scholarship-condition group links" ON link_scholarship_to_condition_groups;

-- Authenticated read access (needed for eligibility checking)
CREATE POLICY "Authenticated read access for scholarship-condition group links" ON link_scholarship_to_condition_groups
    FOR SELECT
    USING (public.is_authenticated());

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship-condition group links" ON link_scholarship_to_condition_groups
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship-condition group links" ON link_scholarship_to_condition_groups
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship-condition group links" ON link_scholarship_to_condition_groups
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_scholarship_to_condition_groups IS 'RLS enabled: Admin management, authenticated read access for eligibility checking (consolidated policies)'; 