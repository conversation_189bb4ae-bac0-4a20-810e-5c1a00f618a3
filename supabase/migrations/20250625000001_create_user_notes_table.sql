CREATE TABLE IF NOT EXISTS user_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL DEFAULT auth.jwt()->>'sub',
    reported_user_id TEXT NOT NULL,
    note TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE (user_id, reported_user_id, note, created_at)
);

COMMENT ON TABLE user_notes IS 'Notes for each user';
COMMENT ON COLUMN user_notes.user_id IS 'Reference to the user';
COMMENT ON COLUMN user_notes.reported_user_id IS 'Reference to the reported user';
COMMENT ON COLUMN user_notes.note IS 'Note for the reported user';

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_notes_updated_at') THEN
        CREATE TRIGGER update_user_notes_updated_at
            BEFORE UPDATE ON user_notes
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_user_notes_user_id ON user_notes(user_id);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE user_notes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for user notes" ON user_notes;
DROP POLICY IF EXISTS "Consolidated INSERT access for user notes" ON user_notes;
DROP POLICY IF EXISTS "Consolidated UPDATE access for user notes" ON user_notes;
DROP POLICY IF EXISTS "Consolidated DELETE access for user notes" ON user_notes;

-- Single SELECT policy: users can view their own notes OR admins can view all
CREATE POLICY "Consolidated SELECT access for user notes" ON user_notes
    FOR SELECT
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single INSERT policy: users can insert their own notes OR admins can insert any
CREATE POLICY "Consolidated INSERT access for user notes" ON user_notes
    FOR INSERT
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single UPDATE policy: users can update their own notes OR admins can update any
CREATE POLICY "Consolidated UPDATE access for user notes" ON user_notes
    FOR UPDATE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    )
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Single DELETE policy: users can delete their own notes OR admins can delete any
CREATE POLICY "Consolidated DELETE access for user notes" ON user_notes
    FOR DELETE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

COMMENT ON TABLE user_notes IS 'RLS enabled: Users can access their own notes, admins can access all (fully consolidated policies)';
