CREATE TYPE coupon_type_enum AS ENUM ('fixed_amount', 'percentage');

CREATE TABLE IF NOT EXISTS coupons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coupon_code TEXT UNIQUE NOT NULL,
    coupon_type coupon_type_enum NOT NULL DEFAULT 'fixed_amount',
    discount_value NUMERIC NOT NULL,
    expiration_date TIMESTAMP WITH TIME ZONE,
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0 NOT NULL,
    coupon_group_id UUID REFERENCES groups_coupon(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.coupons IS 'Stores coupon codes for discounts on subscriptions or services.';
COMMENT ON COLUMN public.coupons.id IS 'Unique identifier for each coupon.';
COMMENT ON COLUMN public.coupons.coupon_code IS 'Unique code for the coupon.';
COMMENT ON COLUMN public.coupons.coupon_type IS 'Type of discount: fixed_amount or percentage.';
COMMENT ON COLUMN public.coupons.discount_value IS 'Value of the discount (amount for fixed, percentage for percentage type).';
COMMENT ON COLUMN public.coupons.expiration_date IS 'The date after which the coupon is no longer valid.';
COMMENT ON COLUMN public.coupons.usage_limit IS 'Maximum number of times the coupon can be used.';
COMMENT ON COLUMN public.coupons.used_count IS 'How many times the coupon has been used.';
COMMENT ON COLUMN public.coupons.coupon_group_id IS 'Reference to the coupon group this coupon belongs to.';
COMMENT ON COLUMN public.coupons.created_at IS 'Date and time when the coupon was created.';
COMMENT ON COLUMN public.coupons.updated_at IS 'Date and time when the coupon was last updated.';

CREATE INDEX idx_coupons_expiration_date ON coupons(expiration_date);
CREATE INDEX idx_coupons_coupon_code ON coupons(coupon_code);
CREATE INDEX idx_coupons_coupon_group_id ON coupons(coupon_group_id);

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_coupons_updated_at') THEN
        CREATE TRIGGER update_coupons_updated_at
            BEFORE UPDATE ON coupons
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$; 


CREATE OR REPLACE FUNCTION increment_coupon_usage(p_coupon_code TEXT)
RETURNS TABLE(incremented BOOLEAN) AS $$
DECLARE
  v_coupon_id UUID;
  v_usage_limit INT;
  v_used_count INT;
BEGIN
  -- Get coupon in a locked mode to prevent concurrent updates
  SELECT id, usage_limit, used_count INTO v_coupon_id, v_usage_limit, v_used_count
  FROM coupons 
  WHERE coupon_code = p_coupon_code
  FOR UPDATE;
  
  IF v_coupon_id IS NULL THEN
    RETURN QUERY SELECT false AS incremented;
    RETURN;
  END IF;
  
  -- Check if usage limit is reached
  IF v_usage_limit IS NOT NULL AND v_used_count >= v_usage_limit THEN
    RETURN QUERY SELECT false AS incremented;
    RETURN;
  END IF;
  
  -- Update the count atomically
  UPDATE coupons 
  SET used_count = used_count + 1 
  WHERE id = v_coupon_id;
  
  RETURN QUERY SELECT true AS incremented;
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can access all coupons" ON coupons;
DROP POLICY IF EXISTS "Service role can read coupons for validation" ON coupons;
DROP POLICY IF EXISTS "Consolidated SELECT access for coupons" ON coupons;

CREATE POLICY "Admin SELECT access for coupons" ON coupons
    FOR SELECT
    USING (public.is_admin());

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for coupons" ON coupons
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for coupons" ON coupons
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for coupons" ON coupons
    FOR DELETE
    USING (public.is_admin());

COMMENT ON TABLE coupons IS 'RLS enabled: Admin-only access (security fix: removed unsafe service role check)';