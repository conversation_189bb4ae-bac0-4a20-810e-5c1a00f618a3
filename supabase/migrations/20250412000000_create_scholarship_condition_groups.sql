-- Create scholarship condition groups table
CREATE TABLE IF NOT EXISTS groups_condition (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.groups_condition IS 'Defines groups of conditions that can be applied to scholarships.';
COMMENT ON COLUMN public.groups_condition.id IS 'Unique identifier for each condition group.';
COMMENT ON COLUMN public.groups_condition.name IS 'Name of the condition group.';
COMMENT ON COLUMN public.groups_condition.created_at IS 'Date and time when the condition group was created.';
COMMENT ON COLUMN public.groups_condition.updated_at IS 'Date and time when the condition group was last updated.';

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_groups_condition_updated_at') THEN
        CREATE TRIGGER update_groups_condition_updated_at
            BEFORE UPDATE ON groups_condition
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE groups_condition ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can access all condition groups" ON groups_condition;
DROP POLICY IF EXISTS "Users can view condition groups" ON groups_condition;
DROP POLICY IF EXISTS "Consolidated SELECT access for condition groups" ON groups_condition;

-- Consolidated SELECT policy combining admin and authenticated user access
CREATE POLICY "Consolidated SELECT access for condition groups" ON groups_condition
    FOR SELECT
    USING (
        public.is_admin() 
        OR public.is_authenticated()
    );

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for condition groups" ON groups_condition
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for condition groups" ON groups_condition
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for condition groups" ON groups_condition
    FOR DELETE
    USING (public.is_admin());

COMMENT ON TABLE groups_condition IS 'RLS enabled: Admin-only management, public read access for forms'; 