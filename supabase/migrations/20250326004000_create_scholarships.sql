CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

CREATE TYPE scholarship_type AS ENUM ('submission', 'guidance');

CREATE TABLE IF NOT EXISTS groups_scholarship (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    icon TEXT NOT NULL,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

comment on table groups_scholarship is 'Stores scholarship groups';
comment on column groups_scholarship.id is 'The unique identifier for the scholarship group';
comment on column groups_scholarship.title is 'The title of the scholarship group';
comment on column groups_scholarship.description is 'The description of the scholarship group';
comment on column groups_scholarship.slug is 'The slug of the scholarship group';
comment on column groups_scholarship.icon is 'The icon of the scholarship group';
comment on column groups_scholarship.image_url is 'The image url of the scholarship group';
comment on column groups_scholarship.created_at is 'The date and time when the scholarship group was created';
comment on column groups_scholarship.updated_at is 'The date and time when the scholarship group was last updated';


CREATE TABLE IF NOT EXISTS scholarships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    slug TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    short_description TEXT NOT NULL,
    volunteer_hours INTEGER NOT NULL DEFAULT 0,
    min_amount INTEGER NOT NULL DEFAULT 0,
    max_amount INTEGER NOT NULL DEFAULT 0,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    requirements JSONB NOT NULL DEFAULT '[]'::jsonb,
    benefits JSONB NOT NULL DEFAULT '[]'::jsonb,
    target_audience TEXT NOT NULL DEFAULT '',
    image_url TEXT,
    url TEXT,
    internal_notes TEXT,
    is_public BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    scholarship_type scholarship_type NOT NULL DEFAULT 'submission',
    contact_person TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    response_date DATE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

comment on table scholarships is 'Stores scholarships';
comment on column scholarships.id is 'The unique identifier for the scholarship';
comment on column scholarships.slug is 'The slug of the scholarship';
comment on column scholarships.title is 'The title of the scholarship';
comment on column scholarships.description is 'The description of the scholarship';
comment on column scholarships.short_description is 'The short description of the scholarship';
comment on column scholarships.scholarship_type is 'The type of the scholarship';
comment on column scholarships.created_at is 'The date and time when the scholarship was created';
comment on column scholarships.updated_at is 'The date and time when the scholarship was last updated';
comment on column scholarships.image_url is 'The image url of the scholarship';
comment on column scholarships.url is 'The external URL of the scholarship';
comment on column scholarships.volunteer_hours is 'The number of volunteer hours required for the scholarship';
comment on column scholarships.min_amount is 'The minimum amount of the scholarship';
comment on column scholarships.max_amount is 'The maximum amount of the scholarship';
comment on column scholarships.start_date is 'The start date of the scholarship';
comment on column scholarships.end_date is 'The end date of the scholarship';
comment on column scholarships.requirements is 'The requirements of the scholarship';
comment on column scholarships.benefits is 'The benefits of the scholarship';
comment on column scholarships.target_audience is 'The target audience of the scholarship';
comment on column scholarships.is_public is 'Whether the scholarship is publicly visible';
comment on column scholarships.is_active is 'Indicates whether the scholarship is currently active and visible to users.';
comment on column scholarships.internal_notes is 'Internal notes for staff, not visible to users.';
comment on column scholarships.contact_person is 'The name of the contact person for the scholarship.';
comment on column scholarships.contact_email is 'The email of the contact person for the scholarship.';
comment on column scholarships.contact_phone is 'The phone number of the contact person for the scholarship.';
comment on column scholarships.response_date is 'The date by which applicants can expect a response.';

CREATE INDEX IF NOT EXISTS idx_scholarships_slug_is_public ON scholarships(slug, is_public);
CREATE INDEX IF NOT EXISTS idx_scholarships_is_active ON scholarships(is_active);

CREATE TABLE IF NOT EXISTS link_scholarship_to_scholarship_groups (
    scholarship_group_id UUID REFERENCES groups_scholarship(id) ON DELETE CASCADE,
    scholarship_id UUID REFERENCES scholarships(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (scholarship_group_id, scholarship_id)
);

comment on table link_scholarship_to_scholarship_groups is 'Stores the relationship between scholarship groups and scholarships';
comment on column link_scholarship_to_scholarship_groups.scholarship_group_id is 'The unique identifier for the scholarship group';
comment on column link_scholarship_to_scholarship_groups.scholarship_id is 'The unique identifier for the scholarship';

CREATE INDEX IF NOT EXISTS idx_link_scholarship_to_scholarship_groups_scholarship_id ON link_scholarship_to_scholarship_groups(scholarship_id);
CREATE INDEX IF NOT EXISTS idx_link_scholarship_to_scholarship_groups_group_id ON link_scholarship_to_scholarship_groups(scholarship_group_id);

DROP TRIGGER IF EXISTS update_groups_scholarship_updated_at ON groups_scholarship;
CREATE TRIGGER update_groups_scholarship_updated_at
    BEFORE UPDATE ON groups_scholarship
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_scholarships_updated_at ON scholarships;
CREATE TRIGGER update_scholarships_updated_at
    BEFORE UPDATE ON scholarships
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE groups_scholarship ENABLE ROW LEVEL SECURITY;
ALTER TABLE scholarships ENABLE ROW LEVEL SECURITY;
ALTER TABLE link_scholarship_to_scholarship_groups ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for scholarship groups" ON groups_scholarship;
DROP POLICY IF EXISTS "Public read access for scholarship groups" ON groups_scholarship;
DROP POLICY IF EXISTS "Admin INSERT access for scholarship groups" ON groups_scholarship;
DROP POLICY IF EXISTS "Admin UPDATE access for scholarship groups" ON groups_scholarship;
DROP POLICY IF EXISTS "Admin DELETE access for scholarship groups" ON groups_scholarship;
DROP POLICY IF EXISTS "Admin management access for scholarship groups" ON groups_scholarship;

DROP POLICY IF EXISTS "Consolidated SELECT access for scholarships" ON scholarships;
DROP POLICY IF EXISTS "Public read access for scholarships" ON scholarships;
DROP POLICY IF EXISTS "Admin INSERT access for scholarships" ON scholarships;
DROP POLICY IF EXISTS "Admin UPDATE access for scholarships" ON scholarships;
DROP POLICY IF EXISTS "Admin DELETE access for scholarships" ON scholarships;
DROP POLICY IF EXISTS "Admin management access for scholarships" ON scholarships;

DROP POLICY IF EXISTS "Consolidated SELECT access for scholarship-group links" ON link_scholarship_to_scholarship_groups;
DROP POLICY IF EXISTS "Authenticated read access for scholarship-group links" ON link_scholarship_to_scholarship_groups;
DROP POLICY IF EXISTS "Admin INSERT access for scholarship-group links" ON link_scholarship_to_scholarship_groups;
DROP POLICY IF EXISTS "Admin UPDATE access for scholarship-group links" ON link_scholarship_to_scholarship_groups;
DROP POLICY IF EXISTS "Admin DELETE access for scholarship-group links" ON link_scholarship_to_scholarship_groups;
DROP POLICY IF EXISTS "Admin management access for scholarship-group links" ON link_scholarship_to_scholarship_groups;

-- GROUPS_SCHOLARSHIP TABLE POLICIES
-- Public read access for scholarship groups
CREATE POLICY "Public read access for scholarship groups" ON groups_scholarship
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship groups" ON groups_scholarship
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship groups" ON groups_scholarship
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship groups" ON groups_scholarship
    FOR DELETE
    USING (public.is_admin());

-- SCHOLARSHIPS TABLE POLICIES
-- Public access to active scholarships, admin access to all
CREATE POLICY "Public read access for scholarships" ON scholarships
    FOR SELECT
    USING (
        (is_active = true AND is_public = true) 
        OR public.is_admin()
    );

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarships" ON scholarships
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarships" ON scholarships
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarships" ON scholarships
    FOR DELETE
    USING (public.is_admin());

-- LINK_SCHOLARSHIP_TO_SCHOLARSHIP_GROUPS TABLE POLICIES
-- Public read access for scholarship-group links
CREATE POLICY "Public read access for scholarship-group links" ON link_scholarship_to_scholarship_groups
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship-group links" ON link_scholarship_to_scholarship_groups
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship-group links" ON link_scholarship_to_scholarship_groups
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship-group links" ON link_scholarship_to_scholarship_groups
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE groups_scholarship IS 'RLS enabled: Admin-only management, public read access (fully consolidated policies)';
COMMENT ON TABLE scholarships IS 'RLS enabled: Admin-only management, public read for active/public scholarships (fully consolidated policies)';
COMMENT ON TABLE link_scholarship_to_scholarship_groups IS 'RLS enabled: Admin-only management, authenticated read access (fully consolidated policies)';

