-- Create groups_document_type table
CREATE TABLE IF NOT EXISTS groups_document_type (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.groups_document_type IS 'Groups of document types for organizing document types.';
COMMENT ON COLUMN public.groups_document_type.id IS 'The unique identifier for the document type group.';
COMMENT ON COLUMN public.groups_document_type.name IS 'The name of the document type group.';
COMMENT ON COLUMN public.groups_document_type.description IS 'Description of the document type group.';
COMMENT ON COLUMN public.groups_document_type.created_at IS 'The date and time when the document type group was created.';
COMMENT ON COLUMN public.groups_document_type.updated_at IS 'The date and time when the document type group was last updated.';

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_groups_document_type_updated_at') THEN
        CREATE TRIGGER update_groups_document_type_updated_at
            BEFORE UPDATE ON groups_document_type
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Add group_id to document_types and reference groups_document_type
CREATE TABLE IF NOT EXISTS document_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    example_file_path TEXT,
    link_url TEXT,
    allowed_mime_types JSONB NOT NULL DEFAULT '[]'::jsonb CHECK (jsonb_typeof(allowed_mime_types) = 'array'),
    group_id UUID NOT NULL REFERENCES groups_document_type(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.document_types IS 'Stores document types for scholarship applications.';
COMMENT ON COLUMN public.document_types.id IS 'Unique identifier for each document type.';
COMMENT ON COLUMN public.document_types.name IS 'Name of the document type.';
COMMENT ON COLUMN public.document_types.description IS 'Description of the document type.';
COMMENT ON COLUMN public.document_types.example_file_path IS 'Path to an example file for this document type.';
COMMENT ON COLUMN public.document_types.link_url IS 'Optional link to an example or template for the document.';
COMMENT ON COLUMN public.document_types.allowed_mime_types IS 'JSON array of allowed MIME types for this document type.';
COMMENT ON COLUMN public.document_types.group_id IS 'The group of the document type.';
COMMENT ON COLUMN public.document_types.created_at IS 'Date and time when the document type was created.';
COMMENT ON COLUMN public.document_types.updated_at IS 'Date and time when the document type was last updated.';

-- Create trigger for auto-updating updated_at column
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_document_types_updated_at') THEN
        CREATE TRIGGER update_document_types_updated_at
            BEFORE UPDATE ON document_types
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_document_types_name ON document_types(name);
CREATE INDEX IF NOT EXISTS idx_document_types_created_at ON document_types(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS on both tables
ALTER TABLE groups_document_type ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_types ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for document type groups" ON groups_document_type;
DROP POLICY IF EXISTS "Public read access for document type groups" ON groups_document_type;
DROP POLICY IF EXISTS "Admin INSERT access for document type groups" ON groups_document_type;
DROP POLICY IF EXISTS "Admin UPDATE access for document type groups" ON groups_document_type;
DROP POLICY IF EXISTS "Admin DELETE access for document type groups" ON groups_document_type;
DROP POLICY IF EXISTS "Admin management access for document type groups" ON groups_document_type;

DROP POLICY IF EXISTS "Consolidated SELECT access for document types" ON document_types;
DROP POLICY IF EXISTS "Public read access for document types" ON document_types;
DROP POLICY IF EXISTS "Admin INSERT access for document types" ON document_types;
DROP POLICY IF EXISTS "Admin UPDATE access for document types" ON document_types;
DROP POLICY IF EXISTS "Admin DELETE access for document types" ON document_types;
DROP POLICY IF EXISTS "Admin management access for document types" ON document_types;

-- GROUPS_DOCUMENT_TYPE TABLE POLICIES
-- Public read access for document type groups (for forms)
CREATE POLICY "Public read access for document type groups" ON groups_document_type
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for document type groups" ON groups_document_type
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for document type groups" ON groups_document_type
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for document type groups" ON groups_document_type
    FOR DELETE
    USING (public.is_admin());

-- DOCUMENT_TYPES TABLE POLICIES
-- Public read access for document types (for forms)
CREATE POLICY "Public read access for document types" ON document_types
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for document types" ON document_types
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for document types" ON document_types
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for document types" ON document_types
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE groups_document_type IS 'RLS enabled: Admin-only management, public read access for forms (fully consolidated policies)';
COMMENT ON TABLE document_types IS 'RLS enabled: Admin-only management, public read access for forms (fully consolidated policies)';

