
CREATE TABLE IF NOT EXISTS answers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    answer TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

comment on table public.answers is 'User answers to questions in dynamic forms.';
comment on column public.answers.id is 'The unique identifier for the answer.';
comment on column public.answers.question_id is 'The unique identifier for the question.';
comment on column public.answers.user_id is 'The unique identifier for the user.';
comment on column public.answers.answer is 'The answer to the question.';
comment on column public.answers.created_at is 'The date and time when the answer was created.';
comment on column public.answers.updated_at is 'The date and time when the answer was last updated.';

CREATE INDEX idx_answers_question_id ON answers(question_id);
CREATE INDEX idx_answers_user_id ON answers(user_id);

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_answers_updated_at') THEN
        CREATE TRIGGER update_answers_updated_at
            BEFORE UPDATE ON answers
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$; 

ALTER TABLE public.answers
ADD CONSTRAINT answers_question_id_user_id_key UNIQUE (question_id, user_id);

COMMENT ON CONSTRAINT answers_question_id_user_id_key ON public.answers IS 'Ensures that each user can only answer a specific question once.'; 

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE answers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users and admins can view answers" ON answers;
DROP POLICY IF EXISTS "Users and admins can insert answers" ON answers;
DROP POLICY IF EXISTS "Users and admins can update answers" ON answers;
DROP POLICY IF EXISTS "Users and admins can delete answers" ON answers;

-- Combined SELECT policy for users (own answers) and admins (all answers)
CREATE POLICY "Users and admins can view answers" ON answers
    FOR SELECT
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Combined INSERT policy for users (own answers) and admins (all answers)
CREATE POLICY "Users and admins can insert answers" ON answers
    FOR INSERT
    WITH CHECK (
        user_id = public.requesting_user_id()
        OR public.is_admin()
    );

-- Combined UPDATE policy for users (own answers) and admins (all answers)
CREATE POLICY "Users and admins can update answers" ON answers
    FOR UPDATE
    USING (
        user_id = public.requesting_user_id()
        OR public.is_admin()
    )
    WITH CHECK (
        user_id = public.requesting_user_id()
        OR public.is_admin()
    );

-- Combined DELETE policy for users (own answers) and admins (all answers)
CREATE POLICY "Users and admins can delete answers" ON answers
    FOR DELETE
    USING (
        user_id = public.requesting_user_id()
        OR public.is_admin()
    );

COMMENT ON TABLE answers IS 'RLS enabled: Users can only access their own answers, admins can access all (consolidated policies)'; 