CREATE TABLE IF NOT EXISTS link_scholarship_to_document_type (
    scholarship_id UUID REFERENCES scholarships(id) ON DELETE CASCADE,
    document_type_id UUID REFERENCES document_types(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_id, document_type_id)
);

COMMENT ON TABLE public.link_scholarship_to_document_type IS 'Links scholarships to required document types.';
COMMENT ON COLUMN public.link_scholarship_to_document_type.scholarship_id IS 'Reference to the scholarship.';
COMMENT ON COLUMN public.link_scholarship_to_document_type.document_type_id IS 'Reference to the document type.';
COMMENT ON COLUMN public.link_scholarship_to_document_type.created_at IS 'Date and time when the link was created.';
COMMENT ON COLUMN public.link_scholarship_to_document_type.updated_at IS 'Date and time when the link was last updated.';

-- Create trigger for auto-updating updated_at column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_link_scholarship_to_document_type_updated_at') THEN
        CREATE TRIGGER update_link_scholarship_to_document_type_updated_at
            BEFORE UPDATE ON link_scholarship_to_document_type
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create indexes
CREATE INDEX idx_link_scholarship_to_document_type_scholarship_id ON link_scholarship_to_document_type(scholarship_id);
CREATE INDEX idx_link_scholarship_to_document_type_document_type_id ON link_scholarship_to_document_type(document_type_id);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_scholarship_to_document_type ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage scholarship-document type links" ON link_scholarship_to_document_type;
DROP POLICY IF EXISTS "Public read access for scholarship-document type links" ON link_scholarship_to_document_type;

-- Public read access (needed for scholarship applications and browsing)
CREATE POLICY "Public read access for scholarship-document type links" ON link_scholarship_to_document_type
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship-document type links" ON link_scholarship_to_document_type
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship-document type links" ON link_scholarship_to_document_type
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship-document type links" ON link_scholarship_to_document_type
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_scholarship_to_document_type IS 'RLS enabled: Admin management, public read access for scholarship requirements (consolidated policies)';
