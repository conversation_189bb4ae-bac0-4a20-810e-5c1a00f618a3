INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'groups_scholarship',
    'groups_scholarship',
    true,
    5242880, 
    ARRAY['image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;


DROP POLICY IF EXISTS "Public Access" ON storage.objects;
CREATE POLICY "Public Access"
ON storage.objects FOR SELECT
USING (bucket_id = 'groups_scholarship');

DROP POLICY IF EXISTS "Employees and admins can upload files" ON storage.objects;
CREATE POLICY "Employees and admins can upload files"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'groups_scholarship'
    AND (SELECT current_setting('role')) = 'authenticated'
);

DROP POLICY IF EXISTS "Employees and admins can update files" ON storage.objects;
CREATE POLICY "Employees and admins can update files"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'groups_scholarship'
    AND (SELECT current_setting('role')) = 'authenticated'
);

DROP POLICY IF EXISTS "Employees and admins can delete files" ON storage.objects;
CREATE POLICY "Employees and admins can delete files"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'groups_scholarship'
    AND (SELECT current_setting('role')) = 'authenticated'
); 