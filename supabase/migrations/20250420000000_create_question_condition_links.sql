-- First check if the conditions table exists, if not, we have to create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'conditions') THEN
        -- Create conditions table
        CREATE TABLE IF NOT EXISTS conditions (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            group_id UUID NOT NULL REFERENCES groups_condition(id) ON DELETE CASCADE,
            question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
            condition JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );

        CREATE INDEX idx_conditions_group_id ON conditions(group_id);
        CREATE INDEX idx_conditions_question_id ON conditions(question_id);

        CREATE TRIGGER update_conditions_updated_at
            BEFORE UPDATE ON conditions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create link_question_to_condition table to replace dependencies column in questions
CREATE TABLE IF NOT EXISTS link_question_to_condition (
    question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
    condition_id UUID REFERENCES conditions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (question_id, condition_id)
);

comment on table link_question_to_condition is 'Links between questions and their dependency conditions';
comment on column link_question_to_condition.question_id is 'The unique identifier for the question that has dependencies';
comment on column link_question_to_condition.condition_id is 'The unique identifier for the condition that the question depends on';
comment on column link_question_to_condition.created_at is 'The date and time when the link was created';
comment on column link_question_to_condition.updated_at is 'The date and time when the link was last updated';

CREATE INDEX IF NOT EXISTS idx_link_question_to_condition_question_id ON link_question_to_condition(question_id);
CREATE INDEX IF NOT EXISTS idx_link_question_to_condition_condition_id ON link_question_to_condition(condition_id);

DROP TRIGGER IF EXISTS update_link_question_to_condition_updated_at ON link_question_to_condition;
CREATE TRIGGER update_link_question_to_condition_updated_at
    BEFORE UPDATE ON link_question_to_condition
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Migrate data and remove dependencies column from questions table
DO $$
DECLARE
    question_record RECORD;
    dependency RECORD;
    condition_group_id UUID;
    condition_id UUID;
BEGIN
    -- Check if dependencies column exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'questions' 
        AND column_name = 'dependencies'
    ) THEN
        -- For each question with dependencies
        FOR question_record IN 
            SELECT id, dependencies FROM questions 
            WHERE dependencies IS NOT NULL AND jsonb_array_length(dependencies) > 0
        LOOP
            -- Create a condition group for this question
            INSERT INTO groups_condition (name)
            VALUES (question_record.id)
            RETURNING id INTO condition_group_id;

            -- Process each dependency
            FOR dependency IN 
                SELECT * FROM jsonb_array_elements(question_record.dependencies)
            LOOP
                -- Create a condition
                INSERT INTO conditions (
                    group_id,
                    question_id,
                    condition
                ) VALUES (
                    condition_group_id,
                    (dependency->>'dependent_question_id')::UUID,
                    jsonb_build_object(
                        'condition_type', 
                        CASE 
                            WHEN dependency ? 'condition_type' THEN dependency->>'condition_type'
                            WHEN dependency ? 'condition_value' AND jsonb_typeof(dependency->'condition_value') = 'array' THEN 'in'
                            ELSE 'equals'
                        END,
                        'condition_value', dependency->'condition_value'
                    )
                ) RETURNING id INTO condition_id;

                -- Link the question to the condition
                INSERT INTO link_question_to_condition (question_id, condition_id)
                VALUES (question_record.id, condition_id);
            END LOOP;
        END LOOP;

        -- Remove the dependencies column
        ALTER TABLE questions DROP COLUMN dependencies;
    END IF;
END $$; 

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_question_to_condition ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage question-condition links" ON link_question_to_condition;
DROP POLICY IF EXISTS "Public read access for question-condition links" ON link_question_to_condition;
DROP POLICY IF EXISTS "Consolidated SELECT access for question-condition links" ON link_question_to_condition;

-- Consolidated SELECT policy (authenticated read access needed for form conditional logic)
CREATE POLICY "Consolidated SELECT access for question-condition links" ON link_question_to_condition
    FOR SELECT
    USING (public.is_authenticated());

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for question-condition links" ON link_question_to_condition
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for question-condition links" ON link_question_to_condition
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for question-condition links" ON link_question_to_condition
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_question_to_condition IS 'RLS enabled: Authenticated read access, admin write access (consolidated policies)'; 