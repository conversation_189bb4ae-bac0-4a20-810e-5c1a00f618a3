CREATE TABLE IF NOT EXISTS contact (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
); 

comment on table public.contact is 'Contact form submissions table.';
comment on column public.contact.id is 'The unique identifier for the contact form submission.';
comment on column public.contact.email is 'The email address of the user who submitted the form.';
comment on column public.contact.created_at is 'The date and time when the contact form submission was created.';
comment on column public.contact.updated_at is 'The date and time when the contact form submission was last updated.';

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_contact_updated_at') THEN
        CREATE TRIGGER update_contact_updated_at
            BEFORE UPDATE ON contact
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Optimize ordering by creation time
CREATE INDEX IF NOT EXISTS idx_contact_created_at ON contact(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE contact ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can submit contact forms" ON contact;
DROP POLICY IF EXISTS "Admins can access all contact submissions" ON contact;

-- Anyone can submit contact forms with valid email (public submissions)
CREATE POLICY "Anyone can submit contact forms" ON contact
    FOR INSERT
    WITH CHECK (
        email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        AND length(email) <= 254
        AND length(email) >= 5
    );

-- Only admins can read all contact submissions
CREATE POLICY "Admins can access all contact submissions" ON contact
    FOR SELECT
    USING (public.is_admin());

COMMENT ON TABLE contact IS 'RLS enabled: Public submissions with email validation, admin-only read access';
