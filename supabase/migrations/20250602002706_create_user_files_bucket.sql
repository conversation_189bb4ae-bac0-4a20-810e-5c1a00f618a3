-- Create user_documents bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'user_documents',
    'user_documents',
    false, -- User files are typically private
    10485760, -- 10MB, adjust as needed
    ARRAY[
        'application/pdf', 
        'image/jpeg', 
        'image/png', 
        'image/webp', 
        'text/plain',
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ]
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Policies for user_documents bucket
-- These policies assume that documents are stored with the user's ID (UUID) as the first segment of the file path.
-- For example: '{user_id}/documents/report.pdf'

-- Policies for user_documents bucket are being removed.
-- Access control will be handled by server actions using Clerk authentication.
-- Server actions should use a Supabase client with service_role privileges for storage operations.

-- Drop any existing policies on storage.objects for the user_documents bucket to ensure a clean state.
-- Note: Policy names might have varied through iterations. Dropping known names.
DROP POLICY IF EXISTS "Users can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload documents to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can select documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can insert documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update documents" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete documents" ON storage.objects;

-- Helper function to check if the requesting user owns a document in user_documents bucket
-- Extracts the first path segment and compares it to the requesting user's ID
CREATE OR REPLACE FUNCTION public.is_owner_user_document(file_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    RETURN split_part(storage.foldername(file_name), '/', 1) = public.requesting_user_id()::text;
END;
$$;

-- Users can only view their own documents (folder structure: user_id/...)
CREATE POLICY "Users view own documents" 
ON storage.objects 
FOR SELECT 
TO authenticated
USING (
    bucket_id = 'user_documents' 
    AND public.is_owner_user_document(name)
);

-- Users can only upload to their own folder
CREATE POLICY "Users upload own documents" 
ON storage.objects 
FOR INSERT 
TO authenticated
WITH CHECK (
    bucket_id = 'user_documents' 
    AND public.is_owner_user_document(name)
);

-- Users can only update their own documents
CREATE POLICY "Users update own documents" 
ON storage.objects 
FOR UPDATE 
TO authenticated
USING (
    bucket_id = 'user_documents' 
    AND public.is_owner_user_document(name)
) 
WITH CHECK (
    bucket_id = 'user_documents' 
    AND public.is_owner_user_document(name)
);

-- Users can only delete their own documents
CREATE POLICY "Users delete own documents" 
ON storage.objects 
FOR DELETE 
TO authenticated
USING (
    bucket_id = 'user_documents' 
    AND public.is_owner_user_document(name)
);

-- Admins can access all user documents for support purposes
CREATE POLICY "Admins access all user documents" 
ON storage.objects 
FOR ALL 
TO authenticated
USING (
    bucket_id = 'user_documents' 
    AND public.is_admin()
) 
WITH CHECK (
    bucket_id = 'user_documents' 
    AND public.is_admin()
);
