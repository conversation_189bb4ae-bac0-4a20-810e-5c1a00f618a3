-- Fix RLS policies for groups_scholarship bucket to work with Clerk authentication
-- This resolves the "new row violates row-level security policy" error when uploading files

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can update files" ON storage.objects;
DROP POLICY IF EXISTS "Employees and admins can delete files" ON storage.objects;
DROP POLICY IF EXISTS public_upload_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS public_update_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS public_read_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS anon_upload_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS anon_update_groups_scholarship ON storage.objects;
DROP POLICY IF EXISTS authenticated_delete_groups_scholarship ON storage.objects;

-- Allow only admins to upload scholarship group files
CREATE POLICY admin_upload_groups_scholarship 
ON storage.objects 
FOR INSERT 
TO authenticated
WITH CHECK (
    bucket_id = 'groups_scholarship' 
    AND public.is_admin()
);

-- Allow only admins to update scholarship group files  
CREATE POLICY admin_update_groups_scholarship 
ON storage.objects 
FOR UPDATE 
TO authenticated
USING (
    bucket_id = 'groups_scholarship' 
    AND public.is_admin()
) 
WITH CHECK (
    bucket_id = 'groups_scholarship' 
    AND public.is_admin()
);

-- Keep the existing read and delete policies but update them for the new naming convention
-- Allow public read access (this was already working)
CREATE POLICY public_read_groups_scholarship
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'groups_scholarship');

-- Allow only admins to delete scholarship group files (security fix)
CREATE POLICY admin_delete_groups_scholarship
ON storage.objects FOR DELETE
TO authenticated
USING (
    bucket_id = 'groups_scholarship' 
    AND public.is_admin()
);
