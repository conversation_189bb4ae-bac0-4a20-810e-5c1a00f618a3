-- Setup Clerk JWT Integration and RLS Infrastructure
-- This migration creates the foundation for Row Level Security with Clerk authentication

-- =============================================================================
-- CREATE FUNCTION TO EXTRACT CLERK USER ID FROM JWT
-- =============================================================================

-- Create a function to extract the Clerk user ID from JWT claims
-- This function will be used in RLS policies to get the current user's ID
CREATE OR REPLACE FUNCTION requesting_user_id()
RETURNS TEXT AS $$
    SELECT NULLIF(
        current_setting('request.jwt.claims', true)::json->>'sub',
        ''
    )::text;
$$ LANGUAGE SQL STABLE
SET search_path = 'public';

-- Create a helper function to check if user is authenticated
CREATE OR REPLACE FUNCTION is_authenticated()
RETURNS BOOLEAN AS $$
    SELECT requesting_user_id() IS NOT NULL;
$$ LANGUAGE SQL STABLE
SET search_path = 'public';

-- =============================================================================
-- CREATE FUNCTION TO CHECK ADMIN ROLE
-- =============================================================================

-- Create a function to check if the current user has admin role
-- This extracts org role from JWT claims (supports both old and new Clerk formats)
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
DECLARE
    jwt_claims json;
    organizations_text text;
    user_id text;
BEGIN
    -- Get JWT claims
    jwt_claims := current_setting('request.jwt.claims', true)::json;
    
    -- Check if jwt_claims is null or empty
    IF jwt_claims IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Get user ID for potential future use
    user_id := jwt_claims->>'sub';
    
    -- Check old format: org_role claim
    IF jwt_claims->>'org_role' IN ('admin', 'employee', 'org:admin') THEN
        RETURN TRUE;
    END IF;
    
    -- Robust admin role validation to prevent bypass
    IF jwt_claims->'organizations' IS NOT NULL THEN
        organizations_text := jwt_claims->'organizations'::text;
        -- Strict pattern matching to prevent false positives
        IF organizations_text ~ '"role"\s*:\s*"(org:)?admin"' OR 
           organizations_text ~ '"org:admin"' THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE
SET search_path = 'public';

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant execute permissions on these functions to authenticated users
GRANT EXECUTE ON FUNCTION requesting_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION is_authenticated() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;

-- Ensure anon users can also call these functions for some use cases
GRANT EXECUTE ON FUNCTION requesting_user_id() TO anon;
GRANT EXECUTE ON FUNCTION is_authenticated() TO anon;
GRANT EXECUTE ON FUNCTION is_admin() TO anon;

-- =============================================================================
-- COMMENTS
-- =============================================================================

COMMENT ON FUNCTION requesting_user_id() IS 'Extracts the Clerk user ID from JWT claims';
COMMENT ON FUNCTION is_authenticated() IS 'Checks if the current request is authenticated';
COMMENT ON FUNCTION is_admin() IS 'Checks if the current user has admin or employee role'; 