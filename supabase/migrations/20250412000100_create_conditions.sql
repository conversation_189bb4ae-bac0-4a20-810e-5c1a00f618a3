-- Create the enum type for condition types
CREATE TYPE condition_type AS ENUM ('range', 'date_range', 'in');

CREATE TABLE IF NOT EXISTS conditions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES groups_condition(id) ON DELETE CASCADE,
    question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    type condition_type NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.conditions IS 'Defines specific conditions for scholarship eligibility.';
COMMENT ON COLUMN public.conditions.id IS 'Unique identifier for each condition.';
COMMENT ON COLUMN public.conditions.group_id IS 'Reference to the condition group this condition belongs to.';
COMMENT ON COLUMN public.conditions.question_id IS 'Reference to the question this condition evaluates.';
COMMENT ON COLUMN public.conditions.type IS 'Type of condition: range, date_range, or in.';
COMMENT ON COLUMN public.conditions.value IS 'JSON object containing type-specific values for the condition.';
COMMENT ON COLUMN public.conditions.created_at IS 'Date and time when the condition was created.';
COMMENT ON COLUMN public.conditions.updated_at IS 'Date and time when the condition was last updated.';

CREATE INDEX idx_conditions_group_id ON conditions(group_id);
CREATE INDEX idx_conditions_question_id ON conditions(question_id);

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_conditions_updated_at') THEN
        CREATE TRIGGER update_conditions_updated_at
            BEFORE UPDATE ON conditions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$; 

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE conditions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for conditions" ON conditions;
DROP POLICY IF EXISTS "Public read access for conditions" ON conditions;
DROP POLICY IF EXISTS "Admin INSERT access for conditions" ON conditions;
DROP POLICY IF EXISTS "Admin UPDATE access for conditions" ON conditions;
DROP POLICY IF EXISTS "Admin DELETE access for conditions" ON conditions;
DROP POLICY IF EXISTS "Admin management access for conditions" ON conditions;

-- Authenticated read access for conditions (for form logic)
CREATE POLICY "Authenticated read access for conditions" ON conditions
    FOR SELECT
    USING (public.is_authenticated());

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for conditions" ON conditions
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for conditions" ON conditions
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for conditions" ON conditions
    FOR DELETE
    USING (public.is_admin());

COMMENT ON TABLE conditions IS 'RLS enabled: Admin-only management, authenticated read access for forms (fully consolidated policies)'; 