
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  plan_id TEXT NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  expiration_date TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN NOT NULL DEFAULT true,
  coupon_id UUID REFERENCES coupons(id) ON DELETE SET NULL,
  plan_price NUMERIC(10, 2),
  paid_amount NUMERIC(10, 2),
  payment_details JSONB,
  transaction_id TEXT,
  order_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);


COMMENT ON TABLE public.user_subscriptions IS 'Stores user subscription information';
COMMENT ON COLUMN public.user_subscriptions.id IS 'Unique identifier for the subscription';
COMMENT ON COLUMN public.user_subscriptions.user_id IS 'Reference to the user';
COMMENT ON COLUMN public.user_subscriptions.plan_id IS 'ID of the subscription plan from config';
COMMENT ON COLUMN public.user_subscriptions.start_date IS 'When the subscription starts';
COMMENT ON COLUMN public.user_subscriptions.expiration_date IS 'When the subscription expires';
COMMENT ON COLUMN public.user_subscriptions.is_active IS 'Whether the subscription is currently active';
COMMENT ON COLUMN public.user_subscriptions.coupon_id IS 'Reference to the coupon used, if any';
COMMENT ON COLUMN public.user_subscriptions.plan_price IS 'Original price of the plan';
COMMENT ON COLUMN public.user_subscriptions.paid_amount IS 'Amount paid after discounts';
COMMENT ON COLUMN public.user_subscriptions.payment_details IS 'JSON with payment gateway details';
COMMENT ON COLUMN public.user_subscriptions.transaction_id IS 'Payment gateway transaction ID';
COMMENT ON COLUMN public.user_subscriptions.order_id IS 'Payment gateway order ID';
COMMENT ON COLUMN public.user_subscriptions.created_at IS 'When the subscription was created';
COMMENT ON COLUMN public.user_subscriptions.updated_at IS 'When the subscription was last updated';


DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_subscriptions_updated_at') THEN
        CREATE TRIGGER update_user_subscriptions_updated_at
            BEFORE UPDATE ON user_subscriptions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;


-- Note: User metadata updates are handled in the application code
-- instead of using a database trigger to avoid permission issues with auth.users table

-- Function to deactivate expired subscriptions and update user metadata
CREATE OR REPLACE FUNCTION deactivate_expired_subscriptions()
RETURNS void AS $$
BEGIN
    UPDATE user_subscriptions
    SET is_active = false
    WHERE expiration_date < NOW()
    AND is_active = true;
    
    RAISE NOTICE 'Deactivated expired subscriptions at %', NOW();
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Improve subscription lookups
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_subscriptions_transaction_id ON user_subscriptions(transaction_id);

DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
    ) THEN
        PERFORM cron.schedule(
            'deactivate-expired-subscriptions',  
            '0 0 * * *',                         
            'SELECT deactivate_expired_subscriptions()'
        );
        
        RAISE NOTICE 'Scheduled deactivate_expired_subscriptions to run daily at midnight';
    ELSE
        RAISE NOTICE 'pg_cron extension is not available. The deactivate_expired_subscriptions function is created but not scheduled.';
    END IF;
END $$;


-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can delete their own subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Admins can access all subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Service role can manage all subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Consolidated SELECT access for subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Consolidated INSERT access for subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Consolidated UPDATE access for subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Consolidated DELETE access for subscriptions" ON user_subscriptions;

-- Users can see their own subscriptions OR admins can see all
CREATE POLICY "Consolidated SELECT access for subscriptions" ON user_subscriptions
    FOR SELECT
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can insert their own subscriptions OR admins can insert any
CREATE POLICY "Consolidated INSERT access for subscriptions" ON user_subscriptions
    FOR INSERT
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can update their own subscriptions OR admins can update any
CREATE POLICY "Consolidated UPDATE access for subscriptions" ON user_subscriptions
    FOR UPDATE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    )
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can delete their own subscriptions OR admins can delete any
CREATE POLICY "Consolidated DELETE access for subscriptions" ON user_subscriptions
    FOR DELETE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

COMMENT ON TABLE user_subscriptions IS 'RLS enabled: Users can only access their own subscriptions';


CREATE OR REPLACE FUNCTION update_user_subscription(
  p_user_id TEXT,
  p_subscription_data JSONB
) RETURNS VOID AS $$
DECLARE
  v_subscription_record public.user_subscriptions;
BEGIN
  -- Start transaction
  -- Deactivate existing subscriptions
  UPDATE user_subscriptions
  SET is_active = false
  WHERE user_id = p_user_id AND is_active = true;
  
  -- Populate record from JSON data
  SELECT * INTO v_subscription_record FROM jsonb_populate_record(null::public.user_subscriptions, p_subscription_data);
  
  -- Insert new subscription with explicit ID generation
  INSERT INTO user_subscriptions (
    id, user_id, plan_id, start_date, expiration_date, is_active,
    coupon_id, plan_price, paid_amount, payment_details, transaction_id, order_id
  ) VALUES (
    gen_random_uuid(),
    v_subscription_record.user_id,
    v_subscription_record.plan_id,
    v_subscription_record.start_date,
    v_subscription_record.expiration_date,
    v_subscription_record.is_active,
    v_subscription_record.coupon_id,
    v_subscription_record.plan_price,
    v_subscription_record.paid_amount,
    v_subscription_record.payment_details,
    v_subscription_record.transaction_id,
    v_subscription_record.order_id
  );
  
  -- End transaction
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

