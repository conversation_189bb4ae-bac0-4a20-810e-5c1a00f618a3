CREATE TABLE IF NOT EXISTS faq (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  order_index INTEGER NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
); 


comment on table public.faq is 'Frequently asked questions table.';
comment on column public.faq.id is 'The unique identifier for the FAQ item.';
comment on column public.faq.question is 'The question that the user asks.';
comment on column public.faq.answer is 'The answer to the question.';
comment on column public.faq.order_index is 'The order index of the FAQ item.';
comment on column public.faq.created_at is 'The date and time when the FAQ item was created.';
comment on column public.faq.updated_at is 'The date and time when the FAQ item was last updated.';


DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_faq_updated_at') THEN
        CREATE TRIGGER update_faq_updated_at
            BEFORE UPDATE ON faq
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE faq ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Consolidated SELECT access for FAQ items" ON faq;
DROP POLICY IF EXISTS "Admin INSERT access for FAQ items" ON faq;
DROP POLICY IF EXISTS "Admin UPDATE access for FAQ items" ON faq;
DROP POLICY IF EXISTS "Admin DELETE access for FAQ items" ON faq;

-- Public read access for FAQ items
CREATE POLICY "Public read access for FAQ items" ON faq
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for FAQ items" ON faq
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for FAQ items" ON faq
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for FAQ items" ON faq
    FOR DELETE
    USING (public.is_admin());

COMMENT ON TABLE faq IS 'RLS enabled: Admin-only management, public read access (fully consolidated policies)';
