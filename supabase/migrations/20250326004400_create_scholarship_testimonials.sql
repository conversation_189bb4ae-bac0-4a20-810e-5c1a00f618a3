
CREATE TABLE IF NOT EXISTS link_scholarship_to_testimonial (
    scholarship_id UUID REFERENCES scholarships(id) ON DELETE CASCADE,
    testimonial_id UUID REFERENCES testimonials(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_id, testimonial_id)
);

comment on table link_scholarship_to_testimonial is 'Connection table between scholarships and their testimonials.';
comment on column link_scholarship_to_testimonial.scholarship_id is 'The unique identifier for the scholarship';
comment on column link_scholarship_to_testimonial.testimonial_id is 'The unique identifier for the testimonial';
comment on column link_scholarship_to_testimonial.created_at is 'The date and time when the testimonial was created';
comment on column link_scholarship_to_testimonial.updated_at is 'The date and time when the testimonial was last updated';

CREATE INDEX idx_link_scholarship_to_testimonial_scholarship ON link_scholarship_to_testimonial(scholarship_id);
CREATE INDEX idx_link_scholarship_to_testimonial_testimonial ON link_scholarship_to_testimonial(testimonial_id);

DROP TRIGGER IF EXISTS update_link_scholarship_to_testimonial_updated_at ON link_scholarship_to_testimonial;
CREATE TRIGGER update_link_scholarship_to_testimonial_updated_at
    BEFORE UPDATE ON link_scholarship_to_testimonial
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_scholarship_to_testimonial ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage scholarship-testimonial links" ON link_scholarship_to_testimonial;
DROP POLICY IF EXISTS "Public read access for scholarship-testimonial links" ON link_scholarship_to_testimonial;

-- Public read access (needed for testimonial display)
CREATE POLICY "Public read access for scholarship-testimonial links" ON link_scholarship_to_testimonial
    FOR SELECT
    USING (true);

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for scholarship-testimonial links" ON link_scholarship_to_testimonial
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for scholarship-testimonial links" ON link_scholarship_to_testimonial
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for scholarship-testimonial links" ON link_scholarship_to_testimonial
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_scholarship_to_testimonial IS 'RLS enabled: Admin management, public read access for testimonial display (consolidated policies)';