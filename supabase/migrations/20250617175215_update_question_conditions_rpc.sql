-- Update RPC function for atomic condition creation to create standalone conditions
CREATE OR R<PERSON>LACE FUNCTION create_question_conditions(
    p_question_id UUID,
    p_dependencies JSONB
) RETURNS JSONB AS $$
DECLARE
    v_condition_id UUID;
    v_dependency JSONB;
    v_condition_type public.condition_type;
    v_condition_value JSONB;
    v_result JSONB := '{"success": true, "condition_ids": []}'::JSONB;
    v_condition_ids UUID[] := ARRAY[]::UUID[];
BEGIN
    -- Start transaction (implicit in function)
    
    -- Validate that the main question exists
    IF NOT EXISTS (SELECT 1 FROM questions WHERE id = p_question_id) THEN
        RAISE EXCEPTION 'Question with id % does not exist', p_question_id;
    END IF;
    
    -- Process each dependency (no group creation needed)
    FOR v_dependency IN SELECT * FROM jsonb_array_elements(p_dependencies)
    LOOP
        -- Validate required fields
        IF NOT (v_dependency ? 'question_id') OR (v_dependency->>'question_id') IS NULL THEN
            RAISE EXCEPTION 'Missing question_id in dependency: %', v_dependency;
        END IF;
        
        -- Determine condition type and value
        IF v_dependency ? 'condition_type' THEN
            CASE v_dependency->>'condition_type'
                WHEN 'range' THEN
                    v_condition_type := 'range';
                    v_condition_value := v_dependency->'condition_value';
                WHEN 'date_range' THEN
                    v_condition_type := 'date_range';
                    v_condition_value := v_dependency->'condition_value';
                WHEN 'in' THEN
                    v_condition_type := 'in';
                    v_condition_value := v_dependency->'condition_value';
                ELSE
                    RAISE EXCEPTION 'Unsupported condition_type: %', v_dependency->>'condition_type';
            END CASE;
        ELSE
            -- Default to 'in' type
            v_condition_type := 'in';
            v_condition_value := v_dependency->'condition_value';
        END IF;
        
        -- Validate that the dependent question exists
        IF NOT EXISTS (SELECT 1 FROM questions WHERE id = (v_dependency->>'question_id')::UUID) THEN
            RAISE EXCEPTION 'Dependent question with id % does not exist', v_dependency->>'question_id';
        END IF;
        
        -- Create standalone condition (without group_id)
        INSERT INTO conditions (
            question_id,
            type,
            value
        ) VALUES (
            (v_dependency->>'question_id')::UUID,
            v_condition_type,
            v_condition_value
        ) RETURNING id INTO v_condition_id;
        
        -- Create link between question and condition
        INSERT INTO link_question_to_condition (
            question_id,
            condition_id
        ) VALUES (
            p_question_id,
            v_condition_id
        );
        
        -- Add condition ID to result array
        v_condition_ids := v_condition_ids || v_condition_id;
    END LOOP;
    
    -- Update result with created condition IDs
    v_result := jsonb_set(
        v_result,
        '{condition_ids}',
        to_jsonb(v_condition_ids)
    );
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Any error will automatically rollback the transaction
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Update RPC function for atomic condition deletion
CREATE OR REPLACE FUNCTION delete_question_conditions(
    p_question_id UUID
) RETURNS JSONB AS $$
DECLARE
    v_condition_ids UUID[];
    v_result JSONB := '{"success": true}'::JSONB;
BEGIN
    -- Get condition IDs linked to this question
    SELECT ARRAY_AGG(condition_id) INTO v_condition_ids
    FROM link_question_to_condition
    WHERE question_id = p_question_id;
    
    IF v_condition_ids IS NOT NULL AND array_length(v_condition_ids, 1) > 0 THEN
        -- Delete question-condition links
        DELETE FROM link_question_to_condition
        WHERE question_id = p_question_id;
        
        -- Delete standalone conditions
        DELETE FROM conditions
        WHERE id = ANY(v_condition_ids);
        
        v_result := jsonb_set(
            v_result,
            '{deleted_conditions}',
            to_jsonb(array_length(v_condition_ids, 1))
        );
    ELSE
        v_result := jsonb_set(
            v_result,
            '{deleted_conditions}',
            to_jsonb(0)
        );
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Update comments for documentation
COMMENT ON FUNCTION create_question_conditions(UUID, JSONB) IS 'Atomically creates standalone conditions and their links for a question';
COMMENT ON FUNCTION delete_question_conditions(UUID) IS 'Atomically deletes all standalone conditions and their links for a question';
