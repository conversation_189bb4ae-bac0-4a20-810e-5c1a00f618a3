DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'banners_audience') THEN
        CREATE TYPE banners_audience AS ENUM ('Guest', 'User', 'Subscriber');
    END IF;
END$$;

CREATE TABLE IF NOT EXISTS public.banners (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    text TEXT NOT NULL,
    background_color TEXT NOT NULL,
    text_color TEXT NOT NULL,
    icon TEXT NOT NULL DEFAULT 'Info',
    cta_text TEXT,
    cta_link TEXT,
    days_to_live INTEGER NOT NULL DEFAULT 7,
    seconds_before_show INTEGER NOT NULL DEFAULT 5,
    enable_dismiss BOOLEAN NOT NULL DEFAULT true,
    enabled BOOLEAN NOT NULL DEFAULT true,
    audience banners_audience NOT NULL DEFAULT 'Guest',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.banners IS 'Stores banners for the website.';
COMMENT ON COLUMN public.banners.id IS 'Unique identifier for each banner.';
COMMENT ON COLUMN public.banners.title IS 'Title of the banner.';
COMMENT ON COLUMN public.banners.text IS 'Text to display in the banner.';
COMMENT ON COLUMN public.banners.background_color IS 'Background color of the banner.';
COMMENT ON COLUMN public.banners.text_color IS 'Text color of the banner.';
COMMENT ON COLUMN public.banners.icon IS 'Lucide icon name to display in the banner.';
COMMENT ON COLUMN public.banners.cta_text IS 'Call-to-action button text (optional).';
COMMENT ON COLUMN public.banners.cta_link IS 'Call-to-action button link URL (optional).';
COMMENT ON COLUMN public.banners.days_to_live IS 'Number of days before showing the banner again to the same user.';
COMMENT ON COLUMN public.banners.seconds_before_show IS 'Delay in seconds before showing the banner.';
COMMENT ON COLUMN public.banners.enable_dismiss IS 'Whether to show the dismiss button.';
COMMENT ON COLUMN public.banners.enabled IS 'Whether the banner is enabled.';
COMMENT ON COLUMN public.banners.audience IS 'Who will see this banner: Guest, User, or Subscriber';

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_banners_updated_at') THEN
        CREATE TRIGGER update_banners_updated_at
            BEFORE UPDATE ON banners
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_banners_created_at ON banners(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE banners ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins and public can view banners" ON banners;
DROP POLICY IF EXISTS "Admins can insert banners" ON banners;
DROP POLICY IF EXISTS "Admins can update banners" ON banners;
DROP POLICY IF EXISTS "Admins can delete banners" ON banners;
DROP POLICY IF EXISTS "Admin management access for banners" ON banners;

CREATE POLICY "Admins and public can view banners" ON banners
    FOR SELECT
    USING (
        public.is_admin()
        OR (
            enabled = true 
            AND (
                audience = 'Guest' 
                OR (public.is_authenticated() AND audience IN ('User', 'Subscriber'))
            )
        )
    );

-- Admin management access (INSERT, UPDATE, DELETE only - SELECT handled separately)
CREATE POLICY "Admin INSERT access for banners" ON banners
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for banners" ON banners
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for banners" ON banners
    FOR DELETE
    USING (public.is_admin());

COMMENT ON TABLE banners IS 'RLS enabled: Admin-only management, audience-based read access (consolidated policies)';
