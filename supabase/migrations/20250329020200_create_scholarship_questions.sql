CREATE TABLE IF NOT EXISTS link_question_to_scholarship (
    scholarship_id UUID REFERENCES scholarships(id) ON DELETE CASCADE,
    question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    PRIMARY KEY (scholarship_id, question_id)
);

comment on table link_question_to_scholarship is 'Links between scholarships and their specific questions';
comment on column link_question_to_scholarship.scholarship_id is 'The unique identifier for the scholarship';
comment on column link_question_to_scholarship.question_id is 'The unique identifier for the question';
comment on column link_question_to_scholarship.created_at is 'The date and time when the link was created';
comment on column link_question_to_scholarship.updated_at is 'The date and time when the link was last updated';

CREATE INDEX IF NOT EXISTS idx_link_question_to_scholarship_scholarship_id ON link_question_to_scholarship(scholarship_id);
CREATE INDEX IF NOT EXISTS idx_link_question_to_scholarship_question_id ON link_question_to_scholarship(question_id);

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_link_question_to_scholarship_updated_at') THEN
        CREATE TRIGGER update_link_question_to_scholarship_updated_at
            BEFORE UPDATE ON link_question_to_scholarship
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE link_question_to_scholarship ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can access question-scholarship links" ON link_question_to_scholarship;
DROP POLICY IF EXISTS "Users can view question-scholarship links" ON link_question_to_scholarship;
DROP POLICY IF EXISTS "Consolidated SELECT access for question-scholarship links" ON link_question_to_scholarship;

-- Consolidated SELECT policy combining admin and authenticated user access
CREATE POLICY "Consolidated SELECT access for question-scholarship links" ON link_question_to_scholarship
    FOR SELECT
    USING (
        public.is_admin() 
        OR public.is_authenticated()
    );

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for question-scholarship links" ON link_question_to_scholarship
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for question-scholarship links" ON link_question_to_scholarship
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for question-scholarship links" ON link_question_to_scholarship
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE link_question_to_scholarship IS 'RLS enabled: Admin management, authenticated read access for scholarship forms (consolidated policies)';