-- Create a table for mapping old scholarship slugs to new ones
CREATE TABLE IF NOT EXISTS _migration_scholarships_slugs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    old_slug TEXT NOT NULL,
    new_slug TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(old_slug),
    UNIQUE(new_slug)
);

CREATE TRIGGER update_migration_scholarships_slugs_updated_at
    BEFORE UPDATE ON _migration_scholarships_slugs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX IF NOT EXISTS idx_migration_scholarships_slugs_old_slug ON _migration_scholarships_slugs(old_slug);

comment on table _migration_scholarships_slugs is 'Table for mapping old scholarship slugs to new ones';
comment on column _migration_scholarships_slugs.old_slug is 'The old slug of the scholarship';
comment on column _migration_scholarships_slugs.new_slug is 'The new slug of the scholarship';
comment on column _migration_scholarships_slugs.created_at is 'The date and time when the scholarship was created';
comment on column _migration_scholarships_slugs.updated_at is 'The date and time when the scholarship was last updated';

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE _migration_scholarships_slugs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can manage migration scholarship slugs" ON _migration_scholarships_slugs;
DROP POLICY IF EXISTS "Public read access for migration scholarship slugs" ON _migration_scholarships_slugs;
DROP POLICY IF EXISTS "Consolidated access for migration scholarship slugs" ON _migration_scholarships_slugs;

-- Combines admin management access with public read access
CREATE POLICY "Consolidated access for migration scholarship slugs" ON _migration_scholarships_slugs
    FOR SELECT
    USING (true);

-- Separate policies for admin write operations to avoid SELECT policy overlap
CREATE POLICY "Admin INSERT access for migration scholarship slugs" ON _migration_scholarships_slugs
    FOR INSERT
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin UPDATE access for migration scholarship slugs" ON _migration_scholarships_slugs
    FOR UPDATE
    USING (public.is_admin())
    WITH CHECK (public.is_admin());

CREATE POLICY "Admin DELETE access for migration scholarship slugs" ON _migration_scholarships_slugs
    FOR DELETE
    USING (public.is_admin());

-- RLS Comments
COMMENT ON TABLE _migration_scholarships_slugs IS 'RLS enabled: Public read access, admin write access (consolidated policies)';

