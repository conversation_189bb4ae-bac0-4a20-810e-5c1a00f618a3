-- Create user_scholarship_applications table to track user intent for each scholarship
CREATE TABLE IF NOT EXISTS user_scholarship_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    scholarship_id UUID NOT NULL REFERENCES scholarships(id) ON DELETE CASCADE,
    should_apply BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE (user_id, scholarship_id)
);

COMMENT ON TABLE user_scholarship_applications IS 'Tracks whether a user wants us to apply for a scholarship on their behalf';
COMMENT ON COLUMN user_scholarship_applications.user_id IS 'Reference to the user';
COMMENT ON COLUMN user_scholarship_applications.scholarship_id IS 'Reference to the scholarship';
COMMENT ON COLUMN user_scholarship_applications.should_apply IS 'Whether the user wants us to apply for them';
COMMENT ON COLUMN user_scholarship_applications.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN user_scholarship_applications.updated_at IS 'Timestamp when the record was last updated';

-- Add updated_at trigger
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_scholarship_applications_updated_at') THEN
        CREATE TRIGGER update_user_scholarship_applications_updated_at
            BEFORE UPDATE ON user_scholarship_applications
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_user_scholarship_applications_user_id ON user_scholarship_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_scholarship_applications_scholarship_id ON user_scholarship_applications(scholarship_id);
CREATE INDEX IF NOT EXISTS idx_user_scholarship_applications_created_at ON user_scholarship_applications(created_at);

-- =============================================================================
-- ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS
ALTER TABLE user_scholarship_applications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Users can insert their own applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Users can update their own applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Users can delete their own applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Admins can access all applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Consolidated SELECT access for applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Consolidated INSERT access for applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Consolidated UPDATE access for applications" ON user_scholarship_applications;
DROP POLICY IF EXISTS "Consolidated DELETE access for applications" ON user_scholarship_applications;

-- Users can see their own applications OR admins can see all
CREATE POLICY "Consolidated SELECT access for applications" ON user_scholarship_applications
    FOR SELECT
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can insert their own applications OR admins can insert any
CREATE POLICY "Consolidated INSERT access for applications" ON user_scholarship_applications
    FOR INSERT
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can update their own applications OR admins can update any
CREATE POLICY "Consolidated UPDATE access for applications" ON user_scholarship_applications
    FOR UPDATE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    )
    WITH CHECK (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

-- Users can delete their own applications OR admins can delete any
CREATE POLICY "Consolidated DELETE access for applications" ON user_scholarship_applications
    FOR DELETE
    USING (
        user_id = public.requesting_user_id() 
        OR public.is_admin()
    );

COMMENT ON TABLE user_scholarship_applications IS 'RLS enabled: Users can only access their own applications';
