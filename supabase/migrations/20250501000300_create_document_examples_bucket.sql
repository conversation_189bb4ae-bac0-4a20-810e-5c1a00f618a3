INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'document_examples',
    'document_examples',
    true,
    10485760, -- 10MB
    ARRAY['application/pdf', 'image/jpeg', 'image/png', 'image/webp', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Public access policy
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
CREATE POLICY "Public Access"
ON storage.objects FOR SELECT
USING (bucket_id = 'document_examples');

-- Upload policy for public (anyone can upload)
DROP POLICY IF EXISTS "Public can upload document examples" ON storage.objects;
CREATE POLICY "Public can upload document examples"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'document_examples'
);

-- Update policy for employees and admins
DROP POLICY IF EXISTS "Employees and admins can update document examples" ON storage.objects;
CREATE POLICY "Employees and admins can update document examples"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'document_examples'
    AND (SELECT current_setting('role')) = 'authenticated'
);

-- Delete policy for employees and admins
DROP POLICY IF EXISTS "Employees and admins can delete document examples" ON storage.objects;
CREATE POLICY "Employees and admins can delete document examples"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'document_examples'
    AND (SELECT current_setting('role')) = 'authenticated'
);
