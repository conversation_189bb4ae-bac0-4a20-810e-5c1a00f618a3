# Supabase

NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key


# Google Auth

SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID=your-google-client-id
SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=your-google-secret

# Facebook Auth

SUPABASE_AUTH_EXTERNAL_FACEBOOK_CLIENT_ID=your-facebook-client-id
SUPABASE_AUTH_EXTERNAL_FACEBOOK_SECRET=your-facebook-secret

# Twilio Auth

SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN=your-twilio-auth-token
SUPABASE_AUTH_SMS_TWILIO_ACCOUNT_SID=your-twilio-account-sid
SUPABASE_AUTH_SMS_TWILIO_MESSAGE_SERVICE_SID=your-twilio-message-service-sid

# Email

RESEND_API_KEY=your-resend-api-key

# Mapbox

NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# Vercel

NEXT_PUBLIC_VERCEL_URL=your-vercel-url

# Payment Gateway 

PAYMENT_GATEWAY_MERCHANT_ID=your-merchant-terminal-id 
PAYMENT_GATEWAY_API_KEY=your-gateway-api-key 
PAYMENT_GATEWAY_PASSP=your-gateway-terminal-password
PAYMENT_GATEWAY_SIGN_URL=https://your-gateway-signing-endpoint/
PAYMENT_GATEWAY_VERIFY_URL=https://your-gateway-verification-endpoint/
NEXT_PUBLIC_PAYMENT_GATEWAY_REDIRECT_URL=http://localhost:3000/payment/callback 

# Maileroo

MAILEROO_API_KEY=your-maileroo-api-key

# Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
CLERK_SECRET_KEY=your-clerk-secret-key
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/login
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/
NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL=/onboarding