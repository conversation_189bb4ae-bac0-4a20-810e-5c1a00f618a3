"use client";

import { create<PERSON>ontext, useContext, useReducer, ReactNode } from "react";

// Types
export interface SignupPreferences {
    acceptedTerms: boolean;
    subscribeNewsletter: boolean;
}

interface SignupPreferencesState {
    preferences: SignupPreferences;
    isSet: boolean; // Track if preferences have been set by user
}

type SignupPreferencesAction =
    | { type: "SET_ACCEPTED_TERMS"; payload: boolean }
    | { type: "SET_SUBSCRIBE_NEWSLETTER"; payload: boolean }
    | { type: "SET_PREFERENCES"; payload: SignupPreferences }
    | { type: "CLEAR_PREFERENCES" };

// Initial state
export const initialState: SignupPreferencesState = {
    preferences: {
        acceptedTerms: false,
        subscribeNewsletter: true // Default to true as per current implementation
    },
    isSet: false
};

// Reducer
export function signupPreferencesReducer(
    state: SignupPreferencesState,
    action: SignupPreferencesAction
): SignupPreferencesState {
    switch (action.type) {
        case "SET_ACCEPTED_TERMS":
            return {
                ...state,
                preferences: {
                    ...state.preferences,
                    acceptedTerms: action.payload
                },
                isSet: true
            };
        case "SET_SUBSCRIBE_NEWSLETTER":
            return {
                ...state,
                preferences: {
                    ...state.preferences,
                    subscribeNewsletter: action.payload
                },
                isSet: true
            };
        case "SET_PREFERENCES":
            return {
                ...state,
                preferences: action.payload,
                isSet: true
            };
        case "CLEAR_PREFERENCES":
            return initialState;
        default:
            return state;
    }
}

// Context
interface SignupPreferencesContextType {
    state: SignupPreferencesState;
    setAcceptedTerms: (accepted: boolean) => void;
    setSubscribeNewsletter: (subscribe: boolean) => void;
    setPreferences: (preferences: SignupPreferences) => void;
    clearPreferences: () => void;
    hasPreferences: () => boolean;
}

const SignupPreferencesContext = createContext<SignupPreferencesContextType | undefined>(undefined);

// Provider
interface SignupPreferencesProviderProps {
    children: ReactNode;
}

export function SignupPreferencesProvider({ children }: SignupPreferencesProviderProps) {
    const [state, dispatch] = useReducer(signupPreferencesReducer, initialState);

    const setAcceptedTerms = (accepted: boolean) => {
        dispatch({ type: "SET_ACCEPTED_TERMS", payload: accepted });
    };

    const setSubscribeNewsletter = (subscribe: boolean) => {
        dispatch({ type: "SET_SUBSCRIBE_NEWSLETTER", payload: subscribe });
    };

    const setPreferences = (preferences: SignupPreferences) => {
        dispatch({ type: "SET_PREFERENCES", payload: preferences });
    };

    const clearPreferences = () => {
        dispatch({ type: "CLEAR_PREFERENCES" });
    };

    const hasPreferences = () => {
        return state.isSet;
    };

    const value: SignupPreferencesContextType = {
        state,
        setAcceptedTerms,
        setSubscribeNewsletter,
        setPreferences,
        clearPreferences,
        hasPreferences
    };

    return (
        <SignupPreferencesContext.Provider value={value}>
            {children}
        </SignupPreferencesContext.Provider>
    );
}

// Hook
export function useSignupPreferences() {
    const context = useContext(SignupPreferencesContext);
    if (context === undefined) {
        throw new Error("useSignupPreferences must be used within a SignupPreferencesProvider");
    }
    return context;
}
