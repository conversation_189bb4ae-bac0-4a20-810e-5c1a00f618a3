"use client";

import { create<PERSON>ontext, useContext, useReducer, ReactNode, useEffect } from "react";

// Types
export interface SignupPreferences {
    acceptedTerms: boolean;
    subscribeNewsletter: boolean;
}

interface SignupPreferencesState {
    preferences: SignupPreferences;
    isSet: boolean; // Track if preferences have been set by user
}

type SignupPreferencesAction =
    | { type: "SET_ACCEPTED_TERMS"; payload: boolean }
    | { type: "SET_SUBSCRIBE_NEWSLETTER"; payload: boolean }
    | { type: "SET_PREFERENCES"; payload: SignupPreferences }
    | { type: "CLEAR_PREFERENCES" };

// SessionStorage key
const STORAGE_KEY = "milgapo_signup_preferences";

// Helper functions for persistence
const saveToStorage = (state: SignupPreferencesState) => {
    if (typeof window !== "undefined") {
        try {
            sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
        } catch (error) {
            console.warn("Failed to save preferences to sessionStorage:", error);
        }
    }
};

const loadFromStorage = (): SignupPreferencesState | null => {
    if (typeof window !== "undefined") {
        try {
            const stored = sessionStorage.getItem(STORAGE_KEY);
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.warn("Failed to load preferences from sessionStorage:", error);
            return null;
        }
    }
    return null;
};

const clearStorage = () => {
    if (typeof window !== "undefined") {
        try {
            sessionStorage.removeItem(STORAGE_KEY);
        } catch (error) {
            console.warn("Failed to clear preferences from sessionStorage:", error);
        }
    }
};

// Initial state
export const initialState: SignupPreferencesState = {
    preferences: {
        acceptedTerms: false,
        subscribeNewsletter: true // Default to true as per current implementation
    },
    isSet: false
};

// Reducer
export function signupPreferencesReducer(
    state: SignupPreferencesState,
    action: SignupPreferencesAction
): SignupPreferencesState {
    let newState: SignupPreferencesState;

    switch (action.type) {
        case "SET_ACCEPTED_TERMS":
            newState = {
                ...state,
                preferences: {
                    ...state.preferences,
                    acceptedTerms: action.payload
                },
                isSet: true
            };
            break;
        case "SET_SUBSCRIBE_NEWSLETTER":
            newState = {
                ...state,
                preferences: {
                    ...state.preferences,
                    subscribeNewsletter: action.payload
                },
                isSet: true
            };
            break;
        case "SET_PREFERENCES":
            newState = {
                ...state,
                preferences: action.payload,
                isSet: true
            };
            break;
        case "CLEAR_PREFERENCES":
            newState = initialState;
            clearStorage();
            return newState;
        default:
            return state;
    }

    // Save to sessionStorage for persistence across pages
    saveToStorage(newState);
    return newState;
}

// Context
interface SignupPreferencesContextType {
    state: SignupPreferencesState;
    setAcceptedTerms: (accepted: boolean) => void;
    setSubscribeNewsletter: (subscribe: boolean) => void;
    setPreferences: (preferences: SignupPreferences) => void;
    clearPreferences: () => void;
    hasPreferences: () => boolean;
}

const SignupPreferencesContext = createContext<SignupPreferencesContextType | undefined>(undefined);

// Provider
interface SignupPreferencesProviderProps {
    children: ReactNode;
}

export function SignupPreferencesProvider({ children }: SignupPreferencesProviderProps) {
    const [state, dispatch] = useReducer(signupPreferencesReducer, initialState);

    // Load from sessionStorage on mount
    useEffect(() => {
        const storedState = loadFromStorage();
        if (storedState && storedState.isSet) {
            dispatch({ type: "SET_PREFERENCES", payload: storedState.preferences });
        }
    }, []);

    const setAcceptedTerms = (accepted: boolean) => {
        dispatch({ type: "SET_ACCEPTED_TERMS", payload: accepted });
    };

    const setSubscribeNewsletter = (subscribe: boolean) => {
        dispatch({ type: "SET_SUBSCRIBE_NEWSLETTER", payload: subscribe });
    };

    const setPreferences = (preferences: SignupPreferences) => {
        dispatch({ type: "SET_PREFERENCES", payload: preferences });
    };

    const clearPreferences = () => {
        dispatch({ type: "CLEAR_PREFERENCES" });
    };

    const hasPreferences = () => {
        return state.isSet;
    };

    const value: SignupPreferencesContextType = {
        state,
        setAcceptedTerms,
        setSubscribeNewsletter,
        setPreferences,
        clearPreferences,
        hasPreferences
    };

    return (
        <SignupPreferencesContext.Provider value={value}>
            {children}
        </SignupPreferencesContext.Provider>
    );
}

// Hook
export function useSignupPreferences() {
    const context = useContext(SignupPreferencesContext);
    if (context === undefined) {
        throw new Error("useSignupPreferences must be used within a SignupPreferencesProvider");
    }
    return context;
}
