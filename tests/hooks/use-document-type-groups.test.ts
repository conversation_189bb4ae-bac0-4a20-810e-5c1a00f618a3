import { renderHook, act, waitFor } from "@testing-library/react";
import { useDocumentTypeGroups } from "@/hooks/use-document-type-groups";
import * as actions from "@/app/actions/document-type-group-actions";

jest.mock("@/app/actions/document-type-group-actions", () => ({
    getDocumentTypeGroups: jest.fn()
}));

const mockedGetGroups = actions.getDocumentTypeGroups as jest.Mock;

describe("useDocumentTypeGroups", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should start with loading true and empty items", () => {
        mockedGetGroups.mockResolvedValue({ success: true, data: [] });
        const { result } = renderHook(() => useDocumentTypeGroups());
        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should fetch groups and set items on successful load", async () => {
        const mockData = [{ id: "1", name: "Group 1", document_types_count: 2 }];
        mockedGetGroups.mockResolvedValue({ success: true, data: mockData });

        const { result } = renderHook(() => useDocumentTypeGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.items).toEqual(mockData);
            expect(result.current.error).toBeNull();
        });
    });

    it("should set error state on fetch failure", async () => {
        const errorMessage = "Failed to fetch";
        mockedGetGroups.mockResolvedValue({ success: false, error: errorMessage });

        const { result } = renderHook(() => useDocumentTypeGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.items).toEqual([]);
            expect(result.current.error).toBe(errorMessage);
        });
    });

    it("should refetch data when refetch is called", async () => {
        mockedGetGroups.mockResolvedValueOnce({ success: true, data: [{ id: "1", name: "First" }] });
        const { result } = renderHook(() => useDocumentTypeGroups());

        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.items).toEqual([{ id: "1", name: "First" }]);

        const newData = [{ id: "2", name: "Second" }];
        mockedGetGroups.mockResolvedValueOnce({ success: true, data: newData });

        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.items).toEqual(newData);
    });
});
