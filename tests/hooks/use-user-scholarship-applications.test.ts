import { act, renderHook, waitFor } from "@testing-library/react";

import * as actions from "@/app/actions/user-scholarship-applications-actions";
import { useUserScholarshipApplications } from "@/hooks/use-user-scholarship-applications";

jest.mock("@/app/actions/user-scholarship-applications-actions", () => ({
    getUserScholarshipApplications: jest.fn(),
    updateUserScholarshipApplication: jest.fn()
}));

const mockedGetUserApplications = actions.getUserScholarshipApplications as jest.Mock;
const mockedUpdateUserApplication = actions.updateUserScholarshipApplication as jest.Mock;

const TEXTS = {
    fetchError: "Failed to fetch user applications"
};

describe("useUserScholarshipApplications", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should have correct initial state", () => {
        const { result } = renderHook(() => useUserScholarshipApplications([]));

        expect(result.current.shouldApplyMap).toEqual({});
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it("should not fetch if scholarshipIds is empty", () => {
        renderHook(() => useUserScholarshipApplications([]));
        expect(mockedGetUserApplications).not.toHaveBeenCalled();
    });

    it("should fetch applications and update state on success", async () => {
        const scholarshipIds = ["1", "2"];
        const applications = [
            { scholarship_id: "1", should_apply: true },
            { scholarship_id: "2", should_apply: false }
        ];
        mockedGetUserApplications.mockResolvedValue({ success: true, applications });

        const { result } = renderHook(() => useUserScholarshipApplications(scholarshipIds));

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(mockedGetUserApplications).toHaveBeenCalledWith(scholarshipIds);
        expect(result.current.shouldApplyMap).toEqual({ "1": true, "2": false });
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error", async () => {
        const scholarshipIds = ["1"];
        mockedGetUserApplications.mockResolvedValue({ success: false, error: TEXTS.fetchError });

        const { result } = renderHook(() => useUserScholarshipApplications(scholarshipIds));

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.error).toBe(TEXTS.fetchError);
        expect(result.current.shouldApplyMap).toEqual({});
    });

    it("should update shouldApply optimistically and call the action", async () => {
        const scholarshipId = "1";
        const value = true;
        mockedUpdateUserApplication.mockResolvedValue({ success: true });

        const { result } = renderHook(() => useUserScholarshipApplications([]));

        await act(async () => {
            await result.current.updateShouldApply(scholarshipId, value);
        });

        expect(result.current.shouldApplyMap).toEqual({ [scholarshipId]: value });
        expect(mockedUpdateUserApplication).toHaveBeenCalledWith(scholarshipId, value);
    });

    it("should revert optimistic update on action failure", async () => {
        const scholarshipId = "1";
        const initialValue = false;
        const optimisticValue = true;
        mockedUpdateUserApplication.mockRejectedValue(new Error("Update failed"));

        const { result } = renderHook(() => useUserScholarshipApplications([]));

        // Set initial state
        act(() => {
            result.current.shouldApplyMap[scholarshipId] = initialValue;
        });

        await act(async () => {
            await result.current.updateShouldApply(scholarshipId, optimisticValue);
        });

        expect(result.current.shouldApplyMap).toEqual({ [scholarshipId]: initialValue });
        expect(result.current.error).toBe("Update failed");
    });

    it("should refetch data when refetch is called", async () => {
        const scholarshipIds = ["1"];
        mockedGetUserApplications
            .mockResolvedValueOnce({
                success: true,
                applications: [{ scholarship_id: "1", should_apply: false }]
            })
            .mockResolvedValueOnce({
                success: true,
                applications: [{ scholarship_id: "1", should_apply: true }]
            });

        const { result } = renderHook(() => useUserScholarshipApplications(scholarshipIds));

        await waitFor(() => expect(result.current.shouldApplyMap).toEqual({ "1": false }));
        expect(mockedGetUserApplications).toHaveBeenCalledTimes(1);

        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.shouldApplyMap).toEqual({ "1": true });
        expect(mockedGetUserApplications).toHaveBeenCalledTimes(2);
    });
});
