import { renderHook, act, waitFor } from "@testing-library/react";
import { useAdminBanners } from "@/hooks/use-admin-banners";
import { getBanners, deleteBanner } from "@/app/actions/banner-actions";

jest.mock("@/app/actions/banner-actions", () => ({
    getBanners: jest.fn(),
    deleteBanner: jest.fn()
}));

const getBannersMock = getBanners as jest.Mock;
const deleteBannerMock = deleteBanner as jest.Mock;

const mockBanners = [
    { id: "1", title: "Banner 1", created_at: new Date().toISOString() },
    { id: "2", title: "Banner 2", created_at: new Date().toISOString() }
];

describe("useAdminBanners", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should start in a loading state and fetch banners successfully", async () => {
        getBannersMock.mockResolvedValue({ success: true, data: mockBanners });

        const { result } = renderHook(() => useAdminBanners());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.items).toEqual(mockBanners);
            expect(result.current.error).toBeNull();
        });
    });

    it("should handle fetch errors correctly", async () => {
        getBannersMock.mockResolvedValue({ success: false, error: "Fetch Error" });

        const { result } = renderHook(() => useAdminBanners());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.items).toEqual([]);
            expect(result.current.error).toBeInstanceOf(Error);
            expect(result.current.error?.message).toBe("Fetch Error");
        });
    });

    it("should handle banner deletion successfully and refetch", async () => {
        getBannersMock.mockResolvedValueOnce({ success: true, data: mockBanners });
        deleteBannerMock.mockResolvedValue({ success: true });
        getBannersMock.mockResolvedValueOnce({
            success: true,
            data: [mockBanners[1]]
        });

        const { result } = renderHook(() => useAdminBanners());

        await waitFor(() => expect(result.current.loading).toBe(false));

        await act(async () => {
            await result.current.handleDelete("1");
        });

        expect(deleteBannerMock).toHaveBeenCalledWith("1");
        expect(getBannersMock).toHaveBeenCalledTimes(2);
        expect(result.current.items).toEqual([mockBanners[1]]);
    });

    it("should handle banner deletion failure", async () => {
        getBannersMock.mockResolvedValue({ success: true, data: mockBanners });
        deleteBannerMock.mockResolvedValue({ success: false, error: "Delete Error" });

        const { result } = renderHook(() => useAdminBanners());

        await waitFor(() => expect(result.current.loading).toBe(false));

        let deleteResult;
        await act(async () => {
            deleteResult = await result.current.handleDelete("1");
        });

        expect(deleteBannerMock).toHaveBeenCalledWith("1");
        expect(getBannersMock).toHaveBeenCalledTimes(1); // Should not refetch
        expect(deleteResult).toEqual({ success: false, error: "Delete Error" });
    });

    it("should refetch banners when refetch is called", async () => {
        getBannersMock.mockResolvedValueOnce({ success: true, data: [] });
        const { result } = renderHook(() => useAdminBanners());
        await waitFor(() => expect(result.current.loading).toBe(false));

        getBannersMock.mockResolvedValueOnce({ success: true, data: mockBanners });
        await act(async () => {
            await result.current.refetch();
        });

        expect(getBannersMock).toHaveBeenCalledTimes(2);
        expect(result.current.items).toEqual(mockBanners);
    });
});
