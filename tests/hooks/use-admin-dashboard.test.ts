import { renderHook, act, waitFor } from "@testing-library/react";
import { useAdminDashboard } from "@/hooks/use-admin-dashboard";
import { useAuth } from "@clerk/nextjs";
import { getSubscriptionStats, getUserStats } from "@/app/actions/admin-dashboard-actions";

jest.mock("@/app/actions/admin-dashboard-actions", () => ({
    getSubscriptionStats: jest.fn(),
    getUserStats: jest.fn()
}));
jest.mock("@clerk/nextjs");

const mockGetSubscriptionStats = getSubscriptionStats as jest.Mock;
const mockGetUserStats = getUserStats as jest.Mock;
const mockUseAuth = useAuth as jest.Mock;

describe("useAdminDashboard", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseAuth.mockReturnValue({ isLoaded: true });
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should initialize with loading state", () => {
        mockGetUserStats.mockResolvedValue({ success: true, data: { total: 0 } });
        mockGetSubscriptionStats.mockResolvedValue({ success: true, data: [] });
        const { result } = renderHook(() => useAdminDashboard());
        expect(result.current.stats.subscriptions.loading).toBe(true);
        expect(result.current.stats.users.loading).toBe(true);
    });

    it("should not fetch data if auth is not loaded", () => {
        mockUseAuth.mockReturnValue({ isLoaded: false });
        renderHook(() => useAdminDashboard());
        expect(mockGetSubscriptionStats).not.toHaveBeenCalled();
        expect(mockGetUserStats).not.toHaveBeenCalled();
    });

    it("should fetch stats successfully", async () => {
        const userStats = { total: 150 };
        const subStats = [{ planId: "pro", planName: "Pro", count: 10, percentage: 100, newLast24h: 1 }];
        mockGetUserStats.mockResolvedValue({ success: true, data: userStats });
        mockGetSubscriptionStats.mockResolvedValue({ success: true, data: subStats });

        const { result } = renderHook(() => useAdminDashboard());

        await waitFor(() => {
            expect(result.current.stats.users.loading).toBe(false);
            expect(result.current.stats.subscriptions.loading).toBe(false);
        });

        expect(result.current.stats.users.data).toEqual(userStats);
        expect(result.current.stats.subscriptions.data).toEqual(subStats);
        expect(result.current.stats.users.error).toBeNull();
        expect(result.current.stats.subscriptions.error).toBeNull();
    });

    it("should handle fetch failure for all stats", async () => {
        const error = new Error("Failed to fetch");
        mockGetUserStats.mockResolvedValue({ success: false, error: error.message });
        mockGetSubscriptionStats.mockResolvedValue({ success: false, error: error.message });

        const { result } = renderHook(() => useAdminDashboard());

        await waitFor(() => {
            expect(result.current.stats.users.loading).toBe(false);
            expect(result.current.stats.subscriptions.loading).toBe(false);
        });

        expect(result.current.stats.users.data).toBeNull();
        expect(result.current.stats.subscriptions.data).toEqual([]);
        expect(result.current.stats.users.error).toEqual(new Error("Failed to fetch, Failed to fetch"));
        expect(result.current.stats.subscriptions.error).toEqual(new Error("Failed to fetch, Failed to fetch"));
    });

    it("should refetch data when refetch is called", async () => {
        mockGetUserStats.mockResolvedValue({ success: true, data: { total: 100 } });
        mockGetSubscriptionStats.mockResolvedValue({ success: true, data: [] });

        const { result } = renderHook(() => useAdminDashboard());

        await waitFor(() => {
            expect(mockGetUserStats).toHaveBeenCalledTimes(1);
            expect(mockGetSubscriptionStats).toHaveBeenCalledTimes(1);
        });

        mockGetUserStats.mockResolvedValue({ success: true, data: { total: 200 } });
        mockGetSubscriptionStats.mockResolvedValue({
            success: true,
            data: [{ planId: "pro", planName: "Pro", count: 1, percentage: 100, newLast24h: 1 }]
        });

        act(() => {
            result.current.refetch();
        });

        await waitFor(() => {
            expect(mockGetUserStats).toHaveBeenCalledTimes(2);
            expect(mockGetSubscriptionStats).toHaveBeenCalledTimes(2);
            expect(result.current.stats.users.data?.total).toBe(200);
            expect(result.current.stats.subscriptions.data).toHaveLength(1);
        });
    });
});
