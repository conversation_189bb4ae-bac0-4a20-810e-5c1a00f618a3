import { renderHook, act } from "@testing-library/react";
import { useDynamicQuestionsState } from "@/hooks/dynamic-questions-form/use-dynamic-questions-state";
import type {
    Question,
    QuestionGroup,
    Condition,
    QuestionConditionLink
} from "@/components/forms/dynamic-questions-form/types";

const mockQuestions: Question[] = [
    {
        id: "q1",
        section: "personal_details",
        type: "short_text",
        group_id: "group1",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        metadata: {
            label: "שם",
            required: true
        }
    },
    {
        id: "q2",
        section: "personal_details",
        type: "number_input",
        group_id: "group1",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        metadata: {
            label: "גיל",
            required: true
        }
    }
];

const mockQuestionGroups: Record<string, QuestionGroup> = {
    group1: {
        id: "group1",
        name: "Personal Information",
        description: "Basic personal details",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    }
};

const mockConditions: Condition[] = [
    {
        id: "c1",
        question_id: "q1",
        group_id: null,
        type: "in",
        value: ["test"],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    }
];

const mockQuestionConditionLinks: QuestionConditionLink[] = [
    {
        question_id: "q2",
        condition_id: "c1",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    }
];

describe("useDynamicQuestionsState", () => {
    it("should initialize with default state", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        expect(result.current.questions).toEqual([]);
        expect(result.current.questionGroups).toEqual({});
        expect(result.current.conditions).toEqual([]);
        expect(result.current.questionConditionLinks).toEqual([]);
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBeNull();
        expect(result.current.lastFetchedSections).toEqual([]);
        expect(result.current.lastFetchedScholarshipId).toBeUndefined();
        expect(typeof result.current.dispatch).toBe("function");
    });

    it("should handle SET_LOADING action", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({ type: "SET_LOADING", payload: false });
        });

        expect(result.current.loading).toBe(false);

        act(() => {
            result.current.dispatch({ type: "SET_LOADING", payload: true });
        });

        expect(result.current.loading).toBe(true);
    });

    it("should handle SET_ERROR action", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        const errorMessage = "Test error message";

        act(() => {
            result.current.dispatch({ type: "SET_ERROR", payload: errorMessage });
        });

        expect(result.current.error).toBe(errorMessage);
        expect(result.current.loading).toBe(false);
    });

    it("should clear error when SET_ERROR is called with null", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({ type: "SET_ERROR", payload: "Some error" });
        });

        expect(result.current.error).toBe("Some error");

        act(() => {
            result.current.dispatch({ type: "SET_ERROR", payload: null });
        });

        expect(result.current.error).toBeNull();
        expect(result.current.loading).toBe(false);
    });

    it("should handle SET_DATA action", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        const sections = ["personal", "education"];
        const scholarshipId = "scholarship123";

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: mockQuestions,
                    questionGroups: mockQuestionGroups,
                    conditions: mockConditions,
                    questionConditionLinks: mockQuestionConditionLinks,
                    sections,
                    scholarshipId
                }
            });
        });

        expect(result.current.questions).toEqual(mockQuestions);
        expect(result.current.questionGroups).toEqual(mockQuestionGroups);
        expect(result.current.conditions).toEqual(mockConditions);
        expect(result.current.questionConditionLinks).toEqual(mockQuestionConditionLinks);
        expect(result.current.lastFetchedSections).toEqual(sections);
        expect(result.current.lastFetchedScholarshipId).toBe(scholarshipId);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it("should handle SET_DATA action without scholarshipId", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        const sections = ["personal"];

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: mockQuestions,
                    questionGroups: mockQuestionGroups,
                    conditions: mockConditions,
                    questionConditionLinks: mockQuestionConditionLinks,
                    sections
                }
            });
        });

        expect(result.current.questions).toEqual(mockQuestions);
        expect(result.current.questionGroups).toEqual(mockQuestionGroups);
        expect(result.current.conditions).toEqual(mockConditions);
        expect(result.current.questionConditionLinks).toEqual(mockQuestionConditionLinks);
        expect(result.current.lastFetchedSections).toEqual(sections);
        expect(result.current.lastFetchedScholarshipId).toBeUndefined();
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it("should handle RESET action", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: mockQuestions,
                    questionGroups: mockQuestionGroups,
                    conditions: mockConditions,
                    questionConditionLinks: mockQuestionConditionLinks,
                    sections: ["personal"],
                    scholarshipId: "test123"
                }
            });
        });

        expect(result.current.questions).toEqual(mockQuestions);
        expect(result.current.loading).toBe(false);

        act(() => {
            result.current.dispatch({ type: "RESET" });
        });

        expect(result.current.questions).toEqual([]);
        expect(result.current.questionGroups).toEqual({});
        expect(result.current.conditions).toEqual([]);
        expect(result.current.questionConditionLinks).toEqual([]);
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBeNull();
        expect(result.current.lastFetchedSections).toEqual([]);
        expect(result.current.lastFetchedScholarshipId).toBeUndefined();
    });

    it("should handle multiple sequential actions", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({ type: "SET_LOADING", payload: true });
        });

        expect(result.current.loading).toBe(true);

        act(() => {
            result.current.dispatch({ type: "SET_ERROR", payload: "Network error" });
        });

        expect(result.current.error).toBe("Network error");
        expect(result.current.loading).toBe(false);

        act(() => {
            result.current.dispatch({ type: "SET_ERROR", payload: null });
            result.current.dispatch({ type: "SET_LOADING", payload: true });
        });

        expect(result.current.error).toBeNull();
        expect(result.current.loading).toBe(true);

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: mockQuestions,
                    questionGroups: mockQuestionGroups,
                    conditions: mockConditions,
                    questionConditionLinks: mockQuestionConditionLinks,
                    sections: ["personal"],
                    scholarshipId: "test"
                }
            });
        });

        expect(result.current.questions).toEqual(mockQuestions);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it("should handle SET_DATA with empty arrays", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: [],
                    questionGroups: {},
                    conditions: [],
                    questionConditionLinks: [],
                    sections: []
                }
            });
        });

        expect(result.current.questions).toEqual([]);
        expect(result.current.questionGroups).toEqual({});
        expect(result.current.conditions).toEqual([]);
        expect(result.current.questionConditionLinks).toEqual([]);
        expect(result.current.lastFetchedSections).toEqual([]);
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it("should preserve existing state when setting data multiple times", () => {
        const { result } = renderHook(() => useDynamicQuestionsState());

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: [mockQuestions[0]],
                    questionGroups: mockQuestionGroups,
                    conditions: mockConditions,
                    questionConditionLinks: mockQuestionConditionLinks,
                    sections: ["personal"],
                    scholarshipId: "first"
                }
            });
        });

        expect(result.current.questions).toHaveLength(1);
        expect(result.current.lastFetchedScholarshipId).toBe("first");

        act(() => {
            result.current.dispatch({
                type: "SET_DATA",
                payload: {
                    questions: mockQuestions,
                    questionGroups: {},
                    conditions: [],
                    questionConditionLinks: [],
                    sections: ["education"],
                    scholarshipId: "second"
                }
            });
        });

        expect(result.current.questions).toHaveLength(2);
        expect(result.current.questionGroups).toEqual({});
        expect(result.current.conditions).toEqual([]);
        expect(result.current.lastFetchedSections).toEqual(["education"]);
        expect(result.current.lastFetchedScholarshipId).toBe("second");
    });
});
