import { renderHook, act, waitFor } from "@testing-library/react";
import { useBankData } from "@/hooks/dynamic-questions-form/use-bank-data";
import type { Bank } from "@/components/forms/dynamic-questions-form/types";

global.fetch = jest.fn();

const mockBanks: Bank[] = [
    {
        bankCode: 1,
        bankName: "בנק לאומי",
        branches: [
            { branchCode: 101, branchName: "סניף ראשי" },
            { branchCode: 102, branchName: "סניף מזרח" }
        ]
    },
    {
        bankCode: 2,
        bankName: "בנק הפועלים",
        branches: [{ branchCode: 201, branchName: "סניף מרכז" }]
    }
];

const mockWindow = global.window as unknown as { __BANKS_CACHE__?: Bank[] };

describe("useBankData", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});

        if (mockWindow.__BANKS_CACHE__) {
            delete mockWindow.__BANKS_CACHE__;
        }
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should start in non-loading state when cache exists", async () => {
        mockWindow.__BANKS_CACHE__ = mockBanks;

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(false);
        expect(result.current.banks).toEqual(mockBanks);
        expect(result.current.error).toBeNull();
        expect(fetch).not.toHaveBeenCalled();
    });

    it("should fetch banks successfully when no cache exists", async () => {
        const mockResponse = {
            ok: true,
            json: async () => ({ banks: mockBanks })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);
        expect(result.current.banks).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual(mockBanks);
        expect(result.current.error).toBeNull();
        expect(fetch).toHaveBeenCalledWith("/api/banks", {
            signal: expect.any(AbortSignal)
        });
        expect(mockWindow.__BANKS_CACHE__).toEqual(mockBanks);
    });

    it("should handle empty cache properly", async () => {
        mockWindow.__BANKS_CACHE__ = [];
        const mockResponse = {
            ok: true,
            json: async () => ({ banks: mockBanks })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual(mockBanks);
        expect(fetch).toHaveBeenCalled();
    });

    it("should handle HTTP error responses", async () => {
        const mockResponse = {
            ok: false,
            status: 500
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual([]);
        expect(result.current.error).toBe("Failed to load bank data");
        expect(console.error).toHaveBeenCalledWith("Failed loading banks:", expect.any(Error));
    });

    it("should handle network errors", async () => {
        const networkError = new Error("Network error");
        (fetch as jest.Mock).mockRejectedValue(networkError);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual([]);
        expect(result.current.error).toBe("Failed to load bank data");
        expect(console.error).toHaveBeenCalledWith("Failed loading banks:", networkError);
    });

    it("should handle malformed JSON response", async () => {
        const mockResponse = {
            ok: true,
            json: async () => ({ invalidData: "test" })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should handle abort error gracefully", async () => {
        const abortError = new Error("The operation was aborted");
        abortError.name = "AbortError";
        (fetch as jest.Mock).mockRejectedValue(abortError);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual([]);
        expect(result.current.error).toBeNull();
        expect(console.error).not.toHaveBeenCalled();
    });

    it("should abort request when component unmounts", async () => {
        const mockResponse = {
            ok: true,
            json: async () => ({ banks: mockBanks })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result, unmount } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        unmount();

        expect(fetch).toHaveBeenCalledWith("/api/banks", {
            signal: expect.any(AbortSignal)
        });
    });

    it("should cache data after successful fetch", async () => {
        const mockResponse = {
            ok: true,
            json: async () => ({ banks: mockBanks })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockWindow.__BANKS_CACHE__).toEqual(mockBanks);

        const { result: result2 } = renderHook(() => useBankData());

        expect(result2.current.loading).toBe(false);
        expect(result2.current.banks).toEqual(mockBanks);
        expect(fetch).toHaveBeenCalledTimes(1);
    });

    it("should handle invalid cache data", async () => {
        mockWindow.__BANKS_CACHE__ = null as unknown as Bank[];

        const mockResponse = {
            ok: true,
            json: async () => ({ banks: mockBanks })
        };
        (fetch as jest.Mock).mockResolvedValue(mockResponse);

        const { result } = renderHook(() => useBankData());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.banks).toEqual(mockBanks);
        expect(fetch).toHaveBeenCalled();
    });
});
