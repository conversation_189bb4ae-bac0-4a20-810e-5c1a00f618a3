import { renderHook, waitFor } from "@testing-library/react";
import { useQuestionsData } from "@/hooks/dynamic-questions-form/use-questions-data";
import {
    fetchQuestionsData,
    type ShapedQuestion,
    type ShapedCondition
} from "@/app/actions/dynamic-questions-form-actions";
import { createDefaultFormValues, processAnswers } from "@/components/forms/dynamic-questions-form/utils";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import type { UserResource } from "@clerk/types";
import type { QuestionGroup } from "@/components/forms/dynamic-questions-form/types";
import type { DynamicQuestionsFormProps } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";

jest.mock("@/app/actions/dynamic-questions-form-actions");
jest.mock("@/components/forms/dynamic-questions-form/utils");
jest.mock("sonner");
jest.mock("react-hook-form");
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({
        userId: "test-user-id",
        sessionClaims: { orgs: [{ role: "admin", org_id: "org-1" }] }
    }))
}));
jest.mock("@/hooks/use-user-org-role", () => ({
    useUserOrgRole: jest.fn(() => ({ role: "admin", orgId: "org-1", isLoaded: true }))
}));

const mockFetchQuestionsData = fetchQuestionsData as jest.Mock;
const mockCreateDefaultFormValues = createDefaultFormValues as jest.Mock;
const mockProcessAnswers = processAnswers as jest.Mock;
const mockToast = { error: jest.fn() };
(toast as unknown as { error: jest.Mock }).error = mockToast.error;
const mockUseForm = useForm as jest.Mock;

const mockUser = {
    id: "user123",
    firstName: "John",
    lastName: "Doe"
} as unknown as UserResource;

const mockQuestions: ShapedQuestion[] = [
    {
        id: "q1",
        section: "personal_details",
        type: "short_text",
        group_id: "group1",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        metadata: {
            label: "שם",
            required: true
        }
    }
];

const mockQuestionGroups: Record<string, QuestionGroup> = {
    group1: {
        id: "group1",
        name: "Personal Information",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    }
};

describe("useQuestionsData", () => {
    const mockDispatch = jest.fn();
    const mockIsSubmittingRef = { current: false };
    const mockFormMethods = {
        reset: jest.fn(),
        formState: { isSubmitting: false },
        control: {},
        handleSubmit: jest.fn(),
        watch: jest.fn(),
        setValue: jest.fn(),
        getValues: jest.fn()
    };

    const defaultParams = {
        sections: ["personal_details" as const],
        scholarshipId: undefined,
        isLoaded: true,
        user: mockUser,
        prefetchedData: undefined,
        dispatch: mockDispatch,
        lastFetchedSections: [],
        lastFetchedScholarshipId: undefined,
        isSubmittingRef: mockIsSubmittingRef
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockCreateDefaultFormValues.mockReturnValue({ q1: "" });
        mockProcessAnswers.mockReturnValue({ q1: "John Doe" });
        mockUseForm.mockReturnValue(mockFormMethods);
        mockIsSubmittingRef.current = false;
    });

    it("should handle prefetched data successfully", async () => {
        const prefetchedData: DynamicQuestionsFormProps["prefetchedData"] = {
            questions: mockQuestions,
            questionGroups: mockQuestionGroups,
            conditions: [],
            questionConditionLinks: [],
            answers: []
        };

        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                prefetchedData
            })
        );

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith({
                type: "SET_DATA",
                payload: expect.objectContaining({
                    questions: mockQuestions,
                    questionGroups: mockQuestionGroups
                })
            });
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: "SET_LOADING",
            payload: false
        });
    });

    it("should handle empty prefetched data", async () => {
        const prefetchedData: DynamicQuestionsFormProps["prefetchedData"] = {
            questions: [],
            questionGroups: {},
            conditions: [],
            questionConditionLinks: [],
            answers: []
        };

        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                prefetchedData
            })
        );

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith({
                type: "SET_DATA",
                payload: {
                    questions: [],
                    questionGroups: {},
                    conditions: [],
                    questionConditionLinks: [],
                    sections: ["personal_details"],
                    scholarshipId: undefined
                }
            });
        });
    });

    it("should fetch data when no prefetched data exists", async () => {
        const fetchResult = {
            success: true,
            data: {
                questions: mockQuestions,
                questionGroups: mockQuestionGroups,
                conditions: [],
                questionConditionLinks: [],
                answers: []
            }
        };

        mockFetchQuestionsData.mockResolvedValue(fetchResult);

        renderHook(() => useQuestionsData(defaultParams));

        await waitFor(() => {
            expect(mockFetchQuestionsData).toHaveBeenCalledWith(["personal_details"], "user123", undefined);
        });

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith({
                type: "SET_DATA",
                payload: expect.objectContaining({
                    questions: mockQuestions
                })
            });
        });
    });

    it("should handle fetch errors", async () => {
        mockFetchQuestionsData.mockResolvedValue({
            success: false,
            error: "Network error"
        });

        renderHook(() => useQuestionsData(defaultParams));

        await waitFor(() => {
            expect(mockDispatch).toHaveBeenCalledWith({
                type: "SET_ERROR",
                payload: "Network error"
            });
        });

        expect(mockToast.error).toHaveBeenCalledWith("שגיאה בטעינת השאלות");
    });

    it("should not fetch when user is not authenticated", () => {
        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                user: null
            })
        );

        expect(mockFetchQuestionsData).not.toHaveBeenCalled();
        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("should not fetch when not loaded", () => {
        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                isLoaded: false
            })
        );

        expect(mockFetchQuestionsData).not.toHaveBeenCalled();
    });

    it("should not fetch when sections are empty", () => {
        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                sections: []
            })
        );

        expect(mockFetchQuestionsData).not.toHaveBeenCalled();
    });

    it("should skip processing when submitting", () => {
        mockIsSubmittingRef.current = true;

        const prefetchedData: DynamicQuestionsFormProps["prefetchedData"] = {
            questions: mockQuestions,
            questionGroups: mockQuestionGroups,
            conditions: [],
            questionConditionLinks: [],
            answers: []
        };

        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                prefetchedData
            })
        );

        expect(mockDispatch).not.toHaveBeenCalled();
    });

    it("should not refetch when data is current", () => {
        renderHook(() =>
            useQuestionsData({
                ...defaultParams,
                lastFetchedSections: ["personal_details"],
                lastFetchedScholarshipId: undefined
            })
        );

        expect(mockFetchQuestionsData).not.toHaveBeenCalled();
    });
});
