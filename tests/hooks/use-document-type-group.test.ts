import { renderHook, act, waitFor } from "@testing-library/react";
import { useDocumentTypeGroup } from "@/hooks/use-document-type-group";
import * as actions from "@/app/actions/document-type-group-actions";
import { type Tables } from "@/types/database.types";

jest.mock("@/app/actions/document-type-group-actions", () => ({
    getDocumentTypeGroup: jest.fn()
}));

const mockedGetGroup = actions.getDocumentTypeGroup as jest.Mock;

describe("useDocumentTypeGroup", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should not fetch if groupId is not provided", () => {
        const { result } = renderHook(() => useDocumentTypeGroup(undefined));
        expect(result.current.loading).toBe(false);
        expect(result.current.group).toBeNull();
        expect(mockedGetGroup).not.toHaveBeenCalled();
    });

    it("should fetch group and set data on success", async () => {
        const mockGroup: Tables<"groups_document_type"> = {
            id: "1",
            name: "Test Group",
            description: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        mockedGetGroup.mockResolvedValue({ success: true, data: mockGroup });

        const { result } = renderHook(() => useDocumentTypeGroup("1"));

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.group).toEqual(mockGroup);
            expect(result.current.error).toBeNull();
        });
    });

    it("should set error state on fetch failure", async () => {
        const errorMessage = "Failed to fetch";
        mockedGetGroup.mockResolvedValue({ success: false, error: errorMessage });

        const { result } = renderHook(() => useDocumentTypeGroup("1"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.group).toBeNull();
            expect(result.current.error).toBe(errorMessage);
        });
    });

    it("should refetch data when refetch is called", async () => {
        const initialGroup = { id: "1", name: "Initial" };
        mockedGetGroup.mockResolvedValueOnce({ success: true, data: initialGroup });
        const { result } = renderHook(() => useDocumentTypeGroup("1"));

        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.group).toEqual(initialGroup);

        const updatedGroup = { id: "1", name: "Updated" };
        mockedGetGroup.mockResolvedValueOnce({ success: true, data: updatedGroup });

        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.group).toEqual(updatedGroup);
    });
});
