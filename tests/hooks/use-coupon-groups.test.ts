import { act, renderHook, waitFor } from "@testing-library/react";

import * as actions from "@/app/actions/coupon-group-actions";
import { useCouponGroups, type CouponGroupWithCount } from "@/hooks/use-coupon-groups";

jest.mock("@/app/actions/coupon-group-actions", () => ({
    getCouponGroups: jest.fn()
}));

const mockedGetCouponGroups = actions.getCouponGroups as jest.Mock;

const mockCouponGroups: CouponGroupWithCount[] = [
    {
        id: "1",
        name: "Group 1",
        description: "Description 1",
        coupons_count: 5,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    },
    {
        id: "2",
        name: "Group 2",
        description: "Description 2",
        coupons_count: 10,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }
];

const TEXTS = {
    fetchError: "Failed to fetch coupon groups"
};

describe("useCouponGroups", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should have correct initial state", () => {
        const { result } = renderHook(() => useCouponGroups());
        expect(result.current.items).toEqual([]);
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBe(null);
    });

    it("should fetch groups and update state on success", async () => {
        mockedGetCouponGroups.mockResolvedValue({ success: true, data: mockCouponGroups });
        const { result } = renderHook(() => useCouponGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual(mockCouponGroups);
        expect(result.current.error).toBe(null);
    });

    it("should handle fetch error", async () => {
        mockedGetCouponGroups.mockResolvedValue({ success: false, error: TEXTS.fetchError });
        const { result } = renderHook(() => useCouponGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe(TEXTS.fetchError);
    });

    it("should handle unexpected error during fetch", async () => {
        const unexpectedError = new Error("Unexpected error");
        mockedGetCouponGroups.mockRejectedValue(unexpectedError);
        const { result } = renderHook(() => useCouponGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe(unexpectedError.message);
    });

    it("should refetch data when refetch is called", async () => {
        mockedGetCouponGroups.mockResolvedValueOnce({ success: true, data: mockCouponGroups });
        const { result } = renderHook(() => useCouponGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });
        expect(result.current.items).toEqual(mockCouponGroups);

        const newMockData = [mockCouponGroups[0]];
        mockedGetCouponGroups.mockResolvedValueOnce({ success: true, data: newMockData });

        await act(async () => {
            await result.current.refetch();
        });

        await waitFor(() => {
            expect(result.current.items).toEqual(newMockData);
            expect(result.current.loading).toBe(false);
        });
        expect(mockedGetCouponGroups).toHaveBeenCalledTimes(2);
    });
});
