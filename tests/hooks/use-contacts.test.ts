import { renderHook, waitFor, act } from "@testing-library/react";
import { useContacts } from "@/hooks/use-contacts";
import { getContacts } from "@/app/actions/contact-actions";

jest.mock("@/app/actions/contact-actions", () => ({
    getContacts: jest.fn()
}));

const mockedGetContacts = getContacts as jest.Mock;

describe("useContacts hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return initial state and fetch contacts", async () => {
        const mockData = { success: true, data: [{ id: "1", email: "<EMAIL>", message: "Hello" }] };
        mockedGetContacts.mockResolvedValue(mockData);

        let result: any;
        act(() => {
            ({ result } = renderHook(() => useContacts()));
        });

        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error state", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        const mockError = { success: false, error: "Failed to fetch contacts" };
        mockedGetContacts.mockResolvedValue(mockError);

        let result: any;
        act(() => {
            ({ result } = renderHook(() => useContacts()));
        });

        expect(result.current.loading).toBe(true);

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toEqual(new Error(mockError.error));
        consoleErrorSpy.mockRestore();
    });

    it("refetch function should recall the server action", async () => {
        mockedGetContacts.mockResolvedValueOnce({ success: true, data: [] });

        let result: any;
        act(() => {
            ({ result } = renderHook(() => useContacts()));
        });

        await waitFor(() => expect(result.current.loading).toBe(false));

        const refetchData = { success: true, data: [{ id: "2", email: "<EMAIL>", message: "Refetch" }] };
        mockedGetContacts.mockResolvedValueOnce(refetchData);

        act(() => {
            result.current.refetch();
        });

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(refetchData.data);
        expect(mockedGetContacts).toHaveBeenCalledTimes(2);
    });
});
