import { renderHook, waitFor, act } from "@testing-library/react";
import { useFAQ } from "@/hooks/use-faq";
import { getPublicFaqs } from "@/app/actions/faq-actions";
import { Faq } from "@/lib/faq-constants";

jest.mock("@/app/actions/faq-actions", () => ({
    getPublicFaqs: jest.fn()
}));

const mockedGetPublicFaqs = getPublicFaqs as jest.Mock;

describe("useFAQ hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch FAQs on mount and handle success", async () => {
        const mockData: Faq[] = [
            {
                id: "1",
                question: "Q1",
                answer: "A1",
                order_index: 1,
                created_at: "2023-01-01",
                updated_at: "2023-01-02"
            }
        ];
        mockedGetPublicFaqs.mockResolvedValue({ items: mockData, error: null });

        const { result } = renderHook(() => useFAQ());
        expect(result.current.loading).toBe(true);

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(mockData);
        expect(result.current.error).toBeNull();
        expect(mockedGetPublicFaqs).toHaveBeenCalledTimes(1);
    });

    it("should handle fetch error state", async () => {
        mockedGetPublicFaqs.mockResolvedValue({ items: [], error: "Fetch failed" });

        const { result } = renderHook(() => useFAQ());

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe("Fetch failed");
    });

    it("refetch function should recall the server action", async () => {
        mockedGetPublicFaqs.mockResolvedValueOnce({ items: [], error: null });
        const { result } = renderHook(() => useFAQ());
        await waitFor(() => expect(result.current.loading).toBe(false));

        const refetchData: Faq[] = [
            {
                id: "2",
                question: "Q2",
                answer: "A2",
                order_index: 2,
                created_at: "2023-01-03",
                updated_at: "2023-01-04"
            }
        ];
        mockedGetPublicFaqs.mockResolvedValueOnce({ items: refetchData, error: null });

        act(() => {
            result.current.refetch();
        });

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(refetchData);
        expect(mockedGetPublicFaqs).toHaveBeenCalledTimes(2);
    });
});
