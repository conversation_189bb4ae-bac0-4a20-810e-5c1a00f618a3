import { renderHook, act } from "@testing-library/react";
import { useAuth } from "@clerk/nextjs";
import { useUserOrgRole } from "@/hooks/use-user-org-role";

jest.mock("@clerk/nextjs", () => ({
    useAuth: jest.fn()
}));

describe("useUserOrgRole", () => {
    const mockUseAuth = useAuth as jest.Mock;

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return isLoaded: false and default role/orgId when Clerk is still loading", () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: null,
            isLoaded: false
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(false);
        expect(result.current.role).toBe("user");
        expect(result.current.orgId).toBeUndefined();
    });

    it('should correctly return "admin" role and orgId for org:admin claims', () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: { organizations: { org_admin_id: "org:admin" } },
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("admin");
        expect(result.current.orgId).toBe("org_admin_id");
    });

    it('should correctly return "employee" role and orgId for org:employee claims', () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: { organizations: { org_employee_id: "org:employee" } },
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("employee");
        expect(result.current.orgId).toBe("org_employee_id");
    });

    it('should correctly return "user" role and orgId for other org claims (e.g., org:member)', () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: { organizations: { org_member_id: "org:member" } },
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("user");
        expect(result.current.orgId).toBe("org_member_id");
    });

    it('should correctly return "user" role and undefined orgId when no organization claims are present', () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: { someOtherClaim: "value" },
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("user");
        expect(result.current.orgId).toBeUndefined();
    });

    it("should handle sessionClaims with multiple organizations by picking the first one", () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: {
                organizations: {
                    org_first: "org:admin",
                    org_second: "org:employee"
                }
            },
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("admin"); // Assuming Object.entries order is consistent
        expect(result.current.orgId).toBe("org_first");
    });

    it("should handle undefined or null sessionClaims gracefully after loading", () => {
        mockUseAuth.mockReturnValue({
            sessionClaims: undefined,
            isLoaded: true
        });

        const { result } = renderHook(() => useUserOrgRole());

        expect(result.current.isLoaded).toBe(true);
        expect(result.current.role).toBe("user");
        expect(result.current.orgId).toBeUndefined();

        mockUseAuth.mockReturnValue({
            sessionClaims: null,
            isLoaded: true
        });

        const { result: result2 } = renderHook(() => useUserOrgRole());

        expect(result2.current.isLoaded).toBe(true);
        expect(result2.current.role).toBe("user");
        expect(result2.current.orgId).toBeUndefined();
    });
});
