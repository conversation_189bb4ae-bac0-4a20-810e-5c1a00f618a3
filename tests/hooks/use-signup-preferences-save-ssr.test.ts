/**
 * SSR-specific tests for useSignupPreferencesSave hook
 * These tests verify that the hook handles server-side rendering correctly
 */

import { renderHook, waitFor } from "@testing-library/react";
import { ReactNode } from "react";

import { SignupPreferencesProvider } from "@/contexts/signup-preferences-context";
import { useSignupPreferencesSave } from "@/hooks/use-signup-preferences-save";

// Mock dependencies
jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("@/app/actions/signup-actions", () => ({
    saveSignupPreferences: jest.fn()
}));

jest.mock("@/utils/user-claims-client", () => ({
    getUserClaim: jest.fn(),
    setUserClaim: jest.fn(),
    UserClaimKey: {
        ACCEPTED_TERMS: "accepted_terms",
        SUBSCRIBED_TO_UPDATES: "subscribed_to_updates"
    }
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({}))
}));

const mockUseUser = require("@clerk/nextjs").useUser;
const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;
const mockGetUserClaim = require("@/utils/user-claims-client").getUserClaim;
const mockSetUserClaim = require("@/utils/user-claims-client").setUserClaim;

// Test wrapper
const TestWrapper = ({ children }: { children: ReactNode }) => {
    return <SignupPreferencesProvider>{children}</SignupPreferencesProvider>;
};

describe("useSignupPreferencesSave SSR", () => {
    const mockUser = { id: "test-user-id" };

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Default mocks
        mockUseUser.mockReturnValue({ user: null });
        mockSaveSignupPreferences.mockResolvedValue({ success: true });
        mockGetUserClaim.mockResolvedValue(null);
        mockSetUserClaim.mockResolvedValue(undefined);
    });

    describe("Server-side safety", () => {
        it("should not execute save operations during SSR", () => {
            // Mock server environment (no window)
            const originalWindow = global.window;
            delete (global as any).window;

            mockUseUser.mockReturnValue({ user: mockUser });

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Should not attempt any saves during SSR
            expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
            expect(mockSetUserClaim).not.toHaveBeenCalled();
            expect(result.current.isSaving).toBe(false);
            expect(result.current.saveResult).toBe(null);

            // Restore window
            global.window = originalWindow;
        });

        it("should handle client-side hydration correctly", async () => {
            // Start with server environment
            const originalWindow = global.window;
            delete (global as any).window;

            mockUseUser.mockReturnValue({ user: mockUser });

            const { result, rerender } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Should not save during SSR
            expect(mockSetUserClaim).not.toHaveBeenCalled();

            // Restore window (simulate hydration)
            global.window = originalWindow;

            // Re-render to simulate client-side hydration
            rerender();

            // Should now attempt save after hydration
            await waitFor(() => {
                expect(mockSetUserClaim).toHaveBeenCalled();
            });
        });

        it("should return safe defaults during SSR", () => {
            const originalWindow = global.window;
            delete (global as any).window;

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            expect(result.current.isSaving).toBe(false);
            expect(result.current.saveResult).toBe(null);
            expect(typeof result.current.performSave).toBe("function");

            // Restore window
            global.window = originalWindow;
        });
    });

    describe("Client-side execution", () => {
        it("should execute normally in browser environment", async () => {
            // Ensure window exists
            expect(typeof window).toBe("object");

            mockUseUser.mockReturnValue({ user: mockUser });

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            await waitFor(() => {
                expect(mockSetUserClaim).toHaveBeenCalled();
            });

            expect(result.current.saveResult?.success).toBe(true);
        });
    });
});
