import { renderHook, waitFor, act } from "@testing-library/react";
import { useCollaborations } from "@/hooks/use-collaborations";
import { getAllCollaborations } from "@/app/actions/collaboration-actions";

jest.mock("@/app/actions/collaboration-actions", () => ({
    getAllCollaborations: jest.fn()
}));

const mockedGetAllCollaborations = getAllCollaborations as jest.Mock;

describe("useCollaborations hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return initial state and fetch collaborations", async () => {
        const mockData = {
            success: true,
            data: [
                {
                    id: "1",
                    name: "Collaboration 1",
                    description: "Test description",
                    api_endpoint: "https://api.example.com/endpoint",
                    auth_type: "none",
                    auth_value: null,
                    created_at: "2024-01-01T00:00:00.000Z",
                    updated_at: "2024-01-02T00:00:00.000Z"
                }
            ]
        };
        mockedGetAllCollaborations.mockResolvedValue(mockData);

        const { result } = renderHook(() => useCollaborations());

        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error state", async () => {
        const mockError = { success: false, error: "Failed to fetch" };
        mockedGetAllCollaborations.mockResolvedValue(mockError);

        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});

        const { result } = renderHook(() => useCollaborations());

        expect(result.current.loading).toBe(true);

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toEqual(new Error(mockError.error));

        consoleErrorSpy.mockRestore();
    });

    it("refetch function should recall the server action", async () => {
        // Mock initial fetch with empty data
        mockedGetAllCollaborations.mockResolvedValueOnce({ success: true, data: [] });

        const { result } = renderHook(() => useCollaborations());

        await waitFor(() => expect(result.current.loading).toBe(false));

        // Mock refetch with new data
        const refetchData = {
            success: true,
            data: [
                {
                    id: "2",
                    name: "Collaboration 2",
                    description: "Another description",
                    api_endpoint: "https://api.example.com/endpoint2",
                    auth_type: "bearer_token",
                    auth_value: "token123",
                    created_at: "2024-02-01T00:00:00.000Z",
                    updated_at: "2024-02-02T00:00:00.000Z"
                }
            ]
        };
        mockedGetAllCollaborations.mockResolvedValueOnce(refetchData);

        act(() => {
            result.current.refetch();
        });

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(refetchData.data);
        expect(mockedGetAllCollaborations).toHaveBeenCalledTimes(2);
    });
});
