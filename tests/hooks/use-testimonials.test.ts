import { renderHook, waitFor } from "@testing-library/react";
import { useTestimonials } from "@/hooks/use-testimonials";
import { getTestimonials } from "@/app/actions/testimonial-actions";

jest.mock("@/app/actions/testimonial-actions", () => ({
    getTestimonials: jest.fn()
}));

const mockedGetTestimonials = getTestimonials as jest.Mock;

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("useTestimonials hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch testimonials on mount and handle success", async () => {
        const mockData = { success: true, data: [{ id: "1", name: "Testimonial" }] };
        mockedGetTestimonials.mockResolvedValue(mockData);

        const { result } = renderHook(() => useTestimonials());

        expect(result.current.loading).toBe(true);

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.error).toBeNull();
        expect(mockedGetTestimonials).toHaveBeenCalledWith(undefined);
    });

    it("should pass IDs to the server action", async () => {
        const ids = ["id-1", "id-2"];
        const mockData = { success: true, data: [{ id: "1" }, { id: "2" }] };
        mockedGetTestimonials.mockResolvedValue(mockData);

        renderHook(() => useTestimonials(ids));

        await waitFor(() => {
            expect(mockedGetTestimonials).toHaveBeenCalledWith(ids);
        });
    });

    it("should handle fetch error state", async () => {
        const errorMessage = "Failed to fetch";
        mockedGetTestimonials.mockResolvedValue({ success: false, error: errorMessage });
        const { result } = renderHook(() => useTestimonials());

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe(errorMessage);
    });
});
