import { renderHook, waitFor } from "@testing-library/react";
import { useBanners } from "@/hooks/use-banners";
import { getActiveBanners } from "@/app/actions/banner-actions";

jest.mock("@/app/actions/banner-actions", () => ({
    getActiveBanners: jest.fn()
}));

jest.mock("@clerk/nextjs", () => ({
    useAuth: jest.fn()
}));

const mockedGetActiveBanners = getActiveBanners as jest.Mock;
const mockedUseAuth = jest.requireMock("@clerk/nextjs").useAuth;

describe("useBanners hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should not fetch when auth is not loaded", () => {
        mockedUseAuth.mockReturnValue({ isLoaded: false });
        renderHook(() => useBanners());
        expect(mockedGetActiveBanners).not.toHaveBeenCalled();
    });

    it("should fetch banners when auth is loaded and handle success", async () => {
        mockedUseAuth.mockReturnValue({ isLoaded: true });
        const mockData = { success: true, data: [{ id: "1", title: "Test Banner" }] };
        mockedGetActiveBanners.mockResolvedValue(mockData);

        const { result } = renderHook(() => useBanners());

        expect(result.current.loading).toBe(true);
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error state", async () => {
        mockedUseAuth.mockReturnValue({ isLoaded: true });
        const mockError = { success: false, error: "Fetch failed" };
        mockedGetActiveBanners.mockResolvedValue(mockError);

        const { result } = renderHook(() => useBanners());

        expect(result.current.loading).toBe(true);
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe(mockError.error);
    });
});
