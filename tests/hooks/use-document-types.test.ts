import { act, renderHook, waitFor } from "@testing-library/react";
import * as actions from "@/app/actions/document-type-actions";
import { useDocumentTypes } from "@/hooks/use-document-types";
import { type DocumentTypeWithUrls } from "@/app/actions/document-type-actions";

jest.mock("@/app/actions/document-type-actions", () => ({
    getDocumentTypes: jest.fn()
}));

const mockedGetDocumentTypes = actions.getDocumentTypes as jest.Mock;

const mockDocumentTypes: DocumentTypeWithUrls[] = [
    {
        id: "1",
        name: "Type 1",
        description: "Description 1",
        allowed_mime_types: ["application/pdf"],
        group_id: "g1",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        example_file_path: "example.pdf",
        link_url: null,
        max_file_size_mb: 5,
        example_file_url: "http://example.com/example.pdf"
    }
];

const TEXTS = {
    fetchError: "Failed to fetch document types"
};

describe("useDocumentTypes", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should have correct initial state", () => {
        const { result } = renderHook(() => useDocumentTypes());
        expect(result.current.items).toEqual([]);
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBe(null);
    });

    it("should fetch document types and update state on success", async () => {
        mockedGetDocumentTypes.mockResolvedValue({ success: true, data: mockDocumentTypes });
        const { result } = renderHook(() => useDocumentTypes());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual(mockDocumentTypes);
        expect(result.current.error).toBe(null);
    });

    it("should handle fetch error", async () => {
        mockedGetDocumentTypes.mockResolvedValue({ success: false, error: TEXTS.fetchError });
        const { result } = renderHook(() => useDocumentTypes());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBe(TEXTS.fetchError);
    });

    it("should refetch data when refetch is called", async () => {
        mockedGetDocumentTypes.mockResolvedValueOnce({ success: true, data: mockDocumentTypes });
        const { result } = renderHook(() => useDocumentTypes());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const newMockData = [mockDocumentTypes[0]];
        mockedGetDocumentTypes.mockResolvedValueOnce({ success: true, data: newMockData });

        await act(async () => {
            await result.current.refetch();
        });

        await waitFor(() => {
            expect(result.current.items).toEqual(newMockData);
        });
    });
});
