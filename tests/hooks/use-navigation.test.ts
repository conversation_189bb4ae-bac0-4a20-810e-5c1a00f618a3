import { renderHook, act } from "@testing-library/react";
import * as nextNavigation from "next/navigation";
import * as sidebarModule from "@/components/ui/sidebar";
import * as navUtils from "@/components/layout/navigation/nav-utils";
import { useNavigation } from "@/hooks/use-navigation";
import { Home } from "lucide-react";

jest.mock("next/navigation");
jest.mock("@/components/ui/sidebar");
jest.mock("@/components/layout/navigation/nav-utils");

const mockUsePathname = nextNavigation.usePathname as jest.Mock;
const mockUseSidebar = sidebarModule.useSidebar as jest.Mock;
const mockIsNavItemActive = navUtils.isNavItemActive as jest.Mock;

describe("useNavigation", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/dashboard");
        mockUseSidebar.mockReturnValue({
            isMobile: false,
            setOpenMobile: jest.fn(),
            state: "expanded"
        });
        mockIsNavItemActive.mockReturnValue(false);
    });

    const mainItems = [
        { title: "Home", url: "/dashboard", icon: "home" },
        { title: "Settings", url: "/settings", icon: "settings", disabled: true }
    ];

    const secondaryItems = [
        { label: "Profile", href: "/profile", icon: Home },
        { label: "Billing", href: "/billing", disabled: false }
    ];

    const mixedItems = [...mainItems, ...secondaryItems];

    it("normalizes main navigation items", () => {
        const { result } = renderHook(() => useNavigation(mainItems));
        expect(result.current.normalizedItems).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ href: "/dashboard", label: "Home", originalIcon: "home" }),
                expect.objectContaining({
                    href: "/settings",
                    label: "Settings",
                    originalIcon: "settings",
                    disabled: true
                })
            ])
        );
    });

    it("normalizes secondary navigation items with Lucide icons", () => {
        const { result } = renderHook(() => useNavigation(secondaryItems));
        expect(result.current.normalizedItems).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ href: "/profile", label: "Profile", icon: Home }),
                expect.objectContaining({ href: "/billing", label: "Billing", disabled: false })
            ])
        );
    });

    it("normalizes mixed navigation items", () => {
        const { result } = renderHook(() => useNavigation(mixedItems));
        expect(result.current.normalizedItems).toHaveLength(4);
        expect(result.current.normalizedItems[0]).toHaveProperty("originalIcon", "home");
        expect(result.current.normalizedItems[2]).toHaveProperty("icon", Home);
    });

    it("handles items with missing optional properties", () => {
        const itemsWithMissingProps = [{ title: "Just a title", url: "/just-a-title" }];
        const { result } = renderHook(() => useNavigation(itemsWithMissingProps as any));
        expect(result.current.normalizedItems[0]).toEqual(
            expect.objectContaining({
                href: "/just-a-title",
                label: "Just a title"
            })
        );
    });

    it("handleLinkClick closes mobile sidebar and calls onLinkClick", () => {
        const setOpenMobile = jest.fn();
        mockUseSidebar.mockReturnValue({ isMobile: true, setOpenMobile, state: "collapsed" });
        const onLinkClick = jest.fn();
        const { result } = renderHook(() => useNavigation(mainItems, { onLinkClick }));
        act(() => {
            result.current.handleLinkClick();
        });
        expect(setOpenMobile).toHaveBeenCalledWith(false);
        expect(onLinkClick).toHaveBeenCalled();
    });

    it("handleLinkClick works without onLinkClick prop", () => {
        const setOpenMobile = jest.fn();
        mockUseSidebar.mockReturnValue({ isMobile: true, setOpenMobile, state: "collapsed" });
        const { result } = renderHook(() => useNavigation(mainItems));
        act(() => {
            result.current.handleLinkClick();
        });
        expect(setOpenMobile).toHaveBeenCalledWith(false);
    });

    it("isItemActive delegates to isNavItemActive", () => {
        mockIsNavItemActive.mockReturnValue(true);
        const { result } = renderHook(() => useNavigation(mainItems));
        expect(result.current.isItemActive("/dashboard")).toBe(true);
        expect(mockIsNavItemActive).toHaveBeenCalledWith("/dashboard", "/dashboard", true);
    });

    it("isItemActive respects exactMatch option", () => {
        const { result } = renderHook(() => useNavigation(mainItems, { exactMatch: false }));
        result.current.isItemActive("/dashboard/sub");
        expect(mockIsNavItemActive).toHaveBeenCalledWith("/dashboard", "/dashboard/sub", false);
    });

    it("returns sidebarState, isMobile, and pathname", () => {
        mockUseSidebar.mockReturnValue({ isMobile: true, setOpenMobile: jest.fn(), state: "collapsed" });
        mockUsePathname.mockReturnValue("/settings");
        const { result } = renderHook(() => useNavigation(mainItems));
        expect(result.current.sidebarState).toBe("collapsed");
        expect(result.current.isMobile).toBe(true);
        expect(result.current.pathname).toBe("/settings");
    });

    it("handles missing sidebar context gracefully", () => {
        mockUseSidebar.mockImplementation(() => {
            throw new Error("no context");
        });
        const { result } = renderHook(() => useNavigation(mainItems));
        expect(result.current.sidebarState).toBe("expanded");
        expect(result.current.isMobile).toBe(false);
    });
});
