import { renderHook, waitFor } from "@testing-library/react";

import { useUserNotes } from "@/hooks/use-user-notes";
import { notesFetchError } from "@/app/actions/user-notes-actions";

// Mock the actions
jest.mock("@/app/actions/user-notes-actions", () => ({
    getUserNotes: jest.fn(),
    notesFetchError: "שגיאה בטעינת הערות"
}));

const mockGetUserNotes = require("@/app/actions/user-notes-actions").getUserNotes;

describe("useUserNotes", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("returns initial state", () => {
        mockGetUserNotes.mockResolvedValue({ success: true, notes: [] });

        const { result } = renderHook(() => useUserNotes("user123"));

        expect(result.current.notes).toEqual([]);
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBe(null);
        expect(typeof result.current.refetch).toBe("function");
    });

    it("fetches notes successfully", async () => {
        const mockNotes = [
            { id: "1", note: "Test note 1", createdAt: "2023-01-01" },
            { id: "2", note: "Test note 2", createdAt: "2023-01-02" }
        ];

        mockGetUserNotes.mockResolvedValue({
            success: true,
            notes: mockNotes
        });

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual(mockNotes);
        expect(result.current.error).toBe(null);
        expect(mockGetUserNotes).toHaveBeenCalledWith("user123");
    });

    it("handles error response from action", async () => {
        mockGetUserNotes.mockResolvedValue({
            success: false,
            error: "Failed to fetch notes"
        });

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual([]);
        expect(result.current.error?.message).toBe("Failed to fetch notes");
    });

    it("handles network error", async () => {
        mockGetUserNotes.mockRejectedValue(new Error("Network error"));

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual([]);
        expect(result.current.error?.message).toBe("Network error");
    });

    it("handles non-Error objects thrown", async () => {
        mockGetUserNotes.mockRejectedValue("String error");

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual([]);
        expect(result.current.error?.message).toBe(notesFetchError);
    });

    it("handles empty user ID", async () => {
        const { result } = renderHook(() => useUserNotes(""));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual([]);
        expect(result.current.error).toBe(null);
        expect(mockGetUserNotes).not.toHaveBeenCalled();
    });

    it("handles whitespace-only user ID", async () => {
        const { result } = renderHook(() => useUserNotes("   "));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual([]);
        expect(result.current.error).toBe(null);
        expect(mockGetUserNotes).not.toHaveBeenCalled();
    });

    it("refetches data when refetch is called", async () => {
        const initialNotes = [{ id: "1", note: "Initial note", createdAt: "2023-01-01" }];
        const updatedNotes = [
            { id: "1", note: "Initial note", createdAt: "2023-01-01" },
            { id: "2", note: "New note", createdAt: "2023-01-02" }
        ];

        mockGetUserNotes
            .mockResolvedValueOnce({ success: true, notes: initialNotes })
            .mockResolvedValueOnce({ success: true, notes: updatedNotes });

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual(initialNotes);

        // Call refetch
        result.current.refetch();

        await waitFor(() => {
            expect(result.current.notes).toEqual(updatedNotes);
        });

        expect(mockGetUserNotes).toHaveBeenCalledTimes(2);
    });

    it("re-fetches when user ID changes", async () => {
        const notesUser1 = [{ id: "1", note: "User 1 note", createdAt: "2023-01-01" }];
        const notesUser2 = [{ id: "2", note: "User 2 note", createdAt: "2023-01-02" }];

        mockGetUserNotes
            .mockResolvedValueOnce({ success: true, notes: notesUser1 })
            .mockResolvedValueOnce({ success: true, notes: notesUser2 });

        const { result, rerender } = renderHook(({ userId }) => useUserNotes(userId), {
            initialProps: { userId: "user1" }
        });

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.notes).toEqual(notesUser1);
        expect(mockGetUserNotes).toHaveBeenCalledWith("user1");

        // Change user ID
        rerender({ userId: "user2" });

        await waitFor(() => {
            expect(result.current.notes).toEqual(notesUser2);
        });

        expect(mockGetUserNotes).toHaveBeenCalledWith("user2");
        expect(mockGetUserNotes).toHaveBeenCalledTimes(2);
    });

    it("provides refetch function", () => {
        const { result } = renderHook(() => useUserNotes("user123"));
        expect(typeof result.current.refetch).toBe("function");
    });

    it("clears error state on successful refetch", async () => {
        mockGetUserNotes
            .mockRejectedValueOnce(new Error("Initial error"))
            .mockResolvedValueOnce({ success: true, notes: [] });

        const { result } = renderHook(() => useUserNotes("user123"));

        await waitFor(() => {
            expect(result.current.error?.message).toBe("Initial error");
        });

        // Refetch should clear error
        result.current.refetch();

        await waitFor(() => {
            expect(result.current.error).toBe(null);
        });
    });
});
