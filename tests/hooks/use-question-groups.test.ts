import { renderHook, waitFor, act } from "@testing-library/react";
import { useQuestionGroups } from "@/hooks/use-question-groups";
import { getSupabaseClient } from "@/utils/supabase/client";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn()
}));

const mockedGetSupabaseClient = getSupabaseClient as jest.Mock;

const mockGroupsData = [
    {
        id: "group1",
        name: "Personal Information",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
    },
    {
        id: "group2",
        name: "Academic Background",
        created_at: "2023-01-02T00:00:00Z",
        updated_at: "2023-01-02T00:00:00Z"
    },
    {
        id: "group3",
        name: "Financial Information",
        created_at: "2023-01-03T00:00:00Z",
        updated_at: "2023-01-03T00:00:00Z"
    }
];

const mockQuestionsData = [
    { group_id: "group1" },
    { group_id: "group1" },
    { group_id: "group2" },
    { group_id: "group3" },
    { group_id: "group3" },
    { group_id: "group3" }
];

describe("useQuestionGroups", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should start in a loading state and fetch question groups successfully", async () => {
        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: mockQuestionsData, error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toHaveLength(3);
        expect(result.current.error).toBeNull();
    });

    it("should calculate question counts correctly", async () => {
        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: mockQuestionsData, error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const items = result.current.items;

        // group1 has 2 questions
        expect(items.find((item) => item.id === "group1")?.questions_count).toBe(2);

        // group2 has 1 question
        expect(items.find((item) => item.id === "group2")?.questions_count).toBe(1);

        // group3 has 3 questions
        expect(items.find((item) => item.id === "group3")?.questions_count).toBe(3);
    });

    it("should handle groups with no questions", async () => {
        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: [{ group_id: "group1" }], error: null } // questions query - only group1 has questions
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const items = result.current.items;

        // group1 has 1 question
        expect(items.find((item) => item.id === "group1")?.questions_count).toBe(1);

        // group2 and group3 have 0 questions
        expect(items.find((item) => item.id === "group2")?.questions_count).toBe(0);
        expect(items.find((item) => item.id === "group3")?.questions_count).toBe(0);
    });

    it("should handle empty groups data", async () => {
        const mockBuilder = createThenableBuilder([
            { data: [], error: null }, // groups_question query
            { data: [], error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should handle null data", async () => {
        const mockBuilder = createThenableBuilder([
            { data: null, error: null }, // groups_question query
            { data: null, error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should handle questions with null group_id", async () => {
        const questionsWithNullGroupId = [
            { group_id: "group1" },
            { group_id: null },
            { group_id: "group1" },
            { group_id: "" }
        ];

        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: questionsWithNullGroupId, error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const items = result.current.items;

        // Only group1 should have questions counted (null and empty string should be ignored)
        expect(items.find((item) => item.id === "group1")?.questions_count).toBe(2);
        expect(items.find((item) => item.id === "group2")?.questions_count).toBe(0);
        expect(items.find((item) => item.id === "group3")?.questions_count).toBe(0);
    });

    it("should handle groups fetch error", async () => {
        const mockError = new Error("Groups fetch failed");
        const mockBuilder = createThenableBuilder([
            { data: null, error: mockError }, // groups_question query fails
            { data: mockQuestionsData, error: null } // questions query
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
        expect(result.current.error?.message).toBe("Groups fetch failed");
        expect(console.error).toHaveBeenCalledWith("Error fetching question groups:", mockError);
    });

    it("should handle questions fetch error", async () => {
        const mockError = new Error("Questions fetch failed");
        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: null, error: mockError } // questions query fails
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
        expect(result.current.error?.message).toBe("Questions fetch failed");
        expect(console.error).toHaveBeenCalledWith("Error fetching question groups:", mockError);
    });

    it("should handle non-Error exceptions", async () => {
        const mockBuilder = createThenableBuilder([
            { data: null, error: new Error("String error") }, // groups_question query fails
            { data: mockQuestionsData, error: null } // questions query
        ]);

        // Make the builder throw a string instead of an Error
        mockBuilder.then = () => {
            throw "String error";
        };

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
        expect(result.current.error?.message).toBe("Unknown error occurred");
    });

    it("should refetch question groups when refetch is called", async () => {
        const mockBuilder1 = createThenableBuilder([
            { data: [], error: null }, // groups_question query
            { data: [], error: null } // questions query
        ]);
        const mockBuilder2 = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: mockQuestionsData, error: null } // questions query
        ]);

        mockedGetSupabaseClient
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder1)
            })
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder2)
            });

        const { result } = renderHook(() => useQuestionGroups());

        // Wait for initial fetch
        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);

        // Call refetch
        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.items).toHaveLength(3);
        expect(result.current.error).toBeNull();
    });

    it("should set loading state correctly during refetch", async () => {
        const mockBuilder1 = createThenableBuilder([
            { data: [], error: null }, // groups_question query
            { data: [], error: null } // questions query
        ]);
        const mockBuilder2 = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: mockQuestionsData, error: null } // questions query
        ]);

        mockedGetSupabaseClient
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder1)
            })
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder2)
            });

        const { result } = renderHook(() => useQuestionGroups());

        // Wait for initial fetch
        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        // Start refetch and check loading state
        act(() => {
            result.current.refetch();
        });

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });
    });

    it("should verify correct Supabase query structure", async () => {
        const mockBuilder = createThenableBuilder([
            { data: mockGroupsData, error: null }, // groups_question query
            { data: mockQuestionsData, error: null } // questions query
        ]);

        const mockFrom = jest.fn(() => mockBuilder);
        mockedGetSupabaseClient.mockReturnValue({
            from: mockFrom
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockFrom).toHaveBeenCalledWith("groups_question");
        expect(mockFrom).toHaveBeenCalledWith("questions");
        expect(mockBuilder.select).toHaveBeenCalledWith("id, name, created_at, updated_at");
        expect(mockBuilder.select).toHaveBeenCalledWith("group_id");
        expect(mockBuilder.not).toHaveBeenCalledWith("group_id", "is", null);
        expect(mockBuilder.order).toHaveBeenCalledWith("created_at", { ascending: false });
    });

    it("should handle multiple questions with same group_id correctly", async () => {
        const duplicateGroupQuestions = [
            { group_id: "group1" },
            { group_id: "group1" },
            { group_id: "group1" },
            { group_id: "group1" },
            { group_id: "group1" }
        ];

        const mockBuilder = createThenableBuilder([
            { data: [mockGroupsData[0]], error: null }, // only group1
            { data: duplicateGroupQuestions, error: null } // 5 questions for group1
        ]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const items = result.current.items;
        expect(items).toHaveLength(1);
        expect(items[0].questions_count).toBe(5);
    });
});
