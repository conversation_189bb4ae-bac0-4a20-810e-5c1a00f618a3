import { renderHook, waitFor, act } from "@testing-library/react";
import {
    useScholarshipConditionGroups,
    type ScholarshipConditionGroup
} from "@/hooks/use-scholarship-condition-groups";
import { getScholarshipConditionGroups } from "@/app/actions/scholarship-condition-group-actions";

jest.mock("@/app/actions/scholarship-condition-group-actions");

const mockGetScholarshipConditionGroups = getScholarshipConditionGroups as jest.Mock;

const mockGroups: ScholarshipConditionGroup[] = [
    {
        id: "1",
        name: "Test Group 1",
        conditions_count: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    },
    {
        id: "2",
        name: "Test Group 2",
        conditions_count: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }
];

describe("useScholarshipConditionGroups", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should set loading to true initially and fetch data", async () => {
        mockGetScholarshipConditionGroups.mockResolvedValue({ success: true, data: [] });

        const { result } = renderHook(() => useScholarshipConditionGroups());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockGetScholarshipConditionGroups).toHaveBeenCalledTimes(1);
    });

    it("should handle successful data fetching", async () => {
        mockGetScholarshipConditionGroups.mockResolvedValue({ success: true, data: mockGroups });

        const { result } = renderHook(() => useScholarshipConditionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual(mockGroups);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error", async () => {
        const errorMessage = "Failed to fetch";
        mockGetScholarshipConditionGroups.mockResolvedValue({ success: false, error: errorMessage });

        const { result } = renderHook(() => useScholarshipConditionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toEqual(new Error(errorMessage));
    });

    it("should handle thrown error during fetch", async () => {
        const thrownError = new Error("Network error");
        mockGetScholarshipConditionGroups.mockRejectedValue(thrownError);

        const { result } = renderHook(() => useScholarshipConditionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toEqual(thrownError);
    });

    it("should handle thrown non-error during fetch", async () => {
        const thrownError = "Network error as string";
        mockGetScholarshipConditionGroups.mockRejectedValue(thrownError);

        const { result } = renderHook(() => useScholarshipConditionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toEqual(new Error("Failed to fetch scholarship condition groups"));
    });

    it("should refetch data when refetch is called", async () => {
        mockGetScholarshipConditionGroups.mockResolvedValue({ success: true, data: mockGroups });

        const { result } = renderHook(() => useScholarshipConditionGroups());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockGetScholarshipConditionGroups).toHaveBeenCalledTimes(1);
        expect(result.current.items).toEqual(mockGroups);

        const newMockGroups = [
            ...mockGroups,
            {
                id: "3",
                name: "Test Group 3",
                conditions_count: 5,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];
        mockGetScholarshipConditionGroups.mockResolvedValue({ success: true, data: newMockGroups });

        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.loading).toBe(false);
        expect(mockGetScholarshipConditionGroups).toHaveBeenCalledTimes(2);
        expect(result.current.items).toEqual(newMockGroups);
    });
});
