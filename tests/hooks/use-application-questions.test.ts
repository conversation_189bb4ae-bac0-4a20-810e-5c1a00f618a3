import { renderHook, waitFor } from "@testing-library/react";
import { useApplicationQuestions } from "@/hooks/use-application-questions";
import { getSupabaseClient } from "@/utils/supabase/client";

// Mock Supabase client
jest.mock("@/utils/supabase/client");
const mockSupabaseClient = getSupabaseClient as jest.MockedFunction<typeof getSupabaseClient>;

describe("use-application-questions", () => {
    const mockSupabase = {
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockSupabaseClient.mockReturnValue(mockSupabase as any);
    });

    describe("successful data fetching", () => {
        it("loads application question with options from database", async () => {
            const mockQuestionData = [
                {
                    id: "test-question-id",
                    type: "single_select",
                    metadata: {
                        label: "האם תרצה להגיש בקשה?",
                        placeholder: "בחר תשובה",
                        options: [
                            { id: "yes", label: "כן, אני מעוניין" },
                            { id: "no", label: "לא, תודה" }
                        ]
                    },
                    section: "specific_scholarship"
                }
            ];

            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: mockQuestionData,
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            const { result } = renderHook(() => useApplicationQuestions());

            // Initial state
            expect(result.current.loading).toBe(true);
            expect(result.current.applicationQuestion).toBe(null);

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(result.current.applicationQuestion).toEqual({
                id: "test-question-id",
                label: "האם תרצה להגיש בקשה?",
                placeholder: "בחר תשובה",
                options: [
                    { id: "yes", label: "כן, אני מעוניין" },
                    { id: "no", label: "לא, תודה" }
                ]
            });
            expect(result.current.error).toBe(null);
        });

        it("returns null when no questions found", async () => {
            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            const { result } = renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(result.current.applicationQuestion).toBe(null);
            expect(result.current.error).toBe(null);
        });

        it("handles questions without options", async () => {
            const mockQuestionData = [
                {
                    id: "test-question-id",
                    type: "single_select",
                    metadata: {
                        label: "Test question without options"
                    },
                    section: "specific_scholarship"
                }
            ];

            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: mockQuestionData,
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            const { result } = renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(result.current.applicationQuestion).toBe(null);
            expect(result.current.error).toBe(null);
        });
    });

    describe("error handling", () => {
        it("handles database errors gracefully", async () => {
            const mockError = new Error("Database connection failed");
            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: null,
                    error: mockError
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            const { result } = renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(result.current.applicationQuestion).toBe(null);
            expect(result.current.error).toEqual(mockError);
        });

        it("handles query exceptions", async () => {
            const mockSelect = jest.fn().mockImplementation(() => {
                throw new Error("Query failed");
            });

            mockSupabase.from.mockReturnValue({
                select: mockSelect
            });

            const { result } = renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(result.current.applicationQuestion).toBe(null);
            expect(result.current.error).toBeInstanceOf(Error);
            expect(result.current.error?.message).toBe("Query failed");
        });
    });

    describe("refetch functionality", () => {
        it("provides refetch function", async () => {
            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            const { result } = renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(result.current.loading).toBe(false);
            });

            expect(typeof result.current.refetch).toBe("function");

            // Test refetch
            await result.current.refetch();

            expect(mockSupabase.from).toHaveBeenCalledTimes(2);
        });
    });

    describe("database query parameters", () => {
        it("queries for correct table and parameters", async () => {
            const mockChain = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockChain);

            renderHook(() => useApplicationQuestions());

            await waitFor(() => {
                expect(mockSupabase.from).toHaveBeenCalledWith("questions");
                expect(mockChain.select).toHaveBeenCalledWith("id, type, metadata, section");
                expect(mockChain.eq).toHaveBeenCalledWith("section", "specific_scholarship");
                expect(mockChain.eq).toHaveBeenCalledWith("type", "single_select");
                expect(mockChain.order).toHaveBeenCalledWith("created_at", { ascending: false });
                expect(mockChain.limit).toHaveBeenCalledWith(1);
            });
        });
    });
});
