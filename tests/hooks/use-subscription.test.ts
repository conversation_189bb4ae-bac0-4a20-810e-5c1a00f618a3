import { renderHook, waitFor, act } from "@testing-library/react";
import { useSubscription } from "@/hooks/use-subscription";
import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

jest.mock("@/app/actions/subscriptions-actions", () => ({
    getCurrentUserSubscription: jest.fn()
}));

const mockGetCurrentUserSubscription = getCurrentUserSubscription as jest.Mock;

const mockSubscription: UserSubscriptionWithPlan = {
    id: "sub_123",
    user_id: "user_123",
    plan_id: "plan_pro",
    start_date: new Date().toISOString(),
    expiration_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
    is_active: true,
    planType: "milgapro",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    coupon_id: null,
    order_id: null,
    paid_amount: null,
    payment_details: null,
    plan_price: null,
    transaction_id: null
};

describe("useSubscription", () => {
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        mockGetCurrentUserSubscription.mockClear();
        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    it("should return loading true on initial render", () => {
        const { result } = renderHook(() => useSubscription());
        expect(result.current.loading).toBe(true);
    });

    it("should return subscription data on successful fetch", async () => {
        const subData = { id: "sub1", status: "active" };
        mockGetCurrentUserSubscription.mockResolvedValue(subData);
        const { result } = renderHook(() => useSubscription());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.subscription).toEqual(subData);
            expect(result.current.error).toBe(null);
        });
    });

    it("should return an error if the fetch fails", async () => {
        const error = new Error("Failed to fetch");
        mockGetCurrentUserSubscription.mockRejectedValue(error);
        const { result } = renderHook(() => useSubscription());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
            expect(result.current.subscription).toBe(null);
            expect(result.current.error).toEqual(error);
        });
    });

    it("should refetch data when refetch is called", async () => {
        const subData1 = { id: "sub1", status: "active" };
        const subData2 = { id: "sub2", status: "canceled" };
        mockGetCurrentUserSubscription.mockResolvedValueOnce(subData1);

        const { result } = renderHook(() => useSubscription());

        await waitFor(() => expect(result.current.subscription).toEqual(subData1));

        mockGetCurrentUserSubscription.mockResolvedValueOnce(subData2);

        await act(async () => {
            result.current.refetch();
        });

        await waitFor(() => {
            expect(result.current.subscription).toEqual(subData2);
        });
    });
});
