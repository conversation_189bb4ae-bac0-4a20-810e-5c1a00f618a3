import { renderHook, waitFor, act } from "@testing-library/react";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";

import { useUserDocuments } from "@/hooks/use-user-documents";
import {
    getRequiredAndUploadedDocuments,
    uploadUserDocument,
    UserDocumentStatus
} from "@/app/actions/user-document-actions";

jest.mock("@clerk/nextjs", () => ({
    useAuth: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        loading: jest.fn(),
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/user-document-actions", () => ({
    getRequiredAndUploadedDocuments: jest.fn(),
    uploadUserDocument: jest.fn()
}));

const mockUseAuth = useAuth as jest.Mock;
const mockGetRequiredAndUploadedDocuments = getRequiredAndUploadedDocuments as jest.Mock;
const mockUploadUserDocument = uploadUserDocument as jest.Mock;
const mockToastLoading = toast.loading as jest.Mock;
const mockToastSuccess = toast.success as jest.Mock;
const mockToastError = toast.error as jest.Mock;

const mockDocuments: UserDocumentStatus[] = [
    {
        documentTypeId: "1",
        documentTypeName: "ID Card",
        documentTypeDescription: "A copy of your ID card.",
        documentGroupId: "group1",
        documentGroupName: "Personal Documents",
        link_url: null,
        allowedMimeTypes: ["application/pdf", "image/jpeg"],
        maxFileSizeInMB: 5,
        isUploaded: false,
        uploadedFileName: null,
        acceptedFileTypeDescription: "PDF, JPG"
    },
    {
        documentTypeId: "2",
        documentTypeName: "Diploma",
        documentTypeDescription: "Your high school diploma.",
        documentGroupId: "group2",
        documentGroupName: "Academic Records",
        link_url: null,
        allowedMimeTypes: ["application/pdf"],
        maxFileSizeInMB: 10,
        isUploaded: true,
        uploadedFileName: "diploma.pdf",
        uploadedFileUrl: "http://example.com/diploma.pdf",
        acceptedFileTypeDescription: "PDF"
    }
];

describe("useUserDocuments", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should be in loading state initially when auth is loading", () => {
        mockUseAuth.mockReturnValue({ isLoaded: false, userId: null });
        const { result } = renderHook(() => useUserDocuments());
        expect(result.current.loading).toBe(true);
        expect(result.current.documents).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should handle unauthenticated user state", () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: null });
        const { result } = renderHook(() => useUserDocuments());
        expect(result.current.loading).toBe(false);
        expect(result.current.documents).toEqual([]);
        expect(result.current.error).toBe("User not authenticated. Please log in.");
    });

    it("should fetch documents successfully for an authenticated user", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: "user-123" });
        mockGetRequiredAndUploadedDocuments.mockResolvedValue({
            success: true,
            documents: mockDocuments
        });

        const { result } = renderHook(() => useUserDocuments());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.documents).toEqual(mockDocuments);
        expect(result.current.error).toBeNull();
        expect(mockGetRequiredAndUploadedDocuments).toHaveBeenCalledTimes(1);
    });

    it("should handle failure when fetching documents", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: "user-123" });
        mockGetRequiredAndUploadedDocuments.mockResolvedValue({
            success: false,
            error: "Failed to fetch"
        });

        const { result } = renderHook(() => useUserDocuments());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.documents).toEqual([]);
        expect(result.current.error).toBe("Failed to fetch");
    });

    it("should handle document upload successfully", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: "user-123" });
        mockGetRequiredAndUploadedDocuments.mockResolvedValue({
            success: true,
            documents: []
        });
        mockUploadUserDocument.mockResolvedValue({ success: true });
        mockToastLoading.mockReturnValue("toast-id");

        const { result } = renderHook(() => useUserDocuments());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const file = new File([""], "test.pdf", { type: "application/pdf" });
        await act(async () => {
            await result.current.handleDocumentUpload("doc-type-1", file);
        });

        expect(mockToastLoading).toHaveBeenCalledWith("מעלה את המסמך, אנא המתן...");
        expect(mockUploadUserDocument).toHaveBeenCalledWith("doc-type-1", expect.any(File), undefined);
        expect(mockToastSuccess).toHaveBeenCalledWith("המסמך הועלה בהצלחה.", { id: "toast-id" });
        expect(mockGetRequiredAndUploadedDocuments).toHaveBeenCalledTimes(2);
    });

    it("should handle document upload failure", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: "user-123" });
        mockGetRequiredAndUploadedDocuments.mockResolvedValue({
            success: true,
            documents: []
        });
        mockUploadUserDocument.mockResolvedValue({ success: false, error: "Upload failed" });
        mockToastLoading.mockReturnValue("toast-id");

        const { result } = renderHook(() => useUserDocuments());

        await waitFor(() => expect(result.current.loading).toBe(false));

        const file = new File([""], "test.pdf", { type: "application/pdf" });
        await act(async () => {
            await result.current.handleDocumentUpload("doc-type-1", file);
        });

        expect(mockToastError).toHaveBeenCalledWith("Upload failed", { id: "toast-id" });
    });

    it("should prevent upload if user is not authenticated", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: null });

        const { result } = renderHook(() => useUserDocuments());
        const file = new File([""], "test.pdf", { type: "application/pdf" });

        await act(async () => {
            await result.current.handleDocumentUpload("doc-type-1", file);
        });

        expect(mockToastError).toHaveBeenCalledWith("User not authenticated. Please log in.");
        expect(mockUploadUserDocument).not.toHaveBeenCalled();
    });

    it("should refetch documents when refetchDocuments is called", async () => {
        mockUseAuth.mockReturnValue({ isLoaded: true, userId: "user-123" });
        mockGetRequiredAndUploadedDocuments.mockResolvedValue({
            success: true,
            documents: mockDocuments
        });

        const { result } = renderHook(() => useUserDocuments());

        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(mockGetRequiredAndUploadedDocuments).toHaveBeenCalledTimes(1);

        await act(async () => {
            await result.current.refetchDocuments();
        });

        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(mockGetRequiredAndUploadedDocuments).toHaveBeenCalledTimes(2);
    });
});
