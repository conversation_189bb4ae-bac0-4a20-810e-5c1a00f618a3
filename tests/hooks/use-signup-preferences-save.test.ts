import { renderHook, waitFor } from "@testing-library/react";
import { ReactNode } from "react";

import { saveSignupPreferences } from "@/app/actions/signup-actions";
import { SignupPreferencesProvider } from "@/contexts/signup-preferences-context";
import { useSignupPreferencesSave } from "@/hooks/use-signup-preferences-save";
import { getUserClaim, setUserClaim, UserClaimKey } from "@/utils/user-claims-client";

// Mock dependencies
jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("@/app/actions/signup-actions", () => ({
    saveSignupPreferences: jest.fn()
}));

jest.mock("@/utils/user-claims-client", () => ({
    getUserClaim: jest.fn(),
    setUserClaim: jest.fn(),
    UserClaimKey: {
        ACCEPTED_TERMS: "accepted_terms",
        SUBSCRIBED_TO_UPDATES: "subscribed_to_updates"
    }
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({}))
}));

jest.mock("@/contexts/signup-preferences-context", () => ({
    ...jest.requireActual("@/contexts/signup-preferences-context"),
    useSignupPreferences: jest.fn()
}));

const mockUseUser = require("@clerk/nextjs").useUser;
const mockSaveSignupPreferences = saveSignupPreferences as jest.MockedFunction<typeof saveSignupPreferences>;
const mockGetUserClaim = getUserClaim as jest.MockedFunction<typeof getUserClaim>;
const mockSetUserClaim = setUserClaim as jest.MockedFunction<typeof setUserClaim>;
const mockUseSignupPreferences = require("@/contexts/signup-preferences-context").useSignupPreferences;

// Mock sessionStorage
const mockSessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
};
Object.defineProperty(window, "sessionStorage", { value: mockSessionStorage });

describe("useSignupPreferencesSave", () => {
    const mockUser = { id: "test-user-id" };
    const mockClearPreferences = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseUser.mockReturnValue({ user: null });
        mockUseSignupPreferences.mockReturnValue({
            state: { preferences: { acceptedTerms: false, subscribeNewsletter: true }, isSet: false },
            clearPreferences: mockClearPreferences,
            hasPreferences: jest.fn(() => false)
        });
        mockGetUserClaim.mockResolvedValue(null);
        mockSetUserClaim.mockResolvedValue(undefined);
        mockSaveSignupPreferences.mockResolvedValue({ success: true });
    });

    it("should not save when user is not available", () => {
        renderHook(() => useSignupPreferencesSave());

        expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should not save when user already has preferences", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });
        mockGetUserClaim.mockResolvedValueOnce(true); // accepted_terms exists

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toEqual({ success: true });
        });

        expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
        expect(mockSetUserClaim).not.toHaveBeenCalled();
        expect(mockClearPreferences).toHaveBeenCalled();
    });

    it("should save context preferences when available", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });
        mockUseSignupPreferences.mockReturnValue({
            state: { preferences: { acceptedTerms: true, subscribeNewsletter: false }, isSet: true },
            clearPreferences: mockClearPreferences,
            hasPreferences: jest.fn(() => true)
        });

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toEqual({ success: true, usedDefaults: false });
        });

        expect(mockSaveSignupPreferences).toHaveBeenCalledWith({
            acceptedTerms: true,
            subscribeNewsletter: false
        });
        expect(mockClearPreferences).toHaveBeenCalled();
    });

    it("should use defaults when no context preferences", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toEqual({ success: true, usedDefaults: true });
        });

        expect(mockSetUserClaim).toHaveBeenCalledWith(
            {},
            "test-user-id",
            UserClaimKey.ACCEPTED_TERMS,
            true
        );
        expect(mockSetUserClaim).toHaveBeenCalledWith(
            {},
            "test-user-id",
            UserClaimKey.SUBSCRIBED_TO_UPDATES,
            false
        );
        expect(mockClearPreferences).toHaveBeenCalled();
    });

    it("should fall back to defaults when context save fails", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });
        mockUseSignupPreferences.mockReturnValue({
            state: { preferences: { acceptedTerms: true, subscribeNewsletter: false }, isSet: true },
            clearPreferences: mockClearPreferences,
            hasPreferences: jest.fn(() => true)
        });
        mockSaveSignupPreferences.mockResolvedValue({ success: false, error: "Save failed" });

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toEqual({ success: true, usedDefaults: true });
        });

        expect(mockSaveSignupPreferences).toHaveBeenCalled();
        expect(mockSetUserClaim).toHaveBeenCalledWith(
            {},
            "test-user-id",
            UserClaimKey.ACCEPTED_TERMS,
            true
        );
    });

    it("should handle errors gracefully", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });
        mockGetUserClaim.mockRejectedValue(new Error("Database error"));

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            // When getUserClaim fails, it falls back to defaults
            expect(result.current.saveResult).toEqual({
                success: true,
                usedDefaults: true
            });
        });
    });

    it("should only save once per user", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });

        const { result, rerender } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toBeTruthy();
        });

        const firstCallCount = mockSetUserClaim.mock.calls.length;

        // Re-render should not trigger another save
        rerender();

        expect(mockSetUserClaim.mock.calls.length).toBe(firstCallCount);
    });

    it("should allow manual retry via performSave", async () => {
        mockUseUser.mockReturnValue({ user: mockUser });

        const { result } = renderHook(() => useSignupPreferencesSave());

        await waitFor(() => {
            expect(result.current.saveResult).toBeTruthy();
        });

        const firstCallCount = mockSetUserClaim.mock.calls.length;

        // Manual retry should work
        result.current.performSave();

        await waitFor(() => {
            expect(mockSetUserClaim.mock.calls.length).toBeGreaterThan(firstCallCount);
        });
    });
});
