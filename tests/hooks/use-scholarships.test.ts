import { renderHook, act, waitFor } from "@testing-library/react";
import { useScholarships } from "@/hooks/use-scholarships";
import { getScholarshipsPage } from "@/app/actions/scholarship-actions";
import { type Tables } from "@/types/database.types";

jest.mock("@/app/actions/scholarship-actions", () => ({
    getScholarshipsPage: jest.fn()
}));

const mockedGetScholarshipsPage = getScholarshipsPage as jest.Mock;

const createMockScholarship = (id: number): Tables<"scholarships"> => ({
    id: `s${id}`,
    title: `Scholarship ${id}`,
    description: `Description for scholarship ${id}`,
    short_description: `Short description for scholarship ${id}`,
    slug: `scholarship-${id}`,
    is_public: true,
    is_active: true,
    scholarship_type: "submission",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    start_date: new Date().toISOString(),
    end_date: new Date(new Date().setMonth(new Date().getMonth() + 6)).toISOString(),
    min_amount: 1000,
    max_amount: 5000,
    benefits: {},
    requirements: {},
    contact_email: null,
    contact_person: null,
    contact_phone: null,
    image_url: null,
    internal_notes: null,
    response_date: null,
    target_audience: "everyone",
    url: null,
    volunteer_hours: 0
});

const page1: Tables<"scholarships">[] = Array.from({ length: 5 }, (_, i) => createMockScholarship(i + 1));
const page2: Tables<"scholarships">[] = Array.from({ length: 2 }, (_, i) => createMockScholarship(i + 6));

describe("useScholarships", () => {
    beforeEach(() => {
        mockedGetScholarshipsPage.mockClear();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it("loads first page successfully and handles loading state", async () => {
        mockedGetScholarshipsPage.mockResolvedValue({ data: page1, count: 10 });

        const { result } = renderHook(() => useScholarships());

        expect(result.current.loading).toBe(true);

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.scholarships).toHaveLength(5);
        expect(result.current.totalCount).toBe(10);
        expect(result.current.hasMore).toBe(true);
        expect(mockedGetScholarshipsPage).toHaveBeenCalledWith(1, undefined);
    });

    it("handles empty result", async () => {
        mockedGetScholarshipsPage.mockResolvedValue({ data: [], count: 0 });
        const { result } = renderHook(() => useScholarships());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.scholarships).toHaveLength(0);
        expect(result.current.hasMore).toBe(false);
    });

    it("applies groupSlug filter", async () => {
        mockedGetScholarshipsPage.mockResolvedValue({ data: page1, count: 5 });
        const { result } = renderHook(() => useScholarships("tech"));
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(mockedGetScholarshipsPage).toHaveBeenCalledWith(1, "tech");
    });

    it("paginates with loadMore", async () => {
        mockedGetScholarshipsPage.mockResolvedValueOnce({ data: page1, count: 7 });
        const { result } = renderHook(() => useScholarships());
        await waitFor(() => expect(result.current.hasMore).toBe(true));

        mockedGetScholarshipsPage.mockResolvedValueOnce({ data: page2, count: 7 });

        await act(async () => {
            result.current.loadMore();
        });

        await waitFor(() => {
            expect(result.current.scholarships).toHaveLength(7);
        });

        expect(result.current.hasMore).toBe(false);
        expect(mockedGetScholarshipsPage).toHaveBeenCalledTimes(2);
        expect(mockedGetScholarshipsPage).toHaveBeenCalledWith(2, undefined);
    });

    it("clears isNew flag after timeout", async () => {
        mockedGetScholarshipsPage.mockResolvedValue({ data: page1, count: 5 });
        const { result } = renderHook(() => useScholarships());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.displayedScholarships.every((s) => s.isNew)).toBe(true);

        act(() => {
            jest.advanceTimersByTime(1000);
        });

        expect(result.current.displayedScholarships.every((s) => !s.isNew)).toBe(true);
    });

    it("handles fetch error", async () => {
        const errorMessage = "Failed to fetch";
        mockedGetScholarshipsPage.mockResolvedValue({ error: errorMessage });
        const { result } = renderHook(() => useScholarships());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.error).toBe(errorMessage);
    });
});
