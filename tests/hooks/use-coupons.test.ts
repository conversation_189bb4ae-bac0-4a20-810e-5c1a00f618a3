import { renderHook, waitFor, act } from "@testing-library/react";
import { useCoupons } from "@/hooks/use-coupons";
import { getCoupons } from "@/app/actions/coupon-actions";

jest.mock("@/app/actions/coupon-actions", () => ({
    getCoupons: jest.fn()
}));

const mockedGetCoupons = getCoupons as jest.Mock;

describe("useCoupons hook", () => {
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();
        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    it("should fetch coupons on mount and handle success", async () => {
        const mockData = { success: true, data: [{ id: "1", coupon_code: "ABC" }] };
        mockedGetCoupons.mockResolvedValue(mockData);

        const { result } = renderHook(() => useCoupons());
        expect(result.current.loading).toBe(true);
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.error).toBeNull();
        expect(mockedGetCoupons).toHaveBeenCalled();
    });

    it("should handle fetch error state", async () => {
        const mockError = { success: false, error: "Fetch failed" };
        mockedGetCoupons.mockResolvedValue(mockError);

        const { result } = renderHook(() => useCoupons());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
    });

    it("refetch function should recall the server action", async () => {
        mockedGetCoupons.mockResolvedValueOnce({ success: true, data: [] });
        const { result } = renderHook(() => useCoupons());
        await waitFor(() => expect(result.current.loading).toBe(false));

        const refetchData = { success: true, data: [{ id: "2", coupon_code: "XYZ" }] };
        mockedGetCoupons.mockResolvedValueOnce(refetchData);

        await act(async () => {
            result.current.refetch();
        });

        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.items).toEqual(refetchData.data);
        expect(mockedGetCoupons).toHaveBeenCalledTimes(2);
    });
});
