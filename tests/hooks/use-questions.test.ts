import { renderHook, waitFor, act } from "@testing-library/react";
import { useQuestions } from "@/hooks/use-questions";
import { getSupabaseClient } from "@/utils/supabase/client";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn()
}));

const mockedGetSupabaseClient = getSupabaseClient as jest.Mock;

const mockQuestionsData = [
    {
        id: "1",
        type: "single_select",
        metadata: {
            label: "What is your age?",
            required: true
        },
        group_id: "group1",
        section: "personal_details" as const,
        groups_question: {
            id: "group1",
            name: "Personal Information"
        }
    },
    {
        id: "2",
        type: "multi_select",
        metadata: {
            label: "What are your interests?",
            required: false
        },
        group_id: "group2",
        section: "data_entry" as const,
        groups_question: [
            {
                id: "group2",
                name: "Interests"
            }
        ]
    },
    {
        id: "3",
        type: "short_text",
        metadata: {
            label: "Describe your goals",
            required: true
        },
        group_id: "group3",
        section: "specific_scholarship" as const,
        groups_question: null
    }
];

describe("useQuestions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    it("should start in a loading state and fetch questions successfully", async () => {
        const mockBuilder = createThenableBuilder([{ data: mockQuestionsData, error: null }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toHaveLength(3);
        expect(result.current.error).toBeNull();
    });

    it("should transform groups_question data correctly", async () => {
        const mockBuilder = createThenableBuilder([{ data: mockQuestionsData, error: null }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        const items = result.current.items;

        // First item has groups_question as object
        expect(items[0].groups_question).toEqual({
            id: "group1",
            name: "Personal Information"
        });

        // Second item has groups_question as array - should be transformed to object
        expect(items[1].groups_question).toEqual({
            id: "group2",
            name: "Interests"
        });

        // Third item has null groups_question
        expect(items[2].groups_question).toBeNull();
    });

    it("should handle empty groups_question array", async () => {
        const dataWithEmptyArray = [
            {
                id: "1",
                type: "single_select",
                metadata: {
                    label: "Test question",
                    required: true
                },
                group_id: "group1",
                section: "personal_details" as const,
                groups_question: []
            }
        ];

        const mockBuilder = createThenableBuilder([{ data: dataWithEmptyArray, error: null }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items[0].groups_question).toBeNull();
    });

    it("should handle groups_question with missing fields", async () => {
        const dataWithMissingFields = [
            {
                id: "1",
                type: "single_select",
                metadata: {
                    label: "Test question",
                    required: true
                },
                group_id: "group1",
                section: "personal_details" as const,
                groups_question: {
                    id: null,
                    name: null
                }
            }
        ];

        const mockBuilder = createThenableBuilder([{ data: dataWithMissingFields, error: null }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items[0].groups_question).toEqual({
            id: "",
            name: ""
        });
    });

    it("should handle null or undefined data", async () => {
        const mockBuilder = createThenableBuilder([{ data: null, error: null }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch errors correctly", async () => {
        const mockError = new Error("Database connection failed");
        const mockBuilder = createThenableBuilder([{ data: null, error: mockError }]);

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
        expect(result.current.error?.message).toBe("Database connection failed");
        expect(console.error).toHaveBeenCalledWith("Error fetching questions:", mockError);
    });

    it("should handle non-Error exceptions", async () => {
        const mockBuilder = createThenableBuilder([{ data: null, error: new Error("String error") }]);

        // Make the builder throw a string instead of an Error
        mockBuilder.then = () => {
            throw "String error";
        };

        mockedGetSupabaseClient.mockReturnValue({
            from: jest.fn(() => mockBuilder)
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);
        expect(result.current.error).toBeInstanceOf(Error);
        expect(result.current.error?.message).toBe("Unknown error occurred");
    });

    it("should refetch questions when refetch is called", async () => {
        const mockBuilder1 = createThenableBuilder([{ data: [], error: null }]);
        const mockBuilder2 = createThenableBuilder([{ data: mockQuestionsData, error: null }]);

        mockedGetSupabaseClient
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder1)
            })
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder2)
            });

        const { result } = renderHook(() => useQuestions());

        // Wait for initial fetch
        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual([]);

        // Call refetch
        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.items).toHaveLength(3);
        expect(result.current.error).toBeNull();
    });

    it("should set loading state correctly during refetch", async () => {
        const mockBuilder1 = createThenableBuilder([{ data: [], error: null }]);
        const mockBuilder2 = createThenableBuilder([{ data: mockQuestionsData, error: null }]);

        mockedGetSupabaseClient
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder1)
            })
            .mockReturnValueOnce({
                from: jest.fn(() => mockBuilder2)
            });

        const { result } = renderHook(() => useQuestions());

        // Wait for initial fetch
        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        // Start refetch and check loading state
        act(() => {
            result.current.refetch();
        });

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });
    });

    it("should verify correct Supabase query structure", async () => {
        const mockBuilder = createThenableBuilder([{ data: mockQuestionsData, error: null }]);

        const mockFrom = jest.fn(() => mockBuilder);
        mockedGetSupabaseClient.mockReturnValue({
            from: mockFrom
        });

        const { result } = renderHook(() => useQuestions());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockFrom).toHaveBeenCalledWith("questions");
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("id,"));
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("type,"));
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("metadata,"));
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("group_id,"));
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("section,"));
        expect(mockBuilder.select).toHaveBeenCalledWith(expect.stringContaining("groups_question"));
        expect(mockBuilder.order).toHaveBeenCalledWith("created_at", { ascending: false });
    });
});
