import { renderHook, waitFor } from "@testing-library/react";
import { useAdminScholarships } from "@/hooks/use-admin-scholarships";
import { getAdminScholarships } from "@/app/actions/scholarship-actions";

jest.mock("@/app/actions/scholarship-actions", () => ({
    getAdminScholarships: jest.fn()
}));

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

const mockedGetAdminScholarships = getAdminScholarships as jest.Mock;

describe("useAdminScholarships hook", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return initial state and fetch scholarships", async () => {
        const mockData = {
            data: [{ id: "1", title: "Scholarship 1" }],
            count: 1,
            error: undefined
        };
        mockedGetAdminScholarships.mockResolvedValue(mockData);

        const { result } = renderHook(() => useAdminScholarships());

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.items).toEqual(mockData.data);
        expect(result.current.totalItems).toBe(mockData.count);
        expect(result.current.totalPages).toBe(1);
        expect(result.current.error).toBeNull();
    });

    it("should handle errors from the server action", async () => {
        const errorMessage = "Failed to fetch";
        mockedGetAdminScholarships.mockResolvedValue({
            data: [],
            count: 0,
            error: errorMessage
        });

        const { result } = renderHook(() => useAdminScholarships());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error).toEqual(new Error(errorMessage));
        expect(result.current.items).toEqual([]);
    });

    it("should handle thrown errors during fetch", async () => {
        const thrownError = new Error("Network Error");
        mockedGetAdminScholarships.mockRejectedValue(thrownError);

        const { result } = renderHook(() => useAdminScholarships());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error?.message).toBe(thrownError.message);
        expect(result.current.items).toEqual([]);
    });

    it("should handle non-Error thrown objects during fetch", async () => {
        const thrownObject = "Something bad happened";
        mockedGetAdminScholarships.mockRejectedValue(thrownObject);

        const { result } = renderHook(() => useAdminScholarships());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error).toEqual(new Error("An unknown error occurred"));
        expect(result.current.items).toEqual([]);
    });

    it("should call fetch function with correct pagination", async () => {
        mockedGetAdminScholarships.mockResolvedValue({ data: [], count: 0 });

        const { rerender } = renderHook(({ page, pageSize }) => useAdminScholarships({ page, pageSize }), {
            initialProps: { page: 1, pageSize: 10 }
        });

        await waitFor(() => {
            expect(getAdminScholarships).toHaveBeenCalledWith({ page: 1, pageSize: 10, filters: {} });
        });

        rerender({ page: 2, pageSize: 20 });

        await waitFor(() => {
            expect(getAdminScholarships).toHaveBeenCalledWith({ page: 2, pageSize: 20, filters: {} });
        });
    });

    it("refetch function should recall the server action", async () => {
        mockedGetAdminScholarships.mockResolvedValue({ data: [], count: 0 });

        const { result } = renderHook(() => useAdminScholarships());

        await waitFor(() => {
            // Initial call (or 2 in strict mode)
            expect(getAdminScholarships).toHaveBeenCalled();
        });

        const callCount = mockedGetAdminScholarships.mock.calls.length;

        result.current.refetch();

        await waitFor(() => {
            expect(getAdminScholarships).toHaveBeenCalledTimes(callCount + 1);
        });
    });
});
