import { renderHook, act, waitFor } from "@testing-library/react";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";
import { getScholarshipGroupsWithCounts } from "@/app/actions/scholarship-group-actions";
import { ScholarshipGroup } from "@/lib/scholarship-group-constants";

jest.mock("@/app/actions/scholarship-group-actions", () => ({
    getScholarshipGroupsWithCounts: jest.fn()
}));

const mockGetScholarshipGroupsWithCounts = getScholarshipGroupsWithCounts as jest.Mock;

// Mock data ------------------------------------------------------------------
const mockGroups: ScholarshipGroup[] = Array.from({ length: 10 }, (_, i) => ({
    id: `group-${i + 1}`,
    title: `Group ${i + 1}`,
    description: `Description ${i + 1}`,
    slug: `group-${i + 1}`,
    icon: `/icon-${i + 1}.svg`,
    image_url: `/image-${i + 1}.jpg`,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    scholarships_count: i % 2 === 0 ? 2 : 1 // Example counts
}));

describe("useScholarshipGroups", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it("should have the correct initial state before fetching", () => {
        mockGetScholarshipGroupsWithCounts.mockImplementation(() => new Promise(() => {}));
        const { result } = renderHook(() => useScholarshipGroups());
        expect(result.current.loading).toBe(true);
        expect(result.current.items).toEqual([]);
        expect(result.current.displayedGroups).toEqual([]);
        expect(result.current.error).toBeNull();
        expect(result.current.hasMore).toBe(false);
    });

    it("should fetch groups, map counts, and display the first page", async () => {
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: mockGroups });

        const { result } = renderHook(() => useScholarshipGroups());

        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toHaveLength(10);
        expect(result.current.displayedGroups).toHaveLength(8); // ITEMS_PER_PAGE
        expect(result.current.items[0].scholarships_count).toBe(2);
        expect(result.current.items[1].scholarships_count).toBe(1);
        expect(result.current.hasMore).toBe(true);
    });

    it("should handle cases where no scholarship groups are found", async () => {
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: [] });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.items).toHaveLength(0);
        expect(result.current.hasMore).toBe(false);
    });

    it("should load the next page when loadMore is called", async () => {
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: mockGroups });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.displayedGroups).toHaveLength(8);

        await act(async () => {
            result.current.loadMore();
        });

        expect(result.current.displayedGroups).toHaveLength(10);
        expect(result.current.hasMore).toBe(false);
    });

    it("should set the isNew flag and clear it after a timeout", async () => {
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: mockGroups });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.displayedGroups.every((g) => g.isNew)).toBe(true);

        act(() => {
            jest.advanceTimersByTime(1000);
        });

        expect(result.current.displayedGroups.every((g) => !g.isNew)).toBe(true);
    });

    it("should refetch all data when refetch is called", async () => {
        const initialGroups = mockGroups.slice(0, 5);
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: initialGroups });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.items).toHaveLength(5));

        const updatedGroups = mockGroups.slice(0, 3);
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: true, data: updatedGroups });

        await act(async () => {
            await result.current.refetch();
        });

        expect(result.current.items).toHaveLength(3);
        expect(mockGetScholarshipGroupsWithCounts).toHaveBeenCalledTimes(2);
    });

    it("should set an error state if fetching groups fails", async () => {
        const error = new Error("Failed to fetch groups");
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: false, error: error.message });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.error).toEqual(error);
        expect(result.current.items).toHaveLength(0);
    });

    it("should set an error state if fetching counts fails", async () => {
        const error = new Error("Failed to fetch counts");
        mockGetScholarshipGroupsWithCounts.mockResolvedValueOnce({ success: false, error: error.message });

        const { result } = renderHook(() => useScholarshipGroups());
        await waitFor(() => expect(result.current.loading).toBe(false));

        expect(result.current.error).toEqual(error);
        expect(result.current.items).toHaveLength(0);
    });
});
