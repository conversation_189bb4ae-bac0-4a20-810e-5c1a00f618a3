import { renderHook, waitFor } from "@testing-library/react";
import { useScholarshipEligibility, TEXTS } from "@/hooks/use-scholarship-eligibility";
import type { ScholarshipEligibility } from "@/app/services/scholarship-eligibility";

const mockEligibility: ScholarshipEligibility[] = [
    {
        scholarshipId: "s1",
        title: "מלגה 1",
        slug: "scholarship-1",
        min_amount: 1000,
        max_amount: 2000,
        isEligible: true,
        description: "תיאור 1",
        short_description: "קצר 1",
        start_date: "2024-01-01",
        end_date: "2024-12-31",
        url: "https://example.com",
        volunteer_hours: 10,
        scholarship_type: "submission",
        contact_person: "איש קשר",
        contact_email: "<EMAIL>",
        contact_phone: "050-0000000",
        conditionResults: [],
        missingAnswers: []
    },
    {
        scholarshipId: "s2",
        title: "מלגה 2",
        slug: "scholarship-2",
        min_amount: 500,
        max_amount: 1500,
        isEligible: false,
        description: "תיאור 2",
        short_description: "קצר 2",
        start_date: "2024-01-01",
        end_date: "2024-12-31",
        url: "https://example.com",
        volunteer_hours: 5,
        scholarship_type: "guidance",
        contact_person: null,
        contact_email: null,
        contact_phone: null,
        conditionResults: [],
        missingAnswers: []
    }
];

describe("useScholarshipEligibility", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
        global.fetch = jest.fn();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it("should initialize with loading state", () => {
        (global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {}));
        const { result } = renderHook(() => useScholarshipEligibility());
        expect(result.current.loading).toBe(true);
        expect(result.current.eligibility).toBeUndefined();
        expect(result.current.error).toBeNull();
    });

    it("should fetch and set eligibility data on success", async () => {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({ eligibility: mockEligibility })
        });
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toEqual(mockEligibility);
        expect(result.current.error).toBeNull();
        expect(result.current.eligibleScholarships).toEqual([expect.objectContaining({ scholarshipId: "s1" })]);
        expect(result.current.totalMinAmount).toBe(1000);
        expect(result.current.totalMaxAmount).toBe(2000);
    });

    it("should handle empty eligibility array", async () => {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({ eligibility: [] })
        });
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toEqual([]);
        expect(result.current.eligibleScholarships).toEqual([]);
        expect(result.current.totalMinAmount).toBe(0);
        expect(result.current.totalMaxAmount).toBe(0);
        expect(result.current.error).toBeNull();
    });

    it("should handle missing eligibility property in response", async () => {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({})
        });
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toEqual([]);
        expect(result.current.eligibleScholarships).toEqual([]);
        expect(result.current.totalMinAmount).toBe(0);
        expect(result.current.totalMaxAmount).toBe(0);
        expect(result.current.error).toBeNull();
    });

    it("should handle fetch error (network or thrown)", async () => {
        (global.fetch as jest.Mock).mockRejectedValueOnce(new Error("Network error"));
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toEqual([]);
        expect(result.current.error).toBe(TEXTS.errorLoadingScholarships);
    });

    it("should handle fetch error (response not ok)", async () => {
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: false
        });
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toEqual([]);
        expect(result.current.error).toBe(TEXTS.errorLoadingScholarships);
    });

    it("should not set error if fetch is aborted", async () => {
        const abortError = new DOMException("Aborted", "AbortError");
        (global.fetch as jest.Mock).mockImplementation(() => Promise.reject(abortError));
        const { result } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(result.current.loading).toBe(false));
        expect(result.current.eligibility).toBeUndefined();
        expect(result.current.error).toBeNull();
    });

    it("should abort fetch on unmount", async () => {
        const abortSpy = jest.fn();
        const mockAbortController = {
            signal: {},
            abort: abortSpy
        };
        const origAbortController = global.AbortController;
        // @ts-ignore
        global.AbortController = jest.fn(() => mockAbortController);
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({ eligibility: mockEligibility })
        });
        const { unmount } = renderHook(() => useScholarshipEligibility());
        await waitFor(() => expect(abortSpy).not.toHaveBeenCalled());
        unmount();
        expect(abortSpy).toHaveBeenCalled();
        // @ts-ignore
        global.AbortController = origAbortController;
    });
});
