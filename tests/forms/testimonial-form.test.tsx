import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { createTestimonial, getTestimonial, updateTestimonial } from "@/app/actions/testimonial-actions";
import { TestimonialForm } from "@/components/forms/testimonial-form";
import { TESTIMONIAL_TEXTS, type TestimonialFormValues } from "@/lib/testimonial-constants";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("@/app/actions/testimonial-actions", () => ({
    getTestimonial: jest.fn(),
    createTestimonial: jest.fn(),
    updateTestimonial: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

describe("TestimonialForm", () => {
    const mockRouter = {
        push: jest.fn()
    };

    const mockTestimonial = {
        id: "test-id",
        name: "Test Testimonial",
        text: "This is a test testimonial",
        type: "personal",
        created_at: "2023-01-01T00:00:00.000Z",
        updated_at: "2023-01-01T00:00:00.000Z"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);
    });

    it("renders the form in create mode", () => {
        render(<TestimonialForm />);

        expect(screen.getByLabelText(TESTIMONIAL_TEXTS.nameLabel)).toBeInTheDocument();
        expect(screen.getByLabelText(TESTIMONIAL_TEXTS.textLabel)).toBeInTheDocument();
        expect(screen.getByText(TESTIMONIAL_TEXTS.createButtonText)).toBeInTheDocument();
    });

    it("loads testimonial data in edit mode", async () => {
        (getTestimonial as jest.Mock).mockResolvedValue({
            success: true,
            data: mockTestimonial
        });

        render(<TestimonialForm testimonialId="test-id" />);

        await waitFor(() => {
            expect(getTestimonial).toHaveBeenCalledWith("test-id");
        });

        await waitFor(() => {
            expect(screen.getByDisplayValue(mockTestimonial.name)).toBeInTheDocument();
            expect(screen.getByDisplayValue(mockTestimonial.text)).toBeInTheDocument();
            expect(screen.getByText(TESTIMONIAL_TEXTS.updateButtonText)).toBeInTheDocument();
        });
    });

    it("handles create testimonial submission", async () => {
        const user = userEvent.setup();
        (createTestimonial as jest.Mock).mockResolvedValue({ success: true });

        render(<TestimonialForm />);

        await user.type(screen.getByLabelText(TESTIMONIAL_TEXTS.nameLabel), "New Testimonial");
        await user.type(screen.getByLabelText(TESTIMONIAL_TEXTS.textLabel), "This is a new testimonial");

        await user.click(screen.getByText(TESTIMONIAL_TEXTS.createButtonText));

        await waitFor(() => {
            expect(createTestimonial).toHaveBeenCalledWith({
                name: "New Testimonial",
                text: "This is a new testimonial",
                type: { id: "personal", label: "אישי" }
            });
            expect(toast.success).toHaveBeenCalledWith(
                TESTIMONIAL_TEXTS.createSuccessMessage,
                expect.objectContaining({ id: "testimonial-created" })
            );
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/testimonials");
        });
    });

    it("handles update testimonial submission", async () => {
        const user = userEvent.setup();
        (getTestimonial as jest.Mock).mockResolvedValue({
            success: true,
            data: mockTestimonial
        });
        (updateTestimonial as jest.Mock).mockResolvedValue({ success: true });

        render(<TestimonialForm testimonialId="test-id" />);

        await waitFor(() => {
            expect(screen.getByDisplayValue(mockTestimonial.name)).toBeInTheDocument();
        });

        const textInput = screen.getByLabelText(TESTIMONIAL_TEXTS.textLabel);
        await user.clear(textInput);
        await user.type(textInput, "Updated testimonial text");

        await user.click(screen.getByText(TESTIMONIAL_TEXTS.updateButtonText));

        await waitFor(() => {
            expect(updateTestimonial).toHaveBeenCalledWith("test-id", {
                name: mockTestimonial.name,
                text: "Updated testimonial text",
                type: { id: mockTestimonial.type, label: "אישי" }
            });
            expect(toast.success).toHaveBeenCalledWith(
                TESTIMONIAL_TEXTS.editSuccessMessage,
                expect.objectContaining({ id: "testimonial-updated" })
            );
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/testimonials");
        });
    });

    it("handles error when fetching testimonial", async () => {
        (getTestimonial as jest.Mock).mockResolvedValue({
            success: false,
            error: TESTIMONIAL_TEXTS.TESTIMONIAL_NOT_FOUND
        });

        render(<TestimonialForm testimonialId="invalid-id" />);

        await waitFor(() => {
            expect(toast.error).toHaveBeenCalledWith(TESTIMONIAL_TEXTS.editNotFoundMessage);
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/testimonials");
        });
    });

    it("handles error when creating testimonial", async () => {
        const user = userEvent.setup();
        (createTestimonial as jest.Mock).mockResolvedValue({
            success: false,
            error: TESTIMONIAL_TEXTS.TESTIMONIAL_CREATE_ERROR
        });

        render(<TestimonialForm />);

        await user.type(screen.getByLabelText(TESTIMONIAL_TEXTS.nameLabel), "New Testimonial");
        await user.type(screen.getByLabelText(TESTIMONIAL_TEXTS.textLabel), "This is a new testimonial");

        await user.click(screen.getByText(TESTIMONIAL_TEXTS.createButtonText));

        await waitFor(() => {
            expect(toast.error).toHaveBeenCalledWith(
                TESTIMONIAL_TEXTS.TESTIMONIAL_CREATE_ERROR,
                expect.objectContaining({ id: "testimonial-create-error" })
            );
            expect(mockRouter.push).not.toHaveBeenCalled();
        });
    });

    it("handles cancel button click", async () => {
        const user = userEvent.setup();
        render(<TestimonialForm />);

        await user.click(screen.getByText(TESTIMONIAL_TEXTS.cancelButtonText));

        expect(mockRouter.push).toHaveBeenCalledWith("/admin/testimonials");
    });
});
