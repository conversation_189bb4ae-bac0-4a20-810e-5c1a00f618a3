import "@testing-library/jest-dom";

import { cleanup, render, screen } from "@testing-library/react";
import React from "react";

import type { ShapedQuestion } from "@/app/actions/dynamic-questions-form-actions";
import type { Database } from "@/types/database.types";

import { DynamicQuestionsForm } from "../../components/forms/dynamic-questions-form/dynamic-questions-form";
import type {
    Condition,
    QuestionConditionLink,
    QuestionGroup,
    QuestionSection
} from "../../components/forms/dynamic-questions-form/types";

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({
        from: jest.fn(() => ({
            select: jest.fn(() => ({
                eq: jest.fn(() => ({
                    data: [],
                    error: null
                })),
                in: jest.fn(() => ({
                    data: [],
                    error: null
                })),
                order: jest.fn(() => ({
                    data: [],
                    error: null
                }))
            })),
            upsert: jest.fn(() => ({
                data: [],
                error: null
            })),
            delete: jest.fn(() => ({
                eq: jest.fn(() => ({
                    in: jest.fn(() => ({
                        data: [],
                        error: null
                    }))
                }))
            }))
        }))
    }))
}));

jest.mock("@/app/actions/dynamic-questions-form-actions", () => ({
    fetchQuestionsData: jest.fn(() =>
        Promise.resolve({
            success: true,
            data: {
                questions: [],
                questionGroups: {},
                conditions: [],
                questionConditionLinks: [],
                answers: []
            }
        })
    ),
    upsertAnswers: jest.fn(() => Promise.resolve({ success: true }))
}));

const mockUseQuestionsData = jest.fn();
jest.mock("@/hooks/dynamic-questions-form/use-questions-data", () => ({
    useQuestionsData: (args: unknown) => mockUseQuestionsData(args)
}));

const mockUseDynamicQuestionsState = jest.fn();
jest.mock("@/hooks/dynamic-questions-form/use-dynamic-questions-state", () => ({
    useDynamicQuestionsState: () => mockUseDynamicQuestionsState()
}));

jest.mock("@clerk/nextjs", () => ({
    useUser: () => ({ user: { id: "user_123" }, isLoaded: true })
}));

jest.mock("@/hooks/dynamic-questions-form/use-bank-data", () => ({
    useBankData: () => ({
        banks: [
            {
                bankCode: 10,
                bankName: "Bank Hapoalim",
                branches: [
                    { branchCode: 101, branchName: "Tel Aviv Branch" },
                    { branchCode: 102, branchName: "Jerusalem Branch" }
                ]
            }
        ]
    })
}));

jest.mock("@/components/forms/fields/render-field", () => ({
    renderField: jest.fn((question) => (
        <div data-testid={`question-${question.id}`} data-question-type={question.type}>
            <label htmlFor={question.id}>{question.metadata.label}</label>
            <input
                id={question.id}
                name={question.id}
                data-required={question.metadata.required}
                placeholder={question.metadata.placeholder}
            />
        </div>
    ))
}));

type TestQuestion = ShapedQuestion & {
    name: string;
};

describe("DynamicQuestionsForm", () => {
    afterEach(cleanup);

    const TEXTS = {
        loading: "טוען שאלות...",
        submit: "שלח"
    };

    const createMockQuestion = (
        id: string,
        type: Database["public"]["Enums"]["question_type"],
        label: string,
        groupId: string = "g1",
        required: boolean = false,
        options?: { value: string; label: string }[]
    ): TestQuestion => ({
        id,
        name: label,
        type,
        group_id: groupId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        section: "personal_details",
        metadata: {
            label,
            required,
            ...(options && { options }),
            ...(type === "number_input" && { min: 0, max: 100 }),
            ...(type === "date_picker" && { min_date: "2020-01-01", max_date: "2030-12-31" })
        }
    });

    const mockQuestionGroup: QuestionGroup = {
        id: "g1",
        name: "Test Group",
        created_at: null,
        updated_at: null
    };

    const setupMockState = (
        questions: TestQuestion[],
        questionGroups: Record<string, QuestionGroup> = { g1: mockQuestionGroup },
        conditions: Condition[] = [],
        questionConditionLinks: QuestionConditionLink[] = [],
        formValues: Record<string, unknown> = {}
    ) => {
        mockUseDynamicQuestionsState.mockReturnValue({
            questions,
            questionGroups,
            conditions,
            questionConditionLinks,
            loading: false,
            error: null,
            lastFetchedSections: ["personal_details"],
            lastFetchedScholarshipId: undefined,
            dispatch: jest.fn()
        });
        mockUseQuestionsData.mockReturnValue({
            watch: () => formValues,
            handleSubmit: (fn: () => void) => fn,
            formState: { isSubmitting: false },
            control: {}
        });
    };

    describe("Basic Rendering", () => {
        it("renders the form with initial questions", () => {
            const mockSections: QuestionSection[] = ["personal_details"];
            const mockPrefetchedData = {
                questions: [createMockQuestion("q1", "short_text", "Question 1")],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={mockSections} prefetchedData={mockPrefetchedData} />);

            expect(screen.getByText("Test Group")).toBeInTheDocument();
            expect(screen.getByText(/Question 1/i)).toBeInTheDocument();
        });

        it("renders loading state when no prefetched data", () => {
            const mockSections: QuestionSection[] = ["personal_details"];

            mockUseDynamicQuestionsState.mockReturnValue({
                questions: [],
                questionGroups: {},
                conditions: [],
                questionConditionLinks: [],
                loading: true,
                error: null,
                lastFetchedSections: [],
                lastFetchedScholarshipId: undefined,
                dispatch: jest.fn()
            });

            render(<DynamicQuestionsForm sections={mockSections} />);

            expect(screen.getByText(TEXTS.loading)).toBeInTheDocument();
        });
    });

    describe("Question Types", () => {
        it("renders short_text question", () => {
            const question = createMockQuestion("q1", "short_text", "Short Text Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q1");
            expect(questionElement).toHaveAttribute("data-question-type", "short_text");
            expect(screen.getByLabelText("Short Text Question")).toBeInTheDocument();
        });

        it("renders number_input question", () => {
            const question = createMockQuestion("q3", "number_input", "Number Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q3");
            expect(questionElement).toHaveAttribute("data-question-type", "number_input");
        });

        it("renders single_select question", () => {
            const options = [
                { value: "opt1", label: "Option 1" },
                { value: "opt2", label: "Option 2" }
            ];
            const question = createMockQuestion("q4", "single_select", "Single Select Question", "g1", false, options);
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q4");
            expect(questionElement).toHaveAttribute("data-question-type", "single_select");
        });

        it("renders multi_select question", () => {
            const options = [
                { value: "opt1", label: "Option 1" },
                { value: "opt2", label: "Option 2" },
                { value: "opt3", label: "Option 3" }
            ];
            const question = createMockQuestion("q5", "multi_select", "Multi Select Question", "g1", false, options);
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q5");
            expect(questionElement).toHaveAttribute("data-question-type", "multi_select");
        });

        it("renders date_picker question", () => {
            const question = createMockQuestion("q6", "date_picker", "Date Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q6");
            expect(questionElement).toHaveAttribute("data-question-type", "date_picker");
        });

        it("renders address_select question", () => {
            const question = createMockQuestion("q7", "address_select", "Address Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q7");
            expect(questionElement).toHaveAttribute("data-question-type", "address_select");
        });

        it("renders bank_select question", () => {
            const question = createMockQuestion("q8", "bank_select", "Bank Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const questionElement = screen.getByTestId("question-q8");
            expect(questionElement).toHaveAttribute("data-question-type", "bank_select");
        });
    });

    describe("Form Features", () => {
        it("renders submit button when questions are present", () => {
            const question = createMockQuestion("q1", "short_text", "Test Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            expect(screen.getByRole("button", { name: new RegExp(TEXTS.submit, "i") })).toBeInTheDocument();
        });

        it("hides submit button when hideSubmit prop is true", () => {
            const question = createMockQuestion("q1", "short_text", "Test Question");
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            render(
                <DynamicQuestionsForm
                    sections={["personal_details"]}
                    prefetchedData={mockPrefetchedData}
                    hideSubmit={true}
                />
            );

            expect(screen.queryByRole("button", { name: new RegExp(TEXTS.submit, "i") })).not.toBeInTheDocument();
        });

        it("shows required indicator for required questions", () => {
            const question = createMockQuestion("q1", "short_text", "Required Question", "g1", true);
            const mockPrefetchedData = {
                questions: [question],
                questionGroups: { g1: mockQuestionGroup },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);

            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            const input = screen.getByRole("textbox");
            expect(input).toHaveAttribute("data-required", "true");
        });
    });

    describe("Multiple Question Groups", () => {
        it("renders questions grouped by their group_id", () => {
            const group1: QuestionGroup = { id: "g1", name: "Personal Info", created_at: null, updated_at: null };
            const group2: QuestionGroup = { id: "g2", name: "Contact Info", created_at: null, updated_at: null };

            const questions = [
                createMockQuestion("q1", "short_text", "First Name", "g1"),
                createMockQuestion("q2", "short_text", "Last Name", "g1"),
                createMockQuestion("q3", "short_text", "Email", "g2"),
                createMockQuestion("q4", "short_text", "Phone", "g2")
            ];

            const mockPrefetchedData = {
                questions,
                questionGroups: { g1: group1, g2: group2 },
                conditions: [],
                questionConditionLinks: [],
                answers: []
            };

            setupMockState(mockPrefetchedData.questions, mockPrefetchedData.questionGroups);
            render(<DynamicQuestionsForm sections={["personal_details"]} prefetchedData={mockPrefetchedData} />);

            expect(screen.getByText("Personal Info")).toBeInTheDocument();
            expect(screen.getByText("Contact Info")).toBeInTheDocument();
            expect(screen.getByText("First Name")).toBeInTheDocument();
            expect(screen.getByText("Last Name")).toBeInTheDocument();
            expect(screen.getByText("Email")).toBeInTheDocument();
            expect(screen.getByText("Phone")).toBeInTheDocument();
        });
    });

    describe("Conditional Question Rendering", () => {
        const mockSections: QuestionSection[] = ["personal_details"];

        describe("'in' condition", () => {
            const q1Options = [
                { value: "opt1", label: "Option 1" },
                { value: "opt2", label: "Option 2" }
            ];
            const q1 = createMockQuestion("q1", "single_select", "Select an option", "g1", false, q1Options);
            const q2 = createMockQuestion("q2", "short_text", "Conditional Question", "g1");

            const condition: Condition = {
                id: "cond1",
                question_id: "q1",
                type: "in",
                value: [{ id: "opt1", label: "Option 1" }],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                group_id: "g1"
            };

            const link: QuestionConditionLink = {
                question_id: "q2",
                condition_id: "cond1",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            it("shows question when condition is met", () => {
                setupMockState([q1, q2], { g1: mockQuestionGroup }, [condition], [link], {
                    q1: { id: "opt1", label: "Option 1" }
                });

                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.getByText("Conditional Question")).toBeInTheDocument();
            });

            it("hides question when condition is not met", () => {
                setupMockState([q1, q2], { g1: mockQuestionGroup }, [condition], [link], {
                    q1: { id: "opt2", label: "Option 2" }
                });
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.queryByText("Conditional Question")).not.toBeInTheDocument();
            });

            it("hides question when dependent value is not set", () => {
                setupMockState([q1, q2], { g1: mockQuestionGroup }, [condition], [link], {});
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.queryByText("Conditional Question")).not.toBeInTheDocument();
            });
        });

        describe("'range' condition", () => {
            const qAge = createMockQuestion("qAge", "number_input", "Your Age");
            const qChild = createMockQuestion("qChild", "short_text", "Child Question");
            const qAdult = createMockQuestion("qAdult", "short_text", "Adult Question");

            const childCondition: Condition = {
                id: "condChild",
                question_id: "qAge",
                type: "range",
                value: { max: 17 },
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                group_id: "g1"
            };
            const adultCondition: Condition = {
                id: "condAdult",
                question_id: "qAge",
                type: "range",
                value: { min: 18 },
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                group_id: "g1"
            };

            const childLink: QuestionConditionLink = {
                question_id: "qChild",
                condition_id: "condChild",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            const adultLink: QuestionConditionLink = {
                question_id: "qAdult",
                condition_id: "condAdult",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            it("shows child question and hides adult question for age 17", () => {
                setupMockState(
                    [qAge, qChild, qAdult],
                    { g1: mockQuestionGroup },
                    [childCondition, adultCondition],
                    [childLink, adultLink],
                    { qAge: 17 }
                );
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.getByText("Child Question")).toBeInTheDocument();
                expect(screen.queryByText("Adult Question")).not.toBeInTheDocument();
            });

            it("shows adult question and hides child question for age 18", () => {
                setupMockState(
                    [qAge, qChild, qAdult],
                    { g1: mockQuestionGroup },
                    [childCondition, adultCondition],
                    [childLink, adultLink],
                    { qAge: 18 }
                );
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.queryByText("Child Question")).not.toBeInTheDocument();
                expect(screen.getByText("Adult Question")).toBeInTheDocument();
            });
        });

        describe("'date_range' condition", () => {
            const qBirthdate = createMockQuestion("qBirthdate", "date_picker", "Your Birthdate");
            const qOver18 = createMockQuestion("qOver18", "short_text", "You are over 18");
            const qUnder18 = createMockQuestion("qUnder18", "short_text", "You are under 18");

            const daysIn18Years = 18 * 365.25;

            const over18Condition: Condition = {
                id: "condOver18",
                question_id: "qBirthdate",
                type: "date_range",
                value: { operator: "greater_than", days_from_today: daysIn18Years },
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                group_id: "g1"
            };
            const under18Condition: Condition = {
                id: "condUnder18",
                question_id: "qBirthdate",
                type: "date_range",
                value: { operator: "less_than", days_from_today: daysIn18Years },
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                group_id: "g1"
            };

            const over18Link: QuestionConditionLink = {
                question_id: "qOver18",
                condition_id: "condOver18",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            const under18Link: QuestionConditionLink = {
                question_id: "qUnder18",
                condition_id: "condUnder18",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            it("shows 'under 18' for a 10-year-old's birthdate", () => {
                const birthdate = new Date();
                birthdate.setFullYear(birthdate.getFullYear() - 10);
                setupMockState(
                    [qBirthdate, qOver18, qUnder18],
                    { g1: mockQuestionGroup },
                    [over18Condition, under18Condition],
                    [over18Link, under18Link],
                    { qBirthdate: birthdate }
                );
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.getByText("You are under 18")).toBeInTheDocument();
                expect(screen.queryByText("You are over 18")).not.toBeInTheDocument();
            });

            it("shows 'over 18' for a 20-year-old's birthdate", () => {
                const birthdate = new Date();
                birthdate.setFullYear(birthdate.getFullYear() - 20);
                setupMockState(
                    [qBirthdate, qOver18, qUnder18],
                    { g1: mockQuestionGroup },
                    [over18Condition, under18Condition],
                    [over18Link, under18Link],
                    { qBirthdate: birthdate }
                );
                render(<DynamicQuestionsForm sections={mockSections} />);
                expect(screen.queryByText("You are under 18")).not.toBeInTheDocument();
                expect(screen.getByText("You are over 18")).toBeInTheDocument();
            });
        });
    });
});
