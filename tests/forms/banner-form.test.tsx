import "@testing-library/jest-dom";

import { act, cleanup, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";

import { createBanner, getBanner, updateBanner } from "@/app/actions/banner-actions";
import { BannerForm } from "@/components/forms/banner-form";
import { audienceOptions, bannerColors, iconOptions, TEXTS, type BannerFormValues } from "@/lib/banner-constants";
import { type Tables } from "@/types/database.types";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        error: jest.fn(),
        success: jest.fn()
    }
}));

jest.mock("@/app/actions/banner-actions", () => ({
    createBanner: jest.fn(),
    getBanner: jest.fn(),
    updateBanner: jest.fn()
}));

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <input id={name} name={name} data-testid={`input-${name}`} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/long-text", () => ({
    LongText: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <textarea id={name} name={name} data-testid={`textarea-${name}`} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <select id={name} name={name} data-testid={`select-${name}`}>
                <option value="">Select...</option>
                {name === "audience" &&
                    audienceOptions.map((option) => (
                        <option key={option.id} value={option.id}>
                            {option.label}
                        </option>
                    ))}
                {name === "color_scheme" &&
                    bannerColors.map((option) => (
                        <option key={option.id} value={option.id}>
                            {option.label}
                        </option>
                    ))}
                {name === "icon" &&
                    iconOptions.map((option) => (
                        <option key={option.id} value={option.id}>
                            {option.label}
                        </option>
                    ))}
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <input type="number" id={name} name={name} data-testid={`number-${name}`} />
        </div>
    )
}));

jest.mock("@/components/ui/custom-switch", () => ({
    CustomSwitch: ({ name, label }: { name: string; label: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <input type="checkbox" id={name} name={name} data-testid={`switch-${name}`} />
        </div>
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({
        children,
        disabled,
        ...props
    }: {
        children: React.ReactNode;
        disabled?: boolean;
        [key: string]: any;
    }) => {
        const shouldBeDisabled = props.type === "submit" ? mockFormState.isSubmitting : disabled;

        return (
            <button {...props} disabled={shouldBeDisabled} data-testid="button">
                {children}
            </button>
        );
    }
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text: string }) => <div data-testid="loading-icon">{text}</div>
}));

let mockFormData: BannerFormValues = {
    title: null,
    text: null,
    audience: null,
    color_scheme: null,
    icon: null,
    cta_text: null,
    cta_link: null,
    days_to_live: 0,
    seconds_before_show: 0,
    enable_dismiss: true,
    enabled: true
};

let mockFormState = { isSubmitting: false };

const mockFormMethods = {
    handleSubmit: jest.fn((fn) => (e?: React.FormEvent) => {
        e?.preventDefault();
        fn(mockFormData);
    }),
    formState: mockFormState,
    reset: jest.fn((data: Partial<BannerFormValues>) => {
        mockFormData = { ...mockFormData, ...data };
    })
};

jest.mock("react-hook-form", () => ({
    useForm: () => mockFormMethods,
    FormProvider: ({ children }: { children: React.ReactNode }) => children
}));

const mockCreateBanner = createBanner as jest.MockedFunction<typeof createBanner>;
const mockGetBanner = getBanner as jest.MockedFunction<typeof getBanner>;
const mockUpdateBanner = updateBanner as jest.MockedFunction<typeof updateBanner>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockToast = toast as jest.Mocked<typeof toast>;

const mockBannerData: Tables<"banners"> = {
    id: "test-banner-id",
    title: "Test Banner Title",
    text: "Test Banner Text",
    audience: "User",
    background_color: "#BAE1FF",
    text_color: "#000000",
    icon: "Info",
    cta_text: "Click Here",
    cta_link: "https://milgapo.vercel.app",
    days_to_live: 7,
    seconds_before_show: 5,
    enable_dismiss: true,
    enabled: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
};

const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn()
};

const setMockFormData = (data: Partial<BannerFormValues>) => {
    mockFormData = { ...mockFormData, ...data };
};

const resetMockFormData = () => {
    mockFormData = {
        title: null,
        text: null,
        audience: null,
        color_scheme: null,
        icon: null,
        cta_text: null,
        cta_link: null,
        days_to_live: 0,
        seconds_before_show: 0,
        enable_dismiss: true,
        enabled: true
    };
    mockFormState = { isSubmitting: false };
};

describe("BannerForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        resetMockFormData();
        mockUseRouter.mockReturnValue(mockRouter);

        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Create Mode", () => {
        it("renders form fields with initial values", async () => {
            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("input-title")).toBeInTheDocument();
            });

            expect(screen.getByTestId("textarea-text")).toBeInTheDocument();
            expect(screen.getByTestId("select-audience")).toBeInTheDocument();
            expect(screen.getByTestId("select-color_scheme")).toBeInTheDocument();
            expect(screen.getByTestId("select-icon")).toBeInTheDocument();
            expect(screen.getByTestId("input-cta_text")).toBeInTheDocument();
            expect(screen.getByTestId("input-cta_link")).toBeInTheDocument();
            expect(screen.getByTestId("number-days_to_live")).toBeInTheDocument();
            expect(screen.getByTestId("number-seconds_before_show")).toBeInTheDocument();
            expect(screen.getByTestId("switch-enable_dismiss")).toBeInTheDocument();
            expect(screen.getByTestId("switch-enabled")).toBeInTheDocument();

            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
        });

        it("displays form field descriptions", async () => {
            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.daysToLiveDescription)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.secondsBeforeShowDescription)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.enableDismissDescription)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.enabledDescription)).toBeInTheDocument();
        });

        it("successfully creates a banner with valid data", async () => {
            const user = userEvent.setup();
            mockCreateBanner.mockResolvedValue({ success: true });

            const formData: BannerFormValues = {
                title: "Test Title",
                text: "Test Text",
                audience: audienceOptions[0],
                color_scheme: bannerColors[0],
                icon: iconOptions[0],
                cta_text: "Click Here",
                cta_link: "https://milgapo.vercel.app",
                days_to_live: 7,
                seconds_before_show: 5,
                enable_dismiss: true,
                enabled: true
            };

            setMockFormData(formData);

            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(mockCreateBanner).toHaveBeenCalledWith(formData);
                expect(mockToast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessage, { id: "banner-created" });
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/banners");
            });
        });

        it("handles create banner error", async () => {
            const user = userEvent.setup();
            const errorMessage = "Database error";
            mockCreateBanner.mockResolvedValue({ success: false, error: errorMessage });

            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(errorMessage);
                expect(mockRouter.push).not.toHaveBeenCalled();
            });
        });

        it("navigates back when cancel button is clicked", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.cancelButtonText }));

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/banners");
        });

        it("handles network errors gracefully", async () => {
            const user = userEvent.setup();
            mockCreateBanner.mockRejectedValue(new Error("Network error"));

            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.createErrorMessage, {
                    id: "banner-create-error",
                    description: "Network error"
                });
            });
        });
    });

    describe("Edit Mode", () => {
        it("shows loading state while fetching banner data", () => {
            mockGetBanner.mockImplementation(() => new Promise(() => {}));

            render(<BannerForm bannerId="test-banner-id" />);

            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();
        });

        it("loads and displays existing banner data", async () => {
            mockGetBanner.mockResolvedValue({ success: true, data: mockBannerData });

            render(<BannerForm bannerId="test-banner-id" />);

            await waitFor(() => {
                expect(mockGetBanner).toHaveBeenCalledWith("test-banner-id");
            });

            await waitFor(() => {
                expect(mockFormMethods.reset).toHaveBeenCalledWith({
                    title: mockBannerData.title,
                    text: mockBannerData.text,
                    audience: audienceOptions.find((a) => a.id === mockBannerData.audience),
                    color_scheme: bannerColors[0],
                    icon: iconOptions.find((i) => i.id === mockBannerData.icon),
                    cta_text: mockBannerData.cta_text,
                    cta_link: mockBannerData.cta_link,
                    days_to_live: mockBannerData.days_to_live,
                    seconds_before_show: mockBannerData.seconds_before_show,
                    enable_dismiss: mockBannerData.enable_dismiss,
                    enabled: mockBannerData.enabled
                });
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles banner not found error", async () => {
            const errorMessage = "Banner not found";
            mockGetBanner.mockResolvedValue({ success: false, error: errorMessage });

            render(<BannerForm bannerId="nonexistent-id" />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(errorMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/banners");
            });
        });

        it("successfully updates banner with modified data", async () => {
            const user = userEvent.setup();
            mockGetBanner.mockResolvedValue({ success: true, data: mockBannerData });
            mockUpdateBanner.mockResolvedValue({ success: true });

            render(<BannerForm bannerId="test-banner-id" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });

            setMockFormData({
                ...mockFormData,
                title: "Updated Title"
            });

            await user.click(screen.getByRole("button", { name: TEXTS.updateButtonText }));

            await waitFor(() => {
                expect(mockUpdateBanner).toHaveBeenCalledWith(
                    "test-banner-id",
                    expect.objectContaining({
                        title: "Updated Title"
                    })
                );
                expect(mockToast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage, { id: "banner-updated" });
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/banners");
            });
        });

        it("handles update banner error", async () => {
            const user = userEvent.setup();
            const errorMessage = "Update failed";
            mockGetBanner.mockResolvedValue({ success: true, data: mockBannerData });
            mockUpdateBanner.mockResolvedValue({ success: false, error: errorMessage });

            render(<BannerForm bannerId="test-banner-id" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.updateButtonText }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(errorMessage);
                expect(mockRouter.push).not.toHaveBeenCalled();
            });
        });

        it("handles unexpected errors during data fetching", async () => {
            mockGetBanner.mockRejectedValue(new Error("Database connection error"));

            render(<BannerForm bannerId="test-banner-id" />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.editErrorMessage, {
                    description: "Database connection error"
                });
            });
        });
    });

    describe("Form Interactions", () => {
        it("disables submit button when form is submitting", async () => {
            mockFormState.isSubmitting = true;

            await act(async () => {
                render(<BannerForm />);
            });

            await waitFor(() => {
                const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
                expect(submitButton).toBeDisabled();
            });
        });
    });
});
