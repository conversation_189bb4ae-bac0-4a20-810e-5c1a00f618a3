import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { DynamicDocumentUploadForm } from "@/components/forms/dynamic-document-upload-form";
import * as useUserDocumentsModule from "@/hooks/use-user-documents";
import * as uploadActions from "@/app/actions/dynamic-document-upload-actions";
import { TEXTS } from "@/lib/dynamic-document-upload-constants";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({}))
}));

jest.mock("@/components/common/pdf-example-modal", () => ({
    PdfExampleModal: ({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) =>
        open ? (
            <div role="dialog" onClick={() => onOpenChange(false)}>
                PDF Modal
            </div>
        ) : null
}));

jest.mock("@/hooks/use-user-documents");
jest.mock("@/app/actions/dynamic-document-upload-actions");

const mockDocuments = [
    {
        documentTypeId: "doc1",
        documentTypeName: "תעודת זהות",
        documentTypeDescription: "העלה תעודת זהות שלך.",
        allowedMimeTypes: ["application/pdf"],
        maxFileSizeInMB: 2,
        isUploaded: false,
        uploadedFileUrl: null,
        exampleFileUrl: null,
        link_url: null,
        documentGroupName: "קבוצה 1"
    },
    {
        documentTypeId: "doc2",
        documentTypeName: "אישור לימודים",
        documentTypeDescription: "העלה אישור לימודים.",
        allowedMimeTypes: ["application/pdf"],
        maxFileSizeInMB: 2,
        isUploaded: true,
        uploadedFileUrl: "https://example.com/file.pdf",
        exampleFileUrl: "https://example.com/example.pdf",
        link_url: "https://example.com/info",
        documentGroupName: "קבוצה 1"
    }
];

describe("DynamicDocumentUploadForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders loading state", () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({ loading: true });
        render(<DynamicDocumentUploadForm />);
        expect(screen.getByText(TEXTS.loadingDocuments)).toBeInTheDocument();
    });

    it("renders error state", () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({ loading: false, error: "שגיאה" });
        render(<DynamicDocumentUploadForm />);
        expect(screen.getByText(TEXTS.errorLoadingDocuments)).toBeInTheDocument();
        expect(screen.getByText("שגיאה")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: TEXTS.retryButton })).toBeInTheDocument();
    });

    it("renders empty state if no documents", () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: []
        });
        render(<DynamicDocumentUploadForm />);
        expect(screen.getByText(TEXTS.noDocumentsRequired)).toBeInTheDocument();
    });

    it("renders document groups and upload progress", () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments
        });
        render(<DynamicDocumentUploadForm />);
        expect(screen.getByText("מסמכים שהועלו")).toBeInTheDocument();
        expect(screen.getByText("קבוצה 1")).toBeInTheDocument();
        expect(screen.getAllByText("תעודת זהות")).toHaveLength(2);
        expect(screen.getAllByText("אישור לימודים")).toHaveLength(2);
    });

    it("shows PDF modal when example file is clicked", async () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments
        });
        render(<DynamicDocumentUploadForm />);
        const exampleBtn = screen.getByLabelText("מסמך לדוגמא");
        await userEvent.click(exampleBtn);
        await waitFor(() => {
            expect(screen.getByRole("dialog")).toBeInTheDocument();
        });
    });

    it("renders link icon and opens correct URL when clicked", async () => {
        const mockWindowOpen = jest.fn();
        Object.defineProperty(window, "open", {
            writable: true,
            value: mockWindowOpen
        });

        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments
        });
        render(<DynamicDocumentUploadForm />);

        const linkBtn = screen.getByLabelText("קישור להורדת המסמך");
        expect(linkBtn).toBeInTheDocument();

        await userEvent.click(linkBtn);

        expect(mockWindowOpen).toHaveBeenCalledWith("https://example.com/info", "_blank");

        mockWindowOpen.mockRestore();
    });

    it("handles file upload success", async () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments,
            refetchDocuments: jest.fn()
        });
        (uploadActions.uploadDocument as jest.Mock).mockResolvedValue({ success: true });
        render(<DynamicDocumentUploadForm />);
        const uploadInputs = screen.getAllByTestId("file-input");
        const file = new File(["dummy content"], "test.pdf", { type: "application/pdf" });
        await waitFor(() => expect(uploadInputs.length).toBeGreaterThan(0));

        await userEvent.upload(uploadInputs[0], file);

        const uploadButton = screen.getByText("העלה קובץ");
        await userEvent.click(uploadButton);

        await waitFor(() => {
            expect(uploadActions.uploadDocument).toHaveBeenCalled();
        });
    });

    it("handles file upload error", async () => {
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments,
            refetchDocuments: jest.fn()
        });
        (uploadActions.uploadDocument as jest.Mock).mockResolvedValue({ success: false, error: "שגיאת העלאה" });
        render(<DynamicDocumentUploadForm />);
        const uploadInputs = screen.getAllByTestId("file-input");
        const file = new File(["dummy content"], "test.pdf", { type: "application/pdf" });

        await userEvent.upload(uploadInputs[0], file);

        const uploadButton = screen.getByText("העלה קובץ");
        await userEvent.click(uploadButton);

        await waitFor(() => {
            expect(uploadActions.uploadDocument).toHaveBeenCalled();
        });
    });

    it("calls onUploadComplete callback after successful upload", async () => {
        const onUploadComplete = jest.fn();
        (useUserDocumentsModule.useUserDocuments as jest.Mock).mockReturnValue({
            loading: false,
            error: null,
            documents: mockDocuments,
            refetchDocuments: jest.fn()
        });
        (uploadActions.uploadDocument as jest.Mock).mockResolvedValue({ success: true });
        render(<DynamicDocumentUploadForm onUploadComplete={onUploadComplete} />);
        const uploadInputs = screen.getAllByTestId("file-input");
        const file = new File(["dummy content"], "test.pdf", { type: "application/pdf" });

        await userEvent.upload(uploadInputs[0], file);

        const uploadButton = screen.getByText("העלה קובץ");
        await userEvent.click(uploadButton);

        await waitFor(() => {
            expect(onUploadComplete).toHaveBeenCalled();
        });
    });
});
