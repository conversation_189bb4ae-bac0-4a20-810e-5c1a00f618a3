import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { QuestionForm } from "@/components/forms/question-form";
import { createQuestion, getQuestion, getQuestionFormData, updateQuestion } from "@/app/actions/question-actions";
import { useQuestionGroups } from "@/hooks/use-question-groups";
import { QuestionFormValues, defaultValues, TEXTS, typeOptions, SECTION_OPTIONS } from "@/lib/question-constants";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/question-actions", () => ({
    createQuestion: jest.fn(),
    getQuestion: jest.fn(),
    getQuestionFormData: jest.fn(),
    updateQuestion: jest.fn()
}));

jest.mock("@/hooks/use-question-groups", () => ({
    useQuestionGroups: jest.fn()
}));

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, label, placeholder, required }: any) => (
        <div data-testid={`short-text-${name}`}>
            <label>{label}</label>
            <input name={name} placeholder={placeholder} required={required} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ name, label, options, required }: any) => (
        <div data-testid={`single-select-${name}`}>
            <label>{label}</label>
            <select name={name} required={required}>
                {options?.map((option: any) => (
                    <option key={option.id} value={option.id}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ name, label, options }: any) => (
        <div data-testid={`multi-select-${name}`}>
            <label>{label}</label>
            <select name={name} multiple>
                {options?.map((option: any) => (
                    <option key={option.id} value={option.id}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/dropdown-option-editor", () => ({
    DropdownOptionEditor: ({ name, label }: any) => (
        <div data-testid={`dropdown-option-editor-${name}`}>
            <label>{label}</label>
            <textarea name={name} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ name, label }: any) => (
        <div data-testid={`number-input-${name}`}>
            <label>{label}</label>
            <input name={name} type="number" />
        </div>
    )
}));

jest.mock("@/components/forms/fields/condition-selector", () => ({
    ConditionSelector: ({ availableQuestions, onAdd, onRemove }: any) => (
        <div data-testid="condition-selector">
            <button onClick={onAdd} data-testid="add-dependency">
                Add Dependency
            </button>
            <button onClick={() => onRemove(0)} data-testid="remove-dependency">
                Remove Dependency
            </button>
        </div>
    )
}));

jest.mock("@/components/ui/custom-switch", () => ({
    CustomSwitch: ({ name, label }: any) => (
        <div data-testid={`custom-switch-${name}`}>
            <label>{label}</label>
            <input name={name} type="checkbox" />
        </div>
    )
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: any) => <div data-testid="loading-icon">{text}</div>
}));

const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn()
} as any;

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockCreateQuestion = createQuestion as jest.MockedFunction<typeof createQuestion>;
const mockGetQuestion = getQuestion as jest.MockedFunction<typeof getQuestion>;
const mockGetQuestionFormData = getQuestionFormData as jest.MockedFunction<typeof getQuestionFormData>;
const mockUpdateQuestion = updateQuestion as jest.MockedFunction<typeof updateQuestion>;
const mockUseQuestionGroups = useQuestionGroups as jest.MockedFunction<typeof useQuestionGroups>;
const mockToast = toast as jest.Mocked<typeof toast>;

const mockFormData = {
    scholarships: [
        { id: "scholarship-1", title: "Test Scholarship 1" },
        { id: "scholarship-2", title: "Test Scholarship 2" }
    ],
    availableQuestions: [
        {
            id: "question-1",
            type: "single_select" as const,
            metadata: { label: "Test Question 1", options: [{ id: "opt1", label: "Option 1" }] },
            groups_question: { id: "group-1", name: "Test Group" }
        },
        {
            id: "question-2",
            type: "multi_select" as const,
            metadata: { label: "Test Question 2", options: [{ id: "opt2", label: "Option 2" }] },
            groups_question: { id: "group-2", name: "Test Group 2" }
        }
    ]
};

const mockGroups = [
    { id: "group-1", name: "Group 1", questions_count: 0, created_at: "2024-01-01", updated_at: "2024-01-01" },
    { id: "group-2", name: "Group 2", questions_count: 0, created_at: "2024-01-01", updated_at: "2024-01-01" }
];

describe("QuestionForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseRouter.mockReturnValue(mockRouter);
        mockUseQuestionGroups.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: jest.fn()
        } as any);
        mockGetQuestionFormData.mockResolvedValue({
            success: true,
            data: mockFormData
        } as any);
    });

    describe("Component Rendering", () => {
        it("should render form fields correctly in create mode", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-section")).toBeInTheDocument();
                expect(screen.getByTestId("short-text-tooltip")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-group_id")).toBeInTheDocument();
                expect(screen.getByTestId("custom-switch-is_required")).toBeInTheDocument();
                expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
            });
        });

        it("should show loading state initially when editing", async () => {
            mockGetQuestion.mockResolvedValue({
                success: true,
                data: {
                    question: {
                        id: "question-1",
                        type: "single_select",
                        section: "data_entry",
                        group_id: "group-1",
                        created_at: "2024-01-01T00:00:00Z",
                        updated_at: "2024-01-01T00:00:00Z",
                        metadata: { label: "Test Question", required: true }
                    },
                    scholarshipIds: [],
                    dependencies: [],
                    groupInfo: { id: "group-1", name: "Test Group" }
                } as any
            });

            render(<QuestionForm questionId="question-1" />);

            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.loading)).toBeInTheDocument();

            await waitFor(() => {
                expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
            });
        });

        it("should display form title fields based on form constants", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.titleLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.typeLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.sectionLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.tooltipLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.groupLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.isRequiredLabel)).toBeInTheDocument();
            });
        });
    });

    describe("Dynamic Field Rendering", () => {
        it("should render placeholder field for all question types", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
            });
        });

        it("should render text-specific fields for text types", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
            });
        });

        it("should render select-specific fields when type supports options", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
            });
        });
    });

    describe("Conditional Field Rendering", () => {
        it("should show scholarship selection when section is specific_scholarship", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("single-select-section")).toBeInTheDocument();
            });
        });

        it("should show dependency selector when enable_dependencies is true", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
            });
        });
    });

    describe("Form Submission", () => {
        it("should display create button in create mode", async () => {
            mockCreateQuestion.mockResolvedValue({ success: true, data: { id: "new-question" } });

            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.createQuestion)).toBeInTheDocument();
            });

            expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
            expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
        });

        it("should display update button in edit mode", async () => {
            mockGetQuestion.mockResolvedValue({
                success: true,
                data: {
                    question: {
                        id: "question-1",
                        type: "single_select",
                        section: "data_entry",
                        group_id: "group-1",
                        created_at: "2024-01-01T00:00:00Z",
                        updated_at: "2024-01-01T00:00:00Z",
                        metadata: { label: "Test Question", required: true }
                    },
                    scholarshipIds: [],
                    dependencies: [],
                    groupInfo: { id: "group-1", name: "Test Group" }
                }
            });
            mockUpdateQuestion.mockResolvedValue({ success: true });

            render(<QuestionForm questionId="question-1" />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.updateQuestion)).toBeInTheDocument();
            });

            expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
            expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
        });

        it("should have proper form structure for submission", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.createQuestion)).toBeInTheDocument();
            });

            const form = document.querySelector("form");
            expect(form).toBeInTheDocument();
            expect(form).toHaveAttribute("dir", "rtl");

            const submitButton = screen.getByText(TEXTS.createQuestion);
            expect(submitButton).toHaveAttribute("type", "submit");
        });
    });

    describe("Loading States", () => {
        it("should show initial loading state in edit mode", async () => {
            mockGetQuestion.mockResolvedValue({
                success: true,
                data: {
                    question: {
                        id: "question-1",
                        type: "single_select",
                        section: "data_entry",
                        group_id: "group-1",
                        created_at: "2024-01-01T00:00:00Z",
                        updated_at: "2024-01-01T00:00:00Z",
                        metadata: { label: "Test Question", required: true }
                    },
                    scholarshipIds: [],
                    dependencies: [],
                    groupInfo: { id: "group-1", name: "Test Group" }
                }
            });

            render(<QuestionForm questionId="question-1" />);

            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.loading)).toBeInTheDocument();

            await waitFor(() => {
                expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
            });
        });

        it("should have submit button enabled by default", async () => {
            render(<QuestionForm />);

            await waitFor(() => {
                const submitButton = screen.getByText(TEXTS.createQuestion);
                expect(submitButton).not.toBeDisabled();
            });
        });
    });

    describe("Error Handling", () => {
        it("should handle data fetch error during initialization", async () => {
            mockGetQuestionFormData.mockResolvedValue({
                success: false,
                error: "Failed to fetch data"
            });

            render(<QuestionForm />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.dataFetchError);
            });
        });

        it("should handle question fetch error in edit mode", async () => {
            mockGetQuestion.mockResolvedValue({
                success: false,
                error: "Question not found"
            });

            render(<QuestionForm questionId="invalid-id" />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith("Question not found");
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/questions");
            });
        });

        it("should handle unexpected errors gracefully", async () => {
            mockGetQuestionFormData.mockRejectedValue(new Error("Network error"));

            render(<QuestionForm />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.dataFetchError);
            });
        });
    });

    describe("Form Cancellation", () => {
        it("should navigate back when cancel button is clicked", async () => {
            const user = userEvent.setup();
            render(<QuestionForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.cancelButton)).toBeInTheDocument();
            });

            const cancelButton = screen.getByText(TEXTS.cancelButton);
            await user.click(cancelButton);

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/questions");
        });
    });

    describe("Data Transformation", () => {
        it("should populate form with question data in edit mode", async () => {
            const mockQuestionData = {
                question: {
                    id: "question-1",
                    type: "single_select" as const,
                    section: "data_entry" as const,
                    group_id: "group-1",
                    created_at: "2024-01-01T00:00:00Z",
                    updated_at: "2024-01-01T00:00:00Z",
                    metadata: {
                        label: "Test Question",
                        required: true,
                        tooltip: "Test tooltip",
                        placeholder: "Test placeholder",
                        options: [{ id: "opt1", label: "Option 1" }]
                    }
                },
                scholarshipIds: ["scholarship-1"],
                dependencies: [
                    {
                        question_id: "dep-question-1",
                        condition_type: "in",
                        condition_value: ["value1"]
                    }
                ],
                groupInfo: { id: "group-1", name: "Test Group" }
            };

            mockGetQuestion.mockResolvedValue({
                success: true,
                data: mockQuestionData
            });

            render(<QuestionForm questionId="question-1" />);

            await waitFor(() => {
                expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
            });
        });

        it("should handle questions with no dependencies", async () => {
            const mockQuestionData = {
                question: {
                    id: "question-1",
                    type: "short_text" as const,
                    section: "data_entry" as const,
                    group_id: "group-1",
                    created_at: "2024-01-01T00:00:00Z",
                    updated_at: "2024-01-01T00:00:00Z",
                    metadata: {
                        label: "Simple Question",
                        required: false
                    }
                },
                scholarshipIds: [],
                dependencies: [],
                groupInfo: { id: "group-1", name: "Test Group" }
            };

            mockGetQuestion.mockResolvedValue({
                success: true,
                data: mockQuestionData
            });

            render(<QuestionForm questionId="question-1" />);

            await waitFor(() => {
                expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
            });

            const dependenciesSwitch = screen.getByTestId("custom-switch-enable_dependencies").querySelector("input");
            expect(dependenciesSwitch).not.toBeChecked();
        });
    });

    describe("Data Transformation Functions", () => {
        it("should test transformFormDataToQuestionData function behavior", () => {
            const mockFormData: QuestionFormValues = {
                title: "Test Question",
                type: { id: "single_select", label: "Single Select" },
                tooltip: "Test tooltip",
                group_id: { id: "group-1", label: "Group 1" },
                section: { id: "data_entry", label: "Data Entry" },
                is_required: true,
                scholarship_ids: [],
                enable_dependencies: false,
                dependencies: [],
                metadata: {
                    placeholder: "Test placeholder",
                    options: [{ id: "opt1", label: "Option 1" }]
                }
            };

            expect(mockFormData.title).toBe("Test Question");
        });

        it("should test transformQuestionToFormData function behavior", () => {
            const mockDBQuestion = {
                id: "question-1",
                type: "single_select" as const,
                section: "data_entry" as const,
                group_id: "group-1",
                metadata: {
                    label: "Test Question",
                    required: true
                }
            };

            expect(mockDBQuestion.type).toBe("single_select");
        });
    });

    describe("Conditional Rendering States", () => {
        describe("'in' Condition State", () => {
            it("renders form correctly with 'in' condition dependencies", async () => {
                const mockFormDataWithInCondition = {
                    ...mockFormData,
                    availableQuestions: [
                        {
                            id: "question-in",
                            type: "single_select" as const,
                            metadata: {
                                label: "Question with 'in' condition",
                                options: [
                                    { id: "opt1", label: "Option 1" },
                                    { id: "opt2", label: "Option 2" }
                                ]
                            },
                            groups_question: { id: "group-1", name: "Test Group" }
                        }
                    ]
                };

                mockGetQuestionFormData.mockResolvedValue({
                    success: true,
                    data: mockFormDataWithInCondition
                } as any);

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                    expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
                });

                // Verify form renders correctly with 'in' condition context
                expect(screen.getByText(TEXTS.titleLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.enableDependenciesLabel)).toBeInTheDocument();
            });

            it("handles form submission with 'in' condition dependencies", async () => {
                const user = userEvent.setup();
                mockCreateQuestion.mockResolvedValue({ success: true, data: { id: "new-question" } });

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                });

                const titleInput = screen.getByTestId("short-text-title").querySelector("input");
                if (titleInput) {
                    await user.type(titleInput, "Question with 'in' condition");
                }

                // Enable dependencies to test condition handling
                const dependenciesSwitch = screen
                    .getByTestId("custom-switch-enable_dependencies")
                    .querySelector("input");
                if (dependenciesSwitch) {
                    await user.click(dependenciesSwitch);
                }

                // Verify dependencies switch is enabled
                expect(dependenciesSwitch).toBeChecked();
            });

            it("validates 'in' condition dependencies correctly", async () => {
                const user = userEvent.setup();
                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
                });

                // Enable dependencies
                const dependenciesSwitch = screen
                    .getByTestId("custom-switch-enable_dependencies")
                    .querySelector("input");
                if (dependenciesSwitch) {
                    await user.click(dependenciesSwitch);
                }

                // Verify dependencies can be enabled
                expect(dependenciesSwitch).toBeChecked();

                // Verify form still has all required elements for 'in' conditions
                expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
            });
        });

        describe("'range' Condition State", () => {
            it("renders form correctly with 'range' condition dependencies", async () => {
                const mockFormDataWithRangeCondition = {
                    ...mockFormData,
                    availableQuestions: [
                        {
                            id: "question-range",
                            type: "number_input" as const,
                            metadata: {
                                label: "Question with 'range' condition",
                                min: 0,
                                max: 100
                            },
                            groups_question: { id: "group-1", name: "Test Group" }
                        }
                    ]
                };

                mockGetQuestionFormData.mockResolvedValue({
                    success: true,
                    data: mockFormDataWithRangeCondition
                } as any);

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // Verify number input fields for range conditions
                expect(screen.getByText(TEXTS.typeLabel)).toBeInTheDocument();
            });

            it("handles 'range' condition form submission", async () => {
                const user = userEvent.setup();
                mockCreateQuestion.mockResolvedValue({ success: true, data: { id: "range-question" } });

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                });

                const titleInput = screen.getByTestId("short-text-title").querySelector("input");
                if (titleInput) {
                    await user.type(titleInput, "Question with range condition");
                }

                // Test range-specific fields
                expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
            });

            it("validates 'range' condition inputs correctly", async () => {
                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // Verify type selection includes number_input for range conditions
                const typeSelect = screen.getByTestId("single-select-type").querySelector("select");
                expect(typeSelect).toBeInTheDocument();

                // Check for number_input option
                const numberOption = screen.getByText("מספר");
                expect(numberOption).toBeInTheDocument();
            });

            it("displays proper fields for number input type with range conditions", async () => {
                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
                });

                // For number types, additional fields should be available
                // This would include min/max fields when the type is number_input
                expect(screen.getByText(TEXTS.typeLabel)).toBeInTheDocument();
            });
        });

        describe("'date_range' Condition State", () => {
            it("renders form correctly with 'date_range' condition dependencies", async () => {
                const mockFormDataWithDateRange = {
                    ...mockFormData,
                    availableQuestions: [
                        {
                            id: "question-date-range",
                            type: "date_picker" as const,
                            metadata: {
                                label: "Question with date range condition",
                                isFutureAllowed: false
                            },
                            groups_question: { id: "group-1", name: "Test Group" }
                        }
                    ]
                };

                mockGetQuestionFormData.mockResolvedValue({
                    success: true,
                    data: mockFormDataWithDateRange
                } as any);

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // Verify date picker type is available
                const dateOption = screen.getByText("תאריך");
                expect(dateOption).toBeInTheDocument();
            });

            it("handles 'date_range' condition form submission", async () => {
                const user = userEvent.setup();
                mockCreateQuestion.mockResolvedValue({ success: true, data: { id: "date-range-question" } });

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                });

                const titleInput = screen.getByTestId("short-text-title").querySelector("input");
                if (titleInput) {
                    await user.type(titleInput, "Question with date range condition");
                }

                // Verify date-specific elements
                expect(screen.getByText(TEXTS.titleLabel)).toBeInTheDocument();
            });

            it("validates 'date_range' condition settings", async () => {
                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // Check for date_picker option in type selector
                const dateOption = screen.getByText("תאריך");
                expect(dateOption).toBeInTheDocument();

                // Verify all required form elements for date range conditions
                expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-section")).toBeInTheDocument();
            });

            it("shows date-specific configuration options", async () => {
                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // For date picker type, specific configuration should be available
                // This includes future date allowance, etc.
                expect(screen.getByText(TEXTS.typeLabel)).toBeInTheDocument();
            });
        });

        describe("Multiple Conditions Combined", () => {
            it("handles form with all condition types available", async () => {
                const mockFormDataWithAllConditions = {
                    ...mockFormData,
                    availableQuestions: [
                        {
                            id: "question-in",
                            type: "single_select" as const,
                            metadata: {
                                label: "Question with 'in' condition",
                                options: [{ id: "opt1", label: "Option 1" }]
                            },
                            groups_question: { id: "group-1", name: "Test Group" }
                        },
                        {
                            id: "question-range",
                            type: "number_input" as const,
                            metadata: {
                                label: "Question with 'range' condition",
                                min: 0,
                                max: 100
                            },
                            groups_question: { id: "group-2", name: "Test Group 2" }
                        },
                        {
                            id: "question-date-range",
                            type: "date_picker" as const,
                            metadata: {
                                label: "Question with date range condition",
                                isFutureAllowed: true
                            },
                            groups_question: { id: "group-3", name: "Test Group 3" }
                        }
                    ]
                };

                mockGetQuestionFormData.mockResolvedValue({
                    success: true,
                    data: mockFormDataWithAllConditions
                } as any);

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                    expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
                });

                // Verify all question types are available
                expect(screen.getByText("בחירה יחידה")).toBeInTheDocument(); // single_select
                expect(screen.getByText("מספר")).toBeInTheDocument(); // number_input
                expect(screen.getByText("תאריך")).toBeInTheDocument(); // date_picker
            });

            it("handles complex dependency setup with multiple condition types", async () => {
                const user = userEvent.setup();

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
                });

                // Enable dependencies to test multiple condition handling
                const dependenciesSwitch = screen
                    .getByTestId("custom-switch-enable_dependencies")
                    .querySelector("input");
                if (dependenciesSwitch) {
                    await user.click(dependenciesSwitch);
                }

                // Verify dependencies are enabled for complex condition handling
                expect(dependenciesSwitch).toBeChecked();

                // Verify all question types are still available for complex conditions
                expect(screen.getByText("בחירה יחידה")).toBeInTheDocument();
                expect(screen.getByText("מספר")).toBeInTheDocument();
                expect(screen.getByText("תאריך")).toBeInTheDocument();
            });

            it("validates form submission with complex conditions", async () => {
                const user = userEvent.setup();
                mockCreateQuestion.mockResolvedValue({ success: true, data: { id: "complex-question" } });

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                });

                const titleInput = screen.getByTestId("short-text-title").querySelector("input");
                if (titleInput) {
                    await user.type(titleInput, "Complex question with multiple conditions");
                }

                // Test form structure supports complex conditions
                expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                expect(screen.getByTestId("single-select-section")).toBeInTheDocument();
                expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
            });

            it("handles edit mode with multiple condition dependencies", async () => {
                const mockQuestionWithMultipleConditions = {
                    question: {
                        id: "complex-question-1",
                        type: "single_select" as const,
                        section: "data_entry" as const,
                        group_id: "group-1",
                        metadata: {
                            label: "Complex Question",
                            required: true,
                            options: [{ id: "opt1", label: "Option 1" }]
                        },
                        created_at: "2024-01-01T00:00:00Z",
                        updated_at: "2024-01-01T00:00:00Z"
                    },
                    scholarshipIds: [],
                    dependencies: [
                        {
                            question_id: "dep-question-1",
                            condition_type: "in",
                            condition_value: ["value1", "value2"]
                        },
                        {
                            question_id: "dep-question-2",
                            condition_type: "range",
                            condition_value: { min: 10, max: 50 }
                        }
                    ],
                    groupInfo: { id: "group-1", name: "Test Group" }
                };

                mockGetQuestion.mockResolvedValue({
                    success: true,
                    data: mockQuestionWithMultipleConditions
                });

                render(<QuestionForm questionId="complex-question-1" />);

                await waitFor(() => {
                    expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
                });

                // Since form starts with dependencies false in mock data,
                // verify dependencies can be controlled
                const dependenciesSwitch = screen
                    .getByTestId("custom-switch-enable_dependencies")
                    .querySelector("input");
                expect(dependenciesSwitch).not.toBeChecked();
            });
        });

        describe("Condition State Transitions", () => {
            it("handles dynamic type changes affecting condition rendering", async () => {
                const user = userEvent.setup();

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("single-select-type")).toBeInTheDocument();
                });

                // Test changing question type affects available fields
                const typeSelect = screen.getByTestId("single-select-type").querySelector("select");
                if (typeSelect) {
                    // Simulate changing from single_select to number_input
                    fireEvent.change(typeSelect, { target: { value: "number_input" } });
                }

                // Verify form adapts to new type
                expect(screen.getByTestId("short-text-metadata.placeholder")).toBeInTheDocument();
            });

            it("maintains form state during condition type transitions", async () => {
                const user = userEvent.setup();

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("short-text-title")).toBeInTheDocument();
                });

                const titleInput = screen.getByTestId("short-text-title").querySelector("input");
                if (titleInput) {
                    await user.type(titleInput, "Test question");
                }

                // Change question type
                const typeSelect = screen.getByTestId("single-select-type").querySelector("select");
                if (typeSelect) {
                    fireEvent.change(typeSelect, { target: { value: "date_picker" } });
                }

                // Verify title is maintained
                expect(titleInput).toHaveValue("Test question");
            });

            it("handles dependency conditions when switching between complex states", async () => {
                const user = userEvent.setup();

                render(<QuestionForm />);

                await waitFor(() => {
                    expect(screen.getByTestId("custom-switch-enable_dependencies")).toBeInTheDocument();
                });

                // Enable dependencies
                const dependenciesSwitch = screen
                    .getByTestId("custom-switch-enable_dependencies")
                    .querySelector("input");
                if (dependenciesSwitch) {
                    await user.click(dependenciesSwitch);
                }

                // Verify dependencies are enabled
                expect(dependenciesSwitch).toBeChecked();

                // Disable dependencies
                if (dependenciesSwitch) {
                    await user.click(dependenciesSwitch);
                }

                // Verify dependencies are disabled
                expect(dependenciesSwitch).not.toBeChecked();
            });
        });
    });
});
