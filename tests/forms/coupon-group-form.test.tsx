import { act, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import * as couponGroupActions from "@/app/actions/coupon-group-actions";
import { CouponGroupForm, TEXTS } from "@/components/forms/coupon-group-form";
import { Tables } from "@/types/database.types";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
    prefetch: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn()
};

const mockCouponGroup: Tables<"groups_coupon"> = {
    id: "group-1",
    name: "Test Group",
    description: "Test Description",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
};

const couponGroupFormValues: couponGroupActions.CouponGroupFormValues = {
    name: "New Test Group",
    description: "New Test Description"
};

describe("CouponGroupForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);
    });

    describe("Create mode", () => {
        it("renders create form correctly", () => {
            render(<CouponGroupForm />);
            expect(screen.getByText(TEXTS.createButtonText)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.nameLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.descriptionLabel)).toBeInTheDocument();
        });

        it("shows validation error for required fields", async () => {
            render(<CouponGroupForm />);
            await userEvent.click(screen.getByText(TEXTS.createButtonText));
            expect(await screen.findByText(TEXTS.nameRequired)).toBeInTheDocument();
        });

        it("submits the form and calls createCouponGroup", async () => {
            const createCouponGroupSpy = jest
                .spyOn(couponGroupActions, "createCouponGroup")
                .mockResolvedValue({ success: true });

            render(<CouponGroupForm />);

            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), couponGroupFormValues.name);
            await userEvent.type(screen.getByLabelText(TEXTS.descriptionLabel), couponGroupFormValues.description!);

            await userEvent.click(screen.getByText(TEXTS.createButtonText));

            await waitFor(() => {
                expect(createCouponGroupSpy).toHaveBeenCalledWith(couponGroupFormValues);
            });

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessage, expect.any(Object));
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons?tab=groups");
            });
        });

        it("shows error toast on create failure", async () => {
            const createCouponGroupSpy = jest
                .spyOn(couponGroupActions, "createCouponGroup")
                .mockResolvedValue({ success: false, error: "Create failed" });

            render(<CouponGroupForm />);

            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), couponGroupFormValues.name);
            await userEvent.type(screen.getByLabelText(TEXTS.descriptionLabel), couponGroupFormValues.description!);

            await userEvent.click(screen.getByText(TEXTS.createButtonText));

            await waitFor(() => {
                expect(createCouponGroupSpy).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalledWith("Create failed");
            });
        });
    });

    describe("Edit mode", () => {
        beforeEach(() => {
            jest.spyOn(couponGroupActions, "getCouponGroup").mockResolvedValue({
                success: true,
                data: mockCouponGroup
            });
        });

        it("shows a loading indicator while fetching the coupon group data", async () => {
            render(<CouponGroupForm groupId={mockCouponGroup.id} />);

            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });
        });

        it("renders edit form with pre-filled data", async () => {
            render(<CouponGroupForm groupId={mockCouponGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            expect(screen.getByLabelText(TEXTS.nameLabel)).toHaveValue(mockCouponGroup.name);
            expect(screen.getByLabelText(TEXTS.descriptionLabel)).toHaveValue(mockCouponGroup.description);
            expect(screen.getByText(TEXTS.updateButtonText)).toBeInTheDocument();
        });

        it("submits the form and calls updateCouponGroup", async () => {
            const updateCouponGroupSpy = jest
                .spyOn(couponGroupActions, "updateCouponGroup")
                .mockResolvedValue({ success: true });

            render(<CouponGroupForm groupId={mockCouponGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            const updatedName = "Updated Group Name";
            await userEvent.clear(screen.getByLabelText(TEXTS.nameLabel));
            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), updatedName);

            await userEvent.click(screen.getByText(TEXTS.updateButtonText));

            await waitFor(() => {
                expect(updateCouponGroupSpy).toHaveBeenCalledWith(mockCouponGroup.id, {
                    name: updatedName,
                    description: mockCouponGroup.description
                });
                expect(toast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage, expect.any(Object));
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons?tab=groups");
            });
        });

        it("shows error toast on update failure", async () => {
            const updateCouponGroupSpy = jest
                .spyOn(couponGroupActions, "updateCouponGroup")
                .mockResolvedValue({ success: false, error: "Update failed" });

            render(<CouponGroupForm groupId={mockCouponGroup.id} />);
            await waitFor(() => expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument());

            await userEvent.click(screen.getByText(TEXTS.updateButtonText));

            await waitFor(() => {
                expect(updateCouponGroupSpy).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalledWith("Update failed");
            });
        });

        it("handles error when coupon group is not found", async () => {
            jest.spyOn(couponGroupActions, "getCouponGroup").mockResolvedValue({
                success: false,
                error: TEXTS.editNotFoundMessage
            });

            render(<CouponGroupForm groupId="not-found" />);
            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.editNotFoundMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons?tab=groups");
            });
        });
    });

    it("navigates back when cancel button is clicked", async () => {
        render(<CouponGroupForm />);
        await userEvent.click(screen.getByText(TEXTS.cancelButtonText));
        expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons?tab=groups");
    });
});
