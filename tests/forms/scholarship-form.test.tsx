import "@testing-library/jest-dom";

import { act, cleanup, render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";

import { getScholarship } from "@/app/actions/scholarship-crud-actions";
import {
    getAllQuestionsForConditionSelector,
    getScholarshipConditionGroupsForScholarship,
    getScholarshipDefinedConditions,
    getScholarshipDocumentTypes
} from "@/app/actions/scholarship-form-actions";
import { useDocumentTypes } from "@/hooks/use-document-types";
import { useScholarshipConditionGroups } from "@/hooks/use-scholarship-condition-groups";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";
import { useTestimonials } from "@/hooks/use-testimonials";
import type { Database } from "@/types/database.types";

import { ScholarshipForm } from "../../components/forms/scholarship-form";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/scholarship-crud-actions", () => ({
    createScholarship: jest.fn(),
    updateScholarship: jest.fn(),
    getScholarship: jest.fn(),
    linkScholarshipGroups: jest.fn(),
    linkScholarshipTestimonials: jest.fn(),
    uploadScholarshipImage: jest.fn()
}));

jest.mock("@/app/actions/scholarship-form-actions", () => ({
    getAllQuestionsForConditionSelector: jest.fn(),
    getScholarshipConditionGroupsForScholarship: jest.fn(),
    getScholarshipDefinedConditions: jest.fn(),
    getScholarshipDocumentTypes: jest.fn(),
    updateScholarshipConditionGroups: jest.fn(),
    updateScholarshipDefinedConditions: jest.fn(),
    updateScholarshipDocumentTypes: jest.fn()
}));

jest.mock("@/hooks/use-testimonials", () => ({
    useTestimonials: jest.fn()
}));

jest.mock("@/hooks/use-scholarship-groups", () => ({
    useScholarshipGroups: jest.fn()
}));

jest.mock("@/hooks/use-scholarship-condition-groups", () => ({
    useScholarshipConditionGroups: jest.fn()
}));

jest.mock("@/hooks/use-document-types", () => ({
    useDocumentTypes: jest.fn()
}));

let mockFormData = {
    title: "Test Scholarship",
    slug: "test-scholarship",
    description: "Test description",
    short_description: "Short description",
    volunteer_hours: 40,
    min_amount: 1000,
    max_amount: 5000,
    start_date: new Date("2024-01-01"),
    end_date: new Date("2024-12-31"),
    requirements: "Test requirements",
    benefits: "Test benefits",
    target_audience: "Test audience",
    image_file: null as File | null,
    url: "https://example.com",
    testimonial_ids: ["testimonial1"],
    group_ids: ["group1"],
    condition_group_ids: ["condition1"],
    document_type_ids: ["doc1"],
    defined_conditions: [],
    is_public: true,
    internal_notes: "Internal notes",
    contact_person: "John Doe",
    contact_email: "<EMAIL>",
    contact_phone: "0501234567",
    response_date: new Date("2024-06-01"),
    is_active: true
};

let mockFormState = { isSubmitting: false };

jest.mock("react-hook-form", () => {
    const actualModule = jest.requireActual("react-hook-form");
    return {
        ...actualModule,
        useForm: () => ({
            handleSubmit: jest.fn((fn) => (e?: React.FormEvent) => {
                e?.preventDefault();
                fn(mockFormData);
            }),
            control: {},
            formState: mockFormState,
            watch: jest.fn((field) => {
                if (field === "defined_conditions") return mockFormData.defined_conditions;
                if (field === "slug") return mockFormData.slug;
                return mockFormData[field as keyof typeof mockFormData];
            }),
            setValue: jest.fn(),
            reset: jest.fn(),
            getValues: jest.fn(() => mockFormData)
        }),
        useFieldArray: () => ({
            append: jest.fn(),
            remove: jest.fn()
        }),
        FormProvider: ({ children }: { children: React.ReactNode }) => children
    };
});

jest.mock("@/components/forms/fields/condition-selector", () => ({
    ConditionSelector: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/fields/date-picker", () => ({
    DatePicker: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/fields/file-upload", () => ({
    FileUpload: ({ label }: { label: string }) => (
        <div data-testid="file-upload-image">
            <label>{label}</label>
            <input type="file" data-testid="file-input-image" />
        </div>
    )
}));

jest.mock("@/components/forms/fields/long-text", () => ({
    LongText: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ label }: { label: string }) => (
        <div>
            <label>{label}</label>
        </div>
    )
}));

jest.mock("@/components/forms/scholarship-form-skeleton", () => ({
    ScholarshipFormSkeleton: () => <div data-testid="scholarship-form-skeleton">טוען מלגה...</div>
}));

jest.mock("@/components/ui/form", () => ({
    FormField: ({
        name,
        render
    }: {
        name?: string;
        render?: (field: { field: Record<string, unknown> }) => React.ReactNode;
    }) => {
        if (render) {
            const getValue = (fieldName: string) => {
                switch (fieldName) {
                    case "is_active":
                        return mockFormData.is_active;
                    case "is_public":
                        return mockFormData.is_public;
                    default:
                        return undefined;
                }
            };

            return render({
                field: {
                    value: name ? getValue(name) : undefined,
                    onChange: jest.fn()
                }
            });
        }
        return null;
    },
    FormItem: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    FormMessage: () => null
}));

jest.mock("@/components/ui/card", () => ({
    Card: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    CardContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    CardHeader: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    CardTitle: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

jest.mock("@/components/ui/switch", () => ({
    Switch: ({
        id,
        checked,
        onCheckedChange
    }: {
        id?: string;
        checked: boolean;
        onCheckedChange: (checked: boolean) => void;
    }) => (
        <button id={id} type="button" role="switch" aria-checked={checked} onClick={() => onCheckedChange(!checked)}>
            {checked ? "On" : "Off"}
        </button>
    )
}));

jest.mock("@/components/ui/tooltip", () => ({
    TooltipProvider: ({ children }: { children: React.ReactNode }) => children,
    Tooltip: ({ children }: { children: React.ReactNode }) => children,
    TooltipTrigger: ({ children }: { children: React.ReactNode }) => children,
    TooltipContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"];

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMockFormData = () => {
    mockFormData = {
        title: "Test Scholarship",
        slug: "test-scholarship",
        description: "Test description",
        short_description: "Short description",
        volunteer_hours: 40,
        min_amount: 1000,
        max_amount: 5000,
        start_date: new Date("2024-01-01"),
        end_date: new Date("2024-12-31"),
        requirements: "Test requirements",
        benefits: "Test benefits",
        target_audience: "Test audience",
        image_file: null as File | null,
        url: "https://example.com",
        testimonial_ids: ["testimonial1"],
        group_ids: ["group1"],
        condition_group_ids: ["condition1"],
        document_type_ids: ["doc1"],
        defined_conditions: [],
        is_public: true,
        internal_notes: "Internal notes",
        contact_person: "John Doe",
        contact_email: "<EMAIL>",
        contact_phone: "0501234567",
        response_date: new Date("2024-06-01"),
        is_active: true
    };
    mockFormState = { isSubmitting: false };
};

describe("ScholarshipForm", () => {
    const mockPush = jest.fn();
    const mockRouter = { push: mockPush };

    const mockTestimonials = [{ id: "testimonial1", name: "John Doe", text: "Great scholarship!" }];

    const mockScholarshipGroups = [{ id: "group1", title: "Academic", description: "Academic scholarships" }];

    const mockConditionGroups = [{ id: "condition1", name: "Basic Conditions", conditions_count: 5 }];

    const mockDocumentTypes = [{ id: "doc1", name: "ID Document", description: "Identity document" }];

    const mockAvailableQuestions = [
        {
            id: "question1",
            type: "single_select" as const,
            metadata: { label: "Age Range", placeholder: "Select age range" }
        }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        resetMockFormData();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);

        (useTestimonials as jest.Mock).mockReturnValue({ items: mockTestimonials, loading: false });
        (useScholarshipGroups as jest.Mock).mockReturnValue({ items: mockScholarshipGroups, loading: false });
        (useScholarshipConditionGroups as jest.Mock).mockReturnValue({ items: mockConditionGroups, loading: false });
        (useDocumentTypes as jest.Mock).mockReturnValue({ items: mockDocumentTypes, loading: false });

        (getAllQuestionsForConditionSelector as jest.Mock).mockResolvedValue({
            success: true,
            data: mockAvailableQuestions
        });

        (getScholarshipConditionGroupsForScholarship as jest.Mock).mockResolvedValue({
            success: true,
            data: ["condition1"]
        });

        (getScholarshipDefinedConditions as jest.Mock).mockResolvedValue({
            success: true,
            data: []
        });

        (getScholarshipDocumentTypes as jest.Mock).mockResolvedValue({
            success: true,
            data: ["doc1"]
        });

        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    const TEXTS = {
        editLoadingMessage: "טוען מלגה...",
        editNotFoundMessage: "המלגה לא נמצאה",
        editSuccessMessage: "המלגה עודכנה בהצלחה",
        editErrorMessage: "שגיאה בעדכון המלגה",
        createSuccessMessage: "המלגה נוצרה בהצלחה",
        createErrorMessage: "שגיאה ביצירת המלגה",
        createButtonText: "צור מלגה",
        updateButtonText: "עדכן מלגה",
        cancelButtonText: "ביטול",
        questionsLoadError: "שגיאה בטעינת שאלות זמינות עבור תנאים",

        statusSection: "סטטוס",
        generalInfoSection: "מידע כללי",
        associationsSection: "שיוכים",
        centralDetailsSection: "פרטים מרכזיים",
        eligibilityConditionsSection: "תנאי זכאות",
        contactDetailsSection: "פרטי יצירת קשר",

        titleLabel: "שם המלגה",
        slugLabel: "קידומת המלגה",
        shortDescriptionLabel: "תיאור קצר",
        descriptionLabel: "תיאור מלא",
        imageLabel: "תמונת המלגה",
        scholarshipGroupsLabel: "קבוצות מלגה",
        testimonialsLabel: "חוות דעת",
        minAmountLabel: "סכום מינימלי",
        maxAmountLabel: "סכום מקסימלי",
        startDateLabel: "תאריך פתיחת ההרשמה",
        endDateLabel: "תאריך סגירת ההרשמה",
        responseDateLabel: "תאריך תשובות",
        volunteerHoursLabel: "שעות התנדבות נדרשות",
        requirementsLabel: "דרישות זכאות",
        benefitsLabel: "הטבות",
        targetAudienceLabel: "קהל יעד",
        documentTypesLabel: "סוגי מסמכים נדרשים",
        conditionGroupsLabel: "קבוצות תנאים לזכאות",
        specificConditionsLabel: "הגדר תנאים ספציפיים לזכאות",
        contactPersonLabel: "איש קשר",
        contactEmailLabel: "מייל ליצירת קשר",
        contactPhoneLabel: "נייד ליצירת קשר",
        externalUrlLabel: "קישור חיצוני למלגה",
        internalNotesLabel: "הערות פנימיות",

        activityStatusLabel: "סטטוס פעילות",
        publicationStatusLabel: "סטטוס פרסום"
    };

    const mockScholarship: Scholarship = {
        id: "scholarship-1",
        title: "Test Scholarship",
        slug: "test-scholarship",
        description: "Test description",
        short_description: "Short description",
        volunteer_hours: 40,
        min_amount: 1000,
        max_amount: 5000,
        start_date: "2024-01-01T00:00:00.000Z",
        end_date: "2024-12-31T00:00:00.000Z",
        requirements: ["Requirement 1", "Requirement 2"],
        benefits: ["Benefit 1", "Benefit 2"],
        target_audience: "Test audience",
        image_url: "https://example.com",
        url: "https://example.com",
        is_public: true,
        internal_notes: "Internal notes",
        contact_person: "John Doe",
        contact_email: "<EMAIL>",
        contact_phone: "0501234567",
        response_date: "2024-06-01T00:00:00.000Z",
        is_active: true,
        scholarship_type: "submission",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    } as Scholarship & {
        link_scholarship_to_testimonial: Array<{ testimonial_id: string }>;
        link_scholarship_to_scholarship_groups: Array<{ scholarship_group_id: string }>;
    };

    describe("Create Mode", () => {
        it("renders create form with all required fields", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.statusSection)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.generalInfoSection)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.associationsSection)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.centralDetailsSection)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.eligibilityConditionsSection)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.contactDetailsSection)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
        });

        it("renders file upload component", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("file-upload-image")).toBeInTheDocument();
            });

            expect(screen.getByTestId("file-input-image")).toBeInTheDocument();
        });

        it("fetches available questions on mount", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(getAllQuestionsForConditionSelector).toHaveBeenCalled();
            });
        });
    });

    describe("Edit Mode", () => {
        it("shows loading state initially in edit mode", () => {
            (getScholarship as jest.Mock).mockImplementation(() => new Promise(() => {}));

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            expect(screen.getByTestId("scholarship-form-skeleton")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();
        });

        it("fetches scholarship data when scholarshipId is provided", async () => {
            const mockGetResult = { success: true, data: mockScholarship };
            (getScholarship as jest.Mock).mockResolvedValue(mockGetResult);

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                    expect(getScholarshipConditionGroupsForScholarship).toHaveBeenCalledWith("scholarship-1");
                    expect(getScholarshipDefinedConditions).toHaveBeenCalledWith("scholarship-1");
                    expect(getScholarshipDocumentTypes).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 1000 }
            );

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles scholarship not found error", async () => {
            const mockGetResult = { success: false, error: "Scholarship not found", data: null };
            (getScholarship as jest.Mock).mockResolvedValue(mockGetResult);

            render(<ScholarshipForm scholarshipId="non-existent" />);

            await waitFor(
                () => {
                    expect(toast.error).toHaveBeenCalledWith("Scholarship not found");
                    expect(mockPush).toHaveBeenCalledWith("/admin/scholarships");
                },
                { timeout: 1000 }
            );
        });
    });

    describe("Form Actions", () => {
        it("navigates back on cancel button click", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
            });

            const cancelButton = screen.getByRole("button", { name: TEXTS.cancelButtonText });
            cancelButton.click();

            expect(mockPush).toHaveBeenCalledWith("/admin/scholarships");
        });
    });

    describe("Component Structure", () => {
        it("renders all form field labels", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.statusSection)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.titleLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.slugLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.shortDescriptionLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.descriptionLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.imageLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.scholarshipGroupsLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.testimonialsLabel)).toBeInTheDocument();
        });

        it("renders correct button text for create mode", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });
        });

        it("renders correct button text for edit mode", async () => {
            const mockGetResult = { success: true, data: mockScholarship };
            (getScholarship as jest.Mock).mockResolvedValue(mockGetResult);

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
                },
                { timeout: 1000 }
            );
        });
    });

    describe("Data Fetching", () => {
        it("fetches testimonials, scholarship groups, condition groups and document types", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(useTestimonials).toHaveBeenCalled();
                expect(useScholarshipGroups).toHaveBeenCalled();
                expect(useScholarshipConditionGroups).toHaveBeenCalled();
                expect(useDocumentTypes).toHaveBeenCalled();
            });
        });
    });

    describe("Error Handling", () => {
        it("handles available questions fetching errors", async () => {
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
            const mockError = "Failed to fetch questions";

            (getAllQuestionsForConditionSelector as jest.Mock).mockResolvedValue({
                success: false,
                error: mockError
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(consoleSpy).toHaveBeenCalledWith("Failed to fetch questions for ConditionSelector:", mockError);
                expect(toast.error).toHaveBeenCalledWith(TEXTS.questionsLoadError, {
                    description: mockError
                });
            });

            consoleSpy.mockRestore();
        });

        it("handles scholarship fetching errors in edit mode", async () => {
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
            const mockError = new Error("Failed to fetch scholarship");

            (getScholarship as jest.Mock).mockRejectedValue(mockError);

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(() => {
                expect(consoleSpy).toHaveBeenCalledWith("Error fetching scholarship:", mockError);
                expect(toast.error).toHaveBeenCalledWith(TEXTS.editErrorMessage, {
                    description: mockError.message
                });
            });

            consoleSpy.mockRestore();
        });
    });

    describe("Form Validation Labels", () => {
        it("shows Hebrew text constants correctly", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.statusSection)).toBeInTheDocument();
            });

            expect(screen.getByText("סטטוס")).toBeInTheDocument();
            expect(screen.getByText("מידע כללי")).toBeInTheDocument();
            expect(screen.getByText("שיוכים")).toBeInTheDocument();
            expect(screen.getByText("פרטים מרכזיים")).toBeInTheDocument();
            expect(screen.getByText("תנאי זכאות")).toBeInTheDocument();
            expect(screen.getByText("פרטי יצירת קשר")).toBeInTheDocument();
        });
    });

    describe("Form Data Scenarios", () => {
        it("handles empty form data", async () => {
            setMockFormData({
                title: "",
                slug: "",
                description: "",
                short_description: "",
                volunteer_hours: 0,
                min_amount: 0,
                max_amount: 0,
                start_date: undefined,
                end_date: undefined,
                requirements: "",
                benefits: "",
                target_audience: "",
                image_file: null,
                url: "",
                testimonial_ids: [],
                group_ids: [],
                condition_group_ids: [],
                document_type_ids: [],
                defined_conditions: [],
                is_public: true,
                internal_notes: "",
                contact_person: "",
                contact_email: "",
                contact_phone: "",
                response_date: undefined,
                is_active: true
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.statusSection)).toBeInTheDocument();
            });

            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
        });

        it("handles form submission state", async () => {
            setMockFormState({ isSubmitting: true });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });
        });

        it("handles form data with file upload", async () => {
            const mockFile = new File(["test"], "test.webp", { type: "image/webp" });
            setMockFormData({
                image_file: mockFile
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("file-upload-image")).toBeInTheDocument();
            });
        });

        it("handles form data with multiple selections", async () => {
            setMockFormData({
                testimonial_ids: ["testimonial1", "testimonial2"],
                group_ids: ["group1", "group2"],
                condition_group_ids: ["condition1", "condition2"],
                document_type_ids: ["doc1", "doc2"]
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.testimonialsLabel)).toBeInTheDocument();
            });
        });

        it("handles inactive and private scholarship settings", async () => {
            setMockFormData({
                is_active: false,
                is_public: false
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.activityStatusLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.publicationStatusLabel)).toBeInTheDocument();
            });
        });

        it("handles scholarship with defined conditions", async () => {
            setMockFormData({
                defined_conditions: [
                    {
                        question_id: { id: "question1", label: "Age Range" },
                        condition_type: "in" as const,
                        condition_value: ["18-25", "26-35"]
                    }
                ] as any
            });

            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.specificConditionsLabel)).toBeInTheDocument();
            });
        });
    });

    describe("Switch Components", () => {
        it("renders activity and publication status switches", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                const switches = screen.getAllByRole("switch");
                expect(switches).toHaveLength(2);
                expect(screen.getByText("סטטוס פעילות")).toBeInTheDocument();
                expect(screen.getByText("סטטוס פרסום")).toBeInTheDocument();
            });
        });

        it("renders switches that can be toggled", async () => {
            await act(async () => {
                render(<ScholarshipForm />);
            });

            await waitFor(() => {
                const switches = screen.getAllByRole("switch");
                expect(switches).toHaveLength(2);

                switches.forEach((switchElement) => {
                    expect(switchElement).toHaveAttribute("type", "button");
                    expect(switchElement).toHaveAttribute("role", "switch");
                });
            });
        });
    });

    describe("Hook Loading States and Option Conversion", () => {
        // Mock scholarship with linked testimonials and groups
        const mockScholarshipWithLinkedData = {
            ...mockScholarship,
            link_scholarship_to_testimonial: [{ testimonial_id: "testimonial1" }, { testimonial_id: "testimonial2" }],
            link_scholarship_to_scholarship_groups: [
                { scholarship_group_id: "group1" },
                { scholarship_group_id: "group2" }
            ]
        };

        const mockTestimonialsWithMultiple = [
            { id: "testimonial1", name: "John Doe", text: "Great scholarship!" },
            { id: "testimonial2", name: "Jane Smith", text: "Amazing opportunity!" },
            { id: "testimonial3", name: "Bob Wilson", text: "Highly recommended!" }
        ];

        const mockScholarshipGroupsWithMultiple = [
            { id: "group1", title: "Academic", description: "Academic scholarships" },
            { id: "group2", title: "Research", description: "Research scholarships" },
            { id: "group3", title: "Community", description: "Community service scholarships" }
        ];

        it("waits for all hooks to finish loading before processing scholarship data", async () => {
            // Start with all hooks loading
            (useTestimonials as jest.Mock).mockReturnValue({ items: [], loading: true });
            (useScholarshipGroups as jest.Mock).mockReturnValue({ items: [], loading: true });
            (useScholarshipConditionGroups as jest.Mock).mockReturnValue({ items: [], loading: true });
            (useDocumentTypes as jest.Mock).mockReturnValue({ items: [], loading: true });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarshipWithLinkedData
            });

            const { rerender } = render(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Should show loading skeleton
            expect(screen.getByTestId("scholarship-form-skeleton")).toBeInTheDocument();

            // Verify getScholarship is not called yet (still waiting for hooks)
            await waitFor(() => {
                // The scholarship data fetch should be delayed while hooks are loading
                expect(getScholarship).not.toHaveBeenCalled();
            });

            // Update hooks to finish loading
            (useTestimonials as jest.Mock).mockReturnValue({
                items: mockTestimonialsWithMultiple,
                loading: false
            });
            (useScholarshipGroups as jest.Mock).mockReturnValue({
                items: mockScholarshipGroupsWithMultiple,
                loading: false
            });
            (useScholarshipConditionGroups as jest.Mock).mockReturnValue({
                items: mockConditionGroups,
                loading: false
            });
            (useDocumentTypes as jest.Mock).mockReturnValue({
                items: mockDocumentTypes,
                loading: false
            });

            // Re-render to trigger hook updates
            rerender(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Now scholarship data should be fetched
            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );
        });

        it("converts string IDs to proper Option objects with labels when loading scholarship data", async () => {
            (useTestimonials as jest.Mock).mockReturnValue({
                items: mockTestimonialsWithMultiple,
                loading: false
            });
            (useScholarshipGroups as jest.Mock).mockReturnValue({
                items: mockScholarshipGroupsWithMultiple,
                loading: false
            });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarshipWithLinkedData
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );

            // Form should load successfully and show update button
            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles hooks loading in different order", async () => {
            // Start with testimonials loaded but others still loading
            (useTestimonials as jest.Mock).mockReturnValue({
                items: mockTestimonialsWithMultiple,
                loading: false
            });
            (useScholarshipGroups as jest.Mock).mockReturnValue({ items: [], loading: true });
            (useScholarshipConditionGroups as jest.Mock).mockReturnValue({ items: [], loading: true });
            (useDocumentTypes as jest.Mock).mockReturnValue({ items: [], loading: true });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarshipWithLinkedData
            });

            const { rerender } = render(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Should still show loading skeleton
            expect(screen.getByTestId("scholarship-form-skeleton")).toBeInTheDocument();

            // Gradually finish loading other hooks
            (useScholarshipGroups as jest.Mock).mockReturnValue({
                items: mockScholarshipGroupsWithMultiple,
                loading: false
            });
            rerender(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Still loading
            expect(screen.getByTestId("scholarship-form-skeleton")).toBeInTheDocument();

            // Finish loading all hooks
            (useScholarshipConditionGroups as jest.Mock).mockReturnValue({
                items: mockConditionGroups,
                loading: false
            });
            (useDocumentTypes as jest.Mock).mockReturnValue({
                items: mockDocumentTypes,
                loading: false
            });

            rerender(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Now should process data
            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );
        });

        it("handles missing items in Options conversion gracefully", async () => {
            // Mock scholarship with IDs that don't exist in the loaded data
            const scholarshipWithMissingRefs = {
                ...mockScholarship,
                link_scholarship_to_testimonial: [{ testimonial_id: "missing-testimonial" }],
                link_scholarship_to_scholarship_groups: [{ scholarship_group_id: "missing-group" }]
            };

            (useTestimonials as jest.Mock).mockReturnValue({
                items: mockTestimonialsWithMultiple,
                loading: false
            });
            (useScholarshipGroups as jest.Mock).mockReturnValue({
                items: mockScholarshipGroupsWithMultiple,
                loading: false
            });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: scholarshipWithMissingRefs
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );

            // Form should load successfully even with missing references
            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles empty linked data arrays", async () => {
            // Mock scholarship with empty linked arrays
            const scholarshipWithNoLinks = {
                ...mockScholarship,
                link_scholarship_to_testimonial: [],
                link_scholarship_to_scholarship_groups: []
            };

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: scholarshipWithNoLinks
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );

            // Form should load successfully with empty arrays
            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles hooks with empty data arrays", async () => {
            // Set hooks to return empty arrays (but not loading)
            (useTestimonials as jest.Mock).mockReturnValue({ items: [], loading: false });
            (useScholarshipGroups as jest.Mock).mockReturnValue({ items: [], loading: false });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarshipWithLinkedData
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            // Should still proceed with data fetching even with empty arrays
            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );
        });

        it("processes testimonials and groups in correct dependency order", async () => {
            let hookCallOrder: string[] = [];

            (useTestimonials as jest.Mock).mockImplementation(() => {
                hookCallOrder.push("testimonials");
                return { items: mockTestimonialsWithMultiple, loading: false };
            });

            (useScholarshipGroups as jest.Mock).mockImplementation(() => {
                hookCallOrder.push("scholarshipGroups");
                return { items: mockScholarshipGroupsWithMultiple, loading: false };
            });

            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarshipWithLinkedData
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(
                () => {
                    expect(getScholarship).toHaveBeenCalledWith("scholarship-1");
                },
                { timeout: 2000 }
            );

            // Verify hooks were called in expected order
            expect(hookCallOrder).toContain("testimonials");
            expect(hookCallOrder).toContain("scholarshipGroups");
        });
    });

    describe("Form Submission with Option Objects", () => {
        const mockCreateScholarship = jest.requireMock("@/app/actions/scholarship-crud-actions").createScholarship;
        const mockUpdateScholarship = jest.requireMock("@/app/actions/scholarship-crud-actions").updateScholarship;
        const mockLinkScholarshipTestimonials = jest.requireMock(
            "@/app/actions/scholarship-crud-actions"
        ).linkScholarshipTestimonials;
        const mockLinkScholarshipGroups = jest.requireMock(
            "@/app/actions/scholarship-crud-actions"
        ).linkScholarshipGroups;

        beforeEach(() => {
            // Reset form data to use Option objects
            setMockFormData({
                ...mockFormData,
                testimonial_ids: [
                    { id: "testimonial1", label: "John Doe" },
                    { id: "testimonial2", label: "Jane Smith" }
                ] as any,
                group_ids: [
                    { id: "group1", label: "Academic" },
                    { id: "group2", label: "Research" }
                ] as any
            });

            mockCreateScholarship.mockResolvedValue({
                success: true,
                data: { id: "new-scholarship-id" }
            });
            mockUpdateScholarship.mockResolvedValue({ success: true });
            mockLinkScholarshipTestimonials.mockResolvedValue({ success: true });
            mockLinkScholarshipGroups.mockResolvedValue({ success: true });
        });

        it("converts Option objects back to string IDs during form submission in create mode", async () => {
            render(<ScholarshipForm />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });

            await act(async () => {
                submitButton.click();
            });

            await waitFor(() => {
                expect(mockLinkScholarshipTestimonials).toHaveBeenCalledWith("new-scholarship-id", [
                    "testimonial1",
                    "testimonial2"
                ]);
                expect(mockLinkScholarshipGroups).toHaveBeenCalledWith("new-scholarship-id", ["group1", "group2"]);
            });
        });

        it("converts Option objects back to string IDs during form submission in edit mode", async () => {
            (getScholarship as jest.Mock).mockResolvedValue({
                success: true,
                data: mockScholarship
            });

            render(<ScholarshipForm scholarshipId="scholarship-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.updateButtonText });

            await act(async () => {
                submitButton.click();
            });

            await waitFor(() => {
                expect(mockLinkScholarshipTestimonials).toHaveBeenCalledWith("scholarship-1", [
                    "testimonial1",
                    "testimonial2"
                ]);
                expect(mockLinkScholarshipGroups).toHaveBeenCalledWith("scholarship-1", ["group1", "group2"]);
            });
        });

        it("handles empty Option arrays in form submission", async () => {
            setMockFormData({
                ...mockFormData,
                testimonial_ids: [] as any,
                group_ids: [] as any
            });

            render(<ScholarshipForm />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });

            await act(async () => {
                submitButton.click();
            });

            // Should not call linking functions for empty arrays in create mode
            await waitFor(() => {
                expect(mockCreateScholarship).toHaveBeenCalled();
                expect(mockLinkScholarshipTestimonials).not.toHaveBeenCalled();
                expect(mockLinkScholarshipGroups).not.toHaveBeenCalled();
            });
        });

        it("handles Option objects with missing labels gracefully in submission", async () => {
            setMockFormData({
                ...mockFormData,
                testimonial_ids: [
                    { id: "testimonial1", label: "John Doe" },
                    { id: "testimonial2" } // Missing label
                ] as any,
                group_ids: [
                    { id: "group1" }, // Missing label
                    { id: "group2", label: "Research" }
                ] as any
            });

            render(<ScholarshipForm />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });

            await act(async () => {
                submitButton.click();
            });

            await waitFor(() => {
                expect(mockLinkScholarshipTestimonials).toHaveBeenCalledWith("new-scholarship-id", [
                    "testimonial1",
                    "testimonial2"
                ]);
                expect(mockLinkScholarshipGroups).toHaveBeenCalledWith("new-scholarship-id", ["group1", "group2"]);
            });
        });
    });
});
