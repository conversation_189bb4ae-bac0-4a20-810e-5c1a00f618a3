import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FaqForm } from "@/components/forms/faq-form";
import { createFaq, getFaq, updateFaq } from "@/app/actions/faq-actions";
import { toast } from "sonner";
import { TEXTS } from "@/lib/faq-constants";

jest.mock("next/navigation", () => ({
    useRouter: () => ({
        push: jest.fn()
    })
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/faq-actions", () => ({
    createFaq: jest.fn(),
    getFaq: jest.fn(),
    updateFaq: jest.fn()
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text: string }) => <div data-testid="loading-icon">{text}</div>
}));

describe("FaqForm", () => {
    const mockFaq = {
        id: "1",
        question: "Test Question",
        answer: "Test Answer",
        order_index: 1,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z"
    };

    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();

        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe("Create Mode", () => {
        it("renders the create form correctly", () => {
            render(<FaqForm />);

            expect(screen.getByLabelText(TEXTS.QUESTION_LABEL)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.ANSWER_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.CREATE_BUTTON_TEXT)).toBeInTheDocument();
        });

        it("submits the form with valid data", async () => {
            const user = userEvent.setup();
            (createFaq as jest.Mock).mockResolvedValueOnce({ success: true });

            render(<FaqForm />);

            await user.type(screen.getByLabelText(TEXTS.QUESTION_LABEL), "New Question");
            await user.type(screen.getByLabelText(TEXTS.ANSWER_LABEL), "New Answer");
            await user.click(screen.getByText(TEXTS.CREATE_BUTTON_TEXT));

            await waitFor(() => {
                expect(createFaq).toHaveBeenCalledWith({
                    question: "New Question",
                    answer: "New Answer"
                });
                expect(toast.success).toHaveBeenCalled();
            });
        });

        it("handles form submission error", async () => {
            const user = userEvent.setup();
            (createFaq as jest.Mock).mockResolvedValueOnce({
                success: false,
                error: "Error message"
            });

            render(<FaqForm />);

            await user.type(screen.getByLabelText(TEXTS.QUESTION_LABEL), "New Question");
            await user.type(screen.getByLabelText(TEXTS.ANSWER_LABEL), "New Answer");
            await user.click(screen.getByText(TEXTS.CREATE_BUTTON_TEXT));

            await waitFor(() => {
                expect(createFaq).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalled();
            });
        });
    });

    describe("Edit Mode", () => {
        beforeEach(() => {
            (getFaq as jest.Mock).mockImplementation(() =>
                Promise.resolve({
                    success: true,
                    data: mockFaq
                })
            );
        });

        it("renders the edit form with loading state", () => {
            render(<FaqForm faqId="1" />);
            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
        });

        it("loads and displays FAQ data", async () => {
            render(<FaqForm faqId="1" />);
            await waitFor(() => expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument());

            expect(getFaq).toHaveBeenCalledWith("1");
            expect(screen.getByDisplayValue("Test Question")).toBeInTheDocument();
            expect(screen.getByDisplayValue("Test Answer")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.UPDATE_BUTTON_TEXT)).toBeInTheDocument();
        });

        it("handles getFaq error", async () => {
            (getFaq as jest.Mock).mockImplementation(() =>
                Promise.resolve({
                    success: false,
                    error: "Error fetching FAQ"
                })
            );

            render(<FaqForm faqId="1" />);
            await waitFor(() => expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument());

            expect(getFaq).toHaveBeenCalledWith("1");
            expect(toast.error).toHaveBeenCalled();
        });

        it("updates FAQ with valid data", async () => {
            const user = userEvent.setup();
            (updateFaq as jest.Mock).mockResolvedValueOnce({ success: true });

            render(<FaqForm faqId="1" />);
            await waitFor(() => expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument());

            await user.clear(screen.getByLabelText(TEXTS.QUESTION_LABEL));
            await user.type(screen.getByLabelText(TEXTS.QUESTION_LABEL), "Updated Question");
            await user.clear(screen.getByLabelText(TEXTS.ANSWER_LABEL));
            await user.type(screen.getByLabelText(TEXTS.ANSWER_LABEL), "Updated Answer");
            await user.click(screen.getByText(TEXTS.UPDATE_BUTTON_TEXT));

            await waitFor(() => {
                expect(updateFaq).toHaveBeenCalledWith("1", {
                    question: "Updated Question",
                    answer: "Updated Answer"
                });
                expect(toast.success).toHaveBeenCalled();
            });
        });

        it("handles update error", async () => {
            const user = userEvent.setup();
            (updateFaq as jest.Mock).mockResolvedValueOnce({
                success: false,
                error: "Update error"
            });

            render(<FaqForm faqId="1" />);
            await waitFor(() => expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument());

            await user.click(screen.getByText(TEXTS.UPDATE_BUTTON_TEXT));

            await waitFor(() => {
                expect(updateFaq).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalled();
            });
        });

        it("navigates on cancel button click", async () => {
            const user = userEvent.setup();
            const mockPush = jest.fn();

            jest.spyOn(require("next/navigation"), "useRouter").mockImplementation(() => ({
                push: mockPush
            }));

            render(<FaqForm faqId="1" />);
            await waitFor(() => expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument());

            await user.click(screen.getByText(TEXTS.CANCEL_BUTTON_TEXT));

            expect(mockPush).toHaveBeenCalledWith("/admin/faq");
        });
    });
});
