import "@testing-library/jest-dom";

import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";

import { NumberInput, NumberFieldProps, TEXTS as ComponentTEXTS } from "@/components/forms/fields/number-input";

let mockFormState = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        formState: mockFormState
    })
}));

jest.mock("@/components/ui/input", () => ({
    Input: React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(
        ({ className, type, defaultValue, onWheel, ...props }, ref) => (
            <input
                ref={ref}
                type={type}
                className={className}
                data-testid="number-input"
                defaultValue={defaultValue || ""}
                onWheel={onWheel}
                {...props}
            />
        )
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, htmlFor, className }: { children: React.ReactNode; htmlFor?: string; className?: string }) => (
        <label htmlFor={htmlFor} className={className} data-testid="number-label">
            {children}
        </label>
    )
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    jest.clearAllMocks();
};

describe("NumberInput", () => {
    const TEXTS = {
        label: "שדה מספרי",
        placeholder: "הזן מספר",
        defaultValue: 50,
        tooltip: "עזרה נוספת",
        customRequiredText: "שדה חובה מותאם",
        validationError: "שגיאת ולידציה",
        minError: "הערך חייב להיות גדול מ-10",
        maxError: "הערך חייב להיות קטן מ-100",
        errorFromComponent: "שגיאה"
    };

    const defaultProps: NumberFieldProps = {
        name: "testField",
        label: TEXTS.label,
        placeholder: TEXTS.placeholder
    };

    beforeEach(() => {
        resetMocks();
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<NumberInput {...defaultProps} />);
            expect(screen.getByTestId("number-label")).toBeInTheDocument();
            expect(screen.getByTestId("number-input")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.label)).toBeInTheDocument();
        });

        it("renders with a placeholder", () => {
            render(<NumberInput {...defaultProps} />);
            const input = screen.getByTestId("number-input");
            expect(input).toHaveAttribute("placeholder", TEXTS.placeholder);
        });

        it("renders with a default value", () => {
            render(<NumberInput {...defaultProps} defaultValue={TEXTS.defaultValue} />);
            const input = screen.getByTestId("number-input");
            expect(input).toHaveValue(TEXTS.defaultValue);
        });

        it("renders with a tooltip", () => {
            render(<NumberInput {...defaultProps} tooltip={TEXTS.tooltip} />);
            const tooltip = screen.getByTestId("tooltip-icon");
            expect(tooltip).toBeInTheDocument();
            expect(tooltip).toHaveAttribute("title", TEXTS.tooltip);
        });

        it("renders with type='number'", () => {
            render(<NumberInput {...defaultProps} />);
            const input = screen.getByTestId("number-input");
            expect(input).toHaveAttribute("type", "number");
        });
    });

    describe("Event Handling", () => {
        it("blurs input on wheel event", () => {
            render(<NumberInput {...defaultProps} />);
            const input = screen.getByTestId("number-input");
            const blurSpy = jest.spyOn(input, "blur");

            fireEvent.wheel(input);

            expect(blurSpy).toHaveBeenCalledTimes(1);
            blurSpy.mockRestore();
        });
    });

    describe("Form Registration", () => {
        it("registers field with required validation and valueAsNumber by default", () => {
            render(<NumberInput {...defaultProps} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                valueAsNumber: true,
                min: undefined,
                max: undefined
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<NumberInput {...defaultProps} required={false} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: undefined,
                valueAsNumber: true,
                min: undefined,
                max: undefined
            });
        });

        it("registers field with custom required text", () => {
            render(<NumberInput {...defaultProps} requiredText={TEXTS.customRequiredText} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: TEXTS.customRequiredText,
                valueAsNumber: true,
                min: undefined,
                max: undefined
            });
        });

        it("registers field with min validation", () => {
            render(<NumberInput {...defaultProps} min={10} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                valueAsNumber: true,
                min: { value: 10, message: ComponentTEXTS.min(10) },
                max: undefined
            });
        });

        it("registers field with max validation", () => {
            render(<NumberInput {...defaultProps} max={100} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                valueAsNumber: true,
                min: undefined,
                max: { value: 100, message: ComponentTEXTS.max(100) }
            });
        });

        it("registers field with both min and max validation", () => {
            render(<NumberInput {...defaultProps} min={10} max={100} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                valueAsNumber: true,
                min: { value: 10, message: ComponentTEXTS.min(10) },
                max: { value: 100, message: ComponentTEXTS.max(100) }
            });
        });

        it("handles min=0 correctly", () => {
            render(<NumberInput {...defaultProps} min={0} />);
            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                valueAsNumber: true,
                min: { value: 0, message: ComponentTEXTS.min(0) },
                max: undefined
            });
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with a validation error", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.validationError } },
                isSubmitted: true
            });
            render(<NumberInput {...defaultProps} />);
            expect(screen.getByText(TEXTS.validationError)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.validationError)).toHaveClass("text-sm", "text-red-500", "ms-1");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.validationError } },
                isSubmitted: false
            });
            render(<NumberInput {...defaultProps} />);
            expect(screen.queryByText(TEXTS.validationError)).not.toBeInTheDocument();
        });

        it("applies error styling to input when submitted with errors", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.errorFromComponent } },
                isSubmitted: true
            });
            render(<NumberInput {...defaultProps} />);
            const input = screen.getByTestId("number-input");
            expect(input).toHaveClass("border-red-500", "focus-visible:ring-red-500");
        });

        it("does not apply error styling when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.errorFromComponent } },
                isSubmitted: false
            });
            render(<NumberInput {...defaultProps} />);
            const input = screen.getByTestId("number-input");
            expect(input).not.toHaveClass("border-red-500");
            expect(input).not.toHaveClass("focus-visible:ring-red-500");
        });
    });

    describe("RTL Support and Layout", () => {
        it("applies RTL direction to container and input", () => {
            render(<NumberInput {...defaultProps} />);
            const container = screen.getByTestId("number-input").closest("div");
            const input = screen.getByTestId("number-input");
            expect(container).toHaveAttribute("dir", "rtl");
            expect(input).toHaveAttribute("dir", "rtl");
            expect(input).toHaveClass("text-right", "placeholder:text-right");
        });

        it("associates label with input using htmlFor and id", () => {
            render(<NumberInput {...defaultProps} />);
            const label = screen.getByTestId("number-label");
            const input = screen.getByTestId("number-input");
            expect(label).toHaveAttribute("for", "testField");
            expect(input).toHaveAttribute("id", "testField");
        });
    });

    describe("Text Constants", () => {
        it("displays correct default required message from component", () => {
            setMockFormState({
                errors: { testField: { message: ComponentTEXTS.requiredText } },
                isSubmitted: true
            });
            render(<NumberInput {...defaultProps} />);
            expect(screen.getByText(ComponentTEXTS.requiredText)).toBeInTheDocument();
        });

        it("displays correct min message from component", () => {
            const min = 10;
            setMockFormState({
                errors: { testField: { message: ComponentTEXTS.min(min) } },
                isSubmitted: true
            });
            render(<NumberInput {...defaultProps} min={min} />);
            expect(screen.getByText(ComponentTEXTS.min(min))).toBeInTheDocument();
        });

        it("displays correct max message from component", () => {
            const max = 100;
            setMockFormState({
                errors: { testField: { message: ComponentTEXTS.max(max) } },
                isSubmitted: true
            });
            render(<NumberInput {...defaultProps} max={max} />);
            expect(screen.getByText(ComponentTEXTS.max(max))).toBeInTheDocument();
        });
    });
});
