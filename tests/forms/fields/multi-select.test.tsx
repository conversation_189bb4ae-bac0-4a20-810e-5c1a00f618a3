import "@testing-library/jest-dom";

import { act, cleanup, fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";

import { MultiSelect } from "@/components/forms/fields/multi-select";

const mockOptions = [
    { id: "option1", label: "אפשרות 1" },
    { id: "option2", label: "אפשרות 2", subtitle: "תת כותרת" },
    { id: "option3", label: "אפשרות 3" },
    { id: "option4", label: "אפשרות 4" }
];

const mockSetOpen = jest.fn();

let mockFormData: Record<string, Array<{ id: string; label: string }> | null | undefined> = {
    testField: [
        { id: "option1", label: "אפשרות 1" },
        { id: "option2", label: "אפשרות 2" }
    ]
};
let mockFormState = { errors: {}, isSubmitted: false };
let mockSetValue = jest.fn();
let mockRegister = jest.fn();
let mockWatch = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        watch: mockWatch,
        formState: mockFormState
    })
}));

jest.mock("@/components/forms/fields/dropdown-base", () => ({
    DropdownBase: ({
        name,
        label,
        placeholder,
        displayValue,
        children
    }: {
        name: string;
        label: string;
        placeholder: string;
        displayValue?: React.ReactNode;
        children: (filteredOptions: any[], helpers: { setOpen: (open: boolean) => void }) => React.ReactNode;
    }) => (
        <div data-testid={`dropdown-base-${name}`}>
            <label>{label}</label>
            <div data-testid="dropdown-trigger">{displayValue || <span>{placeholder}</span>}</div>
            <div data-testid="dropdown-content">{children(mockOptions, { setOpen: mockSetOpen })}</div>
        </div>
    )
}));

jest.mock("@/components/ui/command", () => ({
    CommandList: ({ children }: { children: React.ReactNode }) => <div data-testid="command-list">{children}</div>,
    CommandGroup: ({ children }: { children: React.ReactNode }) => <div data-testid="command-group">{children}</div>,
    CommandItem: ({
        children,
        onSelect,
        value
    }: {
        children: React.ReactNode;
        onSelect: () => void;
        value: string;
    }) => (
        <div data-testid={`command-item-${value}`} onClick={onSelect} role="option">
            {children}
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Check: ({ className }: { className: string }) => (
        <div data-testid="check-icon" className={className}>
            ✓
        </div>
    ),
    X: ({ className }: { className: string }) => (
        <div data-testid="x-icon" className={className}>
            ✕
        </div>
    )
}));

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormData = {
        testField: [
            { id: "option1", label: "אפשרות 1" },
            { id: "option2", label: "אפשרות 2" }
        ]
    };
    mockFormState = { errors: {}, isSubmitted: false };
    mockWatch.mockImplementation((name: string) => mockFormData[name as keyof typeof mockFormData]);
    jest.clearAllMocks();
};

describe("MultiSelect", () => {
    const defaultProps = {
        name: "testField",
        label: "בחר אפשרויות",
        options: mockOptions
    };

    const TEXTS = {
        required: "יש לבחור לפחות אפשרות אחת",
        placeholder: "בחר אפשרויות"
    };

    beforeEach(() => {
        resetMocks();

        jest.spyOn(console, "error").mockImplementation((message) => {
            if (typeof message === "string" && message.includes("act(")) {
                return;
            }
            console.error(message);
        });
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<MultiSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
            expect(screen.getByText("בחר אפשרויות")).toBeInTheDocument();
        });

        it("renders with custom placeholder", () => {
            const customPlaceholder = "בחר מהרשימה";
            setMockFormData({ testField: [] });
            render(<MultiSelect {...defaultProps} placeholder={customPlaceholder} />);

            expect(screen.getByText(customPlaceholder)).toBeInTheDocument();
        });

        it("renders with tooltip prop", () => {
            render(<MultiSelect {...defaultProps} tooltip="עזרה נוספת" />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });

        it("renders with search functionality", () => {
            render(
                <MultiSelect
                    {...defaultProps}
                    showSearch={true}
                    searchPlaceholder="חפש אפשרות"
                    noResultsText="לא נמצאו תוצאות"
                />
            );

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });
    });

    describe("Options Rendering", () => {
        it("renders dropdown structure correctly", () => {
            render(<MultiSelect {...defaultProps} />);

            expect(screen.getByTestId("command-list")).toBeInTheDocument();
            expect(screen.getByTestId("command-group")).toBeInTheDocument();
        });

        it("renders all command items for options", () => {
            render(<MultiSelect {...defaultProps} />);

            mockOptions.forEach((option) => {
                expect(screen.getByTestId(`command-item-${option.id}`)).toBeInTheDocument();
            });
        });

        it("renders check icons for all options", () => {
            render(<MultiSelect {...defaultProps} />);

            const checkIcons = screen.getAllByTestId("check-icon");
            expect(checkIcons).toHaveLength(mockOptions.length);
        });
    });

    describe("Value Display", () => {
        it("displays selected values as tags in trigger", () => {
            setMockFormData({
                testField: [
                    { id: "option1", label: "אפשרות 1" },
                    { id: "option3", label: "אפשרות 3" }
                ]
            });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent("אפשרות 1");
            expect(trigger).toHaveTextContent("אפשרות 3");
        });

        it("displays placeholder when no values selected", () => {
            setMockFormData({ testField: [] });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent(TEXTS.placeholder);
        });

        it("displays custom placeholder when no values selected", () => {
            const customPlaceholder = "אנא בחר אפשרויות";
            setMockFormData({ testField: [] });

            render(<MultiSelect {...defaultProps} placeholder={customPlaceholder} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent(customPlaceholder);
        });

        it("displays selected values with remove buttons", () => {
            setMockFormData({
                testField: [
                    { id: "option1", label: "אפשרות 1" },
                    { id: "option2", label: "אפשרות 2" }
                ]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButtons = screen.getAllByTestId("x-icon");
            expect(removeButtons).toHaveLength(2);
        });
    });

    describe("Selection Behavior", () => {
        it("adds option to selection when clicked", async () => {
            setMockFormData({ testField: [] });

            render(<MultiSelect {...defaultProps} />);

            const optionElement = screen.getByTestId("command-item-option1");

            await act(async () => {
                fireEvent.click(optionElement);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [{ id: "option1", label: "אפשרות 1" }], {
                shouldValidate: true,
                shouldTouch: true
            });
        });

        it("removes option from selection when already selected", async () => {
            setMockFormData({
                testField: [
                    { id: "option1", label: "אפשרות 1" },
                    { id: "option2", label: "אפשרות 2" }
                ]
            });

            render(<MultiSelect {...defaultProps} />);

            const optionElement = screen.getByTestId("command-item-option1");

            await act(async () => {
                fireEvent.click(optionElement);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [{ id: "option2", label: "אפשרות 2" }], {
                shouldValidate: true,
                shouldTouch: true
            });
        });

        it("handles selecting multiple options", async () => {
            setMockFormData({ testField: [] });

            render(<MultiSelect {...defaultProps} />);

            // Select first option
            const option1Element = screen.getByTestId("command-item-option1");
            await act(async () => {
                fireEvent.click(option1Element);
            });

            // Update mock data to reflect the first selection
            setMockFormData({ testField: [{ id: "option1", label: "אפשרות 1" }] });

            // Select second option
            const option2Element = screen.getByTestId("command-item-option2");
            await act(async () => {
                fireEvent.click(option2Element);
            });

            expect(mockSetValue).toHaveBeenLastCalledWith(
                "testField",
                [
                    { id: "option1", label: "אפשרות 1" },
                    { id: "option2", label: "אפשרות 2" }
                ],
                { shouldValidate: true, shouldTouch: true }
            );
        });

        it("handles selecting option with subtitle", async () => {
            setMockFormData({ testField: [] });

            render(<MultiSelect {...defaultProps} />);

            const optionWithSubtitle = screen.getByTestId("command-item-option2");

            await act(async () => {
                fireEvent.click(optionWithSubtitle);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [{ id: "option2", label: "אפשרות 2" }], {
                shouldValidate: true,
                shouldTouch: true
            });
        });
    });

    describe("Tag Removal", () => {
        it("removes tag when X button is clicked", async () => {
            setMockFormData({
                testField: [
                    { id: "option1", label: "אפשרות 1" },
                    { id: "option2", label: "אפשרות 2" }
                ]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButtons = screen.getAllByTestId("x-icon");

            await act(async () => {
                fireEvent.click(removeButtons[0]);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [{ id: "option2", label: "אפשרות 2" }], {
                shouldValidate: true,
                shouldTouch: true
            });
        });

        it("removes tag without triggering dropdown toggle", async () => {
            setMockFormData({
                testField: [{ id: "option1", label: "אפשרות 1" }]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButton = screen.getByTestId("x-icon");

            await act(async () => {
                fireEvent.click(removeButton);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [], { shouldValidate: true, shouldTouch: true });
        });

        it("handles keyboard navigation for tag removal", async () => {
            setMockFormData({
                testField: [{ id: "option1", label: "אפשרות 1" }]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButton = screen.getByTestId("x-icon").parentElement;

            await act(async () => {
                fireEvent.keyDown(removeButton!, { key: "Enter" });
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [], { shouldValidate: true, shouldTouch: true });
        });

        it("handles space key for tag removal", async () => {
            setMockFormData({
                testField: [{ id: "option1", label: "אפשרות 1" }]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButton = screen.getByTestId("x-icon").parentElement;

            await act(async () => {
                fireEvent.keyDown(removeButton!, { key: " " });
            });

            expect(mockSetValue).toHaveBeenCalledWith("testField", [], { shouldValidate: true, shouldTouch: true });
        });
    });

    describe("Form Validation", () => {
        it("registers field with required validation by default", () => {
            render(<MultiSelect {...defaultProps} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: TEXTS.required,
                validate: expect.any(Function)
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<MultiSelect {...defaultProps} required={false} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: false,
                validate: expect.any(Function)
            });
        });

        it("uses custom required text", () => {
            const customRequiredText = "שדה חובה מותאם";
            render(<MultiSelect {...defaultProps} requiredText={customRequiredText} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: customRequiredText,
                validate: expect.any(Function)
            });
        });

        it("validates that array has at least one item when required", () => {
            render(<MultiSelect {...defaultProps} />);

            const validateFunction = mockRegister.mock.calls[0][1].validate;

            expect(validateFunction([])).toBe(TEXTS.required);
            expect(validateFunction([{ id: "1", label: "Test" }])).toBe(true);
            expect(validateFunction(null)).toBe(TEXTS.required);
            expect(validateFunction(undefined)).toBe(TEXTS.required);
        });

        it("allows empty array when not required", () => {
            render(<MultiSelect {...defaultProps} required={false} />);

            const validateFunction = mockRegister.mock.calls[0][1].validate;

            expect(validateFunction([])).toBe(true);
            expect(validateFunction(null)).toBe(true);
            expect(validateFunction(undefined)).toBe(true);
        });

        it("shows error message when form is submitted with validation error", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.required } },
                isSubmitted: true
            });

            render(<MultiSelect {...defaultProps} />);

            expect(screen.getByText(TEXTS.required)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.required)).toHaveClass("text-red-500");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.required } },
                isSubmitted: false
            });

            render(<MultiSelect {...defaultProps} />);

            expect(screen.queryByText(TEXTS.required)).not.toBeInTheDocument();
        });
    });

    describe("Data Format Support", () => {
        it("handles form values with {id, label} objects", () => {
            const testData = [
                { id: "option1", label: "אפשרות 1" },
                { id: "option2", label: "אפשרות 2" }
            ];
            setMockFormData({ testField: testData });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent("אפשרות 1");
            expect(trigger).toHaveTextContent("אפשרות 2");
        });

        it("filters out invalid data formats", () => {
            const mixedData = [
                { id: "option1", label: "אפשרות 1" },
                "invalid-string",
                { id: "option2", label: "אפשרות 2" },
                { label: "missing-id" },
                null
            ] as any;

            setMockFormData({ testField: mixedData });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent("אפשרות 1");
            expect(trigger).toHaveTextContent("אפשרות 2");
            expect(trigger).not.toHaveTextContent("invalid-string");
        });
    });

    describe("Hidden Input", () => {
        it("renders hidden input for form registration", () => {
            render(<MultiSelect {...defaultProps} />);

            const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
            expect(hiddenInputs).toHaveLength(1);
            expect(hiddenInputs[0]).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty options array", () => {
            render(<MultiSelect {...defaultProps} options={[]} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
            expect(screen.getByTestId("command-group")).toBeInTheDocument();
        });

        it("handles undefined value gracefully", () => {
            setMockFormData({ testField: undefined });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent(TEXTS.placeholder);
        });

        it("handles null value gracefully", () => {
            setMockFormData({ testField: null });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent(TEXTS.placeholder);
        });

        it("handles non-array value gracefully", () => {
            setMockFormData({ testField: "not-an-array" as any });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            expect(trigger).toHaveTextContent(TEXTS.placeholder);
        });

        it("handles selection of non-existent option gracefully", async () => {
            const limitedOptions = [{ id: "option1", label: "אפשרות 1" }];

            render(<MultiSelect {...defaultProps} options={limitedOptions} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper ARIA roles for options", () => {
            render(<MultiSelect {...defaultProps} />);

            mockOptions.forEach((option) => {
                const optionElement = screen.getByTestId(`command-item-${option.id}`);
                expect(optionElement).toHaveAttribute("role", "option");
            });
        });

        it("has proper label association", () => {
            render(<MultiSelect {...defaultProps} />);

            expect(screen.getByText("בחר אפשרויות")).toBeInTheDocument();
        });

        it("has proper tabIndex and role for remove buttons", () => {
            setMockFormData({
                testField: [{ id: "option1", label: "אפשרות 1" }]
            });

            render(<MultiSelect {...defaultProps} />);

            const removeButton = screen.getByTestId("x-icon").parentElement;
            expect(removeButton).toHaveAttribute("role", "button");
            expect(removeButton).toHaveAttribute("tabIndex", "0");
        });
    });

    describe("Component Props", () => {
        it("passes all props to DropdownBase correctly", () => {
            const props = {
                ...defaultProps,
                tooltip: "עזרה",
                searchPlaceholder: "חפש...",
                noResultsText: "אין תוצאות",
                showSearch: true
            };

            render(<MultiSelect {...props} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });

        it("handles all optional props", () => {
            const props = {
                name: "customField",
                label: "תווית מותאמת",
                placeholder: "מקום מחזיק מותאם",
                options: mockOptions,
                required: false,
                requiredText: "הודעת שגיאה מותאמת",
                tooltip: "עזרה מותאמת",
                searchPlaceholder: "חפש מותאם",
                noResultsText: "אין תוצאות מותאם",
                showSearch: true
            };

            setMockFormData({ customField: [] });
            render(<MultiSelect {...props} />);

            expect(screen.getByTestId("dropdown-base-customField")).toBeInTheDocument();
            expect(screen.getByText("תווית מותאמת")).toBeInTheDocument();
        });
    });

    describe("Text Constants", () => {
        it("uses correct Hebrew text constants", () => {
            expect(TEXTS.required).toBe("יש לבחור לפחות אפשרות אחת");
            expect(TEXTS.placeholder).toBe("בחר אפשרויות");
        });

        it("displays Hebrew text in error messages", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.required } },
                isSubmitted: true
            });

            render(<MultiSelect {...defaultProps} />);

            expect(screen.getByText(TEXTS.required)).toBeInTheDocument();
        });
    });

    describe("Mock Watch Function", () => {
        it("watch function returns correct value", () => {
            const testValue = [{ id: "test", label: "Test" }];
            setMockFormData({ testField: testValue });

            render(<MultiSelect {...defaultProps} />);

            expect(mockWatch).toHaveBeenCalledWith("testField");
        });
    });

    describe("Tag Display Styling", () => {
        it("applies correct styling to selected tags", () => {
            setMockFormData({
                testField: [{ id: "option1", label: "אפשרות 1" }]
            });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            const tagContainer = trigger.querySelector(".bg-primary-50");
            expect(tagContainer).toHaveClass(
                "bg-primary-50",
                "border",
                "border-primary-200",
                "rounded-md",
                "px-2",
                "py-1",
                "text-sm",
                "flex",
                "items-center",
                "gap-1.5",
                "my-0.5"
            );
        });

        it("applies scrollable container for many selected tags", () => {
            const manyItems = [
                { id: "option1", label: "אפשרות 1" },
                { id: "option2", label: "אפשרות 2" },
                { id: "option3", label: "אפשרות 3" },
                { id: "option4", label: "אפשרות 4" }
            ];
            setMockFormData({ testField: manyItems });

            render(<MultiSelect {...defaultProps} />);

            const trigger = screen.getByTestId("dropdown-trigger");
            const scrollableContainer = trigger.querySelector(".max-h-32");
            expect(scrollableContainer).toHaveClass("max-h-32", "overflow-y-auto");
        });
    });
});
