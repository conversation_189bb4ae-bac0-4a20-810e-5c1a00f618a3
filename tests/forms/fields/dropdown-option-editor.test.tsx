import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ReactNode } from "react";

import { DropdownOptionEditor, OptionWithId, TEXTS } from "@/components/forms/fields/dropdown-option-editor";

// Mock form state
let mockFormState = { errors: {}, isSubmitted: false };
let mockSetValue = jest.fn();
let mockWatch = jest.fn(() => []);

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        setValue: mockSetValue,
        watch: mockWatch,
        formState: mockFormState
    })
}));

// Mock @dnd-kit modules
jest.mock("@dnd-kit/core", () => ({
    DndContext: ({ children, onDragEnd }: { children: ReactNode; onDragEnd: (event: any) => void }) => (
        <div data-testid="dnd-context" data-ondragend={JSON.stringify(onDragEnd.toString())}>
            {children}
        </div>
    ),
    useSensor: jest.fn(),
    useSensors: jest.fn(() => []),
    PointerSensor: jest.fn(),
    KeyboardSensor: jest.fn(),
    closestCenter: jest.fn()
}));

jest.mock("@dnd-kit/sortable", () => ({
    SortableContext: ({ children }: { children: ReactNode }) => <div data-testid="sortable-context">{children}</div>,
    useSortable: jest.fn(() => ({
        attributes: {},
        listeners: {},
        setNodeRef: jest.fn(),
        transform: null,
        transition: null,
        isDragging: false
    })),
    sortableKeyboardCoordinates: jest.fn(),
    verticalListSortingStrategy: "vertical-list",
    arrayMove: jest.fn().mockImplementation((array, oldIndex, newIndex) => {
        const newArray = [...array];
        const [removed] = newArray.splice(oldIndex, 1);
        newArray.splice(newIndex, 0, removed);
        return newArray;
    })
}));

jest.mock("@dnd-kit/utilities", () => ({
    CSS: {
        Transform: {
            toString: jest.fn(() => "transform: translate3d(0, 0, 0)")
        }
    }
}));

// Mock uuid
jest.mock("uuid", () => ({
    v4: jest.fn(() => "mock-uuid-" + Math.random().toString(36).substr(2, 9))
}));

// Mock UI components
jest.mock("@/components/ui/button", () => ({
    Button: ({ children, onClick, type, variant, className }: any) => (
        <button type={type} onClick={onClick} data-variant={variant} className={className}>
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/input", () => ({
    Input: ({ value, onChange, placeholder, className, dir }: any) => (
        <input
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            className={className}
            dir={dir}
            data-testid="option-input"
        />
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, className }: any) => (
        <label className={className} data-testid="field-label">
            {children}
        </label>
    )
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
    GripVertical: () => <div data-testid="lucide-grip-vertical">≡</div>,
    Plus: () => <div data-testid="lucide-plus">+</div>
}));

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const setMockWatch = (returnValue: any) => {
    mockWatch.mockReturnValue(returnValue);
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    mockWatch.mockReturnValue([]);
    jest.clearAllMocks();
};

describe("DropdownOptionEditor", () => {
    const defaultProps = {
        name: "testOptions"
    };

    beforeEach(() => {
        resetMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    describe("Rendering", () => {
        it("renders without label when no label prop provided", () => {
            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.queryByTestId("field-label")).not.toBeInTheDocument();
            expect(screen.getByText(TEXTS.addOption)).toBeInTheDocument();
        });

        it("renders with label when label prop provided", () => {
            render(<DropdownOptionEditor {...defaultProps} label="Test Label" />);

            expect(screen.getByText("Test Label")).toBeInTheDocument();
        });

        it("renders with required asterisk when required is true", () => {
            render(<DropdownOptionEditor {...defaultProps} label="Required Field" required />);

            expect(screen.getByText("*")).toBeInTheDocument();
        });

        it("renders with tooltip when tooltip prop provided", () => {
            render(<DropdownOptionEditor {...defaultProps} label="With Tooltip" tooltip="Test tooltip" />);

            expect(screen.getByTestId("tooltip-icon")).toBeInTheDocument();
        });

        it("renders empty state when no options", () => {
            setMockWatch([]);
            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();
        });

        it("renders options when form has initial values", () => {
            setMockWatch([
                { id: "1", label: "Option 1" },
                { id: "2", label: "Option 2" }
            ]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByDisplayValue("Option 1")).toBeInTheDocument();
            expect(screen.getByDisplayValue("Option 2")).toBeInTheDocument();
        });

        it("converts string array to OptionWithId format", () => {
            setMockWatch(["String Option 1", "String Option 2"]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByDisplayValue("String Option 1")).toBeInTheDocument();
            expect(screen.getByDisplayValue("String Option 2")).toBeInTheDocument();
        });

        it("handles various option formats", () => {
            setMockWatch([
                "String Option",
                { label: "Object Option" },
                { id: "existing-id", label: "Object with ID" },
                123
            ]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByDisplayValue("String Option")).toBeInTheDocument();
            expect(screen.getByDisplayValue("Object Option")).toBeInTheDocument();
            expect(screen.getByDisplayValue("Object with ID")).toBeInTheDocument();
            expect(screen.getByDisplayValue("123")).toBeInTheDocument();
        });
    });

    describe("Adding Options", () => {
        it("adds new option when add button is clicked", async () => {
            const user = userEvent.setup();
            setMockWatch([]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();

            await user.click(screen.getByText(TEXTS.addOption));

            expect(mockSetValue).toHaveBeenCalled();
        });

        it("adds multiple options", async () => {
            const user = userEvent.setup();
            setMockWatch([]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const addButton = screen.getByText(TEXTS.addOption);

            await user.click(addButton);
            await user.click(addButton);
            await user.click(addButton);

            expect(mockSetValue).toHaveBeenCalledTimes(3);
        });
    });

    describe("Updating Options", () => {
        it("updates option label when input changes", async () => {
            const user = userEvent.setup();
            setMockWatch([{ id: "1", label: "Original Label" }]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const input = screen.getByDisplayValue("Original Label");
            await user.clear(input);
            await user.type(input, "Updated Label");

            expect(mockSetValue).toHaveBeenCalled();
        });

        it("prevents duplicate labels", async () => {
            const user = userEvent.setup();
            setMockWatch([
                { id: "1", label: "Option 1" },
                { id: "2", label: "Option 2" }
            ]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const secondInput = screen.getByDisplayValue("Option 2");
            await user.clear(secondInput);
            await user.type(secondInput, "Option 1");

            // The component should prevent duplicates by not updating
            expect(screen.getAllByDisplayValue("Option 1")).toHaveLength(1);
        });

        it("handles case-insensitive duplicate check", async () => {
            const user = userEvent.setup();
            setMockWatch([
                { id: "1", label: "Option 1" },
                { id: "2", label: "option 2" }
            ]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const secondInput = screen.getByDisplayValue("option 2");
            await user.clear(secondInput);
            await user.type(secondInput, "OPTION 1");

            // The component should prevent duplicates, so the original value should remain
            expect(screen.getByDisplayValue("Option 1")).toBeInTheDocument();
            // The second input should not be fully updated to the duplicate value
            expect(screen.queryByDisplayValue("OPTION 1")).not.toBeInTheDocument();
        });
    });

    describe("Drag and Drop", () => {
        it("renders drag handle for each option", () => {
            setMockWatch([
                { id: "1", label: "Option 1" },
                { id: "2", label: "Option 2" }
            ]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const dragHandles = screen.getAllByTestId("lucide-grip-vertical");
            expect(dragHandles).toHaveLength(2);
        });

        it("renders DndContext and SortableContext", () => {
            setMockWatch([{ id: "1", label: "Option 1" }]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByTestId("dnd-context")).toBeInTheDocument();
            expect(screen.getByTestId("sortable-context")).toBeInTheDocument();
        });
    });

    describe("RTL Support", () => {
        it("applies RTL direction to main container", () => {
            render(<DropdownOptionEditor {...defaultProps} />);

            const mainContainer = screen.getByText(TEXTS.addOption).closest('[dir="rtl"]');
            expect(mainContainer).toBeInTheDocument();
        });

        it("applies RTL classes to input fields", () => {
            setMockWatch([{ id: "1", label: "Option 1" }]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const input = screen.getByDisplayValue("Option 1");
            expect(input).toHaveClass("text-right");
            expect(input).toHaveClass("placeholder:text-right");
            expect(input).toHaveAttribute("dir", "rtl");
        });

        it("applies RTL classes to label", () => {
            render(<DropdownOptionEditor {...defaultProps} label="Test Label" />);

            const label = screen.getByText("Test Label");
            expect(label).toHaveClass("text-right");
        });
    });

    describe("Error Handling", () => {
        it("displays error message when form has error", () => {
            setMockFormState({
                errors: { testOptions: { message: "This field is required" } },
                isSubmitted: true
            });

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText("This field is required")).toBeInTheDocument();
        });

        it("applies error styling to error message", () => {
            setMockFormState({
                errors: { testOptions: { message: "Error message" } },
                isSubmitted: true
            });

            render(<DropdownOptionEditor {...defaultProps} />);

            const errorMessage = screen.getByText("Error message");
            expect(errorMessage).toHaveClass("text-sm", "text-red-500", "text-right");
        });

        it("does not display error when form is not submitted", () => {
            setMockFormState({
                errors: { testOptions: { message: "Error message" } },
                isSubmitted: false
            });

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.queryByText("Error message")).not.toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("associates label with form field", () => {
            render(<DropdownOptionEditor {...defaultProps} label="Options Field" />);

            const label = screen.getByText("Options Field");
            expect(label.tagName).toBe("LABEL");
        });

        it("provides proper button accessibility", () => {
            render(<DropdownOptionEditor {...defaultProps} />);

            // The button includes the plus icon in its accessible name
            const addButton = screen.getByRole("button", { name: `+ ${TEXTS.addOption}` });
            expect(addButton).toHaveAttribute("type", "button");
        });

        it("provides proper input accessibility", () => {
            setMockWatch([{ id: "1", label: "Option 1" }]);

            render(<DropdownOptionEditor {...defaultProps} />);

            const input = screen.getByDisplayValue("Option 1");
            expect(input).toHaveAttribute("placeholder", TEXTS.optionPlaceholder);
        });
    });

    describe("Form Integration", () => {
        it("initializes with empty array when no form value", () => {
            setMockWatch([]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();
        });

        it("calls setValue when options change", async () => {
            const user = userEvent.setup();
            setMockWatch([]);

            render(<DropdownOptionEditor {...defaultProps} />);

            await user.click(screen.getByText(TEXTS.addOption));

            expect(mockSetValue).toHaveBeenCalledWith("testOptions", expect.any(Array), { shouldValidate: true });
        });

        it("handles form validation", () => {
            setMockFormState({
                errors: { testOptions: { message: "At least one option is required" } },
                isSubmitted: true
            });

            render(<DropdownOptionEditor {...defaultProps} required />);

            expect(screen.getByText("At least one option is required")).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles null form value", () => {
            setMockWatch(null);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();
        });

        it("handles undefined form value", () => {
            setMockWatch(undefined);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();
        });

        it("handles empty array form value", () => {
            setMockWatch([]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getByText(TEXTS.noOptions)).toBeInTheDocument();
        });

        it("handles malformed option objects", () => {
            setMockWatch([{ notALabel: "invalid" }, { label: null }, { label: undefined }]);

            render(<DropdownOptionEditor {...defaultProps} />);

            expect(screen.getAllByPlaceholderText(TEXTS.optionPlaceholder)).toHaveLength(3);
        });
    });
});
