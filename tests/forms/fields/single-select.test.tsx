import "@testing-library/jest-dom";

import { act, cleanup, fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";

import { SingleSelect, SingleSelectFieldProps } from "@/components/forms/fields/single-select";

const mockOptions = [
    { id: "option1", label: "אפשרות 1" },
    { id: "option2", label: "אפשרות 2", subtitle: "תת כותרת" },
    { id: "option3", label: "אפשרות 3" }
];

const mockSetOpen = jest.fn();

let mockFormData: Record<string, { id: string; label: string } | null | undefined> = {
    testField: { id: "option1", label: "אפשרות 1" }
};
let mockFormState = { errors: {}, isSubmitted: false };
let mockSetValue = jest.fn();
let mockRegister = jest.fn();
let mockWatch = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        watch: mockWatch,
        formState: mockFormState
    })
}));

jest.mock("@/components/forms/fields/dropdown-base", () => ({
    DropdownBase: ({
        name,
        label,
        placeholder,
        displayValue,
        children
    }: {
        name: string;
        label: string;
        placeholder: string;
        displayValue?: string;
        children: (filteredOptions: any[], helpers: { setOpen: (open: boolean) => void }) => React.ReactNode;
    }) => (
        <div data-testid={`dropdown-base-${name}`}>
            <label>{label}</label>
            <div data-testid="dropdown-trigger">{displayValue || placeholder}</div>
            <div data-testid="dropdown-content">{children(mockOptions, { setOpen: mockSetOpen })}</div>
        </div>
    )
}));

jest.mock("@/components/ui/command", () => ({
    CommandList: ({ children }: { children: React.ReactNode }) => <div data-testid="command-list">{children}</div>,
    CommandGroup: ({ children }: { children: React.ReactNode }) => <div data-testid="command-group">{children}</div>,
    CommandItem: ({
        children,
        onSelect,
        value
    }: {
        children: React.ReactNode;
        onSelect: () => void;
        value: string;
    }) => (
        <div data-testid={`command-item-${value}`} onClick={onSelect} role="option">
            {children}
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Check: ({ className }: { className: string }) => (
        <div data-testid="check-icon" className={className}>
            ✓
        </div>
    )
}));

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormData = { testField: { id: "option1", label: "אפשרות 1" } };
    mockFormState = { errors: {}, isSubmitted: false };
    mockWatch.mockImplementation((name: string) => mockFormData[name as keyof typeof mockFormData]);
    jest.clearAllMocks();
};

describe("SingleSelect", () => {
    const defaultProps: SingleSelectFieldProps = {
        name: "testField",
        label: "בחר אפשרות",
        options: mockOptions
    };

    const TEXTS = {
        required: "יש לבחור אפשרות",
        placeholder: "בחר אפשרות"
    };

    beforeEach(() => {
        resetMocks();

        jest.spyOn(console, "error").mockImplementation((message) => {
            if (typeof message === "string" && message.includes("act(")) {
                return;
            }
            console.error(message);
        });
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
            expect(screen.getByText("בחר אפשרות")).toBeInTheDocument();
        });

        it("renders with custom placeholder", () => {
            const customPlaceholder = "בחר מהרשימה";
            setMockFormData({ testField: undefined });
            render(<SingleSelect {...defaultProps} placeholder={customPlaceholder} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent(customPlaceholder);
        });

        it("renders with tooltip prop", () => {
            render(<SingleSelect {...defaultProps} tooltip="עזרה נוספת" />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });

        it("renders with search functionality", () => {
            render(
                <SingleSelect
                    {...defaultProps}
                    showSearch={true}
                    searchPlaceholder="חפש אפשרות"
                    noResultsText="לא נמצאו תוצאות"
                />
            );

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });
    });

    describe("Options Rendering", () => {
        it("renders dropdown structure correctly", () => {
            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("command-list")).toBeInTheDocument();
            expect(screen.getByTestId("command-group")).toBeInTheDocument();
        });

        it("renders all command items for options", () => {
            render(<SingleSelect {...defaultProps} />);

            mockOptions.forEach((option) => {
                expect(screen.getByTestId(`command-item-${option.id}`)).toBeInTheDocument();
            });
        });

        it("renders check icons for all options", () => {
            render(<SingleSelect {...defaultProps} />);

            const checkIcons = screen.getAllByTestId("check-icon");
            expect(checkIcons).toHaveLength(mockOptions.length);
        });
    });

    describe("Value Display", () => {
        it("displays selected value in trigger", () => {
            setMockFormData({ testField: { id: "option2", label: "אפשרות 2" } });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent("אפשרות 2");
        });

        it("displays placeholder when no value selected", () => {
            setMockFormData({ testField: undefined });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent(TEXTS.placeholder);
        });

        it("displays custom placeholder when no value selected", () => {
            const customPlaceholder = "אנא בחר";
            setMockFormData({ testField: undefined });

            render(<SingleSelect {...defaultProps} placeholder={customPlaceholder} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent(customPlaceholder);
        });
    });

    describe("Selection Behavior", () => {
        it("calls setValue when option is selected", async () => {
            render(<SingleSelect {...defaultProps} />);

            const optionElement = screen.getByTestId("command-item-option2");

            await act(async () => {
                fireEvent.click(optionElement);
            });

            expect(mockSetValue).toHaveBeenCalledWith(
                "testField",
                { id: "option2", label: "אפשרות 2" },
                { shouldValidate: true, shouldTouch: true }
            );
        });

        it("calls setOpen with false after selection", async () => {
            render(<SingleSelect {...defaultProps} />);

            const optionElement = screen.getByTestId("command-item-option3");

            await act(async () => {
                fireEvent.click(optionElement);
            });

            expect(mockSetOpen).toHaveBeenCalledWith(false);
        });

        it("handles selecting option with subtitle", async () => {
            render(<SingleSelect {...defaultProps} />);

            const optionWithSubtitle = screen.getByTestId("command-item-option2");

            await act(async () => {
                fireEvent.click(optionWithSubtitle);
            });

            expect(mockSetValue).toHaveBeenCalledWith(
                "testField",
                { id: "option2", label: "אפשרות 2" },
                { shouldValidate: true, shouldTouch: true }
            );
        });
    });

    describe("Form Validation", () => {
        it("registers field with required validation by default", () => {
            render(<SingleSelect {...defaultProps} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: TEXTS.required
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<SingleSelect {...defaultProps} required={false} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: undefined
            });
        });

        it("uses custom required text", () => {
            const customRequiredText = "שדה חובה מותאם";
            render(<SingleSelect {...defaultProps} requiredText={customRequiredText} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: customRequiredText
            });
        });

        it("shows error message when form is submitted with validation error", () => {
            setMockFormState({
                errors: { testField: { message: "יש לבחור אפשרות" } },
                isSubmitted: true
            });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByText("יש לבחור אפשרות")).toBeInTheDocument();
            expect(screen.getByText("יש לבחור אפשרות")).toHaveClass("text-red-500");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: "יש לבחור אפשרות" } },
                isSubmitted: false
            });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.queryByText("יש לבחור אפשרות")).not.toBeInTheDocument();
        });

        it("does not show error message when there are no errors", () => {
            setMockFormState({
                errors: {},
                isSubmitted: true
            });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.queryByText("יש לבחור אפשרות")).not.toBeInTheDocument();
        });
    });

    describe("Hidden Input", () => {
        it("renders hidden input for form registration", () => {
            render(<SingleSelect {...defaultProps} />);

            const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
            expect(hiddenInputs).toHaveLength(1);
            expect(hiddenInputs[0]).toBeInTheDocument();
        });
    });

    describe("Edge Cases", () => {
        it("handles empty options array", () => {
            render(<SingleSelect {...defaultProps} options={[]} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
            expect(screen.getByTestId("command-group")).toBeInTheDocument();
        });

        it("handles undefined value gracefully", () => {
            setMockFormData({ testField: undefined });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent(TEXTS.placeholder);
        });

        it("handles null value gracefully", () => {
            setMockFormData({ testField: null });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByTestId("dropdown-trigger")).toHaveTextContent(TEXTS.placeholder);
        });

        it("handles selection of non-existent option gracefully", async () => {
            const limitedOptions = [{ id: "option1", label: "אפשרות 1" }];

            render(<SingleSelect {...defaultProps} options={limitedOptions} />);

            // The component should handle this case gracefully
            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper ARIA roles for options", () => {
            render(<SingleSelect {...defaultProps} />);

            mockOptions.forEach((option) => {
                const optionElement = screen.getByTestId(`command-item-${option.id}`);
                expect(optionElement).toHaveAttribute("role", "option");
            });
        });

        it("has proper label association", () => {
            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByText("בחר אפשרות")).toBeInTheDocument();
        });
    });

    describe("Component Props", () => {
        it("passes all props to DropdownBase correctly", () => {
            const props = {
                ...defaultProps,
                tooltip: "עזרה",
                searchPlaceholder: "חפש...",
                noResultsText: "אין תוצאות",
                showSearch: true
            };

            render(<SingleSelect {...props} />);

            expect(screen.getByTestId("dropdown-base-testField")).toBeInTheDocument();
        });

        it("handles all optional props", () => {
            const props = {
                name: "customField",
                label: "תווית מותאמת",
                placeholder: "מקום מחזיק מותאם",
                options: mockOptions,
                required: false,
                requiredText: "הודעת שגיאה מותאמת",
                tooltip: "עזרה מותאמת",
                searchPlaceholder: "חפש מותאם",
                noResultsText: "אין תוצאות מותאם",
                showSearch: true
            };

            setMockFormData({ customField: undefined });
            render(<SingleSelect {...props} />);

            expect(screen.getByTestId("dropdown-base-customField")).toBeInTheDocument();
            expect(screen.getByText("תווית מותאמת")).toBeInTheDocument();
        });
    });

    describe("Text Constants", () => {
        it("uses correct Hebrew text constants", () => {
            expect(TEXTS.required).toBe("יש לבחור אפשרות");
            expect(TEXTS.placeholder).toBe("בחר אפשרות");
        });

        it("displays Hebrew text in error messages", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.required } },
                isSubmitted: true
            });

            render(<SingleSelect {...defaultProps} />);

            expect(screen.getByText(TEXTS.required)).toBeInTheDocument();
        });
    });

    describe("Mock Watch Function", () => {
        it("watch function returns correct value", () => {
            const testValue = { id: "test", label: "Test" };
            setMockFormData({ testField: testValue });

            render(<SingleSelect {...defaultProps} />);

            expect(mockWatch).toHaveBeenCalledWith("testField");
        });
    });
});
