import "@testing-library/jest-dom";

import { act, cleanup, fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";

import { AddressSelect, AddressFieldProps } from "@/components/forms/fields/address-select";

const mockSuggestions = [
    {
        place_name: "תל אביב - יפו, ישראל",
        center: [34.7818, 32.0853]
    },
    {
        place_name: "חיפה, ישראל",
        center: [34.9896, 32.794]
    },
    {
        place_name: "ירושלים, ישראל",
        center: [35.2137, 31.7683]
    }
];

let mockFormState = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();
let mockSetValue = jest.fn();

global.fetch = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        formState: mockFormState
    })
}));

jest.mock("@/components/ui/button", () => ({
    Button: React.forwardRef<
        HTMLButtonElement,
        React.ButtonHTMLAttributes<HTMLButtonElement> & { variant?: string; role?: string }
    >(({ children, className, variant, role, ...props }, ref) => (
        <button
            ref={ref}
            className={className}
            data-testid="address-button"
            data-variant={variant}
            role={role}
            {...props}
        >
            {children}
        </button>
    ))
}));

jest.mock("@/components/ui/popover", () => ({
    Popover: ({
        children,
        open,
        onOpenChange
    }: {
        children: React.ReactNode;
        open: boolean;
        onOpenChange: (open: boolean) => void;
    }) => (
        <div data-testid="popover" data-open={open} onClick={() => onOpenChange(!open)}>
            {children}
        </div>
    ),
    PopoverTrigger: ({ children, asChild }: { children: React.ReactNode; asChild?: boolean }) => (
        <div data-testid="popover-trigger" data-as-child={asChild}>
            {children}
        </div>
    ),
    PopoverContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="popover-content" className={className}>
            {children}
        </div>
    )
}));

jest.mock("@/components/ui/command", () => ({
    Command: ({
        children,
        dir,
        shouldFilter,
        className
    }: {
        children: React.ReactNode;
        dir?: string;
        shouldFilter?: boolean;
        className?: string;
    }) => (
        <div data-testid="command" dir={dir} data-should-filter={shouldFilter} className={className}>
            {children}
        </div>
    ),
    CommandInput: ({
        placeholder,
        value,
        onValueChange,
        className
    }: {
        placeholder?: string;
        value?: string;
        onValueChange?: (value: string) => void;
        className?: string;
    }) => (
        <input
            data-testid="command-input"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onValueChange?.(e.target.value)}
            className={className}
        />
    ),
    CommandList: ({ children }: { children: React.ReactNode }) => <div data-testid="command-list">{children}</div>,
    CommandEmpty: ({ children }: { children: React.ReactNode }) => <div data-testid="command-empty">{children}</div>,
    CommandGroup: ({ children }: { children: React.ReactNode }) => <div data-testid="command-group">{children}</div>,
    CommandItem: ({
        children,
        onSelect,
        value,
        className
    }: {
        children: React.ReactNode;
        onSelect?: () => void;
        value?: string;
        className?: string;
    }) => (
        <div data-testid={`command-item-${value}`} onClick={onSelect} className={className} role="option">
            {children}
        </div>
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, htmlFor, className }: { children: React.ReactNode; htmlFor?: string; className?: string }) => (
        <label htmlFor={htmlFor} className={className} data-testid="address-label">
            {children}
        </label>
    )
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Check: ({ className }: { className?: string }) => (
        <div data-testid="check-icon" className={className}>
            ✓
        </div>
    ),
    MapPin: ({ className }: { className?: string }) => (
        <div data-testid="map-pin-icon" className={className}>
            📍
        </div>
    )
}));

jest.mock("@/lib/utils", () => ({
    cn: (...classes: (string | boolean | undefined)[]) => classes.filter(Boolean).join(" ")
}));

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
};

describe("AddressSelect", () => {
    const TEXTS = {
        label: "כתובת",
        placeholder: "הזן כתובת",
        customPlaceholder: "הזן את הכתובת שלך",
        defaultValue: "תל אביב - יפו, ישראל",
        tooltip: "עזרה בבחירת כתובת",
        requiredText: "יש להזין כתובת",
        customRequiredText: "כתובת חובה",
        validationError: "שגיאת ולידציה",
        longAddress: "רחוב הרצל 1, תל אביב - יפו, מחוז תל אביב, ישראל",
        searchQuery: "תל אביב",
        partialQuery: "ת"
    };

    const defaultProps: AddressFieldProps = {
        name: "address",
        label: TEXTS.label,
        placeholder: TEXTS.placeholder
    };

    beforeEach(() => {
        resetMocks();

        (global.fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: () =>
                Promise.resolve({
                    features: mockSuggestions
                })
        });

        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<AddressSelect {...defaultProps} />);

            expect(screen.getByTestId("address-label")).toBeInTheDocument();
            expect(screen.getByTestId("address-button")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.label)).toBeInTheDocument();
            expect(screen.getByTestId("map-pin-icon")).toBeInTheDocument();
        });

        it("renders with custom placeholder", () => {
            render(<AddressSelect {...defaultProps} placeholder={TEXTS.customPlaceholder} />);

            const hiddenInput = document.querySelector('input[type="hidden"]');
            expect(hiddenInput).toHaveAttribute("placeholder", TEXTS.customPlaceholder);
        });

        it("renders with default value", () => {
            render(<AddressSelect {...defaultProps} defaultValue={TEXTS.defaultValue} />);

            expect(screen.getByText(TEXTS.defaultValue)).toBeInTheDocument();
        });

        it("renders with tooltip", () => {
            render(<AddressSelect {...defaultProps} tooltip={TEXTS.tooltip} />);

            const tooltip = screen.getByTestId("tooltip-icon");
            expect(tooltip).toBeInTheDocument();
            expect(tooltip).toHaveAttribute("title", TEXTS.tooltip);
        });

        it("displays placeholder with muted styling when no value", () => {
            render(<AddressSelect {...defaultProps} />);

            const button = screen.getByTestId("address-button");
            const placeholderSpan = button.querySelector("span");
            expect(placeholderSpan).toHaveClass("text-muted-foreground");
        });

        it("does not apply muted styling when value exists", () => {
            render(<AddressSelect {...defaultProps} defaultValue={TEXTS.defaultValue} />);

            const button = screen.getByTestId("address-button");
            const valueSpan = button.querySelector("span");
            expect(valueSpan).not.toHaveClass("text-muted-foreground");
        });
    });

    describe("Search Functionality", () => {
        it("makes API call when searching with valid query", async () => {
            render(<AddressSelect {...defaultProps} />);

            const input = screen.getByTestId("command-input");

            await act(async () => {
                fireEvent.change(input, { target: { value: TEXTS.searchQuery } });
            });

            await waitFor(() => {
                expect(global.fetch).toHaveBeenCalledWith(
                    `/api/geocode?query=${encodeURIComponent(TEXTS.searchQuery)}&language=he`
                );
            });
        });

        it("does not make API call for queries shorter than 2 characters", async () => {
            render(<AddressSelect {...defaultProps} />);

            const input = screen.getByTestId("command-input");

            await act(async () => {
                fireEvent.change(input, { target: { value: TEXTS.partialQuery } });
            });

            expect(global.fetch).not.toHaveBeenCalled();
        });

        it("displays suggestions after successful API call", async () => {
            render(<AddressSelect {...defaultProps} />);

            const input = screen.getByTestId("command-input");

            await act(async () => {
                fireEvent.change(input, { target: { value: TEXTS.searchQuery } });
            });

            await waitFor(() => {
                mockSuggestions.forEach((suggestion) => {
                    expect(screen.getByTestId(`command-item-${suggestion.place_name}`)).toBeInTheDocument();
                });
            });
        });

        it("handles API error gracefully", async () => {
            (global.fetch as jest.Mock).mockRejectedValue(new Error("API Error"));

            render(<AddressSelect {...defaultProps} />);

            const input = screen.getByTestId("command-input");

            await act(async () => {
                fireEvent.change(input, { target: { value: TEXTS.searchQuery } });
            });

            await waitFor(() => {
                expect(screen.getByText("לא נמצאו תוצאות")).toBeInTheDocument();
            });
        });
    });

    describe("Selection Behavior", () => {
        it("sets value when suggestion is selected", async () => {
            render(<AddressSelect {...defaultProps} />);

            const input = screen.getByTestId("command-input");

            await act(async () => {
                fireEvent.change(input, { target: { value: TEXTS.searchQuery } });
            });

            await waitFor(() => {
                expect(screen.getByTestId(`command-item-${mockSuggestions[0].place_name}`)).toBeInTheDocument();
            });

            const firstSuggestion = screen.getByTestId(`command-item-${mockSuggestions[0].place_name}`);

            await act(async () => {
                fireEvent.click(firstSuggestion);
            });

            expect(mockSetValue).toHaveBeenCalledWith("address", mockSuggestions[0].place_name);
        });
    });

    describe("Form Registration", () => {
        it("registers field with required validation by default", () => {
            render(<AddressSelect {...defaultProps} />);

            expect(mockRegister).toHaveBeenCalledWith("address", {
                required: "יש להזין כתובת"
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<AddressSelect {...defaultProps} required={false} />);

            expect(mockRegister).toHaveBeenCalledWith("address", {
                required: undefined
            });
        });

        it("uses custom required text", () => {
            render(<AddressSelect {...defaultProps} requiredText={TEXTS.customRequiredText} />);

            expect(mockRegister).toHaveBeenCalledWith("address", {
                required: TEXTS.customRequiredText
            });
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with validation error", () => {
            setMockFormState({
                errors: { address: { message: TEXTS.validationError } },
                isSubmitted: true
            });

            render(<AddressSelect {...defaultProps} />);

            expect(screen.getByText(TEXTS.validationError)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.validationError)).toHaveClass("text-sm", "text-red-500", "text-right");
        });

        it("applies error styling to button when submitted with errors", () => {
            setMockFormState({
                errors: { address: { message: TEXTS.validationError } },
                isSubmitted: true
            });

            render(<AddressSelect {...defaultProps} />);

            const button = screen.getByTestId("address-button");
            expect(button).toHaveClass("border-red-500", "focus-visible:ring-red-500");
        });
    });

    describe("RTL Support", () => {
        it("applies RTL direction to container", () => {
            render(<AddressSelect {...defaultProps} />);

            const mainContainer = document.querySelector(".space-y-2");
            expect(mainContainer).toHaveAttribute("dir", "rtl");
        });

        it("applies RTL styling to button", () => {
            render(<AddressSelect {...defaultProps} />);

            const button = screen.getByTestId("address-button");
            expect(button).toHaveClass("justify-between", "text-right");
        });
    });
});
