import "@testing-library/jest-dom";

import { cleanup, render, screen } from "@testing-library/react";
import React from "react";

import { NumberRange, NumberRangeProps } from "@/components/forms/fields/number-range";
import { TEXTS as ComponentTEXTS } from "@/components/forms/fields/number-range";

let mockFormState: any = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();
let mockWatch = jest.fn();
let mockSetValue = jest.fn();
let mockClearErrors = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        watch: mockWatch,
        setValue: mockSetValue,
        clearErrors: mockClearErrors,
        formState: mockFormState
    })
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: jest.fn(({ name, label, required, placeholder }) => (
        <div data-testid={`number-input-${name}`}>
            <label>{label}</label>
            <input name={name} placeholder={placeholder} data-required={required} />
        </div>
    ))
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children }: { children: React.ReactNode }) => <label data-testid="range-label">{children}</label>
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

const { NumberInput: MockedNumberInput } = require("@/components/forms/fields/number-input");

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    mockRegister.mockClear();
    mockWatch.mockClear();
    mockSetValue.mockClear();
    mockClearErrors.mockClear();
    MockedNumberInput.mockClear();
};

describe("NumberRange", () => {
    const TEXTS = {
        label: "טווח מספרים",
        tooltip: "עזרה נוספת",
        minLabel: "מ-",
        maxLabel: "עד-",
        minPlaceholder: "ערך מינימלי",
        maxPlaceholder: "ערך מקסימלי",
        minSmallerThanMaxError: "המינימום חייב להיות קטן מהמקסימום"
    };

    const defaultProps: NumberRangeProps = {
        name: "testRange",
        label: TEXTS.label
    };

    beforeEach(() => {
        resetMocks();
    });

    afterEach(() => {
        cleanup();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<NumberRange {...defaultProps} />);
            expect(screen.getByTestId("range-label")).toHaveTextContent(TEXTS.label);
            expect(screen.getByTestId("number-input-testRange.min")).toBeInTheDocument();
            expect(screen.getByTestId("number-input-testRange.max")).toBeInTheDocument();
        });

        it("renders with a tooltip", () => {
            render(<NumberRange {...defaultProps} tooltip={TEXTS.tooltip} />);
            const tooltip = screen.getByTestId("tooltip-icon");
            expect(tooltip).toBeInTheDocument();
            expect(tooltip).toHaveAttribute("title", TEXTS.tooltip);
        });

        it("renders with default labels and placeholders", () => {
            render(<NumberRange {...defaultProps} />);

            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                1,
                expect.objectContaining({
                    label: ComponentTEXTS.min,
                    placeholder: ComponentTEXTS.minPlaceholder
                }),
                undefined
            );
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                2,
                expect.objectContaining({
                    label: ComponentTEXTS.max,
                    placeholder: ComponentTEXTS.maxPlaceholder
                }),
                undefined
            );
        });

        it("renders with custom labels and placeholders", () => {
            render(
                <NumberRange
                    {...defaultProps}
                    minLabel={TEXTS.minLabel}
                    maxLabel={TEXTS.maxLabel}
                    minPlaceholder={TEXTS.minPlaceholder}
                    maxPlaceholder={TEXTS.maxPlaceholder}
                />
            );

            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                1,
                expect.objectContaining({ label: TEXTS.minLabel, placeholder: TEXTS.minPlaceholder }),
                undefined
            );
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                2,
                expect.objectContaining({ label: TEXTS.maxLabel, placeholder: TEXTS.maxPlaceholder }),
                undefined
            );
        });

        it("passes required prop to NumberInput components", () => {
            render(<NumberRange {...defaultProps} required={true} />);
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                1,
                expect.objectContaining({ required: true }),
                undefined
            );
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                2,
                expect.objectContaining({ required: true }),
                undefined
            );
            expect(MockedNumberInput).toHaveBeenCalledTimes(2);
        });

        it("uses custom minName and maxName for NumberInput components", () => {
            render(<NumberRange {...defaultProps} minName="customMin" maxName="customMax" />);
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                1,
                expect.objectContaining({ name: "customMin" }),
                undefined
            );
            expect(MockedNumberInput).toHaveBeenNthCalledWith(
                2,
                expect.objectContaining({ name: "customMax" }),
                undefined
            );
        });
    });

    describe("Form Integration and Validation", () => {
        it("registers the main field with a validation function", () => {
            render(<NumberRange {...defaultProps} />);
            expect(mockRegister).toHaveBeenCalledWith("testRange", {
                validate: expect.any(Function)
            });
        });

        describe("validation logic", () => {
            it("returns true if min <= max", () => {
                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 5 : 10));
                render(<NumberRange {...defaultProps} />);
                const validateFn = mockRegister.mock.calls[0][1].validate;
                expect(validateFn(null)).toBe(true);
            });

            it("returns true if min === max", () => {
                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 10 : 10));
                render(<NumberRange {...defaultProps} />);
                const validateFn = mockRegister.mock.calls[0][1].validate;
                expect(validateFn(null)).toBe(true);
            });

            it("returns error message if min > max", () => {
                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 11 : 10));
                render(<NumberRange {...defaultProps} />);
                const validateFn = mockRegister.mock.calls[0][1].validate;
                expect(validateFn(null)).toBe(ComponentTEXTS.minSmallerThanMax);
            });

            it("returns custom error message if min > max", () => {
                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 11 : 10));
                render(<NumberRange {...defaultProps} minSmallerThanMaxText={TEXTS.minSmallerThanMaxError} />);
                const validateFn = mockRegister.mock.calls[0][1].validate;
                expect(validateFn(null)).toBe(TEXTS.minSmallerThanMaxError);
            });

            it("returns true if one or both values are undefined or empty", () => {
                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 5 : undefined));
                const { rerender } = render(<NumberRange {...defaultProps} />);
                let validateFn = mockRegister.mock.calls[mockRegister.mock.calls.length - 1][1].validate;
                expect(validateFn(null)).toBe(true);

                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? "" : 10));
                rerender(<NumberRange {...defaultProps} />);
                validateFn = mockRegister.mock.calls[mockRegister.mock.calls.length - 1][1].validate;
                expect(validateFn(null)).toBe(true);

                mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? undefined : undefined));
                rerender(<NumberRange {...defaultProps} />);
                validateFn = mockRegister.mock.calls[mockRegister.mock.calls.length - 1][1].validate;
                expect(validateFn(null)).toBe(true);
            });
        });

        it("sets combined value on mount and when sub-fields change", () => {
            mockWatch.mockImplementation((name: string) => {
                if (name.endsWith(".min")) return 10;
                if (name.endsWith(".max")) return 20;
            });
            const { rerender } = render(<NumberRange {...defaultProps} />);
            expect(mockSetValue).toHaveBeenCalledWith("testRange", { min: 10, max: 20 }, { shouldValidate: true });

            mockWatch.mockImplementation((name: string) => {
                if (name.endsWith(".min")) return 15;
                if (name.endsWith(".max")) return 20;
            });
            rerender(<NumberRange {...defaultProps} />);
            expect(mockSetValue).toHaveBeenCalledWith("testRange", { min: 15, max: 20 }, { shouldValidate: true });
        });

        it("sets value to undefined if both fields are empty", () => {
            mockWatch.mockImplementation(() => "");
            render(<NumberRange {...defaultProps} />);
            expect(mockSetValue).toHaveBeenCalledWith("testRange", undefined);
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with a validation error", () => {
            setMockFormState({
                errors: { testRange: { message: TEXTS.minSmallerThanMaxError } },
                isSubmitted: true
            });
            render(<NumberRange {...defaultProps} />);
            const errorElement = screen.getByText(TEXTS.minSmallerThanMaxError);
            expect(errorElement).toBeInTheDocument();
            expect(errorElement).toHaveClass("text-sm text-red-500 ms-1");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { testRange: { message: TEXTS.minSmallerThanMaxError } },
                isSubmitted: false
            });
            render(<NumberRange {...defaultProps} />);
            expect(screen.queryByText(TEXTS.minSmallerThanMaxError)).not.toBeInTheDocument();
        });

        it("clears error for main field when sub-field changes", () => {
            setMockFormState({ errors: { testRange: { message: "error" } } });
            mockWatch.mockImplementation((name: string) => (name.endsWith(".min") ? 1 : 2));
            render(<NumberRange {...defaultProps} />);
            expect(mockClearErrors).toHaveBeenCalledWith("testRange");
        });
    });
});
