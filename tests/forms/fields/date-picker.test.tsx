import "@testing-library/jest-dom";

import { act, cleanup, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";

import { DatePicker, DateFieldProps, TEXTS } from "@/components/forms/fields/date-picker";
import { Calendar } from "@/components/ui/calendar";

let mockFormState: { errors: object; isSubmitted: boolean };
let mockSetValue: jest.SpyInstance;
let mockWatch: jest.SpyInstance;
let mockSetError: jest.SpyInstance;
let mockClearErrors: jest.SpyInstance;

jest.mock("@/components/ui/calendar");
const mockedCalendar = jest.mocked(Calendar);

jest.mock("react-hook-form", () => {
    const originalModule = jest.requireActual("react-hook-form");
    return {
        ...originalModule,
        useFormContext: () => ({
            setValue: mockSetValue,
            watch: mockWatch,
            setError: mockSetError,
            clearErrors: mockClearErrors,
            formState: mockFormState
        })
    };
});

jest.mock("lucide-react", () => ({
    Calendar: () => <div data-testid="calendar-icon" />,
    __esModule: true
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

const TestWrapper = ({
    children,
    defaultValues
}: {
    children: React.ReactNode;
    defaultValues?: { [key: string]: any };
}) => {
    const methods = useForm({ defaultValues });
    mockSetValue = jest.spyOn(methods, "setValue");
    mockWatch = jest.spyOn(methods, "watch");
    mockSetError = jest.spyOn(methods, "setError");
    mockClearErrors = jest.spyOn(methods, "clearErrors");
    return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("DatePicker", () => {
    const LABEL = "תאריך";
    const TOOLTIP = "בחר תאריך";

    const defaultProps: DateFieldProps = {
        name: "testDate",
        label: LABEL
    };

    beforeEach(() => {
        mockFormState = { errors: {}, isSubmitted: false };
        if (mockSetValue) mockSetValue.mockClear();
        if (mockWatch) mockWatch.mockClear();
        if (mockSetError) mockSetError.mockClear();
        if (mockClearErrors) mockClearErrors.mockClear();
        mockedCalendar.mockClear();
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    const renderComponent = (props: Partial<DateFieldProps> = {}, defaultValues?: object) => {
        return render(
            <TestWrapper defaultValues={defaultValues}>
                <DatePicker {...defaultProps} {...props} />
            </TestWrapper>
        );
    };

    describe("Single Mode", () => {
        it("renders with label and placeholder", async () => {
            renderComponent();
            expect(screen.getByText(LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.placeholder)).toBeInTheDocument();
            expect(screen.getByTestId("calendar-icon")).toBeInTheDocument();
        });

        it("opens popover on click", async () => {
            renderComponent();
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);
            expect(mockedCalendar).toHaveBeenCalled();
        });

        it("calls setValue on date selection", async () => {
            renderComponent();
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);

            const calendarProps = mockedCalendar.mock.lastCall![0] as any;
            const onSelect = calendarProps.onSelect;
            const testDate = new Date(2023, 10, 15);

            await act(async () => {
                onSelect(testDate);
            });

            expect(mockSetValue).toHaveBeenCalledWith("testDate", testDate, { shouldValidate: true });
        });
    });

    describe("Range Mode", () => {
        const rangeProps: DateFieldProps = { ...defaultProps, mode: "range" };

        it("renders with placeholder", async () => {
            renderComponent(rangeProps);
            expect(screen.getByText(TEXTS.placeholder)).toBeInTheDocument();
        });

        it("calls setValue on range selection", async () => {
            renderComponent(rangeProps);
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);

            const calendarProps = mockedCalendar.mock.lastCall![0] as any;
            const onSelect = calendarProps.onSelect;
            const fromDate = new Date(2023, 10, 15);
            const toDate = new Date(2023, 10, 20);

            await act(async () => {
                onSelect({ from: fromDate, to: toDate });
            });

            expect(mockSetValue).toHaveBeenCalledWith(
                "testDate",
                { from: fromDate, to: toDate },
                { shouldValidate: true }
            );
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with error", () => {
            mockFormState = {
                errors: { testDate: { message: TEXTS.invalid } },
                isSubmitted: true
            };
            renderComponent();
            expect(screen.getByText(TEXTS.invalid)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.invalid)).toHaveClass("text-sm", "text-red-500", "ms-1");
        });

        it("applies error styling to button when submitted with error", () => {
            mockFormState = {
                errors: { testDate: { message: TEXTS.invalid } },
                isSubmitted: true
            };
            renderComponent();
            const button = screen.getByRole("button");
            expect(button).toHaveClass("border-red-500", "text-red-500");
        });
    });

    describe("Props and Edge Cases", () => {
        it("renders with a tooltip", () => {
            renderComponent({ tooltip: TOOLTIP });
            expect(screen.getByTestId("tooltip-icon")).toBeInTheDocument();
            expect(screen.getByTestId("tooltip-icon")).toHaveAttribute("title", TOOLTIP);
        });

        it("disables future dates when isFutureAllowed is false", async () => {
            renderComponent({ isFutureAllowed: false });
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);
            const calendarProps = mockedCalendar.mock.lastCall![0];
            expect(calendarProps.disabled).toBeInstanceOf(Function);
        });

        it("disables past dates when allowPast is false", async () => {
            renderComponent({ allowPast: false });
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);
            const calendarProps = mockedCalendar.mock.lastCall![0];
            expect(calendarProps.disabled).toBeInstanceOf(Function);

            // Test the disabled function
            const disabledFn = calendarProps.disabled as (date: Date) => boolean;
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            expect(disabledFn(yesterday)).toBe(true);

            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            expect(disabledFn(tomorrow)).toBe(false);
        });

        it("shows validation error for past dates when allowPast is false", async () => {
            renderComponent({ allowPast: false, pastDateText: "Past date not allowed" });
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);

            const calendarProps = mockedCalendar.mock.lastCall![0] as any;
            const onSelect = calendarProps.onSelect;
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);

            await act(async () => {
                onSelect(yesterday);
            });

            // Should call setValue with the date
            expect(mockSetValue).toHaveBeenCalledWith("testDate", yesterday, { shouldValidate: true });

            // Should call setError with the validation message
            expect(mockSetError).toHaveBeenCalledWith("testDate", {
                type: "manual",
                message: "Past date not allowed"
            });
        });

        it("clears validation error for valid dates when allowPast is false", async () => {
            renderComponent({ allowPast: false });
            const trigger = screen.getByRole("button");
            await userEvent.click(trigger);

            const calendarProps = mockedCalendar.mock.lastCall![0] as any;
            const onSelect = calendarProps.onSelect;
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);

            await act(async () => {
                onSelect(tomorrow);
            });

            // Should call setValue with the date
            expect(mockSetValue).toHaveBeenCalledWith("testDate", tomorrow, { shouldValidate: true });

            // Should call clearErrors to remove any existing validation errors
            expect(mockClearErrors).toHaveBeenCalledWith("testDate");
        });
    });
});
