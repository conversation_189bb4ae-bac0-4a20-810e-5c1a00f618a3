import "@testing-library/jest-dom";

import { act, cleanup, fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";

import {
    ConditionSelector,
    ConditionQuestion,
    ConditionDependency
} from "@/components/forms/fields/condition-selector";
import { Option } from "@/components/forms/fields/dropdown-base";

const mockQuestions: ConditionQuestion[] = [
    {
        id: "question1",
        type: "number_input",
        metadata: {
            label: "גיל",
            placeholder: "הכנס גיל"
        }
    },
    {
        id: "question2",
        type: "date_picker",
        metadata: {
            label: "תאריך לידה",
            placeholder: "בחר תאריך"
        }
    },
    {
        id: "question3",
        type: "single_select",
        metadata: {
            label: "מגדר",
            options: "זכר,נקבה,אחר"
        }
    },
    {
        id: "question4",
        type: "multi_select",
        metadata: {
            label: "תחומי עניין",
            options: [
                { id: "tech", label: "טכנולוגיה" },
                { id: "art", label: "אמנות" },
                { id: "sport", label: "ספורט" }
            ]
        },
        groups_question: {
            id: "group1",
            name: "קבוצת שאלות כללית"
        }
    },
    {
        id: "question5",
        type: "short_text",
        metadata: {
            label: "טקסט קצר",
            placeholder: "הכנס טקסט"
        }
    }
];

const mockDependencies: ConditionDependency[] = [
    {
        id: "dep1",
        question_id: { id: "question1", label: "גיל" },
        condition_type: "range",
        condition_value: { min: 18, max: 65 }
    },
    {
        id: "dep2",
        question_id: { id: "question3", label: "מגדר" },
        condition_type: "in",
        condition_value: [{ id: "male", label: "זכר" }]
    }
];

let mockFormData: Record<string, any> = {
    dependencies: mockDependencies
};
let mockFormState = { errors: {}, isSubmitted: false };
let mockSetValue = jest.fn();
let mockRegister = jest.fn();
let mockWatch = jest.fn();
let mockControl = {};

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        watch: mockWatch,
        formState: mockFormState,
        control: mockControl
    }),
    Controller: ({ name, render: renderProp }: { name: string; render: (props: any) => React.ReactNode }) => {
        const fieldProps = {
            field: {
                onChange: jest.fn(),
                value: "greater_than"
            }
        };
        return <div data-testid={`controller-${name}`}>{renderProp(fieldProps)}</div>;
    }
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ name, label, options, placeholder }: any) => (
        <div data-testid={`single-select-${name}`}>
            <label>{label}</label>
            <select>
                {options.map((option: Option) => (
                    <option key={option.id} value={option.id}>
                        {option.label}
                    </option>
                ))}
            </select>
            <span>{placeholder}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ name, label, options, placeholder }: any) => (
        <div data-testid={`multi-select-${name}`}>
            <label>{label}</label>
            <div>
                {options.map((option: Option) => (
                    <div key={option.id} data-testid={`multi-option-${option.id}`}>
                        {option.label}
                    </div>
                ))}
            </div>
            <span>{placeholder}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ name, label, placeholder }: any) => (
        <div data-testid={`number-input-${name}`}>
            <label>{label}</label>
            <input type="number" placeholder={placeholder} />
        </div>
    )
}));

jest.mock("@/components/ui/select", () => ({
    Select: ({ children, onValueChange, value }: any) => (
        <div data-testid="ui-select" onClick={() => onValueChange && onValueChange("greater_than")}>
            {children}
        </div>
    ),
    SelectTrigger: ({ children }: any) => <div data-testid="select-trigger">{children}</div>,
    SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
    SelectContent: ({ children }: any) => <div data-testid="select-content">{children}</div>,
    SelectItem: ({ children, value }: any) => <div data-testid={`select-item-${value}`}>{children}</div>
}));

jest.mock("@/components/ui/input", () => ({
    Input: ({ placeholder, type, dir, className }: any) => (
        <input data-testid="ui-input" placeholder={placeholder} type={type} dir={dir} className={className} />
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ children, onClick, variant, size, type, className }: any) => (
        <button
            data-testid={`button-${variant || "default"}`}
            onClick={onClick}
            type={type}
            className={`${size || ""} ${className || ""}`}
        >
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, htmlFor }: any) => (
        <label data-testid="ui-label" htmlFor={htmlFor}>
            {children}
        </label>
    )
}));

jest.mock("lucide-react", () => ({
    PlusCircle: ({ className }: { className?: string }) => (
        <div data-testid="plus-circle-icon" className={className}>
            +
        </div>
    ),
    X: ({ className }: { className?: string }) => (
        <div data-testid="x-icon" className={className}>
            ×
        </div>
    )
}));

jest.mock("@/utils/form-utils", () => ({
    mapIdsToOptions: jest.fn((ids: string[], sourceItems: any[], getId: any, getLabel: any) =>
        ids.map((id) => {
            const item = sourceItems.find((source) => getId(source) === id);
            return {
                id,
                label: item ? getLabel(item) : `ID: ${id}`
            };
        })
    )
}));

jest.mock("@/utils/formatters", () => ({
    textToArray: jest.fn((text: string) => text.split(",").map((s) => s.trim()))
}));

const mockOnAdd = jest.fn();
const mockOnRemove = jest.fn();

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormData = { dependencies: mockDependencies };
    mockFormState = { errors: {}, isSubmitted: false };
    mockWatch.mockImplementation((name: string) => mockFormData[name as keyof typeof mockFormData]);
    jest.clearAllMocks();
};

describe("ConditionSelector", () => {
    const defaultProps = {
        availableQuestions: mockQuestions,
        onAdd: mockOnAdd,
        onRemove: mockOnRemove,
        fieldArrayName: "dependencies"
    };

    const TEXTS = {
        numberMinLabel: "גדול מ",
        numberMinPlaceholder: "הכנס ערך מינימלי",
        numberMaxLabel: "קטן מ",
        numberMaxPlaceholder: "הכנס ערך מקסימלי",
        dateTitle: "תנאי על הפרש תאריכים",
        dateOperatorGreaterThan: "גדול מ",
        dateOperatorLessThan: "קטן מ",
        dateHelp: "ההפרש בימים בין התאריך שנבחר לבין היום",
        dateConditionPlaceholder: "בחר תנאי",
        dateDaysPlaceholder: "מספר ימים",
        selectValuesLabel: "ערכים נבחרים",
        selectValuesPlaceholder: "בחר ערכים",
        selectQuestionLabel: "בחר שאלה",
        selectQuestionPlaceholder: "בחר שאלה",
        addDependencyButton: "הוסף תלות",
        errorLabel: "שגיאה:",
        questionNotFoundError: "השאלה הנבחרת לא נמצאה במערכת",
        questionNotFoundHelp: "יש לבחור שאלה חדשה או להסיר את התלות הזו"
    };

    beforeEach(() => {
        resetMocks();

        jest.spyOn(console, "error").mockImplementation((message) => {
            if (typeof message === "string" && message.includes("act(")) {
                return;
            }
            console.error(message);
        });
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.addDependencyButton)).toBeInTheDocument();
        });

        it("renders existing dependencies", () => {
            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/)).toHaveLength(2);
        });

        it("renders with custom field array name", () => {
            const customFieldArrayName = "custom_dependencies";
            setMockFormData({ [customFieldArrayName]: mockDependencies });

            render(<ConditionSelector {...defaultProps} fieldArrayName={customFieldArrayName} />);

            expect(screen.getAllByTestId(/single-select-custom_dependencies\.\d+\.question_id/)).toHaveLength(2);
        });

        it("renders without dependencies", () => {
            setMockFormData({ dependencies: [] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.queryByTestId(/single-select-dependencies\.\d+\.question_id/)).not.toBeInTheDocument();
            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
        });
    });

    describe("Question Filtering", () => {
        it("filters out excluded question types", () => {
            const questionsWithExcluded: ConditionQuestion[] = [
                ...mockQuestions,
                {
                    id: "excluded1",
                    type: "short_text",
                    metadata: { label: "טקסט קצר" }
                },
                {
                    id: "excluded2",
                    type: "long_text",
                    metadata: { label: "טקסט ארוך" }
                },
                {
                    id: "excluded3",
                    type: "address_select",
                    metadata: { label: "כתובת" }
                },
                {
                    id: "excluded4",
                    type: "bank_select",
                    metadata: { label: "בנק" }
                }
            ];

            render(<ConditionSelector {...defaultProps} availableQuestions={questionsWithExcluded} />);

            const singleSelects = screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/);
            expect(singleSelects).toHaveLength(2);

            expect(screen.getByTestId("single-select-dependencies.0.question_id")).toBeInTheDocument();
        });

        it("includes supported question types", () => {
            const supportedQuestions = mockQuestions.filter((q) =>
                ["number_input", "date_picker", "single_select", "multi_select"].includes(q.type)
            );

            render(<ConditionSelector {...defaultProps} availableQuestions={supportedQuestions} />);

            expect(screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/)).toHaveLength(2);
        });
    });

    describe("Add Dependency Button", () => {
        it("calls onAdd when add button is clicked", async () => {
            render(<ConditionSelector {...defaultProps} />);

            const addButton = screen.getByTestId("button-outline");

            await act(async () => {
                fireEvent.click(addButton);
            });

            expect(mockOnAdd).toHaveBeenCalledTimes(1);
        });

        it("renders add button with correct text", () => {
            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByText(TEXTS.addDependencyButton)).toBeInTheDocument();
            expect(screen.getByTestId("plus-circle-icon")).toBeInTheDocument();
        });
    });

    describe("Remove Dependency", () => {
        it("calls onRemove when remove button is clicked", async () => {
            render(<ConditionSelector {...defaultProps} />);

            const removeButtons = screen.getAllByTestId("button-ghost");

            await act(async () => {
                fireEvent.click(removeButtons[0]);
            });

            expect(mockOnRemove).toHaveBeenCalledWith(0);
        });

        it("renders remove button for each dependency", () => {
            render(<ConditionSelector {...defaultProps} />);

            const removeButtons = screen.getAllByTestId("button-ghost");
            expect(removeButtons).toHaveLength(2);

            const xIcons = screen.getAllByTestId("x-icon");
            expect(xIcons).toHaveLength(2);
        });

        it("calls onRemove with correct index", async () => {
            render(<ConditionSelector {...defaultProps} />);

            const removeButtons = screen.getAllByTestId("button-ghost");

            await act(async () => {
                fireEvent.click(removeButtons[1]);
            });

            expect(mockOnRemove).toHaveBeenCalledWith(1);
        });
    });

    describe("Question Selection", () => {
        it("renders SingleSelect for question selection", () => {
            render(<ConditionSelector {...defaultProps} />);

            const questionSelects = screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/);
            expect(questionSelects).toHaveLength(2);

            expect(screen.getAllByText(TEXTS.selectQuestionLabel)).toHaveLength(4);
        });

        it("passes correct options to SingleSelect", () => {
            render(<ConditionSelector {...defaultProps} />);

            const questionSelect = screen.getByTestId("single-select-dependencies.0.question_id");
            expect(questionSelect).toBeInTheDocument();
        });

        it("shows question group name as subtitle", () => {
            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("single-select-dependencies.0.question_id")).toBeInTheDocument();
        });
    });

    describe("Number Input Conditions", () => {
        it("renders number input fields for range conditions", () => {
            const numberDependency: ConditionDependency = {
                question_id: { id: "question1", label: "גיל" },
                condition_type: "range",
                condition_value: { min: 18, max: 65 }
            };

            setMockFormData({ dependencies: [numberDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("number-input-dependencies.0.condition_value.min")).toBeInTheDocument();
            expect(screen.getByTestId("number-input-dependencies.0.condition_value.max")).toBeInTheDocument();

            expect(screen.getByText(TEXTS.numberMinLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.numberMaxLabel)).toBeInTheDocument();
        });

        it("renders number inputs with correct placeholders", () => {
            const numberDependency: ConditionDependency = {
                question_id: { id: "question1", label: "גיל" },
                condition_type: "range",
                condition_value: { min: undefined, max: undefined }
            };

            setMockFormData({ dependencies: [numberDependency] });

            render(<ConditionSelector {...defaultProps} />);

            const minInput = screen.getByTestId("number-input-dependencies.0.condition_value.min");
            const maxInput = screen.getByTestId("number-input-dependencies.0.condition_value.max");

            expect(minInput).toBeInTheDocument();
            expect(maxInput).toBeInTheDocument();
        });
    });

    describe("Date Picker Conditions", () => {
        it("renders date condition fields for date_range conditions", () => {
            const dateDependency: ConditionDependency = {
                question_id: { id: "question2", label: "תאריך לידה" },
                condition_type: "date_range",
                condition_value: { operator: "greater_than", days_from_today: 30 }
            };

            setMockFormData({ dependencies: [dateDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByText(TEXTS.dateTitle)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.dateHelp)).toBeInTheDocument();
            expect(screen.getByTestId("controller-dependencies.0.condition_value.operator")).toBeInTheDocument();
            expect(screen.getByTestId("ui-input")).toBeInTheDocument();
        });

        it("renders date operator select with correct options", () => {
            const dateDependency: ConditionDependency = {
                question_id: { id: "question2", label: "תאריך לידה" },
                condition_type: "date_range",
                condition_value: { operator: "greater_than", days_from_today: 30 }
            };

            setMockFormData({ dependencies: [dateDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("select-item-greater_than")).toBeInTheDocument();
            expect(screen.getByTestId("select-item-less_than")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.dateOperatorGreaterThan)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.dateOperatorLessThan)).toBeInTheDocument();
        });

        it("renders days input with correct attributes", () => {
            const dateDependency: ConditionDependency = {
                question_id: { id: "question2", label: "תאריך לידה" },
                condition_type: "date_range",
                condition_value: { operator: "greater_than", days_from_today: 30 }
            };

            setMockFormData({ dependencies: [dateDependency] });

            render(<ConditionSelector {...defaultProps} />);

            const daysInput = screen.getByTestId("ui-input");
            expect(daysInput).toHaveAttribute("type", "number");
            expect(daysInput).toHaveAttribute("dir", "rtl");
            expect(daysInput).toHaveAttribute("placeholder", TEXTS.dateDaysPlaceholder);
        });
    });

    describe("Select Conditions", () => {
        it("renders MultiSelect for single_select question conditions", () => {
            const selectDependency: ConditionDependency = {
                question_id: { id: "question3", label: "מגדר" },
                condition_type: "in",
                condition_value: [{ id: "male", label: "זכר" }]
            };

            setMockFormData({ dependencies: [selectDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("multi-select-dependencies.0.condition_value")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.selectValuesLabel)).toBeInTheDocument();
        });

        it("renders MultiSelect for multi_select question conditions", () => {
            const multiSelectDependency: ConditionDependency = {
                question_id: { id: "question4", label: "תחומי עניין" },
                condition_type: "in",
                condition_value: [
                    { id: "tech", label: "טכנולוגיה" },
                    { id: "art", label: "אמנות" }
                ]
            };

            setMockFormData({ dependencies: [multiSelectDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByTestId("multi-select-dependencies.0.condition_value")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.selectValuesLabel)).toBeInTheDocument();
        });

        it("handles string options correctly", () => {
            const selectDependency: ConditionDependency = {
                question_id: { id: "question3", label: "מגדר" },
                condition_type: "in",
                condition_value: []
            };

            setMockFormData({ dependencies: [selectDependency] });

            render(<ConditionSelector {...defaultProps} />);

            const multiSelect = screen.getByTestId("multi-select-dependencies.0.condition_value");
            expect(multiSelect).toBeInTheDocument();
        });

        it("handles object array options correctly", () => {
            const selectDependency: ConditionDependency = {
                question_id: { id: "question4", label: "תחומי עניין" },
                condition_type: "in",
                condition_value: []
            };

            setMockFormData({ dependencies: [selectDependency] });

            render(<ConditionSelector {...defaultProps} />);

            const multiSelect = screen.getByTestId("multi-select-dependencies.0.condition_value");
            expect(multiSelect).toBeInTheDocument();
        });
    });

    describe("Error Handling", () => {
        it("shows error when question is not found", () => {
            const invalidDependency: ConditionDependency = {
                question_id: { id: "nonexistent", label: "Missing Question" },
                condition_type: "range",
                condition_value: { min: 1, max: 10 }
            };

            setMockFormData({ dependencies: [invalidDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByText(TEXTS.errorLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.questionNotFoundError + " (ID: nonexistent)")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.questionNotFoundHelp)).toBeInTheDocument();
        });

        it("renders error state with correct styling", () => {
            const invalidDependency: ConditionDependency = {
                question_id: { id: "nonexistent", label: "Missing Question" },
                condition_type: "range",
                condition_value: { min: 1, max: 10 }
            };

            setMockFormData({ dependencies: [invalidDependency] });

            render(<ConditionSelector {...defaultProps} />);

            const errorContainer = screen.getByText(TEXTS.errorLabel).closest("div")?.parentElement;
            expect(errorContainer).toHaveClass(
                "rounded-md",
                "border",
                "border-destructive",
                "bg-destructive/10",
                "p-3"
            );
        });
    });

    describe("useEffect Hook", () => {
        it("sets default condition type for number questions", () => {
            const newDependency: ConditionDependency = {
                question_id: { id: "question1", label: "גיל" },
                condition_type: undefined as any,
                condition_value: undefined as any
            };

            setMockFormData({ dependencies: [newDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(mockSetValue).toHaveBeenCalled();
        });

        it("sets default condition type for date questions", () => {
            const newDependency: ConditionDependency = {
                question_id: { id: "question2", label: "תאריך לידה" },
                condition_type: undefined as any,
                condition_value: undefined as any
            };

            setMockFormData({ dependencies: [newDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(mockSetValue).toHaveBeenCalled();
        });

        it("sets default condition type for select questions", () => {
            const newDependency: ConditionDependency = {
                question_id: { id: "question3", label: "מגדר" },
                condition_type: undefined as any,
                condition_value: undefined as any
            };

            setMockFormData({ dependencies: [newDependency] });

            render(<ConditionSelector {...defaultProps} />);

            expect(mockSetValue).toHaveBeenCalled();
        });
    });

    describe("Stable Keys", () => {
        it("uses dependency id as key when available", () => {
            const dependenciesWithIds = mockDependencies.map((dep, index) => ({
                ...dep,
                id: `stable-id-${index}`
            }));

            setMockFormData({ dependencies: dependenciesWithIds });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/)).toHaveLength(2);
        });

        it("falls back to question id and index when no dependency id", () => {
            const dependenciesWithoutIds = mockDependencies.map((dep) => ({
                ...dep,
                id: undefined
            }));

            setMockFormData({ dependencies: dependenciesWithoutIds });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getAllByTestId(/single-select-dependencies\.\d+\.question_id/)).toHaveLength(2);
        });
    });

    describe("Text Constants", () => {
        it("uses correct Hebrew text constants", () => {
            render(<ConditionSelector {...defaultProps} />);

            expect(screen.getByText(TEXTS.addDependencyButton)).toBeInTheDocument();
        });

        it("displays all required text constants", () => {
            Object.entries(TEXTS).forEach(([key, value]) => {
                expect(typeof value).toBe("string");
                expect(value.length).toBeGreaterThan(0);
            });
        });
    });

    describe("Edge Cases", () => {
        it("handles empty dependencies array", () => {
            setMockFormData({ dependencies: [] });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.queryByTestId(/single-select-dependencies\.\d+\.question_id/)).not.toBeInTheDocument();
            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
        });

        it("handles null dependencies", () => {
            setMockFormData({ dependencies: null });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.queryByTestId(/single-select-dependencies\.\d+\.question_id/)).not.toBeInTheDocument();
            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
        });

        it("handles undefined dependencies", () => {
            setMockFormData({ dependencies: undefined });

            render(<ConditionSelector {...defaultProps} />);

            expect(screen.queryByTestId(/single-select-dependencies\.\d+\.question_id/)).not.toBeInTheDocument();
            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
        });

        it("handles questions without metadata", () => {
            const questionsWithoutMetadata: ConditionQuestion[] = [
                {
                    id: "question-no-meta",
                    type: "number_input"
                }
            ];

            render(<ConditionSelector {...defaultProps} availableQuestions={questionsWithoutMetadata} />);

            expect(screen.getByTestId("button-outline")).toBeInTheDocument();
        });

        it("handles select questions without options", () => {
            const selectQuestionWithoutOptions: ConditionQuestion = {
                id: "question-no-options",
                type: "single_select",
                metadata: {
                    label: "שאלה ללא אפשרויות"
                }
            };

            const dependencyForQuestionWithoutOptions: ConditionDependency = {
                question_id: { id: "question-no-options", label: "שאלה ללא אפשרויות" },
                condition_type: "in",
                condition_value: []
            };

            setMockFormData({
                dependencies: [dependencyForQuestionWithoutOptions]
            });

            render(
                <ConditionSelector
                    {...defaultProps}
                    availableQuestions={[...mockQuestions, selectQuestionWithoutOptions]}
                />
            );

            expect(screen.queryByTestId("multi-select-dependencies.0.condition_value")).not.toBeInTheDocument();
        });
    });
});
