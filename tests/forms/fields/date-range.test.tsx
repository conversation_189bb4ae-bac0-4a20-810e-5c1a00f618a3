import "@testing-library/jest-dom";

import { act, cleanup, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";

import { DateRange, DateRangeProps, TEXTS } from "@/components/forms/fields/date-range";
import { Calendar } from "@/components/ui/calendar";

let mockFormState: { errors: object; isSubmitted: boolean };
let mockSetValue: jest.SpyInstance;
let mockWatch: jest.SpyInstance;

jest.mock("@/components/ui/calendar");
const mockedCalendar = jest.mocked(Calendar);

jest.mock("react-hook-form", () => {
    const originalModule = jest.requireActual("react-hook-form");
    return {
        ...originalModule,
        useFormContext: () => ({
            setValue: mockSetValue,
            watch: mockWatch,
            formState: mockFormState
        })
    };
});

jest.mock("lucide-react", () => ({
    Calendar: () => <div data-testid="calendar-icon" />,
    __esModule: true
}));

const TestWrapper = ({
    children,
    defaultValues
}: {
    children: React.ReactNode;
    defaultValues?: { [key: string]: any };
}) => {
    const methods = useForm({ defaultValues });
    mockSetValue = jest.spyOn(methods, "setValue");
    mockWatch = jest.spyOn(methods, "watch");
    return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("DateRange", () => {
    const LABEL = "טווח תאריכים";

    const defaultProps: DateRangeProps = {
        name: "testDateRange",
        label: LABEL
    };

    beforeEach(() => {
        mockFormState = { errors: {}, isSubmitted: false };
        if (mockSetValue) mockSetValue.mockClear();
        if (mockWatch) mockWatch.mockClear();
        mockedCalendar.mockClear();
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    const renderComponent = (props: Partial<DateRangeProps> = {}, defaultValues?: object) => {
        return render(
            <TestWrapper defaultValues={defaultValues}>
                <DateRange {...defaultProps} {...props} />
            </TestWrapper>
        );
    };

    it("renders with label and placeholder", async () => {
        renderComponent();
        expect(screen.getByText(LABEL)).toBeInTheDocument();
        expect(screen.getByText(TEXTS.placeholder)).toBeInTheDocument();
        expect(screen.getByTestId("calendar-icon")).toBeInTheDocument();
    });

    it("opens popover on click", async () => {
        renderComponent();
        const trigger = screen.getByRole("button");
        await userEvent.click(trigger);
        expect(mockedCalendar).toHaveBeenCalled();
    });

    it("calls setValue with correct format on range selection", async () => {
        renderComponent();
        const trigger = screen.getByRole("button");
        await userEvent.click(trigger);

        const calendarProps = mockedCalendar.mock.lastCall![0] as any;
        const onSelect = calendarProps.onSelect;
        const fromDate = new Date(2023, 10, 15);
        const toDate = new Date(2023, 10, 20);

        await act(async () => {
            onSelect({ from: fromDate, to: toDate });
        });

        expect(mockSetValue).toHaveBeenCalledWith(
            "testDateRange",
            { startDate: fromDate, endDate: toDate },
            { shouldValidate: true }
        );
    });

    it("shows error message when form is submitted with error", () => {
        mockFormState = {
            errors: { testDateRange: { message: TEXTS.required } },
            isSubmitted: true
        };
        renderComponent();
        expect(screen.getByText(TEXTS.required)).toBeInTheDocument();
        expect(screen.getByText(TEXTS.required)).toHaveClass("text-sm", "text-red-500", "ms-1");
    });

    it("disables future dates when isFutureAllowed is false", async () => {
        renderComponent({ isFutureAllowed: false });
        const trigger = screen.getByRole("button");
        await userEvent.click(trigger);
        const calendarProps = mockedCalendar.mock.lastCall![0] as any;
        expect(calendarProps.disabled).toBeInstanceOf(Function);
    });
});
