import "@testing-library/jest-dom";
import { cleanup, render, screen, fireEvent, waitFor } from "@testing-library/react";
import React, { act } from "react";

import { BankSelect, TEXTS } from "@/components/forms/fields/bank-select";

let mockFormState = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();
let mockSetValue = jest.fn();
let mockWatch = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        watch: mockWatch,
        formState: mockFormState
    })
}));

jest.mock("@/components/forms/fields/dropdown-base", () => ({
    DropdownBase: ({
        name,
        placeholder,
        children,
        displayValue
    }: {
        name: string;
        placeholder: string;
        children: (
            p1: Record<string, unknown>,
            p2: { setOpen: (open: boolean) => void; searchQuery: string }
        ) => React.ReactNode;
        displayValue: string;
    }) => (
        <div data-testid={`dropdown-${name}`}>
            {placeholder}
            {displayValue}
            {children && children({}, { setOpen: jest.fn(), searchQuery: "" })}
        </div>
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children }: { children: React.ReactNode }) => <label data-testid="bank-label">{children}</label>
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

jest.mock("@/components/ui/command", () => ({
    CommandGroup: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    CommandItem: ({ children, onSelect, ...props }: { children: React.ReactNode; onSelect: () => void }) => (
        <div data-testid="command-item" onClick={onSelect} {...props}>
            {children}
        </div>
    ),
    CommandList: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockBanks = [
    {
        bankCode: 10,
        bankName: "בנק דוגמה",
        branches: [{ branchCode: 101, branchName: "סניף ראשי" }]
    },
    {
        bankCode: 12,
        bankName: "בנק אחר",
        branches: [{ branchCode: 202, branchName: "סניף משני" }]
    }
];

beforeEach(() => {
    mockFormState = { errors: {}, isSubmitted: false };
    mockRegister.mockClear();
    mockSetValue.mockClear();
    mockWatch.mockClear();
    mockWatch.mockReturnValue({ bankCode: "", branchCode: "" });
    global.fetch = jest.fn(() =>
        Promise.resolve({
            json: () => Promise.resolve({ banks: mockBanks })
        } as Response)
    );
});

afterEach(() => {
    cleanup();
    jest.restoreAllMocks();
});

describe("BankSelect", () => {
    const defaultProps = {
        name: "bankField",
        label: TEXTS.bankPlaceholder,
        tooltip: "עזרה לבחירת בנק"
    };

    it("renders label and tooltip", async () => {
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => {
            expect(screen.getByTestId("bank-label")).toHaveTextContent(defaultProps.label);
            expect(screen.getByTestId("tooltip-icon")).toHaveAttribute("title", defaultProps.tooltip);
        });
    });

    it("shows loading state and then content", async () => {
        render(<BankSelect {...defaultProps} />);
        expect(screen.getByText(TEXTS.loading)).toBeInTheDocument();
        await waitFor(() => expect(screen.queryByText(TEXTS.loading)).not.toBeInTheDocument());
        expect(await screen.findByText(mockBanks[0].bankName)).toBeInTheDocument();
    });

    it("shows error state", async () => {
        global.fetch = jest.fn(() => Promise.reject("fail"));
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => expect(screen.getByText(TEXTS.error)).toBeInTheDocument());
    });

    it("registers hidden input with required validation by default", async () => {
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => expect(mockRegister).toHaveBeenCalled());
        expect(mockRegister).toHaveBeenCalledWith("bankField", expect.objectContaining({ required: TEXTS.required }));
    });

    it("registers hidden input without required validation when required=false", async () => {
        render(<BankSelect {...defaultProps} required={false} />);
        await waitFor(() => expect(mockRegister).toHaveBeenCalled());
        expect(mockRegister).toHaveBeenCalledWith("bankField", expect.objectContaining({ required: undefined }));
    });

    it("shows error message when form is submitted with a validation error", async () => {
        mockFormState = { errors: { bankField: { message: TEXTS.error } }, isSubmitted: true };
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => expect(screen.getByText(TEXTS.error)).toBeInTheDocument());
    });

    it("does not show error message when form is not submitted", async () => {
        mockFormState = { errors: { bankField: { message: TEXTS.error } }, isSubmitted: false };
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => expect(global.fetch).toHaveBeenCalled());
        expect(screen.queryByText(TEXTS.error)).not.toBeInTheDocument();
    });

    it("renders bank and branch dropdowns", async () => {
        render(<BankSelect {...defaultProps} />);
        await waitFor(() => expect(screen.getByTestId("dropdown-bankField_bank")).toBeInTheDocument());
        expect(screen.getByTestId("dropdown-bankField_branch")).toBeInTheDocument();
    });

    it("sets value when selecting a bank", async () => {
        render(<BankSelect {...defaultProps} />);
        const bankItem = await screen.findByText(mockBanks[0].bankName);
        const clickableItem = bankItem.closest('[data-testid="command-item"]');
        expect(clickableItem).toBeInTheDocument();

        await act(async () => {
            fireEvent.click(clickableItem!);
        });

        expect(mockSetValue).toHaveBeenCalledWith(
            "bankField",
            { bankCode: 10, branchCode: "" },
            { shouldValidate: true }
        );
    });

    it("sets value when selecting a branch", async () => {
        mockWatch.mockReturnValue({ bankCode: 10, branchCode: "" });
        render(<BankSelect {...defaultProps} />);

        const branchItem = await screen.findByText(mockBanks[0].branches[0].branchName);
        const clickableItem = branchItem.closest('[data-testid="command-item"]');
        expect(clickableItem).toBeInTheDocument();

        await act(async () => {
            fireEvent.click(clickableItem!);
        });

        expect(mockSetValue).toHaveBeenCalledWith(
            "bankField",
            { bankCode: 10, branchCode: 101 },
            { shouldValidate: true }
        );
    });
});
