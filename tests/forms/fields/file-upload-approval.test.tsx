import "@testing-library/jest-dom";
import { act, cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { FileUploadApproval, FileUploadApprovalProps } from "@/components/forms/fields/file-upload-approval";

jest.mock("sonner", () => ({ toast: { error: jest.fn() } }));
const mockToast = require("sonner").toast;

let mockRegister = jest.fn();
let mockSetValue = jest.fn();
let mockWatch = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        setValue: mockSetValue,
        watch: mockWatch
    })
}));

const defaultProps: FileUploadApprovalProps = {
    name: "testFile",
    required: true,
    requiredText: "שדה חובה",
    acceptedFileType: ["application/pdf"],
    acceptedFileTypeDescription: "רק קבצי PDF מותרים",
    maxSizeInMB: 2,
    fileTypeErrorMessage: "סוג קובץ לא נתמך",
    fileSizeErrorMessage: (max) => `הקובץ גדול מהמותר (${max}MB)`,
    dragDropText: "גרור קובץ לכאן",
    fileFormatDescription: (max) => `PDF עד ${max}MB`,
    selectedFileText: "קובץ שנבחר",
    documentTypeName: "תעודת זהות",
    documentTypeDescription: "העלה צילום תעודת זהות"
};

describe("FileUploadApproval", () => {
    beforeEach(() => {
        mockRegister = jest.fn();
        mockSetValue = jest.fn();
        mockWatch = jest.fn();
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanup();
    });

    it("renders with required props", () => {
        render(<FileUploadApproval {...defaultProps} />);
        expect(screen.getByText(defaultProps.documentTypeName)).toBeInTheDocument();
        expect(screen.getByText(defaultProps.documentTypeDescription!)).toBeInTheDocument();
        expect(screen.getByText("בחר קובץ")).toBeInTheDocument();
    });

    it("shows file format description", () => {
        render(<FileUploadApproval {...defaultProps} />);
        expect(screen.getByText(defaultProps.fileFormatDescription(defaultProps.maxSizeInMB))).toBeInTheDocument();
    });

    it("calls handleFileSelect and validates file type", async () => {
        render(<FileUploadApproval {...defaultProps} />);
        const file = new File(["test"], "test.txt", { type: "text/plain" });
        const input = screen.getByTestId("file-input");
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });
        expect(mockToast.error).toHaveBeenCalledWith(defaultProps.fileTypeErrorMessage, expect.any(Object));
    });

    it("calls handleFileSelect and validates file size", async () => {
        render(<FileUploadApproval {...defaultProps} />);
        const file = new File([new Array(3 * 1024 * 1024).join("a")], "big.pdf", { type: "application/pdf" });
        Object.defineProperty(file, "size", { value: 3 * 1024 * 1024 });
        const input = screen.getByTestId("file-input");
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });
        expect(mockToast.error).toHaveBeenCalledWith(defaultProps.fileSizeErrorMessage(defaultProps.maxSizeInMB));
    });

    it("sets selected file and calls setValue on valid file", async () => {
        render(<FileUploadApproval {...defaultProps} />);
        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByTestId("file-input");
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });
        expect(mockSetValue).toHaveBeenCalledWith(defaultProps.name, file, { shouldValidate: true });
    });

    it("shows selected file UI and triggers upload callback", async () => {
        const onFileSelect = jest.fn().mockResolvedValue(undefined);
        render(<FileUploadApproval {...defaultProps} onFileSelect={onFileSelect} />);
        const file = new File(["test"], "test.pdf", { type: "application/pdf" });
        const input = screen.getByTestId("file-input");
        await act(async () => {
            fireEvent.change(input, { target: { files: [file] } });
        });
        expect(screen.getByText(`קובץ שנבחר: test.pdf`)).toBeInTheDocument();
        const uploadBtn = screen.getByText("העלה קובץ");
        await act(async () => {
            fireEvent.click(uploadBtn);
        });
        expect(onFileSelect).toHaveBeenCalledWith(file);
    });

    it("shows uploaded status and view link when isUploaded", () => {
        render(
            <FileUploadApproval {...defaultProps} isUploaded={true} uploadedFileUrl="http://example.com/file.pdf" />
        );
        expect(screen.getByText("הועלה")).toBeInTheDocument();
        expect(screen.getByLabelText("צפה במסמך")).toHaveAttribute("href", "http://example.com/file.pdf");
    });

    it("shows pending status when not uploaded", () => {
        render(<FileUploadApproval {...defaultProps} isUploaded={false} />);
        expect(screen.getByText("ממתין להעלאה")).toBeInTheDocument();
    });

    it("shows example and link buttons if provided", () => {
        const onExampleClick = jest.fn();
        const onLinkClick = jest.fn();
        render(
            <FileUploadApproval
                {...defaultProps}
                exampleFileUrl="http://example.com/example.pdf"
                linkUrl="http://example.com/link"
                onExampleClick={onExampleClick}
                onLinkClick={onLinkClick}
            />
        );
        expect(screen.getByLabelText("מסמך לדוגמא")).toBeInTheDocument();
        expect(screen.getByLabelText("קישור להורדת המסמך")).toBeInTheDocument();
        fireEvent.click(screen.getByLabelText("מסמך לדוגמא"));
        expect(onExampleClick).toHaveBeenCalled();
        fireEvent.click(screen.getByLabelText("קישור להורדת המסמך"));
        expect(onLinkClick).toHaveBeenCalled();
    });

    it("registers hidden input with required validation", () => {
        render(<FileUploadApproval {...defaultProps} />);
        expect(mockRegister).toHaveBeenCalledWith(defaultProps.name, { required: defaultProps.requiredText });
    });
});
