import "@testing-library/jest-dom";

import { cleanup, render, screen } from "@testing-library/react";
import React from "react";

import { LongText, TextFieldProps, TEXTS as ComponentTEXTS } from "@/components/forms/fields/long-text";

let mockFormState = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        formState: mockFormState
    })
}));

jest.mock("@/components/ui/textarea", () => ({
    Textarea: React.forwardRef<HTMLTextAreaElement, React.TextareaHTMLAttributes<HTMLTextAreaElement>>(
        ({ className, ...props }, ref) => (
            <textarea ref={ref} className={className} data-testid="long-text-input" {...props} />
        )
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, htmlFor, className }: { children: React.ReactNode; htmlFor?: string; className?: string }) => (
        <label htmlFor={htmlFor} className={className} data-testid="long-text-label">
            {children}
        </label>
    )
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    jest.clearAllMocks();
};

describe("LongText", () => {
    const TEXTS = {
        label: "תיאור ארוך",
        placeholder: "הזן תיאור...",
        tooltip: "עזרה נוספת לתיאור",
        customRequiredText: "שדה תיאור הוא חובה",
        validationError: "שגיאת ולידציה בתיאור"
    };

    const defaultProps: TextFieldProps = {
        name: "descriptionField",
        label: TEXTS.label,
        placeholder: TEXTS.placeholder
    };

    beforeEach(() => {
        resetMocks();
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<LongText {...defaultProps} />);
            expect(screen.getByTestId("long-text-label")).toBeInTheDocument();
            expect(screen.getByTestId("long-text-input")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.label)).toBeInTheDocument();
        });

        it("renders with a placeholder", () => {
            render(<LongText {...defaultProps} />);
            const input = screen.getByTestId("long-text-input");
            expect(input).toHaveAttribute("placeholder", TEXTS.placeholder);
        });

        it("renders with a tooltip", () => {
            render(<LongText {...defaultProps} tooltip={TEXTS.tooltip} />);
            const tooltip = screen.getByTestId("tooltip-icon");
            expect(tooltip).toBeInTheDocument();
            expect(tooltip).toHaveAttribute("title", TEXTS.tooltip);
        });
    });

    describe("Form Registration", () => {
        it("registers field with required validation by default", () => {
            render(<LongText {...defaultProps} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: ComponentTEXTS.required,
                minLength: undefined,
                maxLength: undefined
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<LongText {...defaultProps} required={false} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: undefined,
                minLength: undefined,
                maxLength: undefined
            });
        });

        it("registers field with custom required text", () => {
            render(<LongText {...defaultProps} requiredText={TEXTS.customRequiredText} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: TEXTS.customRequiredText,
                minLength: undefined,
                maxLength: undefined
            });
        });

        it("registers field with minLength validation", () => {
            render(<LongText {...defaultProps} minLength={10} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: ComponentTEXTS.required,
                minLength: { value: 10, message: ComponentTEXTS.minLength(10) },
                maxLength: undefined
            });
        });

        it("registers field with maxLength validation", () => {
            render(<LongText {...defaultProps} maxLength={200} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: ComponentTEXTS.required,
                minLength: undefined,
                maxLength: { value: 200, message: ComponentTEXTS.maxLength(200) }
            });
        });

        it("registers field with both minLength and maxLength validation", () => {
            render(<LongText {...defaultProps} minLength={10} maxLength={200} />);
            expect(mockRegister).toHaveBeenCalledWith("descriptionField", {
                required: ComponentTEXTS.required,
                minLength: { value: 10, message: ComponentTEXTS.minLength(10) },
                maxLength: { value: 200, message: ComponentTEXTS.maxLength(200) }
            });
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with a validation error", () => {
            setMockFormState({
                errors: { descriptionField: { message: TEXTS.validationError } },
                isSubmitted: true
            });
            render(<LongText {...defaultProps} />);
            expect(screen.getByText(TEXTS.validationError)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.validationError)).toHaveClass("text-sm", "text-red-500", "ms-1");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { descriptionField: { message: TEXTS.validationError } },
                isSubmitted: false
            });
            render(<LongText {...defaultProps} />);
            expect(screen.queryByText(TEXTS.validationError)).not.toBeInTheDocument();
        });

        it("applies error styling to textarea when submitted with errors", () => {
            setMockFormState({
                errors: { descriptionField: { message: "error" } },
                isSubmitted: true
            });
            render(<LongText {...defaultProps} />);
            const input = screen.getByTestId("long-text-input");
            expect(input).toHaveClass("border-red-500", "focus-visible:ring-red-500");
        });

        it("does not apply error styling when form is not submitted", () => {
            setMockFormState({
                errors: { descriptionField: { message: "error" } },
                isSubmitted: false
            });
            render(<LongText {...defaultProps} />);
            const input = screen.getByTestId("long-text-input");
            expect(input).not.toHaveClass("border-red-500");
            expect(input).not.toHaveClass("focus-visible:ring-red-500");
        });
    });
});
