import "@testing-library/jest-dom";

import { cleanup, render, screen } from "@testing-library/react";
import React from "react";

import { ShortText, TextFieldProps, TEXTS as ComponentTEXTS } from "@/components/forms/fields/short-text";

let mockFormState = { errors: {}, isSubmitted: false };
let mockRegister = jest.fn();

jest.mock("react-hook-form", () => ({
    useFormContext: () => ({
        register: mockRegister,
        formState: mockFormState
    })
}));

jest.mock("@/components/ui/input", () => ({
    Input: React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(
        ({ className, type, defaultValue, ...props }, ref) => (
            <input
                ref={ref}
                type={type}
                className={className}
                data-testid="text-input"
                defaultValue={defaultValue || ""}
                {...props}
            />
        )
    )
}));

jest.mock("@/components/ui/label", () => ({
    Label: ({ children, htmlFor, className }: { children: React.ReactNode; htmlFor?: string; className?: string }) => (
        <label htmlFor={htmlFor} className={className} data-testid="text-label">
            {children}
        </label>
    )
}));

jest.mock("@/components/ui/tooltip-icon", () => ({
    TooltipIcon: ({ text }: { text: string }) => (
        <div data-testid="tooltip-icon" title={text}>
            ?
        </div>
    )
}));

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const resetMocks = () => {
    mockFormState = { errors: {}, isSubmitted: false };
    jest.clearAllMocks();
};

describe("ShortText", () => {
    const TEXTS = {
        label: "שדה טקסט",
        placeholder: "הזן טקסט",
        customPlaceholder: "הזן את הטקסט כאן",
        defaultValue: "ערך ברירת מחדל",
        tooltip: "עזרה נוספת",
        description: "הסבר נוסף על השדה",
        customRequiredText: "שדה חובה מותאם",
        patternMessage: "רק אותיות באנגלית",
        validationError: "שגיאת ולידציה",
        longLabel: "זהו טקסט ארוך מאוד שבודק איך הרכיב מתמודד עם תוויות ארוכות",
        descriptiveError: "הזן לפחות 3 תווים",
        fullLabel: "שדה מלא",
        fullDefaultValue: "ערך התחלתי",
        fullPatternMessage: "רק אותיות ורווחים",
        fullRequiredText: "שדה חובה",
        errorFromComponent: "שגיאה"
    };

    const defaultProps: TextFieldProps = {
        name: "testField",
        label: TEXTS.label,
        placeholder: TEXTS.placeholder
    };

    beforeEach(() => {
        resetMocks();
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Basic Rendering", () => {
        it("renders with required props", () => {
            render(<ShortText {...defaultProps} />);

            expect(screen.getByTestId("text-label")).toBeInTheDocument();
            expect(screen.getByTestId("text-input")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.label)).toBeInTheDocument();
        });

        it("renders with custom placeholder", () => {
            render(<ShortText {...defaultProps} placeholder={TEXTS.customPlaceholder} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("placeholder", TEXTS.customPlaceholder);
        });

        it("renders with default value", () => {
            render(<ShortText {...defaultProps} defaultValue={TEXTS.defaultValue} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveValue(TEXTS.defaultValue);
        });

        it("renders with tooltip", () => {
            render(<ShortText {...defaultProps} tooltip={TEXTS.tooltip} />);

            const tooltip = screen.getByTestId("tooltip-icon");
            expect(tooltip).toBeInTheDocument();
            expect(tooltip).toHaveAttribute("title", TEXTS.tooltip);
        });

        it("renders with description", () => {
            render(<ShortText {...defaultProps} description={TEXTS.description} />);

            expect(screen.getByText(TEXTS.description)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.description)).toHaveClass("text-sm", "text-muted-foreground");
        });
    });

    describe("Input Types", () => {
        it("renders with default text type", () => {
            render(<ShortText {...defaultProps} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("type", "text");
        });

        it("renders with custom input type", () => {
            render(<ShortText {...defaultProps} type="email" />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("type", "email");
        });

        it("supports password input type", () => {
            render(<ShortText {...defaultProps} type="password" />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("type", "password");
        });

        it("supports tel input type", () => {
            render(<ShortText {...defaultProps} type="tel" />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("type", "tel");
        });
    });

    describe("Form Registration", () => {
        it("registers field with required validation by default", () => {
            render(<ShortText {...defaultProps} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                pattern: undefined
            });
        });

        it("registers field without required validation when required=false", () => {
            render(<ShortText {...defaultProps} required={false} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: undefined,
                pattern: undefined
            });
        });

        it("registers field with custom required text", () => {
            render(<ShortText {...defaultProps} requiredText={TEXTS.customRequiredText} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: TEXTS.customRequiredText,
                pattern: undefined
            });
        });

        it("registers field with pattern validation", () => {
            const pattern = /^[a-zA-Z]+$/;
            render(<ShortText {...defaultProps} pattern={pattern} patternMessage={TEXTS.patternMessage} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                pattern: { value: pattern, message: TEXTS.patternMessage }
            });
        });

        it("registers field with pattern using default pattern message", () => {
            const pattern = /^\d+$/;
            render(<ShortText {...defaultProps} pattern={pattern} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: ComponentTEXTS.requiredText,
                pattern: { value: pattern, message: ComponentTEXTS.pattern }
            });
        });

        it("registers field with both required=false and pattern", () => {
            const pattern = /^[a-zA-Z]+$/;
            render(<ShortText {...defaultProps} required={false} pattern={pattern} />);

            expect(mockRegister).toHaveBeenCalledWith("testField", {
                required: undefined,
                pattern: { value: pattern, message: ComponentTEXTS.pattern }
            });
        });
    });

    describe("Error Handling", () => {
        it("shows error message when form is submitted with validation error", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.validationError } },
                isSubmitted: true
            });

            render(<ShortText {...defaultProps} />);

            expect(screen.getByText(TEXTS.validationError)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.validationError)).toHaveClass("text-sm", "text-red-500", "ms-1");
        });

        it("does not show error message when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.validationError } },
                isSubmitted: false
            });

            render(<ShortText {...defaultProps} />);

            expect(screen.queryByText(TEXTS.validationError)).not.toBeInTheDocument();
        });

        it("does not show error message when there are no errors", () => {
            setMockFormState({
                errors: {},
                isSubmitted: true
            });

            render(<ShortText {...defaultProps} />);

            expect(screen.queryByText(TEXTS.validationError)).not.toBeInTheDocument();
        });

        it("applies error styling to input when submitted with errors", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.errorFromComponent } },
                isSubmitted: true
            });

            render(<ShortText {...defaultProps} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveClass("border-red-500", "focus-visible:ring-red-500");
        });

        it("does not apply error styling when form is not submitted", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.errorFromComponent } },
                isSubmitted: false
            });

            render(<ShortText {...defaultProps} />);

            const input = screen.getByTestId("text-input");
            expect(input).not.toHaveClass("border-red-500");
            expect(input).not.toHaveClass("focus-visible:ring-red-500");
        });
    });

    describe("RTL Support", () => {
        it("applies RTL direction to container", () => {
            render(<ShortText {...defaultProps} />);

            const container = screen.getByTestId("text-input").parentElement;
            expect(container).toHaveAttribute("dir", "rtl");
        });

        it("applies RTL styling to input", () => {
            render(<ShortText {...defaultProps} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveClass("text-right", "placeholder:text-right");
            expect(input).toHaveAttribute("dir", "rtl");
        });
    });

    describe("Label Association", () => {
        it("associates label with input using htmlFor and id", () => {
            render(<ShortText {...defaultProps} />);

            const label = screen.getByTestId("text-label");
            const input = screen.getByTestId("text-input");

            expect(label).toHaveAttribute("for", "testField");
            expect(input).toHaveAttribute("id", "testField");
        });

        it("uses correct field name for label association", () => {
            render(<ShortText {...defaultProps} name="customFieldName" />);

            const label = screen.getByTestId("text-label");
            const input = screen.getByTestId("text-input");

            expect(label).toHaveAttribute("for", "customFieldName");
            expect(input).toHaveAttribute("id", "customFieldName");
        });
    });

    describe("Layout and Styling", () => {
        it("applies correct container styling", () => {
            render(<ShortText {...defaultProps} />);

            const container = screen.getByTestId("text-input").parentElement;
            expect(container).toHaveClass("space-y-2");
        });

        it("applies correct label container styling", () => {
            render(<ShortText {...defaultProps} />);

            const labelContainer = screen.getByTestId("text-label").parentElement;
            expect(labelContainer).toHaveClass("flex", "items-center", "justify-start", "gap-1");
        });

        it("positions tooltip before label", () => {
            render(<ShortText {...defaultProps} tooltip={TEXTS.tooltip} />);

            const labelContainer = screen.getByTestId("text-label").parentElement;
            const tooltip = screen.getByTestId("tooltip-icon");
            const label = screen.getByTestId("text-label");

            expect(labelContainer).toContainElement(tooltip);
            expect(labelContainer).toContainElement(label);
        });
    });

    describe("Edge Cases", () => {
        it("handles empty string as default value", () => {
            render(<ShortText {...defaultProps} defaultValue="" />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveValue("");
        });

        it("handles undefined default value", () => {
            render(<ShortText {...defaultProps} defaultValue={undefined} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveValue("");
        });

        it("handles very long label text", () => {
            render(<ShortText {...defaultProps} label={TEXTS.longLabel} />);

            expect(screen.getByText(TEXTS.longLabel)).toBeInTheDocument();
        });

        it("handles special characters in name", () => {
            render(<ShortText {...defaultProps} name="field-with-dashes_and_underscores" />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("id", "field-with-dashes_and_underscores");
        });
    });

    describe("Accessibility", () => {
        it("provides proper label for screen readers", () => {
            render(<ShortText {...defaultProps} />);

            const label = screen.getByTestId("text-label");
            expect(label).toHaveClass("text-right");
        });

        it("maintains proper focus order with tooltip", () => {
            render(<ShortText {...defaultProps} tooltip={TEXTS.tooltip} />);

            const input = screen.getByTestId("text-input");
            const tooltip = screen.getByTestId("tooltip-icon");

            expect(input).toBeInTheDocument();
            expect(tooltip).toBeInTheDocument();
        });

        it("provides descriptive error messages", () => {
            setMockFormState({
                errors: { testField: { message: TEXTS.descriptiveError } },
                isSubmitted: true
            });

            render(<ShortText {...defaultProps} />);

            const errorMessage = screen.getByText(TEXTS.descriptiveError);
            expect(errorMessage).toHaveClass("text-sm", "text-red-500");
        });
    });

    describe("Text Constants", () => {
        it("displays correct default required message from component", () => {
            setMockFormState({
                errors: { testField: { message: ComponentTEXTS.requiredText } },
                isSubmitted: true
            });
            render(<ShortText {...defaultProps} />);
            expect(screen.getByText(ComponentTEXTS.requiredText)).toBeInTheDocument();
        });

        it("displays correct default pattern message from component", () => {
            setMockFormState({
                errors: { testField: { message: ComponentTEXTS.pattern } },
                isSubmitted: true
            });
            render(<ShortText {...defaultProps} />);
            expect(screen.getByText(ComponentTEXTS.pattern)).toBeInTheDocument();
        });
    });

    describe("Component Props", () => {
        it("handles all optional props", () => {
            const pattern = /^[a-zA-Z\s]+$/;
            const props = {
                name: "fullTestField",
                label: TEXTS.fullLabel,
                placeholder: TEXTS.customPlaceholder,
                defaultValue: TEXTS.fullDefaultValue,
                pattern,
                patternMessage: TEXTS.fullPatternMessage,
                requiredText: TEXTS.fullRequiredText,
                maxLength: 100,
                minLength: 2,
                type: "text" as const,
                description: TEXTS.description,
                tooltip: TEXTS.tooltip,
                required: true
            };

            render(<ShortText {...props} />);

            expect(screen.getByTestId("text-input")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.fullLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.description)).toBeInTheDocument();
            expect(screen.getByTestId("tooltip-icon")).toBeInTheDocument();
        });

        it("passes through HTML input attributes", () => {
            render(<ShortText {...defaultProps} />);

            const input = screen.getByTestId("text-input");
            expect(input).toHaveAttribute("placeholder", TEXTS.placeholder);
            expect(input).toHaveAttribute("type", "text");
            expect(input).toHaveAttribute("id", "testField");
        });
    });
});
