import { render, screen } from "@testing-library/react";
import { Control } from "react-hook-form";

import { renderField } from "@/components/forms/fields/render-field";
import type { FormValues, Question } from "@/components/forms/dynamic-questions-form/types";

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, label, required, placeholder, tooltip, description }: any) => (
        <div data-testid="short-text">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="placeholder">{placeholder}</span>
            <span data-testid="tooltip">{tooltip}</span>
            <span data-testid="description">{description}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/long-text", () => ({
    LongText: ({ name, label, required, placeholder, tooltip, description }: any) => (
        <div data-testid="long-text">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="placeholder">{placeholder}</span>
            <span data-testid="tooltip">{tooltip}</span>
            <span data-testid="description">{description}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ name, label, required, options, showSearch }: any) => (
        <div data-testid="single-select">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="options">{JSON.stringify(options)}</span>
            <span data-testid="showSearch">{showSearch?.toString()}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ name, label, required, options, showSearch }: any) => (
        <div data-testid="multi-select">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="options">{JSON.stringify(options)}</span>
            <span data-testid="showSearch">{showSearch?.toString()}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ name, label, required, min, max }: any) => (
        <div data-testid="number-input">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="min">{min?.toString()}</span>
            <span data-testid="max">{max?.toString()}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/date-picker", () => ({
    DatePicker: ({ name, label, required }: any) => (
        <div data-testid="date-picker">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/address-select", () => ({
    AddressSelect: ({ name, label, required, defaultValue }: any) => (
        <div data-testid="address-select">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="defaultValue">{defaultValue}</span>
        </div>
    )
}));

jest.mock("@/components/forms/fields/bank-select", () => ({
    BankSelect: ({ name, label, required, defaultValue }: any) => (
        <div data-testid="bank-select">
            <span data-testid="name">{name}</span>
            <span data-testid="label">{label}</span>
            <span data-testid="required">{required.toString()}</span>
            <span data-testid="defaultValue">{defaultValue ? JSON.stringify(defaultValue) : ""}</span>
        </div>
    )
}));

describe("renderField", () => {
    const mockControl = {} as Control<FormValues>;
    const mockFormValues: FormValues = {};

    const createQuestion = (type: Question["type"], metadata: Partial<Question["metadata"]> = {}): Question => ({
        id: "test-question-id",
        type,
        metadata: {
            label: "Test Label",
            ...metadata
        },
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        group_id: "test-group",
        section: "personal_details"
    });

    describe("short_text field", () => {
        it("renders ShortText component with correct props", () => {
            const question = createQuestion("short_text", {
                label: "Short Text Label",
                required: true,
                placeholder: "Enter text",
                tooltip: "This is a tooltip",
                description: "This is a description"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("short-text")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Short Text Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("placeholder")).toHaveTextContent("Enter text");
            expect(screen.getByTestId("tooltip")).toHaveTextContent("This is a tooltip");
            expect(screen.getByTestId("description")).toHaveTextContent("This is a description");
        });

        it("renders ShortText with default values when metadata is minimal", () => {
            const question = createQuestion("short_text", {
                label: "Minimal Label"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("short-text")).toBeInTheDocument();
            expect(screen.getByTestId("required")).toHaveTextContent("false");
            expect(screen.getByTestId("placeholder")).toHaveTextContent("");
            expect(screen.getByTestId("tooltip")).toHaveTextContent("");
            expect(screen.getByTestId("description")).toHaveTextContent("");
        });
    });

    describe("long_text field", () => {
        it("renders LongText component with correct props", () => {
            const question = createQuestion("long_text", {
                label: "Long Text Label",
                required: true,
                placeholder: "Enter long text",
                tooltip: "This is a tooltip",
                description: "This is a description"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("long-text")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Long Text Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("placeholder")).toHaveTextContent("Enter long text");
            expect(screen.getByTestId("tooltip")).toHaveTextContent("This is a tooltip");
            expect(screen.getByTestId("description")).toHaveTextContent("This is a description");
        });
    });

    describe("single_select field", () => {
        it("renders SingleSelect component with options", () => {
            const options = [
                { id: "option1", label: "Option 1" },
                { id: "option2", label: "Option 2" }
            ];

            const question = createQuestion("single_select", {
                label: "Single Select Label",
                required: true,
                options: options,
                showSearch: true
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("single-select")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Single Select Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("options")).toHaveTextContent(JSON.stringify(options));
            expect(screen.getByTestId("showSearch")).toHaveTextContent("true");
        });

        it("handles empty options array", () => {
            const question = createQuestion("single_select", {
                label: "Single Select Label",
                options: []
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("single-select")).toBeInTheDocument();
            expect(screen.getByTestId("options")).toHaveTextContent("[]");
        });

        it("handles undefined options", () => {
            const question = createQuestion("single_select", {
                label: "Single Select Label"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("single-select")).toBeInTheDocument();
            expect(screen.getByTestId("options")).toHaveTextContent("[]");
        });

        it("handles non-array options", () => {
            const question = createQuestion("single_select", {
                label: "Single Select Label",
                options: "not-an-array" as any
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("single-select")).toBeInTheDocument();
            expect(screen.getByTestId("options")).toHaveTextContent("[]");
        });
    });

    describe("multi_select field", () => {
        it("renders MultiSelect component with options", () => {
            const options = [
                { id: "option1", label: "Option 1" },
                { id: "option2", label: "Option 2" },
                { id: "option3", label: "Option 3" }
            ];

            const question = createQuestion("multi_select", {
                label: "Multi Select Label",
                required: true,
                options: options,
                showSearch: false
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("multi-select")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Multi Select Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("options")).toHaveTextContent(JSON.stringify(options));
            expect(screen.getByTestId("showSearch")).toHaveTextContent("false");
        });
    });

    describe("number_input field", () => {
        it("renders NumberInput component with min and max", () => {
            const question = createQuestion("number_input", {
                label: "Number Label",
                required: true,
                min: 0,
                max: 100
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("number-input")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Number Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("min")).toHaveTextContent("0");
            expect(screen.getByTestId("max")).toHaveTextContent("100");
        });

        it("renders NumberInput without min and max", () => {
            const question = createQuestion("number_input", {
                label: "Number Label"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("number-input")).toBeInTheDocument();
            expect(screen.getByTestId("min")).toHaveTextContent("");
            expect(screen.getByTestId("max")).toHaveTextContent("");
        });
    });

    describe("date_picker field", () => {
        it("renders DatePicker component", () => {
            const question = createQuestion("date_picker", {
                label: "Date Label",
                required: true
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("date-picker")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Date Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
        });
    });

    describe("address_select field", () => {
        it("renders AddressSelect component with string defaultValue", () => {
            const formValues: FormValues = {
                "test-question-id": "Jerusalem"
            };

            const question = createQuestion("address_select", {
                label: "Address Label",
                required: true
            });

            const result = renderField(question, mockControl, formValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("address-select")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Address Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("Jerusalem");
        });

        it("renders AddressSelect component without defaultValue", () => {
            const question = createQuestion("address_select", {
                label: "Address Label"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("address-select")).toBeInTheDocument();
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("");
        });

        it("handles non-string form value", () => {
            const formValues: FormValues = {
                "test-question-id": { notAString: true } as any
            };

            const question = createQuestion("address_select", {
                label: "Address Label"
            });

            const result = renderField(question, mockControl, formValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("address-select")).toBeInTheDocument();
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("");
        });
    });

    describe("bank_select field", () => {
        it("renders BankSelect component with object defaultValue", () => {
            const bankSelection = { bankCode: 12, branchCode: 123 };
            const formValues: FormValues = {
                "test-question-id": bankSelection
            };

            const question = createQuestion("bank_select", {
                label: "Bank Label",
                required: true
            });

            const result = renderField(question, mockControl, formValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("bank-select")).toBeInTheDocument();
            expect(screen.getByTestId("name")).toHaveTextContent("test-question-id");
            expect(screen.getByTestId("label")).toHaveTextContent("Bank Label");
            expect(screen.getByTestId("required")).toHaveTextContent("true");
            expect(screen.getByTestId("defaultValue")).toHaveTextContent(JSON.stringify(bankSelection));
        });

        it("renders BankSelect component without defaultValue", () => {
            const question = createQuestion("bank_select", {
                label: "Bank Label"
            });

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("bank-select")).toBeInTheDocument();
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("");
        });

        it("handles string form value", () => {
            const formValues: FormValues = {
                "test-question-id": "not-an-object"
            };

            const question = createQuestion("bank_select", {
                label: "Bank Label"
            });

            const result = renderField(question, mockControl, formValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("bank-select")).toBeInTheDocument();
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("");
        });

        it("handles array form value", () => {
            const formValues: FormValues = {
                "test-question-id": ["1", "2", "3"]
            };

            const question = createQuestion("bank_select", {
                label: "Bank Label"
            });

            const result = renderField(question, mockControl, formValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("bank-select")).toBeInTheDocument();
            expect(screen.getByTestId("defaultValue")).toHaveTextContent("");
        });
    });

    describe("default case", () => {
        it("returns null for unknown question type", () => {
            const question = createQuestion("unknown_type" as any, {
                label: "Unknown Label"
            });

            const result = renderField(question, mockControl, mockFormValues);

            expect(result).toBeNull();
        });
    });

    describe("common props", () => {
        it("passes control prop to all field types", () => {
            const types: Question["type"][] = [
                "short_text",
                "long_text",
                "single_select",
                "multi_select",
                "number_input",
                "date_picker",
                "address_select",
                "bank_select"
            ];

            types.forEach((type) => {
                const question = createQuestion(type, { label: `${type} label` });
                const result = renderField(question, mockControl, mockFormValues);

                expect(result).not.toBeNull();
            });
        });

        it("uses question id as field name", () => {
            const question = createQuestion("short_text", { label: "Test" });
            question.id = "custom-field-id";

            const result = renderField(question, mockControl, mockFormValues);
            render(<div>{result}</div>);

            expect(screen.getByTestId("name")).toHaveTextContent("custom-field-id");
        });
    });
});
