import "@testing-library/jest-dom";
import { act, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";
import { useFormContext } from "react-hook-form";

import { createContact, getContactById, updateContact } from "@/app/actions/contact-actions";
import { ContactForm } from "@/components/forms/contact-form";
import { TEXTS } from "@/lib/contact-constants";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        error: jest.fn(),
        success: jest.fn(),
        loading: jest.fn(() => "toast-id")
    }
}));

jest.mock("@/app/actions/contact-actions", () => ({
    createContact: jest.fn(),
    getContactById: jest.fn(),
    updateContact: jest.fn()
}));

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, label, required, placeholder, pattern, patternMessage, ...props }: any) => {
        const {
            register,
            formState: { errors }
        } = useFormContext();
        const validation: any = {};
        if (required) {
            validation.required = TEXTS.emailRequired;
        }
        if (pattern) {
            validation.pattern = { value: pattern, message: patternMessage };
        }

        const error = errors[name];

        return (
            <div>
                <label htmlFor={name}>
                    {label} {required && "*"}
                </label>
                <input
                    id={name}
                    data-testid={`input-${name}`}
                    placeholder={placeholder}
                    {...register(name, validation)}
                    {...props}
                />
                {error && <span data-testid={`error-${name}`}>{error.message?.toString()}</span>}
            </div>
        );
    }
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ children, onClick, ...props }: any) => (
        <button {...props} onClick={onClick}>
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/form", () => ({
    FormMessage: () => <div data-testid="form-message" />
}));

const mockCreateContact = createContact as jest.MockedFunction<typeof createContact>;
const mockGetContactById = getContactById as jest.MockedFunction<typeof getContactById>;
const mockUpdateContact = updateContact as jest.MockedFunction<typeof updateContact>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockToast = toast as jest.Mocked<typeof toast>;

const mockRouter = {
    push: jest.fn()
};

describe("ContactForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseRouter.mockReturnValue(mockRouter as any);
    });

    it("renders create form", () => {
        render(<ContactForm />);
        expect(screen.getByTestId("input-email")).toBeInTheDocument();
        expect(screen.getByTestId("submit-button")).toHaveTextContent(TEXTS.submit);
    });

    it("renders edit form and loads data", async () => {
        mockGetContactById.mockResolvedValueOnce({
            success: true,
            data: {
                id: "123",
                email: "<EMAIL>",
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            }
        });
        await act(async () => {
            render(<ContactForm contactId="123" />);
        });
        await waitFor(() => {
            expect(mockGetContactById).toHaveBeenCalledWith("123");
            expect(screen.getByTestId("input-email")).toHaveValue("<EMAIL>");
        });
    });

    it("shows error if loading contact fails", async () => {
        mockGetContactById.mockResolvedValueOnce({ success: false, error: "Not found" });
        await act(async () => {
            render(<ContactForm contactId="bad-id" />);
        });
        await waitFor(() => {
            expect(mockToast.error).toHaveBeenCalledWith(TEXTS.loadingError, expect.anything());
        });
    });

    it("validates required email", async () => {
        const user = userEvent.setup();
        render(<ContactForm />);

        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(screen.getByTestId("error-email")).toHaveTextContent(TEXTS.emailRequired);
        });
    });

    it("validates invalid email format", async () => {
        const user = userEvent.setup();
        render(<ContactForm />);
        const input = screen.getByTestId("input-email");

        await user.type(input, "not-an-email");
        await user.tab();
        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(screen.getByTestId("error-email")).toHaveTextContent(TEXTS.emailError);
        });
    });

    it("submits and creates contact successfully", async () => {
        mockCreateContact.mockResolvedValueOnce({ success: true });
        const user = userEvent.setup();
        render(<ContactForm />);
        const input = screen.getByTestId("input-email");

        await user.type(input, "<EMAIL>");
        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(mockCreateContact).toHaveBeenCalledWith({ email: "<EMAIL>" });
            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.submitSuccess, { id: "toast-id" });
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/contact");
        });
    });

    it("submits and updates contact successfully", async () => {
        mockUpdateContact.mockResolvedValueOnce({ success: true });
        mockGetContactById.mockResolvedValueOnce({
            success: true,
            data: {
                id: "123",
                email: "<EMAIL>",
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            }
        });
        const user = userEvent.setup();

        render(<ContactForm contactId="123" />);

        await waitFor(() => {
            expect(screen.getByTestId("input-email")).toHaveValue("<EMAIL>");
        });

        const input = screen.getByTestId("input-email");
        await user.clear(input);
        await user.type(input, "<EMAIL>");
        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(mockUpdateContact).toHaveBeenCalledWith("123", { email: "<EMAIL>" });
            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.submitSuccess, { id: "toast-id" });
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/contact");
        });
    });

    it("shows error on create failure", async () => {
        mockCreateContact.mockResolvedValueOnce({ success: false, error: "API error" });
        const user = userEvent.setup();
        render(<ContactForm />);
        const input = screen.getByTestId("input-email");

        await user.type(input, "<EMAIL>");
        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(mockToast.error).toHaveBeenCalledWith(
                TEXTS.createError,
                expect.objectContaining({ id: "toast-id" })
            );
        });
    });

    it("shows error on update failure", async () => {
        mockUpdateContact.mockResolvedValueOnce({ success: false, error: "API error" });
        mockGetContactById.mockResolvedValueOnce({
            success: true,
            data: {
                id: "123",
                email: "<EMAIL>",
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            }
        });
        const user = userEvent.setup();

        render(<ContactForm contactId="123" />);

        await waitFor(() => {
            expect(screen.getByTestId("input-email")).toHaveValue("<EMAIL>");
        });

        const input = screen.getByTestId("input-email");
        await user.clear(input);
        await user.type(input, "<EMAIL>");
        await user.click(screen.getByTestId("submit-button"));

        await waitFor(() => {
            expect(mockToast.error).toHaveBeenCalledWith(
                TEXTS.updateError,
                expect.objectContaining({ id: "toast-id" })
            );
        });
    });

    it("disables submit button while submitting", async () => {
        const user = userEvent.setup();
        render(<ContactForm />);
        const button = screen.getByTestId("submit-button");
        expect(button).not.toBeDisabled();

        await user.type(screen.getByTestId("input-email"), "<EMAIL>");
        await user.click(button);
    });

    it("navigates on cancel", async () => {
        const user = userEvent.setup();
        render(<ContactForm />);
        const cancelButton = screen.getByTestId("cancel-button");

        await user.click(cancelButton);

        expect(mockRouter.push).toHaveBeenCalledWith("/admin/contact");
    });
});
