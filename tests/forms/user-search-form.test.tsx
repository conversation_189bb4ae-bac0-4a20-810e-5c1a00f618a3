import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { UserSearchForm } from "@/components/forms/user-search-form";
import { searchUserByEmail } from "@/app/actions/user-actions";
import { TEXTS } from "@/lib/user-constants";

// Mock dependencies
jest.mock("next/navigation");
jest.mock("@/app/actions/user-actions");
jest.mock("sonner");
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({ userId: "test-user-id" }))
}));

const mockPush = jest.fn();
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockSearchUserByEmail = searchUserByEmail as jest.MockedFunction<typeof searchUserByEmail>;
const mockToast: jest.Mocked<Pick<typeof toast, "error" | "success">> = {
    error: jest.fn(),
    success: jest.fn()
};

toast.error = mockToast.error;
toast.success = mockToast.success;
toast.info = jest.fn();
toast.warning = jest.fn();
toast.loading = jest.fn();
toast.custom = jest.fn();
toast.message = jest.fn();
toast.promise = jest.fn();
toast.dismiss = jest.fn();
toast.getHistory = jest.fn();
toast.getToasts = jest.fn();

describe("UserSearchForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseRouter.mockReturnValue({
            push: mockPush,
            back: jest.fn(),
            forward: jest.fn(),
            refresh: jest.fn(),
            replace: jest.fn(),
            prefetch: jest.fn()
        });
    });

    describe("Rendering", () => {
        it("renders all form elements correctly", () => {
            render(<UserSearchForm />);

            expect(screen.getByLabelText(TEXTS.emailLabel)).toBeInTheDocument();
            expect(screen.getByPlaceholderText(TEXTS.emailPlaceholder)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.searchButton })).toBeInTheDocument();
        });

        it("renders email input with correct attributes", () => {
            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            expect(emailInput).toHaveAttribute("type", "text");
            expect(emailInput).toHaveAttribute("placeholder", TEXTS.emailPlaceholder);
            expect(emailInput).toBeInTheDocument();
        });

        it("renders submit button with correct initial state", () => {
            render(<UserSearchForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });
            expect(submitButton).toBeEnabled();
            expect(submitButton).toHaveAttribute("type", "submit");
        });
    });

    describe("Form Validation", () => {
        it("prevents submission with invalid email format", async () => {
            const user = userEvent.setup();
            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, "invalid");
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.emailError)).toBeInTheDocument();
            });
            expect(mockSearchUserByEmail).not.toHaveBeenCalled();
        });

        it("shows validation error for empty email", async () => {
            const user = userEvent.setup();
            render(<UserSearchForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.emailRequired)).toBeInTheDocument();
            });
            expect(mockSearchUserByEmail).not.toHaveBeenCalled();
        });

        it("accepts valid email format", async () => {
            const user = userEvent.setup();
            const validEmail = "<EMAIL>";

            mockSearchUserByEmail.mockResolvedValue({
                success: true,
                user: { id: "1", email: validEmail, user_id: "user-123" }
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, validEmail);
            await user.click(submitButton);

            expect(mockSearchUserByEmail).toHaveBeenCalledWith(validEmail);
        });
    });

    describe("Form Submission", () => {
        it("calls searchUserByEmail with correct email", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";

            mockSearchUserByEmail.mockResolvedValue({
                success: true,
                user: { id: "1", email: testEmail, user_id: "user-123" }
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            expect(mockSearchUserByEmail).toHaveBeenCalledWith(testEmail);
        });

        it("navigates to user detail page on successful search", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";
            const userId = "user-123";

            mockSearchUserByEmail.mockResolvedValue({
                success: true,
                user: { id: "1", email: testEmail, user_id: userId }
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockPush).toHaveBeenCalledWith(`/admin/users/${userId}`);
            });
        });

        it("shows error message when user not found", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";

            mockSearchUserByEmail.mockResolvedValue({
                success: true,
                user: undefined
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.userNotFound);
            });
            expect(mockPush).not.toHaveBeenCalled();
        });

        it("shows error message when search fails", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";
            const errorMessage = "Database error";

            mockSearchUserByEmail.mockResolvedValue({
                success: false,
                error: errorMessage
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(errorMessage);
            });
            expect(mockPush).not.toHaveBeenCalled();
        });

        it("handles unexpected errors gracefully", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";
            const consoleError = jest.spyOn(console, "error").mockImplementation();

            mockSearchUserByEmail.mockRejectedValue(new Error("Network error"));

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.userSearchError);
            });
            expect(consoleError).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();

            consoleError.mockRestore();
        });
    });

    describe("Loading State", () => {
        it("shows loading state during submission", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";

            // Mock a delayed response
            mockSearchUserByEmail.mockImplementation(
                () =>
                    new Promise((resolve) =>
                        setTimeout(
                            () =>
                                resolve({
                                    success: true,
                                    user: { id: "1", email: testEmail, user_id: "user-123" }
                                }),
                            100
                        )
                    )
            );

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            // Check loading state
            expect(screen.getByText(TEXTS.searching)).toBeInTheDocument();
            expect(submitButton).toBeDisabled();
        });

        it("returns to normal state after submission", async () => {
            const user = userEvent.setup();
            const testEmail = "<EMAIL>";

            mockSearchUserByEmail.mockResolvedValue({
                success: true,
                user: { id: "1", email: testEmail, user_id: "user-123" }
            });

            render(<UserSearchForm />);

            const emailInput = screen.getByLabelText(TEXTS.emailLabel);
            const submitButton = screen.getByRole("button", { name: TEXTS.searchButton });

            await user.type(emailInput, testEmail);
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.searchButton)).toBeInTheDocument();
                expect(submitButton).toBeEnabled();
            });
        });
    });

    describe("Form Layout", () => {
        it("uses correct spacing classes", () => {
            render(<UserSearchForm />);

            const form = screen.getByRole("button", { name: TEXTS.searchButton }).closest("form");
            expect(form).toHaveClass("space-y-8");
        });

        it("has proper button container layout", () => {
            render(<UserSearchForm />);

            const buttonContainer = screen.getByRole("button", { name: TEXTS.searchButton }).parentElement;
            expect(buttonContainer).toHaveClass("flex", "justify-between");
        });
    });
});
