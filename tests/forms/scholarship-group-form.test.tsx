import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import {
    createScholarshipGroup,
    getScholarshipGroup,
    updateScholarshipGroup,
    uploadScholarshipGroupImage
} from "@/app/actions/scholarship-group-actions";
import { ScholarshipGroupForm } from "@/components/forms/scholarship-group-form";
import { TEXTS } from "@/lib/scholarship-group-constants";

// Mock dependencies
jest.mock("next/navigation");
jest.mock("sonner");
jest.mock("@/app/actions/scholarship-group-actions");

const mockRouter = {
    push: jest.fn()
};

const mockCreateScholarshipGroup = createScholarshipGroup as jest.MockedFunction<typeof createScholarshipGroup>;
const mockGetScholarshipGroup = getScholarshipGroup as jest.MockedFunction<typeof getScholarshipGroup>;
const mockUpdateScholarshipGroup = updateScholarshipGroup as jest.MockedFunction<typeof updateScholarshipGroup>;
const mockUploadScholarshipGroupImage = uploadScholarshipGroupImage as jest.MockedFunction<
    typeof uploadScholarshipGroupImage
>;
const mockToast = {
    error: jest.fn(),
    success: jest.fn()
};

beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (toast as any).error = mockToast.error;
    (toast as any).success = mockToast.success;
});

describe("ScholarshipGroupForm", () => {
    describe("Create Mode", () => {
        it("renders create form with all required fields", () => {
            render(<ScholarshipGroupForm />);

            expect(screen.getByLabelText(TEXTS.groupNameLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.groupSlugLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.descriptionLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.iconLabel)).toBeInTheDocument(); // Icon uses SingleSelect component
            expect(screen.getByLabelText(TEXTS.imageLabel)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
        });

        it("shows validation errors for required fields", async () => {
            const user = userEvent.setup();
            render(<ScholarshipGroupForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.groupNameRequired)).toBeInTheDocument();
            });
        });

        it("validates slug pattern", async () => {
            const user = userEvent.setup();
            render(<ScholarshipGroupForm />);

            const slugInput = screen.getByLabelText(TEXTS.groupSlugLabel);
            await user.type(slugInput, "invalid slug!");

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.groupSlugPattern)).toBeInTheDocument();
            });
        });

        it("renders form fields correctly and allows input", async () => {
            const user = userEvent.setup();
            render(<ScholarshipGroupForm />);

            // Test form inputs work
            const titleInput = screen.getByLabelText(TEXTS.groupNameLabel);
            const slugInput = screen.getByLabelText(TEXTS.groupSlugLabel);
            const descriptionInput = screen.getByLabelText(TEXTS.descriptionLabel);

            await user.type(titleInput, "Test Title");
            await user.type(slugInput, "test-slug");
            await user.type(descriptionInput, "Test description");

            expect(titleInput).toHaveValue("Test Title");
            expect(slugInput).toHaveValue("test-slug");
            expect(descriptionInput).toHaveValue("Test description");
        });

        it("shows submit and cancel buttons", () => {
            render(<ScholarshipGroupForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            const cancelButton = screen.getByRole("button", { name: TEXTS.cancelButtonText });

            expect(submitButton).toBeInTheDocument();
            expect(cancelButton).toBeInTheDocument();
        });
    });

    describe("Edit Mode", () => {
        const mockScholarshipGroup = {
            id: "test-id",
            title: "Existing Group",
            slug: "existing-group",
            description: "Existing description",
            icon: "award",
            image_url: "http://example.com/image.webp",
            created_at: "2023-01-01T00:00:00Z",
            updated_at: "2023-01-01T00:00:00Z"
        };

        it("loads existing scholarship group data", async () => {
            mockGetScholarshipGroup.mockResolvedValue({
                success: true,
                data: mockScholarshipGroup
            });

            render(<ScholarshipGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(mockGetScholarshipGroup).toHaveBeenCalledWith("test-id");
            });

            await waitFor(() => {
                expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
                expect(screen.getByDisplayValue("existing-group")).toBeInTheDocument();
                expect(screen.getByDisplayValue("Existing description")).toBeInTheDocument();
            });
        });

        it("shows loading state while fetching data", () => {
            mockGetScholarshipGroup.mockImplementation(() => new Promise(() => {})); // Never resolves

            render(<ScholarshipGroupForm groupId="test-id" />);

            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();
        });

        it("handles fetch error", async () => {
            mockGetScholarshipGroup.mockResolvedValue({
                success: false,
                error: "Not found"
            });

            render(<ScholarshipGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith("Not found");
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships");
            });
        });

        it("successfully updates scholarship group", async () => {
            const user = userEvent.setup();
            mockGetScholarshipGroup.mockResolvedValue({
                success: true,
                data: mockScholarshipGroup
            });
            mockUpdateScholarshipGroup.mockResolvedValue({ success: true });

            render(<ScholarshipGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
            });

            // Update title
            const titleInput = screen.getByDisplayValue("Existing Group");
            await user.clear(titleInput);
            await user.type(titleInput, "Updated Group");

            const submitButton = screen.getByRole("button", { name: TEXTS.updateButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockUpdateScholarshipGroup).toHaveBeenCalledWith("test-id", {
                    title: "Updated Group",
                    slug: "existing-group",
                    description: "Existing description",
                    icon: "award"
                });
                expect(mockToast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage, {
                    id: "scholarship-group-updated"
                });
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships");
            });
        });

        it("handles update error", async () => {
            const user = userEvent.setup();
            mockGetScholarshipGroup.mockResolvedValue({
                success: true,
                data: mockScholarshipGroup
            });
            mockUpdateScholarshipGroup.mockResolvedValue({
                success: false,
                error: "Update failed"
            });

            render(<ScholarshipGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.updateButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith("Update failed");
            });
        });
    });

    describe("Icon Selection", () => {
        it("shows icon preview", async () => {
            const user = userEvent.setup();
            render(<ScholarshipGroupForm />);

            // Check default icon preview is shown
            expect(screen.getByTestId("icon-preview")).toBeInTheDocument();

            // Check that icon selection is rendered (SingleSelect component renders as combobox)
            const iconSelect = screen.getByRole("combobox");
            expect(iconSelect).toBeInTheDocument();

            // Default icon text should be visible
            expect(screen.getByText("כובע סיום")).toBeInTheDocument(); // Default icon label

            // Icon preview should remain visible
            expect(screen.getByTestId("icon-preview")).toBeInTheDocument();
        });
    });

    describe("Form Navigation", () => {
        it("navigates to scholarships page on cancel", async () => {
            const user = userEvent.setup();
            render(<ScholarshipGroupForm />);

            const cancelButton = screen.getByRole("button", { name: TEXTS.cancelButtonText });
            await user.click(cancelButton);

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships");
        });
    });

    describe("File Upload", () => {
        it("renders file upload field", () => {
            render(<ScholarshipGroupForm />);

            const fileInput = screen.getByLabelText(TEXTS.imageLabel);
            expect(fileInput).toBeInTheDocument();
            expect(fileInput).toHaveAttribute("accept", "image/webp");
            expect(fileInput).toHaveAttribute("type", "file");
        });

        it("shows upload instructions", () => {
            render(<ScholarshipGroupForm />);

            expect(screen.getByText(TEXTS.dragDropText)).toBeInTheDocument();
            expect(screen.getByText(/קבצי WebP בלבד/)).toBeInTheDocument(); // Part of file format description
        });
    });
});
