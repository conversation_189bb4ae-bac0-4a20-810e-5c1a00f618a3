import "@testing-library/jest-dom";

import { act, cleanup, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";

import * as collaborationActions from "@/app/actions/collaboration-actions";
import * as collaborationFormActions from "@/app/actions/collaboration-form-actions";
import { CollaborationForm } from "@/components/forms/collaboration-form";
import { TEXTS, type AuthType } from "@/lib/collaboration-constants";
import { type Tables } from "@/types/database.types";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/collaboration-actions", () => ({
    createCollaboration: jest.fn(),
    updateCollaboration: jest.fn(),
    getCollaborationById: jest.fn()
}));

jest.mock("@/app/actions/collaboration-form-actions", () => ({
    getAllQuestionsForCollaboration: jest.fn(),
    getCollaborationConditions: jest.fn(),
    getCollaborationQuestions: jest.fn()
}));

// Mock form field components
jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <input id={name} name={name} data-testid={`input-${name}`} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/long-text", () => ({
    LongText: ({ name, label }: { name: string; label: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <textarea id={name} name={name} data-testid={`textarea-${name}`} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <select id={name} name={name} data-testid={`select-${name}`}>
                <option value="">Select...</option>
                <option value="bearer_token">Bearer Token</option>
                <option value="none">ללא אימות</option>
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ name, label, required }: { name: string; label: string; required?: boolean }) => (
        <div>
            <label htmlFor={name}>
                {label} {required && "*"}
            </label>
            <select id={name} name={name} multiple data-testid={`multiselect-${name}`}>
                <option value="question1">Question 1</option>
                <option value="question2">Question 2</option>
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/condition-selector", () => ({
    ConditionSelector: ({ onAdd, onRemove }: { onAdd: () => void; onRemove: (index: number) => void }) => (
        <div data-testid="condition-selector">
            <button type="button" onClick={onAdd} data-testid="add-condition">
                Add Condition
            </button>
            <button type="button" onClick={() => onRemove(0)} data-testid="remove-condition">
                Remove Condition
            </button>
        </div>
    )
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text: string }) => <div data-testid="loading-icon">{text}</div>
}));

jest.mock("@/components/ui/form", () => ({
    FormMessage: () => <div data-testid="form-message" />
}));

// Mock react-hook-form
let mockFormData: {
    name: string;
    description: string;
    api_endpoint: string;
    auth_type: { id: string; label: string } | null;
    auth_value: string;
    dependencies: unknown[];
    question_ids: Array<{ id: string; label: string }>;
} = {
    name: "",
    description: "",
    api_endpoint: "",
    auth_type: null,
    auth_value: "",
    dependencies: [],
    question_ids: []
};

let mockFormState = { isSubmitting: false };

const mockFormMethods = {
    handleSubmit: jest.fn((fn) => (e?: React.FormEvent) => {
        e?.preventDefault();
        fn(mockFormData);
    }),
    formState: mockFormState,
    watch: jest.fn((name: string) => {
        if (name === "auth_type") return mockFormData.auth_type;
        return mockFormData[name as keyof typeof mockFormData];
    }),
    getValues: jest.fn((name?: string) => {
        if (name) return mockFormData[name as keyof typeof mockFormData];
        return mockFormData;
    }),
    setValue: jest.fn((name: string, value: unknown) => {
        (mockFormData as Record<string, unknown>)[name] = value;
    }),
    reset: jest.fn((data: Partial<typeof mockFormData>) => {
        mockFormData = { ...mockFormData, ...data };
    })
};

jest.mock("react-hook-form", () => ({
    useForm: () => mockFormMethods,
    FormProvider: ({ children }: { children: React.ReactNode }) => children
}));

type Collaboration = Tables<"collaborations">;

const mockCollaboration: Collaboration = {
    id: "collab-1",
    name: "Test Collaboration",
    description: "Test Description",
    api_endpoint: "https://api.example.com/webhook",
    auth_type: "bearer_token" as AuthType,
    auth_value: "test-token",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
};

const mockQuestions = [
    {
        id: "question1",
        type: "single_select" as const,
        metadata: { label: "Age Range", placeholder: "Select age" },
        groups_question: { id: "group1", name: "Personal Info" }
    },
    {
        id: "question2",
        type: "short_text" as const,
        metadata: { label: "Full Name", placeholder: "Enter name" },
        groups_question: { id: "group1", name: "Personal Info" }
    }
];

const mockConditions = [
    {
        id: "condition1",
        question_id: "question1",
        condition_type: "in",
        condition_value: ["18-25", "26-35"]
    }
];

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const resetMockFormData = () => {
    mockFormData = {
        name: "",
        description: "",
        api_endpoint: "",
        auth_type: null,
        auth_value: "",
        dependencies: [],
        question_ids: []
    };
    mockFormState = { isSubmitting: false };
};

describe("CollaborationForm", () => {
    const mockPush = jest.fn();
    const mockRouter = { push: mockPush };

    beforeEach(() => {
        jest.clearAllMocks();
        resetMockFormData();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);

        // Default mock implementations
        (collaborationFormActions.getAllQuestionsForCollaboration as jest.Mock).mockResolvedValue({
            success: true,
            data: mockQuestions
        });

        (collaborationFormActions.getCollaborationConditions as jest.Mock).mockResolvedValue({
            success: true,
            data: []
        });

        (collaborationFormActions.getCollaborationQuestions as jest.Mock).mockResolvedValue({
            success: true,
            data: []
        });

        jest.spyOn(console, "error").mockImplementation(() => {
            // Suppress console.error in tests
        });
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Create Mode", () => {
        it("renders create form with all required fields", async () => {
            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByLabelText(new RegExp(TEXTS.nameLabel))).toBeInTheDocument();
            });

            expect(screen.getByLabelText(TEXTS.descriptionLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(new RegExp(TEXTS.apiEndpointLabel))).toBeInTheDocument();
            expect(screen.getByLabelText(new RegExp(TEXTS.authTypeLabel))).toBeInTheDocument();
            expect(screen.getByText(TEXTS.conditionsLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.questionsLabel)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
        });

        it("handles questions fetching error", async () => {
            (collaborationFormActions.getAllQuestionsForCollaboration as jest.Mock).mockResolvedValue({
                success: false,
                error: "Failed to fetch questions"
            });

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.fetchQuestionsError);
            });
        });

        it("submits create form successfully", async () => {
            const user = userEvent.setup();
            (collaborationActions.createCollaboration as jest.Mock).mockResolvedValue({
                success: true,
                id: "new-collab-id"
            });

            const formData = {
                name: "New Collaboration",
                description: "New Description",
                api_endpoint: "https://api.test.com/webhook",
                auth_type: { id: "bearer_token", label: "Bearer Token" },
                auth_value: "test-token",
                dependencies: [],
                question_ids: [{ id: "question1", label: "Age Range" }]
            };

            setMockFormData(formData);

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(collaborationActions.createCollaboration).toHaveBeenCalledWith({
                    ...formData,
                    auth_type: "bearer_token", // Should convert Option back to string
                    question_ids: ["question1"] // Should convert Option array back to string array
                });
                expect(toast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/collaborations");
            });
        });

        it("handles create form error", async () => {
            const user = userEvent.setup();
            (collaborationActions.createCollaboration as jest.Mock).mockResolvedValue({
                success: false,
                error: "Creation failed"
            });

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith("Creation failed");
            });
        });
    });

    describe("Edit Mode", () => {
        it("shows loading state in edit mode", () => {
            (collaborationActions.getCollaborationById as jest.Mock).mockImplementation(
                () => new Promise(() => {}) // Never resolves
            );

            render(<CollaborationForm collaborationId="collab-1" />);

            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.loadingText)).toBeInTheDocument();
        });

        it("fetches and displays collaboration data in edit mode", async () => {
            (collaborationActions.getCollaborationById as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCollaboration
            });

            (collaborationFormActions.getCollaborationConditions as jest.Mock).mockResolvedValue({
                success: true,
                data: mockConditions
            });

            (collaborationFormActions.getCollaborationQuestions as jest.Mock).mockResolvedValue({
                success: true,
                data: ["question1", "question2"]
            });

            render(<CollaborationForm collaborationId="collab-1" />);

            await waitFor(() => {
                expect(collaborationActions.getCollaborationById).toHaveBeenCalledWith("collab-1");
            });

            await waitFor(() => {
                expect(mockFormMethods.reset).toHaveBeenCalledWith({
                    name: mockCollaboration.name,
                    description: mockCollaboration.description,
                    api_endpoint: mockCollaboration.api_endpoint,
                    auth_type: { id: "bearer_token", label: "Bearer Token" },
                    auth_value: "test-token",
                    dependencies: expect.arrayContaining([
                        expect.objectContaining({
                            id: "condition1",
                            question_id: expect.objectContaining({
                                id: "question1"
                            })
                        })
                    ]),
                    question_ids: [
                        { id: "question1", label: "Age Range" },
                        { id: "question2", label: "Full Name" }
                    ]
                });
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });
        });

        it("handles collaboration not found", async () => {
            (collaborationActions.getCollaborationById as jest.Mock).mockResolvedValue({
                success: false,
                error: "Not found"
            });

            render(<CollaborationForm collaborationId="non-existent" />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.notFoundText)).toBeInTheDocument();
                expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
            });
        });

        it("submits update form successfully", async () => {
            const user = userEvent.setup();
            (collaborationActions.getCollaborationById as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCollaboration
            });

            (collaborationActions.updateCollaboration as jest.Mock).mockResolvedValue({
                success: true
            });

            render(<CollaborationForm collaborationId="collab-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
            });

            // Set form data after component mounts and before submission
            setMockFormData({
                name: "Updated Collaboration",
                description: "Updated Description",
                api_endpoint: "https://api.updated.com/webhook",
                auth_type: { id: "none", label: "ללא אימות" },
                auth_value: "",
                dependencies: [],
                question_ids: [{ id: "question2", label: "Full Name" }]
            });

            await user.click(screen.getByRole("button", { name: TEXTS.updateButtonText }));

            await waitFor(() => {
                expect(collaborationActions.updateCollaboration).toHaveBeenCalledWith("collab-1", {
                    name: "Updated Collaboration",
                    description: "Updated Description",
                    api_endpoint: "https://api.updated.com/webhook",
                    auth_type: "none", // Should convert Option back to string
                    auth_value: "",
                    dependencies: [],
                    question_ids: ["question2"] // Should convert Option array back to string array
                });
                expect(toast.success).toHaveBeenCalledWith(TEXTS.updateSuccessMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/collaborations");
            });
        });
    });

    describe("Auth Type Functionality", () => {
        it("shows auth value field when bearer token is selected", async () => {
            setMockFormData({
                ...mockFormData,
                auth_type: { id: "bearer_token", label: "Bearer Token" }
            });

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByLabelText(new RegExp(TEXTS.authValueLabel))).toBeInTheDocument();
            });
        });

        it("hides auth value field when no auth is selected", async () => {
            setMockFormData({
                ...mockFormData,
                auth_type: { id: "none", label: "ללא אימות" }
            });

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByLabelText(new RegExp(TEXTS.nameLabel))).toBeInTheDocument();
            });

            expect(screen.queryByLabelText(new RegExp(TEXTS.authValueLabel))).not.toBeInTheDocument();
        });
    });

    describe("Condition and Question Management", () => {
        it("renders condition selector", async () => {
            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("condition-selector")).toBeInTheDocument();
            });

            expect(screen.getByTestId("add-condition")).toBeInTheDocument();
            expect(screen.getByTestId("remove-condition")).toBeInTheDocument();
        });

        it("handles adding and removing conditions", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("add-condition")).toBeInTheDocument();
            });

            await user.click(screen.getByTestId("add-condition"));

            expect(mockFormMethods.setValue).toHaveBeenCalledWith(
                "dependencies",
                expect.arrayContaining([
                    expect.objectContaining({
                        question_id: { id: "", label: "" },
                        condition_type: "",
                        condition_value: null
                    })
                ])
            );

            await user.click(screen.getByTestId("remove-condition"));

            expect(mockFormMethods.setValue).toHaveBeenCalledWith("dependencies", []);
        });
    });

    describe("Form Navigation", () => {
        it("navigates back on cancel button click", async () => {
            const user = userEvent.setup();

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.cancelButtonText }));

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/collaborations");
        });
    });

    describe("Error Handling", () => {
        it("handles collaboration fetching error", async () => {
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
            const mockError = new Error("Failed to fetch collaboration");

            (collaborationActions.getCollaborationById as jest.Mock).mockRejectedValue(mockError);

            render(<CollaborationForm collaborationId="collab-1" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.fetchCollaborationError);
            });

            expect(consoleSpy).toHaveBeenCalledWith("Error fetching collaboration:", mockError);
            consoleSpy.mockRestore();
        });

        it("handles form submission exception", async () => {
            const user = userEvent.setup();
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
            const mockError = new Error("Network error");

            (collaborationActions.createCollaboration as jest.Mock).mockRejectedValue(mockError);

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.createErrorMessage);
            });

            expect(consoleSpy).toHaveBeenCalledWith("Error creating collaboration:", mockError);
            consoleSpy.mockRestore();
        });
    });

    describe("Data Conversion", () => {
        it("converts auth type from database string to Option object", async () => {
            const collaborationWithAuth = {
                ...mockCollaboration,
                auth_type: "bearer_token" as AuthType,
                auth_value: "test-token"
            };

            (collaborationActions.getCollaborationById as jest.Mock).mockResolvedValue({
                success: true,
                data: collaborationWithAuth
            });

            render(<CollaborationForm collaborationId="collab-1" />);

            await waitFor(() => {
                expect(mockFormMethods.reset).toHaveBeenCalledWith(
                    expect.objectContaining({
                        auth_type: { id: "bearer_token", label: "Bearer Token" },
                        auth_value: "test-token"
                    })
                );
            });
        });

        it("converts Option back to database string on form submission", async () => {
            const user = userEvent.setup();
            (collaborationActions.createCollaboration as jest.Mock).mockResolvedValue({
                success: true,
                id: "new-id"
            });

            setMockFormData({
                ...mockFormData,
                auth_type: { id: "bearer_token", label: "Bearer Token" }
            });

            await act(async () => {
                render(<CollaborationForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            });

            await user.click(screen.getByRole("button", { name: TEXTS.createButtonText }));

            await waitFor(() => {
                expect(collaborationActions.createCollaboration).toHaveBeenCalledWith(
                    expect.objectContaining({
                        auth_type: "bearer_token"
                    })
                );
            });
        });
    });
});
