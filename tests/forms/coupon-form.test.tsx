import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { CouponForm } from "@/components/forms/coupon-form";
import { TEXTS as NumberInputTexts } from "@/components/forms/fields/number-input";
import { TEXTS } from "@/lib/coupon-constants";

jest.mock("next/navigation");
jest.mock("sonner");

jest.mock("@/app/actions/coupon-actions", () => ({
    createCoupons: jest.fn(),
    getCoupon: jest.fn(),
    getCouponGroups: jest.fn(),
    updateCoupon: jest.fn()
}));

const { createCoupons, getCoupon, getCouponGroups, updateCoupon } = require("@/app/actions/coupon-actions");

const mockRouter = {
    push: jest.fn()
};

const mockCouponGroups = [
    { id: "group-1", label: "Group 1" },
    { id: "group-2", label: "Group 2" }
];

const mockCouponData = {
    id: "coupon-1",
    coupon_code: "TEST123",
    coupon_type: "percentage" as const,
    discount_value: 20,
    expiration_date: "2024-12-31T00:00:00.000Z",
    usage_limit: 100,
    coupon_group_id: "group-1"
};

describe("CouponForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);
        (getCouponGroups as jest.Mock).mockResolvedValue({
            success: true,
            data: mockCouponGroups
        });
    });

    describe("Create Mode", () => {
        it("renders create form with default single mode", async () => {
            render(<CouponForm />);

            expect(screen.getByText(TEXTS.singleCouponLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.multipleCouponLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.couponCodeLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.createButtonText)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.cancelButtonText)).toBeInTheDocument();

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalledTimes(1);
            });
        });

        it("switches to multiple mode when multiple tab is clicked", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            const multipleTab = screen.getByText(TEXTS.multipleCouponLabel);
            await user.click(multipleTab);

            expect(screen.getByLabelText(TEXTS.quantityLabel)).toBeInTheDocument();
            expect(screen.queryByLabelText(TEXTS.couponCodeLabel)).not.toBeInTheDocument();
        });

        it("shows all required form fields", async () => {
            render(<CouponForm />);

            await waitFor(() => {
                expect(screen.getByLabelText(TEXTS.couponCodeLabel)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.couponGroupLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.couponTypeLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.discountValueLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.expirationDateLabel)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.usageLimitLabel)).toBeInTheDocument();
        });

        it("allows filling out form fields", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);

            await user.type(codeInput, "TEST123");
            await user.type(discountInput, "20");

            expect(codeInput).toHaveValue("TEST123");
            expect(discountInput).toHaveValue(20);
        });

        it("allows switching to multiple mode and filling quantity", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            const multipleTab = screen.getByText(TEXTS.multipleCouponLabel);
            await user.click(multipleTab);

            const quantityInput = screen.getByLabelText(TEXTS.quantityLabel);
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);

            await user.clear(quantityInput);
            await user.type(quantityInput, "5");
            await user.type(discountInput, "15");

            expect(quantityInput).toHaveValue(5);
            expect(discountInput).toHaveValue(15);
        });

        it("prevents form submission when required fields are missing", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            expect(createCoupons).not.toHaveBeenCalled();
        });

        it("submits form successfully in single mode", async () => {
            const user = userEvent.setup();
            (createCoupons as jest.Mock).mockResolvedValue({ success: true, count: 1 });

            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "SUCCESS123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "25");

            // Submit the form
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Wait for createCoupons to be called with expected data
            await waitFor(() => {
                expect(createCoupons).toHaveBeenCalledWith({
                    creation_mode: "single",
                    coupon_code: "SUCCESS123",
                    quantity: 10, // default value
                    coupon_type: "fixed_amount", // from couponTypeOptions[0]
                    discount_value: 25,
                    expiration_date: null,
                    usage_limit: null,
                    coupon_group_id: "group-1"
                });
            });

            // Assert success toast is shown
            expect(toast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessageSingle, { id: "coupon-created" });

            // Assert router navigates to coupons admin page
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons");
        });
    });

    describe("Edit Mode", () => {
        it("renders loading state initially", () => {
            render(<CouponForm couponId="coupon-1" />);

            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();
        });

        it("loads and displays coupon data", async () => {
            (getCoupon as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCouponData
            });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(getCoupon).toHaveBeenCalledWith("coupon-1");
            });

            await waitFor(() => {
                expect(screen.getByDisplayValue("TEST123")).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.updateButtonText)).toBeInTheDocument();
            expect(screen.queryByText(TEXTS.singleCouponLabel)).not.toBeInTheDocument();
            expect(screen.queryByText(TEXTS.multipleCouponLabel)).not.toBeInTheDocument();
        });

        it("handles coupon not found", async () => {
            (getCoupon as jest.Mock).mockResolvedValue({
                success: false,
                error: "Coupon not found"
            });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith("Coupon not found");
            });

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons");
        });

        it("submits coupon update successfully", async () => {
            const user = userEvent.setup();
            (getCoupon as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCouponData
            });
            (updateCoupon as jest.Mock).mockResolvedValue({ success: true });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("TEST123")).toBeInTheDocument();
            });

            const codeInput = screen.getByDisplayValue("TEST123");
            await user.clear(codeInput);
            await user.type(codeInput, "UPDATED123");

            const submitButton = screen.getByText(TEXTS.updateButtonText);
            await user.click(submitButton);

            await waitFor(() => {
                expect(updateCoupon).toHaveBeenCalledWith(
                    "coupon-1",
                    expect.objectContaining({
                        coupon_code: "UPDATED123"
                    })
                );
            });

            expect(toast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage, { id: "coupon-updated" });
            expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons");
        });

        it("handles update error", async () => {
            const user = userEvent.setup();
            const errorMessage = "Update failed";
            (getCoupon as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCouponData
            });
            (updateCoupon as jest.Mock).mockResolvedValue({
                success: false,
                error: errorMessage
            });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("TEST123")).toBeInTheDocument();
            });

            const submitButton = screen.getByText(TEXTS.updateButtonText);
            await user.click(submitButton);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(errorMessage);
            });

            expect(mockRouter.push).not.toHaveBeenCalled();
        });
    });

    describe("Coupon Groups", () => {
        it("fetches and displays coupon groups", async () => {
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalledTimes(1);
            });
        });

        it("handles coupon groups fetch error", async () => {
            const errorMessage = "Failed to fetch groups";
            (getCouponGroups as jest.Mock).mockResolvedValue({
                success: false,
                error: errorMessage
            });

            render(<CouponForm />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(errorMessage);
            });
        });

        it("refetches groups in edit mode if not already loaded", async () => {
            (getCoupon as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCouponData
            });

            (getCouponGroups as jest.Mock)
                .mockResolvedValueOnce({ success: true, data: [] })
                .mockResolvedValueOnce({ success: true, data: mockCouponGroups });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalledTimes(2);
            });
        });
    });

    describe("Form Validation", () => {
        it("shows required field validation in single mode", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Verify that createCoupons is not called when required fields are missing
            expect(createCoupons).not.toHaveBeenCalled();

            // Check that validation error messages are displayed for required fields
            await waitFor(() => {
                // Coupon code is required in single mode
                expect(screen.getByText(TEXTS.couponCodeRequired)).toBeInTheDocument();

                // Coupon group is always required
                expect(screen.getByText(TEXTS.couponGroupRequired)).toBeInTheDocument();

                // Discount value is required - should show default NumberInput required message
                expect(screen.getByText(NumberInputTexts.requiredText)).toBeInTheDocument();
            });
        });

        it("shows required field validation in multiple mode", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Switch to multiple mode
            const multipleTab = screen.getByText(TEXTS.multipleCouponLabel);
            await user.click(multipleTab);

            // Clear the quantity field to make it empty (default is 10)
            const quantityInput = screen.getByLabelText(TEXTS.quantityLabel);
            await user.clear(quantityInput);

            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Verify that createCoupons is not called when required fields are missing
            expect(createCoupons).not.toHaveBeenCalled();

            // Check that validation error messages are displayed for required fields
            await waitFor(() => {
                // Quantity is required in multiple mode
                expect(screen.getByText(TEXTS.quantityRequired)).toBeInTheDocument();

                // Coupon group is always required
                expect(screen.getByText(TEXTS.couponGroupRequired)).toBeInTheDocument();

                // Discount value is required - should show default NumberInput required message
                expect(screen.getByText(NumberInputTexts.requiredText)).toBeInTheDocument();
            });
        });

        it("validates discount value must be positive", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "TEST123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter invalid (negative) discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "-10");

            // Attempt submission
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Assert createCoupons is not called
            expect(createCoupons).not.toHaveBeenCalled();

            // Check for validation error message
            await waitFor(() => {
                expect(screen.getByText(NumberInputTexts.min(0.01))).toBeInTheDocument();
            });
        });

        it("validates discount value must be greater than zero", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "TEST123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter zero discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "0");

            // Attempt submission
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Assert createCoupons is not called
            expect(createCoupons).not.toHaveBeenCalled();

            // Check for validation error message
            await waitFor(() => {
                expect(screen.getByText(NumberInputTexts.min(0.01))).toBeInTheDocument();
            });
        });

        it("validates usage limit must be positive integer", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "TEST123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter valid discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "10");

            // Enter invalid (negative) usage limit
            const usageLimitInput = screen.getByLabelText(TEXTS.usageLimitLabel);
            await user.type(usageLimitInput, "-5");

            // Attempt submission
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Assert createCoupons is not called
            expect(createCoupons).not.toHaveBeenCalled();

            // Check for validation error message
            await waitFor(() => {
                expect(screen.getByText(NumberInputTexts.min(1))).toBeInTheDocument();
            });
        });

        it("validates usage limit must be greater than zero", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "TEST123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter valid discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "10");

            // Enter zero usage limit
            const usageLimitInput = screen.getByLabelText(TEXTS.usageLimitLabel);
            await user.type(usageLimitInput, "0");

            // Attempt submission
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // Assert createCoupons is not called
            expect(createCoupons).not.toHaveBeenCalled();

            // Check for validation error message
            await waitFor(() => {
                expect(screen.getByText(NumberInputTexts.min(1))).toBeInTheDocument();
            });
        });

        it("allows form submission without expiration date (optional field)", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            await waitFor(() => {
                expect(getCouponGroups).toHaveBeenCalled();
            });

            // Fill required fields
            const codeInput = screen.getByLabelText(TEXTS.couponCodeLabel);
            await user.type(codeInput, "TEST123");

            // Select a coupon group (first combobox is the group selector)
            const comboboxes = screen.getAllByRole("combobox");
            const groupSelect = comboboxes[0]; // First combobox is the coupon group
            await user.click(groupSelect);
            const firstGroup = screen.getByText("Group 1");
            await user.click(firstGroup);

            // Enter valid discount value
            const discountInput = screen.getByLabelText(TEXTS.discountValueLabel);
            await user.type(discountInput, "10");

            // Verify expiration date field is present and optional
            const expirationDateButton = screen.getByRole("button", { name: /תאריך תפוגה/ });
            expect(expirationDateButton).toBeInTheDocument();

            // Test form submission with valid data (no expiration date set)
            // Should succeed since expiration date is optional
            const submitButton = screen.getByText(TEXTS.createButtonText);
            await user.click(submitButton);

            // With all valid data, form should submit successfully
            await waitFor(() => {
                expect(createCoupons).toHaveBeenCalled();
            });
        });
    });

    describe("Navigation", () => {
        it("navigates back when cancel is clicked", async () => {
            const user = userEvent.setup();
            render(<CouponForm />);

            const cancelButton = screen.getByText(TEXTS.cancelButtonText);
            await user.click(cancelButton);

            expect(mockRouter.push).toHaveBeenCalledWith("/admin/coupons");
        });
    });

    describe("Error Handling", () => {
        it("handles errors during coupon data fetch", async () => {
            const fetchError = new Error("Fetch error");
            (getCoupon as jest.Mock).mockRejectedValue(fetchError);

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(
                    TEXTS.editErrorMessage,
                    expect.objectContaining({
                        description: "Fetch error"
                    })
                );
            });
        });
    });

    describe("Form State", () => {
        it("shows submit button in correct state", () => {
            render(<CouponForm />);

            const submitButton = screen.getByText(TEXTS.createButtonText);
            expect(submitButton).toBeInTheDocument();
            expect(submitButton).not.toBeDisabled();
        });

        it("shows update button in edit mode", async () => {
            (getCoupon as jest.Mock).mockResolvedValue({
                success: true,
                data: mockCouponData
            });

            render(<CouponForm couponId="coupon-1" />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.updateButtonText)).toBeInTheDocument();
            });
        });
    });
});
