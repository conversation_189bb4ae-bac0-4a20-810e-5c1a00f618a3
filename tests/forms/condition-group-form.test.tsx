import { act, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import * as conditionGroupActions from "@/app/actions/condition-group-actions";
import { ConditionGroupForm } from "@/components/forms/condition-group-form";
import { TEXTS, type ConditionQuestion, type ConditionGroupFormValues } from "@/lib/condition-group-constants";
import { type Tables } from "@/types/database.types";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/components/forms/fields/condition-selector", () => ({
    ConditionSelector: ({ onAdd, onRemove }: { onAdd: () => void; onRemove: (index: number) => void }) => (
        <div>
            <button onClick={onAdd}>Add Condition</button>
            <button onClick={() => onRemove(0)}>Remove Condition</button>
        </div>
    )
}));

const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
    prefetch: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn()
};

const mockConditionGroup: Tables<"groups_condition"> = {
    id: "group-1",
    name: "Test Condition Group",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
};

const mockQuestions: ConditionQuestion[] = [
    {
        id: "question-1",
        type: "single_select",
        metadata: { label: "Test Question 1" },
        groups_question: {
            id: "group-q1",
            name: "Personal Details"
        }
    },
    {
        id: "question-2",
        type: "number_input",
        metadata: { label: "Test Question 2" }
    }
];

const conditionGroupFormValues: ConditionGroupFormValues = {
    name: "New Test Condition Group",
    dependencies: [
        {
            question_id: { id: "question-1", label: "Test Question 1" },
            condition_type: "in",
            condition_value: ["option1", "option2"]
        }
    ]
};

describe("ConditionGroupForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);

        // Mock getPersonalDetailsQuestions by default
        jest.spyOn(conditionGroupActions, "getPersonalDetailsQuestions").mockResolvedValue({
            success: true,
            data: mockQuestions
        });
    });

    describe("Create mode", () => {
        it("renders create form correctly", async () => {
            render(<ConditionGroupForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.createButtonText)).toBeInTheDocument();
                expect(screen.getByLabelText(TEXTS.nameLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.generalTabLabel)).toBeInTheDocument();
                expect(screen.getByText(TEXTS.conditionsTabLabel)).toBeInTheDocument();
            });
        });

        it("shows validation error for required fields", async () => {
            render(<ConditionGroupForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.createButtonText)).toBeInTheDocument();
            });

            await userEvent.click(screen.getByText(TEXTS.createButtonText));
            expect(await screen.findByText(TEXTS.nameRequired)).toBeInTheDocument();
        });

        it("submits the form and calls createConditionGroup", async () => {
            const createConditionGroupSpy = jest
                .spyOn(conditionGroupActions, "createConditionGroup")
                .mockResolvedValue({ success: true });

            render(<ConditionGroupForm />);

            await waitFor(() => {
                expect(screen.getByLabelText(TEXTS.nameLabel)).toBeInTheDocument();
            });

            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), conditionGroupFormValues.name);
            await userEvent.click(screen.getByText(TEXTS.createButtonText));

            await waitFor(() => {
                expect(createConditionGroupSpy).toHaveBeenCalledWith({
                    name: conditionGroupFormValues.name,
                    dependencies: []
                });
            });

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships/conditions/groups");
            });
        });

        it("shows error toast on create failure", async () => {
            const createConditionGroupSpy = jest
                .spyOn(conditionGroupActions, "createConditionGroup")
                .mockResolvedValue({ success: false, error: "Create failed" });

            render(<ConditionGroupForm />);

            await waitFor(() => {
                expect(screen.getByLabelText(TEXTS.nameLabel)).toBeInTheDocument();
            });

            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), conditionGroupFormValues.name);
            await userEvent.click(screen.getByText(TEXTS.createButtonText));

            await waitFor(() => {
                expect(createConditionGroupSpy).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalledWith("Create failed");
            });
        });

        it("handles questions fetch error", async () => {
            jest.spyOn(conditionGroupActions, "getPersonalDetailsQuestions").mockResolvedValue({
                success: false,
                error: "Questions fetch failed"
            });

            render(<ConditionGroupForm />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith("Questions fetch failed");
            });
        });
    });

    describe("Edit mode", () => {
        beforeEach(() => {
            jest.spyOn(conditionGroupActions, "getConditionGroupWithDependencies").mockResolvedValue({
                success: true,
                data: {
                    group: mockConditionGroup,
                    dependencies: [
                        {
                            id: "dep-1",
                            question_id: { id: "question-1", label: "Test Question 1" },
                            condition_type: "in",
                            condition_value: ["option1"]
                        }
                    ]
                }
            });
        });

        it("shows a loading indicator while fetching the condition group data", async () => {
            render(<ConditionGroupForm groupId={mockConditionGroup.id} />);

            expect(screen.getByText(TEXTS.editLoadingMessage)).toBeInTheDocument();

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });
        });

        it("renders edit form with pre-filled data", async () => {
            render(<ConditionGroupForm groupId={mockConditionGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            expect(screen.getByLabelText(TEXTS.nameLabel)).toHaveValue(mockConditionGroup.name);
            expect(screen.getByText(TEXTS.updateButtonText)).toBeInTheDocument();
        });

        it("submits the form and calls updateConditionGroup", async () => {
            const updateConditionGroupSpy = jest
                .spyOn(conditionGroupActions, "updateConditionGroup")
                .mockResolvedValue({ success: true });

            render(<ConditionGroupForm groupId={mockConditionGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            const updatedName = "Updated Group Name";
            await userEvent.clear(screen.getByLabelText(TEXTS.nameLabel));
            await userEvent.type(screen.getByLabelText(TEXTS.nameLabel), updatedName);

            await userEvent.click(screen.getByText(TEXTS.updateButtonText));

            await waitFor(() => {
                expect(updateConditionGroupSpy).toHaveBeenCalledWith(
                    mockConditionGroup.id,
                    expect.objectContaining({
                        name: updatedName
                    })
                );
                expect(toast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships/conditions/groups");
            });
        });

        it("shows error toast on update failure", async () => {
            const updateConditionGroupSpy = jest
                .spyOn(conditionGroupActions, "updateConditionGroup")
                .mockResolvedValue({ success: false, error: "Update failed" });

            render(<ConditionGroupForm groupId={mockConditionGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            await userEvent.click(screen.getByText(TEXTS.updateButtonText));

            await waitFor(() => {
                expect(updateConditionGroupSpy).toHaveBeenCalled();
                expect(toast.error).toHaveBeenCalledWith("Update failed");
            });
        });

        it("handles error when condition group is not found", async () => {
            jest.spyOn(conditionGroupActions, "getConditionGroupWithDependencies").mockResolvedValue({
                success: false,
                error: TEXTS.editNotFoundMessage
            });

            render(<ConditionGroupForm groupId="not-found" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.editNotFoundMessage);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships/conditions/groups");
            });
        });

        it("handles tabs switching", async () => {
            render(<ConditionGroupForm groupId={mockConditionGroup.id} />);

            await waitFor(() => {
                expect(screen.queryByText(TEXTS.editLoadingMessage)).not.toBeInTheDocument();
            });

            const conditionsTab = screen.getByRole("tab", { name: TEXTS.conditionsTabLabel });
            await userEvent.click(conditionsTab);

            expect(screen.getByRole("heading", { name: TEXTS.conditionsHeading })).toBeInTheDocument();
            expect(screen.getByText(TEXTS.conditionsDescription)).toBeInTheDocument();
        });
    });

    it("navigates back when cancel button is clicked", async () => {
        render(<ConditionGroupForm />);

        await waitFor(() => {
            expect(screen.getByText(TEXTS.cancelButtonText)).toBeInTheDocument();
        });

        await userEvent.click(screen.getByText(TEXTS.cancelButtonText));
        expect(mockRouter.push).toHaveBeenCalledWith("/admin/scholarships/conditions/groups");
    });

    it("handles condition dependencies management", async () => {
        render(<ConditionGroupForm />);

        await waitFor(() => {
            expect(screen.getByText(TEXTS.conditionsTabLabel)).toBeInTheDocument();
        });

        // Switch to conditions tab
        await userEvent.click(screen.getByText(TEXTS.conditionsTabLabel));

        // Check if condition selector is rendered
        expect(screen.getByText("Add Condition")).toBeInTheDocument();
        expect(screen.getByText("Remove Condition")).toBeInTheDocument();
    });
});
