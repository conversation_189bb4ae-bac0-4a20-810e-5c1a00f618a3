import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { createDocumentTypeGroup, updateDocumentTypeGroup } from "@/app/actions/document-type-group-actions";
import { DocumentTypeGroupForm } from "@/components/forms/document-type-group-form";
import { useDocumentTypeGroup } from "@/hooks/use-document-type-group";
import { TEXTS } from "@/lib/document-type-group-constants";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/document-type-group-actions", () => ({
    createDocumentTypeGroup: jest.fn(),
    updateDocumentTypeGroup: jest.fn()
}));

jest.mock("@/hooks/use-document-type-group", () => ({
    useDocumentTypeGroup: jest.fn()
}));

const mockPush = jest.fn();
const mockCreateAction = createDocumentTypeGroup as jest.MockedFunction<typeof createDocumentTypeGroup>;
const mockUpdateAction = updateDocumentTypeGroup as jest.MockedFunction<typeof updateDocumentTypeGroup>;
const mockUseDocumentTypeGroup = useDocumentTypeGroup as jest.MockedFunction<typeof useDocumentTypeGroup>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe("DocumentTypeGroupForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
        mockUseDocumentTypeGroup.mockReturnValue({
            group: null,
            loading: false,
            error: null,
            refetch: jest.fn()
        });
    });

    describe("Create Mode", () => {
        it("renders create form correctly", () => {
            render(<DocumentTypeGroupForm />);

            expect(screen.getByLabelText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.DESCRIPTION_LABEL)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON })).toBeInTheDocument();
        });

        it("creates a group successfully", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");
            await user.type(screen.getByLabelText(TEXTS.DESCRIPTION_LABEL), "Test Description");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(mockCreateAction).toHaveBeenCalledWith({
                    name: "Test Group",
                    description: "Test Description"
                });
            });

            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.CREATE_SUCCESS, {
                id: "document-type-group-created"
            });
            expect(mockPush).toHaveBeenCalledWith("/admin/document-types?tab=groups");
        });

        it("handles create error from server action", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockResolvedValue({
                success: false,
                error: "Custom error message"
            });

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith("Custom error message", {
                    id: "document-type-group-create-error"
                });
            });

            expect(mockPush).not.toHaveBeenCalled();
        });

        it("handles create exception", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockRejectedValue(new Error("Network error"));

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.CREATE_ERROR, {
                    id: "document-type-group-create-error"
                });
            });
        });

        it("submits form with empty description", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(mockCreateAction).toHaveBeenCalledWith({
                    name: "Test Group",
                    description: ""
                });
            });
        });

        it("resets form after successful creation", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm />);

            const nameInput = screen.getByLabelText(TEXTS.NAME_LABEL) as HTMLInputElement;
            const descriptionInput = screen.getByLabelText(TEXTS.DESCRIPTION_LABEL) as HTMLTextAreaElement;

            await user.type(nameInput, "Test Group");
            await user.type(descriptionInput, "Test Description");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(nameInput.value).toBe("");
                expect(descriptionInput.value).toBe("");
            });
        });
    });

    describe("Edit Mode", () => {
        const mockGroup = {
            id: "test-id",
            name: "Existing Group",
            description: "Existing Description",
            created_at: "2023-01-01T00:00:00Z",
            updated_at: "2023-01-01T00:00:00Z"
        };

        beforeEach(() => {
            mockUseDocumentTypeGroup.mockReturnValue({
                group: mockGroup,
                loading: false,
                error: null,
                refetch: jest.fn()
            });
        });

        it("renders edit form correctly", () => {
            render(<DocumentTypeGroupForm groupId="test-id" />);

            expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
            expect(screen.getByDisplayValue("Existing Description")).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON })).toBeInTheDocument();
        });

        it("updates a group successfully", async () => {
            const user = userEvent.setup();
            mockUpdateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            const nameInput = screen.getByDisplayValue("Existing Group");
            await user.clear(nameInput);
            await user.type(nameInput, "Updated Group");

            await user.click(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON }));

            await waitFor(() => {
                expect(mockUpdateAction).toHaveBeenCalledWith("test-id", {
                    name: "Updated Group",
                    description: "Existing Description"
                });
            });

            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.UPDATE_SUCCESS, {
                id: "document-type-group-updated"
            });
            expect(mockPush).toHaveBeenCalledWith("/admin/document-types?tab=groups");
        });

        it("handles update error from server action", async () => {
            const user = userEvent.setup();
            mockUpdateAction.mockResolvedValue({
                success: false,
                error: "Update failed"
            });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            await user.click(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON }));

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith("Update failed", {
                    id: "document-type-group-update-error"
                });
            });

            expect(mockPush).not.toHaveBeenCalled();
        });

        it("handles edit mode with null description", () => {
            mockUseDocumentTypeGroup.mockReturnValue({
                group: { ...mockGroup, description: null },
                loading: false,
                error: null,
                refetch: jest.fn()
            });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
            expect(screen.getByLabelText(TEXTS.DESCRIPTION_LABEL)).toHaveValue("");
        });

        it("does not reset form after successful update", async () => {
            const user = userEvent.setup();
            mockUpdateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            await user.click(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON }));

            await waitFor(() => {
                expect(mockUpdateAction).toHaveBeenCalled();
            });

            // Form should not be reset in edit mode
            expect(screen.getByDisplayValue("Existing Group")).toBeInTheDocument();
        });
    });

    describe("Loading States", () => {
        it("shows loading state when fetching group data", () => {
            mockUseDocumentTypeGroup.mockReturnValue({
                group: null,
                loading: true,
                error: null,
                refetch: jest.fn()
            });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            expect(screen.getByText(TEXTS.LOADING_GROUP)).toBeInTheDocument();
            expect(screen.queryByLabelText(TEXTS.NAME_LABEL)).not.toBeInTheDocument();
        });

        it("shows not found message when group is not found", () => {
            mockUseDocumentTypeGroup.mockReturnValue({
                group: null,
                loading: false,
                error: null,
                refetch: jest.fn()
            });

            render(<DocumentTypeGroupForm groupId="test-id" />);

            expect(screen.getByText(TEXTS.GROUP_NOT_FOUND)).toBeInTheDocument();
            expect(screen.queryByLabelText(TEXTS.NAME_LABEL)).not.toBeInTheDocument();
        });

        it("disables submit button while form is submitting", async () => {
            const user = userEvent.setup();
            // Make the action hang to test loading state
            mockCreateAction.mockImplementation(() => new Promise(() => {}));

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");

            const submitButton = screen.getByRole("button", { name: TEXTS.CREATE_BUTTON });
            await user.click(submitButton);

            expect(submitButton).toBeDisabled();
        });
    });

    describe("Navigation", () => {
        it("navigates back when cancel button is clicked", async () => {
            const user = userEvent.setup();

            render(<DocumentTypeGroupForm />);

            await user.click(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON }));

            expect(mockPush).toHaveBeenCalledWith("/admin/document-types?tab=groups");
        });
    });

    describe("Form Validation", () => {
        it("shows required validation for name field", async () => {
            const user = userEvent.setup();

            render(<DocumentTypeGroupForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.CREATE_BUTTON });
            await user.click(submitButton);

            expect(await screen.findByText(TEXTS.NAME_REQUIRED)).toBeInTheDocument();
            expect(mockCreateAction).not.toHaveBeenCalled();
        });

        it("allows submission with only name filled", async () => {
            const user = userEvent.setup();
            mockCreateAction.mockResolvedValue({ success: true });

            render(<DocumentTypeGroupForm />);

            await user.type(screen.getByLabelText(TEXTS.NAME_LABEL), "Test Group");
            await user.click(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON }));

            await waitFor(() => {
                expect(mockCreateAction).toHaveBeenCalledWith({
                    name: "Test Group",
                    description: ""
                });
            });
        });
    });
});
