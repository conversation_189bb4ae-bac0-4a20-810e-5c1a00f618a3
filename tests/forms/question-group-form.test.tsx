import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { createQuestionGroup, getQuestionGroup, updateQuestionGroup } from "@/app/actions/question-group-actions";
import { QuestionGroupForm } from "@/components/forms/question-group-form";
import { type Tables } from "@/types/database.types";
import { TEXTS } from "@/lib/question-group-constants";

jest.mock("next/navigation");
jest.mock("sonner");
jest.mock("@/app/actions/question-group-actions");

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockToast = toast as jest.Mocked<typeof toast>;
const mockCreateQuestionGroup = createQuestionGroup as jest.MockedFunction<typeof createQuestionGroup>;
const mockGetQuestionGroup = getQuestionGroup as jest.MockedFunction<typeof getQuestionGroup>;
const mockUpdateQuestionGroup = updateQuestionGroup as jest.MockedFunction<typeof updateQuestionGroup>;

const mockPush = jest.fn();
const mockRouter = { push: mockPush };

describe("QuestionGroupForm", () => {
    beforeEach(() => {
        mockUseRouter.mockReturnValue(mockRouter as any);
        mockToast.error = jest.fn();
        mockToast.success = jest.fn();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Create Mode", () => {
        it("renders form in create mode", () => {
            render(<QuestionGroupForm />);

            expect(screen.getByDisplayValue("")).toBeInTheDocument();
            expect(screen.getByPlaceholderText(TEXTS.namePlaceholder)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.createButtonText })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.cancelButtonText })).toBeInTheDocument();
        });

        it("creates question group successfully", async () => {
            const user = userEvent.setup();
            mockCreateQuestionGroup.mockResolvedValue({ success: true });

            render(<QuestionGroupForm />);

            const nameInput = screen.getByPlaceholderText(TEXTS.namePlaceholder);
            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });

            await user.type(nameInput, "שאלות בסיס");
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockCreateQuestionGroup).toHaveBeenCalledWith({
                    name: "שאלות בסיס"
                });
            });

            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.createSuccessMessage);
            expect(mockPush).toHaveBeenCalledWith("/admin/questions?tab=groups");
        });

        it("handles create error", async () => {
            const user = userEvent.setup();
            mockCreateQuestionGroup.mockResolvedValue({
                success: false,
                error: TEXTS.GROUP_CREATE_ERROR
            });

            render(<QuestionGroupForm />);

            const nameInput = screen.getByPlaceholderText(TEXTS.namePlaceholder);
            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });

            await user.type(nameInput, "שאלות בסיס");
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.GROUP_CREATE_ERROR);
            });

            expect(mockPush).not.toHaveBeenCalled();
        });
    });

    describe("Edit Mode", () => {
        const mockQuestionGroup: Tables<"groups_question"> = {
            id: "test-id",
            name: "קבוצה קיימת",
            created_at: "2023-01-01T00:00:00Z",
            updated_at: "2023-01-01T00:00:00Z"
        };

        it("renders form in edit mode with loading state", () => {
            render(<QuestionGroupForm groupId="test-id" />);

            expect(screen.getByText(TEXTS.loadingMessage)).toBeInTheDocument();
        });

        it("loads and displays existing question group data", async () => {
            mockGetQuestionGroup.mockResolvedValue({
                success: true,
                data: mockQuestionGroup
            });

            render(<QuestionGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(mockGetQuestionGroup).toHaveBeenCalledWith("test-id");
            });

            await waitFor(() => {
                expect(screen.getByDisplayValue("קבוצה קיימת")).toBeInTheDocument();
            });

            expect(screen.getByRole("button", { name: TEXTS.updateButtonText })).toBeInTheDocument();
        });

        it("handles fetch error and redirects", async () => {
            mockGetQuestionGroup.mockResolvedValue({
                success: false,
                error: TEXTS.GROUP_FETCH_ERROR
            });

            render(<QuestionGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.GROUP_FETCH_ERROR);
            });

            expect(mockPush).toHaveBeenCalledWith("/admin/questions?tab=groups");
        });

        it("updates question group successfully", async () => {
            const user = userEvent.setup();
            mockGetQuestionGroup.mockResolvedValue({
                success: true,
                data: mockQuestionGroup
            });
            mockUpdateQuestionGroup.mockResolvedValue({ success: true });

            render(<QuestionGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("קבוצה קיימת")).toBeInTheDocument();
            });

            const nameInput = screen.getByDisplayValue("קבוצה קיימת");
            const submitButton = screen.getByRole("button", { name: TEXTS.updateButtonText });

            await user.clear(nameInput);
            await user.type(nameInput, "קבוצה מעודכנת");
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockUpdateQuestionGroup).toHaveBeenCalledWith("test-id", {
                    name: "קבוצה מעודכנת"
                });
            });

            expect(mockToast.success).toHaveBeenCalledWith(TEXTS.editSuccessMessage);
            expect(mockPush).toHaveBeenCalledWith("/admin/questions?tab=groups");
        });

        it("handles update error", async () => {
            const user = userEvent.setup();
            mockGetQuestionGroup.mockResolvedValue({
                success: true,
                data: mockQuestionGroup
            });
            mockUpdateQuestionGroup.mockResolvedValue({
                success: false,
                error: TEXTS.GROUP_UPDATE_ERROR
            });

            render(<QuestionGroupForm groupId="test-id" />);

            await waitFor(() => {
                expect(screen.getByDisplayValue("קבוצה קיימת")).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.updateButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.GROUP_UPDATE_ERROR);
            });

            expect(mockPush).not.toHaveBeenCalled();
        });
    });

    describe("Form Validation", () => {
        it("shows validation error for empty name", async () => {
            const user = userEvent.setup();

            render(<QuestionGroupForm />);

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.nameRequired)).toBeInTheDocument();
            });

            expect(mockCreateQuestionGroup).not.toHaveBeenCalled();
        });

        it("accepts valid input", async () => {
            const user = userEvent.setup();
            mockCreateQuestionGroup.mockResolvedValue({ success: true });

            render(<QuestionGroupForm />);

            const nameInput = screen.getByPlaceholderText(TEXTS.namePlaceholder);
            await user.type(nameInput, "שם תקין");

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockCreateQuestionGroup).toHaveBeenCalledWith({
                    name: "שם תקין"
                });
            });

            expect(screen.queryByText(TEXTS.nameRequired)).not.toBeInTheDocument();
        });
    });

    describe("Navigation", () => {
        it("navigates back when cancel button is clicked", async () => {
            const user = userEvent.setup();

            render(<QuestionGroupForm />);

            const cancelButton = screen.getByRole("button", { name: TEXTS.cancelButtonText });
            await user.click(cancelButton);

            expect(mockPush).toHaveBeenCalledWith("/admin/questions?tab=groups");
        });
    });

    describe("Accessibility", () => {
        it("has proper form labels and descriptions", () => {
            render(<QuestionGroupForm />);

            expect(screen.getByText(TEXTS.nameLabel)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.nameDescription)).toBeInTheDocument();
        });

        it("has proper RTL direction", () => {
            render(<QuestionGroupForm />);

            const form = document.querySelector('form[dir="rtl"]');
            expect(form).toBeInTheDocument();
        });
    });

    describe("Error Handling", () => {
        it("handles network errors gracefully", async () => {
            const user = userEvent.setup();
            mockCreateQuestionGroup.mockRejectedValue(new Error("Network error"));

            render(<QuestionGroupForm />);

            const nameInput = screen.getByPlaceholderText(TEXTS.namePlaceholder);
            await user.type(nameInput, "שאלות בסיס");

            const submitButton = screen.getByRole("button", { name: TEXTS.createButtonText });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith(TEXTS.unknownError);
            });
        });
    });
});
