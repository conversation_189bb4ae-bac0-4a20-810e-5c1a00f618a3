import "@testing-library/jest-dom";

import { act, cleanup, render, screen, waitFor, fireEvent } from "@testing-library/react";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";
import { userEvent } from "@testing-library/user-event";

import { createDocumentType, getDocumentType, updateDocumentType } from "@/app/actions/document-type-actions";
import type { Database } from "@/types/database.types";
import { getSupabaseClient } from "@/utils/supabase/client";
import { getDocumentTypeGroups } from "@/app/actions/document-type-group-actions";

import { DocumentTypeForm } from "../../components/forms/document-type-form";
import { TEXTS } from "@/lib/document-type-constants";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/document-type-actions", () => ({
    createDocumentType: jest.fn(),
    updateDocumentType: jest.fn(),
    getDocumentType: jest.fn()
}));

jest.mock("@/app/actions/document-type-group-actions", () => ({
    getDocumentTypeGroups: jest.fn()
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({
        from: jest.fn(() => ({
            select: jest.fn(() => ({
                order: jest.fn(() =>
                    Promise.resolve({
                        data: [
                            { id: "group1", name: "Personal Documents" },
                            { id: "group2", name: "Academic Documents" }
                        ],
                        error: null
                    })
                )
            }))
        })),
        storage: {
            from: jest.fn(() => ({
                upload: jest.fn(() => Promise.resolve({ error: null }))
            }))
        }
    }))
}));

let mockFormData = {
    group_id: "group1",
    name: "Test Name",
    description: "Test Description",
    link_url: "https://example.com",
    example_file_path: null as string | null,
    allowed_mime_types: ["application/pdf"],
    max_file_size_mb: 10
};

let mockFormState = { isSubmitting: false };
let mockWatchValue = 10;

jest.mock("react-hook-form", () => {
    const actualModule = jest.requireActual("react-hook-form");
    return {
        ...actualModule,
        useForm: () => ({
            handleSubmit: jest.fn((fn) => (e?: React.FormEvent) => {
                e?.preventDefault();
                fn(mockFormData);
            }),
            control: {},
            formState: mockFormState,
            watch: jest.fn(() => mockWatchValue),
            setValue: jest.fn(),
            reset: jest.fn()
        }),
        FormProvider: ({ children }: { children: React.ReactNode }) => children
    };
});

jest.mock("@/components/forms/fields/file-upload", () => ({
    FileUpload: ({ label, name }: { label: string; name: string }) => (
        <div data-testid={`file-upload-${name}`}>
            <label htmlFor={name}>{label}</label>
            <input type="file" id={name} data-testid={`file-input-${name}`} aria-label={label} />
        </div>
    )
}));

jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ label, name }: { label: string; name: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <input
                type="text"
                id={name}
                data-testid={`input-${name}`}
                aria-label={label}
                defaultValue={(mockFormData[name as keyof typeof mockFormData] as string) || ""}
            />
        </div>
    )
}));

jest.mock("@/components/forms/fields/long-text", () => ({
    LongText: ({ label, name }: { label: string; name: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <textarea
                id={name}
                data-testid={`textarea-${name}`}
                aria-label={label}
                defaultValue={(mockFormData[name as keyof typeof mockFormData] as string) || ""}
            />
        </div>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({ label, name }: { label: string; name: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <select id={name} data-testid={`select-${name}`} aria-label={label}>
                <option value="">Select an option</option>
                <option value="group-1">Academic Documents</option>
                <option value="group-2">Personal Documents</option>
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ label, name }: { label: string; name: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <select id={name} data-testid={`multiselect-${name}`} aria-label={label} multiple>
                <option value="application/pdf">PDF</option>
                <option value="image/jpeg">JPEG</option>
                <option value="image/png">PNG</option>
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/fields/number-input", () => ({
    NumberInput: ({ label, name }: { label: string; name: string }) => (
        <div>
            <label htmlFor={name}>{label}</label>
            <input
                type="number"
                id={name}
                data-testid={`number-${name}`}
                aria-label={label}
                defaultValue={(mockFormData[name as keyof typeof mockFormData] as number) || 10}
            />
        </div>
    )
}));

jest.mock("@/components/ui/form", () => ({
    FormField: ({ render }: { render?: (field: { field: Record<string, unknown> }) => React.ReactNode }) => {
        if (render) {
            return render({ field: {} });
        }
        return null;
    },
    FormItem: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    FormMessage: () => null
}));

type DocumentType = Database["public"]["Tables"]["document_types"]["Row"];

const setMockFormData = (data: Partial<typeof mockFormData>) => {
    mockFormData = { ...mockFormData, ...data };
};

const setMockFormState = (state: Partial<typeof mockFormState>) => {
    mockFormState = { ...mockFormState, ...state };
};

const setMockWatchValue = (value: typeof mockWatchValue) => {
    mockWatchValue = value;
};

const resetMockFormData = () => {
    mockFormData = {
        group_id: "group1",
        name: "Test Name",
        description: "Test Description",
        link_url: "https://example.com",
        example_file_path: null as string | null,
        allowed_mime_types: ["application/pdf"],
        max_file_size_mb: 10
    };
    mockFormState = { isSubmitting: false };
    mockWatchValue = 10;
};

const mockGroups = [
    { id: "group-1", name: "Academic Documents" },
    { id: "group-2", name: "Personal Documents" }
];

const mockDocumentType: DocumentType = {
    id: "doc-type-1",
    name: "Test Document Type",
    description: "Test Description",
    link_url: "https://example.com",
    example_file_path: "example.pdf",
    allowed_mime_types: ["application/pdf", "image/jpeg"],
    group_id: "group1",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    max_file_size_mb: 10
};

const mockSupabase = {
    storage: {
        from: jest.fn().mockReturnThis(),
        upload: jest.fn(),
        getPublicUrl: jest.fn()
    }
};

describe("DocumentTypeForm", () => {
    const mockPush = jest.fn();
    const mockRouter = { push: mockPush };

    beforeEach(() => {
        jest.clearAllMocks();
        resetMockFormData();
        (useRouter as jest.Mock).mockReturnValue(mockRouter);
        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabase);
        (getDocumentTypeGroups as jest.Mock).mockResolvedValue({
            success: true,
            data: mockGroups
        });

        // Properly mock console.error to avoid recursion
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
    });

    describe("Create Mode", () => {
        it("renders create form with all required fields", async () => {
            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.GROUP_LABEL)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.DESCRIPTION_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.LINK_URL_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.EXAMPLE_FILE_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.ALLOWED_MIME_TYPES_LABEL)).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON })).toBeInTheDocument();
        });

        it("renders file upload component", async () => {
            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(screen.getByTestId("file-upload-example_file_path")).toBeInTheDocument();
            });

            expect(screen.getByTestId("file-input-example_file_path")).toBeInTheDocument();
        });

        it("loads document type groups on mount", async () => {
            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(getDocumentTypeGroups).toHaveBeenCalledTimes(1);
            });
        });

        it("handles create action correctly", async () => {
            (createDocumentType as jest.Mock).mockResolvedValue({ success: true, id: "new-doc-type" });

            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            });

            // Submit form with default mock data
            const submitButton = screen.getByRole("button", { name: TEXTS.CREATE_BUTTON });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(createDocumentType).toHaveBeenCalledWith(mockFormData);
                expect(toast.success).toHaveBeenCalledWith(TEXTS.CREATE_SUCCESS);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/document-types");
            });
        });

        it("handles create error", async () => {
            (createDocumentType as jest.Mock).mockResolvedValue({
                success: false,
                error: TEXTS.DOCUMENT_TYPE_CREATE_ERROR
            });

            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.CREATE_BUTTON });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.DOCUMENT_TYPE_CREATE_ERROR);
                expect(mockRouter.push).not.toHaveBeenCalled();
            });
        });

        it("handles validation error scenario", async () => {
            // Mock form data without group_id to simulate validation error
            setMockFormData({ group_id: null as any });

            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.CREATE_BUTTON });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.GROUP_REQUIRED);
            });
        });
    });

    describe("Edit Mode", () => {
        it("shows loading state initially in edit mode", () => {
            (getDocumentType as jest.Mock).mockImplementation(() => new Promise(() => {}));

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            expect(screen.getByText(TEXTS.LOADING_DOCUMENT_TYPE)).toBeInTheDocument();
        });

        it("fetches document type data when documentTypeId is provided", async () => {
            const mockGetResult = { data: mockDocumentType, error: null };
            (getDocumentType as jest.Mock).mockImplementation(() => Promise.resolve(mockGetResult));

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(getDocumentType).toHaveBeenCalledWith("doc-type-1");
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            });
        });

        it("handles document type not found error", async () => {
            const mockGetResult = { data: null, error: TEXTS.DOCUMENT_TYPE_NOT_FOUND };
            (getDocumentType as jest.Mock).mockImplementation(() => Promise.resolve(mockGetResult));

            render(<DocumentTypeForm documentTypeId="non-existent" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.DOCUMENT_TYPE_NOT_FOUND_MESSAGE);
                expect(mockPush).toHaveBeenCalledWith("/admin/document-types");
            });
        });

        it("handles update action correctly", async () => {
            (getDocumentType as jest.Mock).mockResolvedValue({ data: mockDocumentType });
            (updateDocumentType as jest.Mock).mockResolvedValue({ success: true });

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(updateDocumentType).toHaveBeenCalledWith("doc-type-1", mockFormData);
                expect(toast.success).toHaveBeenCalledWith(TEXTS.UPDATE_SUCCESS);
                expect(mockRouter.push).toHaveBeenCalledWith("/admin/document-types");
            });
        });

        it("handles update error", async () => {
            (getDocumentType as jest.Mock).mockResolvedValue({ data: mockDocumentType });
            (updateDocumentType as jest.Mock).mockResolvedValue({
                success: false,
                error: TEXTS.DOCUMENT_TYPE_UPDATE_ERROR
            });

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            });

            const submitButton = screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON });
            fireEvent.click(submitButton);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.DOCUMENT_TYPE_UPDATE_ERROR);
                expect(mockRouter.push).not.toHaveBeenCalled();
            });
        });
    });

    describe("Form Actions", () => {
        it("navigates back on cancel button click", async () => {
            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON })).toBeInTheDocument();
            });

            const cancelButton = screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON });
            fireEvent.click(cancelButton);

            expect(mockPush).toHaveBeenCalledWith("/admin/document-types");
        });
    });

    describe("Component Structure", () => {
        it("renders all form field labels", async () => {
            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(screen.getByText(TEXTS.GROUP_LABEL)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.DESCRIPTION_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.LINK_URL_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.EXAMPLE_FILE_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.ALLOWED_MIME_TYPES_LABEL)).toBeInTheDocument();
        });

        it("renders correct button text for create mode", async () => {
            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeInTheDocument();
            });
        });

        it("renders correct button text for edit mode", async () => {
            const mockGetResult = { data: mockDocumentType, error: null };
            (getDocumentType as jest.Mock).mockImplementation(() => Promise.resolve(mockGetResult));

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            });
        });
    });

    describe("Error Handling", () => {
        it("handles groups loading error gracefully", async () => {
            (getDocumentTypeGroups as jest.Mock).mockResolvedValue({
                success: false,
                error: "Failed to load groups"
            });

            await act(async () => {
                render(<DocumentTypeForm />);
            });

            await waitFor(() => {
                expect(getDocumentTypeGroups).toHaveBeenCalledTimes(1);
            });

            // Form should still render even if groups fail to load
            expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
        });

        it("handles document type fetching errors in edit mode", async () => {
            const mockError = new Error("Failed to fetch document type");
            (getDocumentType as jest.Mock).mockImplementation(() => Promise.reject(mockError));

            render(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(console.error).toHaveBeenCalledWith("Error fetching document type:", mockError);
                expect(toast.error).toHaveBeenCalledWith(TEXTS.ERROR_MESSAGE);
            });
        });

        it("renders file upload component structure", async () => {
            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByTestId("file-input-example_file_path")).toBeInTheDocument();
            });

            // Test that the file input exists and has correct attributes
            const fileInput = screen.getByTestId("file-input-example_file_path");
            expect(fileInput).toHaveAttribute("type", "file");
            expect(fileInput).toHaveAttribute("aria-label", TEXTS.EXAMPLE_FILE_LABEL);
        });
    });

    describe("Form State Management", () => {
        it("handles form submission state correctly", async () => {
            setMockFormState({ isSubmitting: true });

            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeDisabled();
            });
        });

        it("handles different watch values for max file size", async () => {
            setMockWatchValue(25);

            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            });

            // The FileUpload component should be rendered
            expect(screen.getByTestId("file-input-example_file_path")).toBeInTheDocument();
        });
    });

    describe("Constants Integration", () => {
        it("uses centralized constants for all text content", async () => {
            render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.NAME_LABEL)).toBeInTheDocument();
            });

            // Verify all labels use constants
            expect(screen.getByText(TEXTS.GROUP_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.DESCRIPTION_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.LINK_URL_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.MAX_FILE_SIZE_LABEL)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.ALLOWED_MIME_TYPES_LABEL)).toBeInTheDocument();

            // Verify buttons use constants
            expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeInTheDocument();
            expect(screen.getByRole("button", { name: TEXTS.CANCEL_BUTTON })).toBeInTheDocument();
        });

        it("displays appropriate button text based on mode", async () => {
            const { rerender } = render(<DocumentTypeForm />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.CREATE_BUTTON })).toBeInTheDocument();
            });

            // Switch to edit mode
            (getDocumentType as jest.Mock).mockResolvedValue({ data: mockDocumentType });
            rerender(<DocumentTypeForm documentTypeId="doc-type-1" />);

            await waitFor(() => {
                expect(screen.getByRole("button", { name: TEXTS.UPDATE_BUTTON })).toBeInTheDocument();
            });
        });
    });
});
