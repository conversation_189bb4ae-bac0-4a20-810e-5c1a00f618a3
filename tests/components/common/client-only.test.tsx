import "@testing-library/jest-dom";
import React from "react";
import { render, screen } from "@testing-library/react";

import { ClientOnly } from "@/components/common/client-only";

const TEXTS = {
    child: "Child Component",
    fallback: "Fallback Component"
};

describe("ClientOnly", () => {
    it("renders the fallback on initial server render", () => {
        // Mock useState to control the mounted state
        const useStateSpy = jest.spyOn(React, "useState");
        useStateSpy.mockImplementation(() => [false, jest.fn()]);

        render(
            <ClientOnly fallback={<div>{TEXTS.fallback}</div>}>
                <div>{TEXTS.child}</div>
            </ClientOnly>
        );

        expect(screen.getByText(TEXTS.fallback)).toBeInTheDocument();
        expect(screen.queryByText(TEXTS.child)).toBeNull();

        useStateSpy.mockRestore();
    });

    it("renders children and not fallback once mounted", () => {
        render(
            <ClientOnly fallback={<div>{TEXTS.fallback}</div>}>
                <div>{TEXTS.child}</div>
            </ClientOnly>
        );
        expect(screen.getByText(TEXTS.child)).toBeInTheDocument();
        expect(screen.queryByText(TEXTS.fallback)).toBeNull();
    });

    it("renders children when no fallback provided", () => {
        render(
            <ClientOnly>
                <div>{TEXTS.child}</div>
            </ClientOnly>
        );
        expect(screen.getByText(TEXTS.child)).toBeInTheDocument();
    });
});
