import "@testing-library/jest-dom";

import { render, screen } from "@testing-library/react";
import { AlertCircle, CheckCircle2, XCircle } from "lucide-react";

import { EmptyState } from "@/components/common/empty-state";

type IconProps = { className?: string };

jest.mock("lucide-react", () => {
    const actual = jest.requireActual("lucide-react");
    const React = require("react");
    const makeMockIcon = (testId: string) =>
        jest.fn(({ className }: { className?: string }) =>
            React.createElement("div", { "data-testid": testId, className })
        );
    const Check = makeMockIcon("success-icon");
    const Warn = makeMockIcon("warning-icon");
    const Err = makeMockIcon("error-icon");
    return (() => {
        const merged: any = Object.assign({}, actual);
        merged.CheckCircle2 = Check;
        merged.AlertCircle = Warn;
        merged.XCircle = Err;
        return merged;
    })();
});

const TEXTS = {
    message: "Test Message",
    child: "Child Content"
};

describe("EmptyState", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders the message correctly", () => {
        render(<EmptyState message={TEXTS.message} />);
        expect(screen.getByText(TEXTS.message)).toBeInTheDocument();
    });

    it("renders children when provided", () => {
        render(
            <EmptyState message={TEXTS.message}>
                <div>{TEXTS.child}</div>
            </EmptyState>
        );
        expect(screen.getByText(TEXTS.child)).toBeInTheDocument();
    });

    it("does not render children when not provided", () => {
        render(<EmptyState message={TEXTS.message} />);
        expect(screen.queryByText(TEXTS.child)).not.toBeInTheDocument();
    });

    it("renders with success variant by default and applies correct icon and styles", () => {
        render(<EmptyState message={TEXTS.message} />);
        expect(CheckCircle2).toHaveBeenCalled();
        const iconContainer = screen.getByTestId("success-icon").parentElement;
        expect(iconContainer).toHaveClass("bg-green-100");
        expect(screen.getByTestId("success-icon")).toHaveClass("text-green-600");
    });

    it("renders with warning variant and applies correct icon and styles", () => {
        render(<EmptyState message={TEXTS.message} variant="warning" />);
        expect(AlertCircle).toHaveBeenCalled();
        const iconContainer = screen.getByTestId("warning-icon").parentElement;
        expect(iconContainer).toHaveClass("bg-yellow-100");
        expect(screen.getByTestId("warning-icon")).toHaveClass("text-yellow-600");
    });

    it("renders with error variant and applies correct icon and styles", () => {
        render(<EmptyState message={TEXTS.message} variant="error" />);
        expect(XCircle).toHaveBeenCalled();
        const iconContainer = screen.getByTestId("error-icon").parentElement;
        expect(iconContainer).toHaveClass("bg-red-100");
        expect(screen.getByTestId("error-icon")).toHaveClass("text-red-600");
    });

    it("applies custom className", () => {
        const customClass = "custom-class";
        const { container } = render(<EmptyState message={TEXTS.message} className={customClass} />);
        expect(container.firstChild).toHaveClass(customClass);
    });
});
