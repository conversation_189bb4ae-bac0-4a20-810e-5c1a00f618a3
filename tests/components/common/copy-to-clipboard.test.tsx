import "@testing-library/jest-dom";

import { cleanup, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "sonner";

import { CopyToClipboard } from "@/components/common/copy-to-clipboard";

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

const TEXTS = {
    copy: "העתק",
    copied: "הועתק!",
    copySuccess: "הטקסט הועתק בהצלחה",
    copyError: "שגיאה בהעתקת הטקסט, אנא נסה שוב"
};

const mockWriteText = jest.fn();

beforeAll(() => {
    Object.defineProperty(window.navigator, "clipboard", {
        value: {
            writeText: mockWriteText
        },
        writable: true,
        configurable: true
    });
});

describe("CopyToClipboard", () => {
    const defaultProps = {
        text: "Sample text to copy"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockWriteText.mockReset();
    });

    afterEach(() => {
        cleanup();
    });

    describe("Basic Rendering", () => {
        it("renders with copy icon initially", () => {
            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            expect(button).toBeInTheDocument();
            expect(button).toHaveAttribute("title", TEXTS.copy);
            expect(button).toHaveAttribute("aria-label", TEXTS.copy);

            const copyIcon = button.querySelector("svg");
            expect(copyIcon).toBeInTheDocument();
            expect(copyIcon).toHaveClass("text-gray-500");
        });

        it("applies custom className", () => {
            const customClass = "custom-class";
            render(<CopyToClipboard {...defaultProps} className={customClass} />);

            const button = screen.getByRole("button");
            expect(button).toHaveClass(customClass);
        });

        it("renders with correct size classes", () => {
            const { rerender } = render(<CopyToClipboard {...defaultProps} size="sm" />);
            let icon = screen.getByRole("button").querySelector("svg");
            expect(icon).toHaveClass("w-4", "h-4");

            rerender(<CopyToClipboard {...defaultProps} size="md" />);
            icon = screen.getByRole("button").querySelector("svg");
            expect(icon).toHaveClass("w-5", "h-5");

            rerender(<CopyToClipboard {...defaultProps} size="lg" />);
            icon = screen.getByRole("button").querySelector("svg");
            expect(icon).toHaveClass("w-6", "h-6");
        });

        it("defaults to small size when no size prop provided", () => {
            render(<CopyToClipboard {...defaultProps} />);

            const icon = screen.getByRole("button").querySelector("svg");
            expect(icon).toHaveClass("w-4", "h-4");
        });
    });

    describe("Copy Functionality", () => {
        it("shows success toast on successful copy", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.copySuccess);
            });
        });

        it("uses custom success message when provided", async () => {
            const user = userEvent.setup();
            const customMessage = "הקישור הועתק בהצלחה";
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} onSuccessMessage={customMessage} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(customMessage);
            });
        });
    });

    describe("State Changes", () => {
        it("changes to check icon after successful copy", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");

            expect(button.querySelector("svg")).toHaveClass("text-gray-500");
            expect(button).toHaveAttribute("title", TEXTS.copy);
            expect(button).toHaveAttribute("aria-label", TEXTS.copy);

            await user.click(button);

            await waitFor(() => {
                expect(button.querySelector("svg")).toHaveClass("text-green-600");
                expect(button).toHaveAttribute("title", TEXTS.copied);
                expect(button).toHaveAttribute("aria-label", TEXTS.copied);
            });
        });

        it("resets to copy icon after timeout", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(button.querySelector("svg")).toHaveClass("text-green-600");
            });

            await waitFor(
                () => {
                    expect(button.querySelector("svg")).toHaveClass("text-gray-500");
                },
                { timeout: 3000 }
            );
        });
    });

    describe("Multiple Clicks", () => {
        it("handles multiple rapid clicks correctly", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");

            await user.click(button);
            await user.click(button);
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledTimes(3);
            });

            await waitFor(() => {
                expect(button.querySelector("svg")).toHaveClass("text-green-600");
            });
        });
    });

    describe("Accessibility", () => {
        it("has proper ARIA attributes in copy state", () => {
            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            expect(button).toHaveAttribute("aria-label", TEXTS.copy);
            expect(button).toHaveAttribute("title", TEXTS.copy);
        });

        it("updates ARIA attributes in copied state", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(button).toHaveAttribute("aria-label", TEXTS.copied);
                expect(button).toHaveAttribute("title", TEXTS.copied);
            });
        });

        it("is focusable and keyboard accessible", () => {
            render(<CopyToClipboard {...defaultProps} />);

            const button = screen.getByRole("button");
            expect(button).toBeInTheDocument();
            expect(button.tagName).toBe("BUTTON");

            expect(button).not.toHaveAttribute("disabled");
        });
    });

    describe("Text Handling", () => {
        it("handles empty string", async () => {
            const user = userEvent.setup();
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard text="" />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.copySuccess);
            });
        });

        it("handles text with special characters", async () => {
            const user = userEvent.setup();
            const specialText = "Text with special chars: !@#$%^&*()[]{}|\\:;\"'<>,.?/~`";
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard text={specialText} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.copySuccess);
            });
        });

        it("handles text with line breaks and unicode", async () => {
            const user = userEvent.setup();
            const textWithLineBreaks = "שלום\nהעולם\n🌍\nמשפט עם\tטאב";
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard text={textWithLineBreaks} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.copySuccess);
            });
        });

        it("handles very long text", async () => {
            const user = userEvent.setup();
            const longText = "A".repeat(1000);
            mockWriteText.mockResolvedValue(undefined);

            render(<CopyToClipboard text={longText} />);

            const button = screen.getByRole("button");
            await user.click(button);

            await waitFor(() => {
                expect(toast.success).toHaveBeenCalledWith(TEXTS.copySuccess);
            });
        });
    });
});
