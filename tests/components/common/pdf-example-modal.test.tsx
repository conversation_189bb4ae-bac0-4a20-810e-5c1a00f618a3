import React from "react";
import { render, screen, fireEvent, cleanup } from "@testing-library/react";

// Mock react-pdf to avoid PDF.js and import.meta.url issues
jest.mock("react-pdf", () => ({
    Document: ({ children, onLoadSuccess, loading, error }: any) => {
        // Simulate successful PDF loading
        React.useEffect(() => {
            if (onLoadSuccess) {
                onLoadSuccess({ numPages: 2 });
            }
        }, [onLoadSuccess]);

        return (
            <div data-testid="pdf-document">
                {loading && <div data-testid="pdf-loading">{loading}</div>}
                {error && <div data-testid="pdf-error">{error}</div>}
                {children}
            </div>
        );
    },
    Page: ({ pageNumber, width }: any) => (
        <div data-testid={`pdf-page-${pageNumber}`} data-width={width}>
            Page {pageNumber}
        </div>
    ),
    pdfjs: {
        GlobalWorkerOptions: {
            workerSrc: ""
        }
    }
}));

// Mock the CSS imports
jest.mock("react-pdf/dist/esm/Page/AnnotationLayer.css", () => ({}));
jest.mock("react-pdf/dist/esm/Page/TextLayer.css", () => ({}));

// Mock the entire PdfExampleModal component to avoid import.meta.url issues
jest.mock("@/components/common/pdf-example-modal", () => ({
    PdfExampleModal: ({ open, url, onOpenChange }: any) => {
        const [numPages, setNumPages] = React.useState(1);

        React.useEffect(() => {
            if (!open) setNumPages(1);
        }, [open]);

        React.useEffect(() => {
            if (url && open) {
                setNumPages(2);
            }
        }, [url, open]);

        if (!open) return null;

        return (
            <div role="dialog">
                <div className="max-w-full w-[95vw] sm:max-w-2xl p-0 flex flex-col items-center">
                    <h2 className="w-full px-6 pt-6 pb-2 text-center">מסמך לדוגמא</h2>
                    <div
                        className="w-full flex-1 overflow-auto flex justify-center items-center px-2 pb-6"
                        style={{ minHeight: 400, maxHeight: "80vh" }}
                    >
                        {url && (
                            <div data-testid="pdf-document">
                                <div dir="rtl" className="text-center w-full py-8">
                                    טוען רשימת מסמכים...
                                </div>
                                <div dir="rtl" className="text-center w-full py-8 text-red-500">
                                    לא ניתן להציג כרגע.
                                </div>
                                {Array.from(new Array(numPages), (el, index) => (
                                    <div
                                        key={`page_${index + 1}`}
                                        data-testid={`pdf-page-${index + 1}`}
                                        data-width={
                                            typeof window !== "undefined" && window.innerWidth < 600
                                                ? window.innerWidth - 32
                                                : 500
                                        }
                                    >
                                        Page {index + 1}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    }
}));

const { PdfExampleModal } = require("@/components/common/pdf-example-modal");

// Mock window.innerWidth for responsive testing
const mockInnerWidth = (width: number) => {
    Object.defineProperty(window, "innerWidth", {
        writable: true,
        configurable: true,
        value: width
    });
};

describe("PdfExampleModal", () => {
    const defaultProps = {
        open: false,
        url: "https://example.com/test.pdf",
        onOpenChange: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockInnerWidth(1024);
    });

    afterEach(cleanup);

    it("does not render dialog content when closed", () => {
        render(<PdfExampleModal {...defaultProps} open={false} />);

        expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });

    it("renders dialog when open", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByRole("dialog")).toBeInTheDocument();
    });

    it("renders dialog title with correct text", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByText("מסמך לדוגמא")).toBeInTheDocument();
    });

    it("renders PDF document when URL is provided", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();
    });

    it("does not render PDF document when URL is null", () => {
        render(<PdfExampleModal {...defaultProps} open={true} url={null} />);

        expect(screen.queryByTestId("pdf-document")).not.toBeInTheDocument();
    });

    it("renders loading and error states", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByText("טוען רשימת מסמכים...")).toBeInTheDocument();
        expect(screen.getByText("לא ניתן להציג כרגע.")).toBeInTheDocument();
    });

    it("renders PDF pages when document loads", () => {
        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-page-1")).toBeInTheDocument();
        expect(screen.getByTestId("pdf-page-2")).toBeInTheDocument();
    });

    it("uses desktop width for pages on large screens", () => {
        mockInnerWidth(1024);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", "500");
    });

    it("uses mobile width for pages on small screens", () => {
        mockInnerWidth(400);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        const expectedWidth = (400 - 32).toString();
        expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", expectedWidth);
    });

    it("resets numPages when modal closes and reopens", () => {
        const { rerender } = render(<PdfExampleModal {...defaultProps} open={true} />);

        expect(screen.getByTestId("pdf-page-1")).toBeInTheDocument();
        expect(screen.getByTestId("pdf-page-2")).toBeInTheDocument();

        // Close modal
        rerender(<PdfExampleModal {...defaultProps} open={false} />);

        // Reopen modal
        rerender(<PdfExampleModal {...defaultProps} open={true} />);

        // Should still render all pages since our mock always returns 2 pages
        expect(screen.getByTestId("pdf-page-1")).toBeInTheDocument();
        expect(screen.getByTestId("pdf-page-2")).toBeInTheDocument();
    });

    it("handles URL change while modal is open", () => {
        const { rerender } = render(<PdfExampleModal {...defaultProps} open={true} url="first.pdf" />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();

        rerender(<PdfExampleModal {...defaultProps} open={true} url="second.pdf" />);

        expect(screen.getByTestId("pdf-document")).toBeInTheDocument();
    });

    it("handles edge case of very small screen width", () => {
        mockInnerWidth(300);

        render(<PdfExampleModal {...defaultProps} open={true} />);

        const expectedWidth = (300 - 32).toString();
        expect(screen.getByTestId("pdf-page-1")).toHaveAttribute("data-width", expectedWidth);
    });
});
