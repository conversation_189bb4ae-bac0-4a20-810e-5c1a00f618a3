import { render, screen, act } from "@testing-library/react";
import { AdminTable } from "@/components/table/admin-table";
import { AdminTableColumn } from "@/components/table/types";
import { toast } from "sonner";

jest.mock("sonner", () => ({
    toast: {
        loading: jest.fn(),
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/components/table/table-header", () => ({
    TableHeader: jest.fn(() => <div>TableHeader</div>)
}));

jest.mock("@/components/table/table-body", () => ({
    TableBody: jest.fn(() => <div>TableBody</div>)
}));

jest.mock("@/components/table/pagination", () => ({
    Pagination: jest.fn(() => <div>Pagination</div>)
}));

type MockItem = {
    id: string;
    name: string;
};

const mockColumns: AdminTableColumn<MockItem>[] = [{ key: "name", label: "Name", render: (item) => item.name }];

const mockItems: MockItem[] = [
    { id: "1", name: "Item 1" },
    { id: "2", name: "Item 2" }
];

const baseProps = {
    title: "Test Table",
    items: mockItems,
    columns: mockColumns,
    loading: false,
    error: null,
    onDelete: jest.fn(),
    onRefetch: jest.fn()
};

describe("AdminTable", () => {
    let originalConsoleError: typeof console.error;

    beforeEach(() => {
        jest.clearAllMocks();
        originalConsoleError = console.error;
        // Suppress console.error for act warnings and expected error logs
        console.error = (...args: any[]) => {
            const firstArg = typeof args[0] === "string" ? args[0] : "";
            if (firstArg.includes("was not wrapped in act") || firstArg.includes("Error deleting item:")) {
                return;
            }
            originalConsoleError(...args);
        };
    });

    afterEach(() => {
        console.error = originalConsoleError;
    });

    it("renders TableHeader, TableBody and Pagination when given items", () => {
        render(<AdminTable {...baseProps} onPageChange={jest.fn()} totalItems={10} totalPages={5} />);
        expect(screen.getByText("TableHeader")).toBeInTheDocument();
        expect(screen.getByText("TableBody")).toBeInTheDocument();
        expect(screen.getByText("Pagination")).toBeInTheDocument();
    });

    it("renders loading state correctly", () => {
        render(<AdminTable {...baseProps} loading={true} />);
        expect(screen.getByText("טוען...")).toBeInTheDocument();
    });

    it("renders error state correctly", () => {
        const error = new Error("Test error");
        render(<AdminTable {...baseProps} error={error} />);
        expect(screen.getByText(/שגיאה בטעינה: Test error/)).toBeInTheDocument();
    });

    it('renders "no items" message when items array is empty', () => {
        render(<AdminTable {...baseProps} items={[]} />);
        expect(screen.getByText("לא נמצאו פריטים")).toBeInTheDocument();
    });

    it("does not render pagination if onPageChange is not provided", () => {
        render(<AdminTable {...baseProps} onPageChange={undefined} />);
        expect(screen.queryByText("Pagination")).not.toBeInTheDocument();
    });

    it("does not render pagination if totalPages is 1", () => {
        render(<AdminTable {...baseProps} onPageChange={jest.fn()} totalPages={1} />);
        expect(screen.queryByText("Pagination")).not.toBeInTheDocument();
    });

    describe("handleDeleteConfirm", () => {
        const TableBodyMock = require("@/components/table/table-body").TableBody;

        it("should call onDelete, show toasts, and refetch on successful deletion", async () => {
            const onDeleteMock = jest.fn().mockResolvedValue({ success: true });
            const onRefetchMock = jest.fn();
            const toastLoadingMock = jest.spyOn(toast, "loading");
            const toastSuccessMock = jest.spyOn(toast, "success");

            render(<AdminTable {...baseProps} onDelete={onDeleteMock} onRefetch={onRefetchMock} />);

            const tableBodyProps = TableBodyMock.mock.calls[0][0];
            await act(async () => {
                await tableBodyProps.onDeleteConfirm("1");
            });

            expect(toastLoadingMock).toHaveBeenCalledWith("מוחק...");
            expect(onDeleteMock).toHaveBeenCalledWith("1");
            expect(toastSuccessMock).toHaveBeenCalledWith("הפריט נמחק בהצלחה", expect.any(Object));
            expect(onRefetchMock).toHaveBeenCalled();
        });

        it("should show an error toast on failed deletion", async () => {
            const onDeleteMock = jest.fn().mockResolvedValue({ success: false, error: "Deletion failed" });
            const onRefetchMock = jest.fn();
            const toastErrorMock = jest.spyOn(toast, "error");
            const toastLoadingMock = jest.spyOn(toast, "loading").mockReturnValue("test-toast-id");

            render(<AdminTable {...baseProps} onDelete={onDeleteMock} onRefetch={onRefetchMock} />);

            const tableBodyProps = TableBodyMock.mock.calls[0][0];
            await act(async () => {
                await tableBodyProps.onDeleteConfirm("1");
            });

            expect(onDeleteMock).toHaveBeenCalledWith("1");
            expect(toastErrorMock).toHaveBeenCalledWith("שגיאה במחיקה", {
                description: "Deletion failed",
                id: "test-toast-id"
            });
            expect(onRefetchMock).not.toHaveBeenCalled();
        });
    });

    describe("filtering", () => {
        const TableHeaderMock = require("@/components/table/table-header").TableHeader;

        it("should call onFilter when handleFilterApply is triggered", () => {
            const onFilterMock = jest.fn();
            render(<AdminTable {...baseProps} onFilter={onFilterMock} />);

            const tableHeaderProps = TableHeaderMock.mock.calls[0][0];
            act(() => {
                tableHeaderProps.onFilterApply({ name: "test" });
            });

            expect(onFilterMock).toHaveBeenCalledWith({ name: "test" });
        });

        it("should call onFilter when clearFilter is triggered", () => {
            const onFilterMock = jest.fn();
            render(<AdminTable {...baseProps} onFilter={onFilterMock} activeFilters={{ name: "test" }} />);

            const tableHeaderProps = TableHeaderMock.mock.calls[0][0];
            act(() => {
                tableHeaderProps.onClearFilter("name");
            });

            expect(onFilterMock).toHaveBeenCalledWith({});
        });

        it("should call onFilter when clearAllFilters is triggered", () => {
            const onFilterMock = jest.fn();
            render(<AdminTable {...baseProps} onFilter={onFilterMock} activeFilters={{ name: "test" }} />);

            const tableHeaderProps = TableHeaderMock.mock.calls[0][0];
            act(() => {
                tableHeaderProps.onClearAllFilters();
            });

            expect(onFilterMock).toHaveBeenCalledWith({});
        });

        it("should call onFilter when updateFilter is triggered", () => {
            const onFilterMock = jest.fn();
            render(<AdminTable {...baseProps} onFilter={onFilterMock} />);

            const tableHeaderProps = TableHeaderMock.mock.calls[0][0];
            act(() => {
                tableHeaderProps.onUpdateFilter("name", "new value");
            });

            expect(onFilterMock).toHaveBeenCalledWith({ name: "new value" });
        });
    });
});
