/**
 * @jest-environment jsdom
 */
import { render, screen, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TableBody } from "@/components/table/table-body";
import { AdminTableColumn } from "@/components/table/types";

// Mocking the DndContext and related components for testing
let capturedOnDragEnd: (event: any) => void = () => {};
jest.mock("@dnd-kit/core", () => ({
    ...jest.requireActual("@dnd-kit/core"),
    DndContext: ({ children, onDragEnd }: { children: React.ReactNode; onDragEnd: (event: any) => void }) => {
        capturedOnDragEnd = onDragEnd;
        return <div>{children}</div>;
    },
    useSensor: jest.fn(),
    useSensors: jest.fn(),
    closestCenter: jest.fn()
}));

jest.mock("@dnd-kit/sortable", () => ({
    ...jest.requireActual("@dnd-kit/sortable"),
    SortableContext: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    sortableKeyboardCoordinates: jest.fn(),
    verticalListSortingStrategy: jest.fn()
}));

jest.mock("@/components/table/draggable-row", () => ({
    DraggableRow: ({ children }: { children: React.ReactNode }) => <tr>{children}</tr>
}));

jest.mock("@/components/common/delete-confirmation-dialog", () => ({
    DeleteConfirmationDialog: ({ children, onConfirm }: { children: React.ReactNode; onConfirm: () => void }) => (
        <div onClick={onConfirm}>{children}</div>
    )
}));

interface MockItem {
    id: string;
    name: string;
    value: number;
}

const mockItems: MockItem[] = [
    { id: "1", name: "Item 1", value: 10 },
    { id: "2", name: "Item 2", value: 20 },
    { id: "3", name: "Item 3", value: 30 }
];

const mockColumns: AdminTableColumn<MockItem>[] = [
    { key: "name", label: "Name", render: (item) => <span>{item.name}</span> },
    { key: "value", label: "Value", render: (item) => <span>{item.value}</span> }
];

const defaultProps = {
    items: mockItems,
    columns: mockColumns,
    addButtonHref: "/admin/items/new",
    isDeleting: false,
    deleteConfirmTitle: "Delete Item",
    deleteConfirmDescription: "Are you sure?",
    confirmDeleteText: "Delete",
    onDeleteConfirm: jest.fn(),
    onReorder: jest.fn()
};

describe("TableBody", () => {
    describe("Non-reorderable", () => {
        it("should render items and columns correctly", () => {
            render(<TableBody {...defaultProps} reorderable={false} />);

            // Check for headers
            expect(screen.getByText("Name")).toBeInTheDocument();
            expect(screen.getByText("Value")).toBeInTheDocument();

            // Check for item data
            expect(screen.getByText("Item 1")).toBeInTheDocument();
            expect(screen.getByText("20")).toBeInTheDocument();
        });

        it("should call onDeleteConfirm when delete is confirmed", async () => {
            const user = userEvent.setup();
            const onDeleteConfirm = jest.fn();
            render(<TableBody {...defaultProps} reorderable={false} onDeleteConfirm={onDeleteConfirm} />);

            const deleteButtons = screen.getAllByRole("button", { name: "Delete" });
            await user.click(deleteButtons[0]);

            expect(onDeleteConfirm).toHaveBeenCalledWith("1");
        });
    });

    describe("Reorderable", () => {
        it("should render reorderable rows correctly", () => {
            render(<TableBody {...defaultProps} reorderable={true} />);

            // Check for item data
            expect(screen.getByText("Item 1")).toBeInTheDocument();
            expect(screen.getByText("20")).toBeInTheDocument();

            // Check that the drag handle column is rendered
            expect(screen.getByText("סדר")).toBeInTheDocument();
        });

        it("should call onReorder with the new item order on drag end", () => {
            render(<TableBody {...defaultProps} reorderable={true} onReorder={defaultProps.onReorder} />);

            const dragEndEvent = {
                active: { id: "1" },
                over: { id: "3" }
            };

            act(() => {
                capturedOnDragEnd(dragEndEvent);
            });

            const expectedNewOrder = [
                { id: "2", name: "Item 2", value: 20 },
                { id: "3", name: "Item 3", value: 30 },
                { id: "1", name: "Item 1", value: 10 }
            ];

            expect(defaultProps.onReorder).toHaveBeenCalledWith(expectedNewOrder);
        });
    });
});
