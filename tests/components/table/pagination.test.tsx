/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Pagination } from "@/components/table/pagination";

const onPageChange = jest.fn();

const defaultProps = {
    currentPage: 1,
    totalPages: 10,
    pageSize: 10,
    totalItems: 100,
    onPageChange
};

describe("Pagination", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should render correctly on the first page", () => {
        render(<Pagination {...defaultProps} />);

        expect(screen.getByText("1-10 מתוך 100")).toBeInTheDocument();
        expect(screen.getByText("דף הבא")).toBeInTheDocument();
        expect(screen.getByText("דף אחרון")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "דף ראשון" })).toBeDisabled();
        expect(screen.getByRole("button", { name: "דף קודם" })).toBeDisabled();
    });

    it("should render correctly on a middle page", () => {
        render(<Pagination {...defaultProps} currentPage={5} />);

        expect(screen.getByText("41-50 מתוך 100")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "דף ראשון" })).not.toBeDisabled();
        expect(screen.getByRole("button", { name: "דף קודם" })).not.toBeDisabled();
        expect(screen.getByRole("button", { name: "דף הבא" })).not.toBeDisabled();
        expect(screen.getByRole("button", { name: "דף אחרון" })).not.toBeDisabled();
    });

    it("should render correctly on the last page", () => {
        render(<Pagination {...defaultProps} currentPage={10} />);

        expect(screen.getByText("91-100 מתוך 100")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "דף הבא" })).toBeDisabled();
        expect(screen.getByRole("button", { name: "דף אחרון" })).toBeDisabled();
    });

    it("should not render if totalPages is 1 or less", () => {
        const { container } = render(<Pagination {...defaultProps} totalPages={1} />);
        expect(container.firstChild).toBeNull();
    });

    it("should call onPageChange with the correct page number", async () => {
        const user = userEvent.setup();
        render(<Pagination {...defaultProps} currentPage={5} />);

        await user.click(screen.getByRole("button", { name: "דף הבא" }));
        expect(onPageChange).toHaveBeenCalledWith(6);

        await user.click(screen.getByRole("button", { name: "דף קודם" }));
        expect(onPageChange).toHaveBeenCalledWith(4);

        await user.click(screen.getByRole("button", { name: "דף אחרון" }));
        expect(onPageChange).toHaveBeenCalledWith(10);

        await user.click(screen.getByRole("button", { name: "דף ראשון" }));
        expect(onPageChange).toHaveBeenCalledWith(1);
    });
});
