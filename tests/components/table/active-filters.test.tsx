import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";

import { ActiveFilters } from "@/components/table/filter/active-filters";
import { AdminTableColumn, DateFilterValue, NumberFilterValue, FilterValue } from "@/components/table/types";
import { Option } from "@/components/forms/fields/dropdown-base";

describe("ActiveFilters component", () => {
    type Row = { id: string };

    // Common helper to create the component with sensible defaults
    const setup = (
        activeFilters: Record<string, FilterValue>,
        overrides?: {
            onClearFilter?: jest.Mock;
            onUpdateFilter?: jest.Mock;
            onFilterClick?: jest.Mock;
        }
    ) => {
        const columns: AdminTableColumn<Row>[] = [
            {
                key: "name",
                label: "שם",
                filterType: "text"
            },
            {
                key: "status",
                label: "סטטוס",
                filterType: "select",
                filterOptions: [
                    { id: "active", label: "פעיל" } as Option,
                    { id: "inactive", label: "לא פעיל" } as Option
                ]
            },
            {
                key: "created_at",
                label: "תאריך יצירה",
                filterType: "date"
            },
            {
                key: "score",
                label: "ציון",
                filterType: "number"
            }
        ];

        const onClearFilter = overrides?.onClearFilter ?? jest.fn();
        const onUpdateFilter = overrides?.onUpdateFilter ?? jest.fn();
        const onFilterClick = overrides?.onFilterClick ?? jest.fn();

        render(
            <ActiveFilters
                columns={columns}
                activeFilters={activeFilters}
                onClearFilter={onClearFilter}
                onClearAllFilters={jest.fn()}
                onUpdateFilter={onUpdateFilter}
                onFilterClick={onFilterClick}
            />
        );

        return { onClearFilter, onUpdateFilter, onFilterClick };
    };

    it("returns null when there are no active filters", () => {
        const { container } = render(
            <ActiveFilters columns={[]} activeFilters={{}} onClearFilter={jest.fn()} onClearAllFilters={jest.fn()} />
        );

        expect(container.firstChild).toBeNull();
    });

    it("renders badges for each filter with correct display values", () => {
        const dateRange: DateFilterValue = {
            startDate: new Date(2024, 0, 1), // 1 Jan 2024
            endDate: new Date(2024, 0, 31) // 31 Jan 2024
        };
        const numberRange: NumberFilterValue = { min: 10, max: 100 };

        setup({
            name: "משה",
            status: ["active", "inactive"],
            created_at: dateRange,
            score: numberRange
        });

        // Text filter value
        expect(screen.getByText("שם:"));
        expect(screen.getByText("משה"));

        // Select filter values (mapped from option labels)
        expect(screen.getByText("פעיל"));
        expect(screen.getByText("לא פעיל"));

        // Date range value – use same locale formatting as component to avoid hard-coding
        const fromStr = dateRange.startDate!.toLocaleDateString("he-IL");
        const toStr = dateRange.endDate!.toLocaleDateString("he-IL");
        expect(screen.getByText(new RegExp(`${toStr}.*${fromStr}`))); // "to - from"

        // Number range value
        expect(screen.getByText("10 - 100"));
    });

    it("calls onUpdateFilter when removing a value from a multi-select filter", () => {
        const { onUpdateFilter } = setup({ status: ["active", "inactive"] });

        // There will be two remove buttons for the status filter.
        // Click the first one (removing "active").
        const removeButtons = screen.getAllByLabelText("הסר סינון");
        fireEvent.click(removeButtons[0]);

        expect(onUpdateFilter).toHaveBeenCalledTimes(1);
        expect(onUpdateFilter).toHaveBeenCalledWith("status", ["inactive"]);
    });

    it("calls onClearFilter when removing a single-value filter", () => {
        const { onClearFilter } = setup({ name: "משה" });

        const removeButtons = screen.getAllByLabelText("הסר סינון");
        fireEvent.click(removeButtons[0]);

        expect(onClearFilter).toHaveBeenCalledTimes(1);
        expect(onClearFilter).toHaveBeenCalledWith("name");
    });

    it("triggers onFilterClick when a badge is clicked", () => {
        const { onFilterClick } = setup({ name: "משה" });

        // Click on the badge (not the remove button)
        fireEvent.click(screen.getByText("משה"));

        expect(onFilterClick).toHaveBeenCalledTimes(1);
        expect(onFilterClick).toHaveBeenCalledWith("name");
    });
});
