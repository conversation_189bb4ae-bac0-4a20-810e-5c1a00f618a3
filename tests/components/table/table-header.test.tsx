import { render, screen } from "@testing-library/react";
import { TableHeader } from "@/components/table/table-header";
import { AdminTableColumn } from "@/components/table/types";

jest.mock("@/components/table/filter/filter-drawer", () => ({
    FilterDrawer: jest.fn(() => <div>FilterDrawer</div>)
}));

jest.mock("@/components/table/filter/active-filters", () => ({
    ActiveFilters: jest.fn(() => <div>ActiveFilters</div>)
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: jest.fn(() => <div>SingleSelect</div>)
}));

type MockItem = {
    id: string;
    name: string;
    category: string;
};

const mockColumns: AdminTableColumn<MockItem>[] = [
    { key: "name", label: "Name", render: (item) => item.name },
    { key: "category", label: "Category", render: (item) => item.category, filterable: true }
];

const mockColumnsNotFilterable: AdminTableColumn<MockItem>[] = [
    { key: "name", label: "Name", render: (item) => item.name }
];

const baseProps = {
    title: "Test Header",
    columns: mockColumns,
    activeFilters: {},
    onFilterApply: jest.fn(),
    onClearFilter: jest.fn(),
    onClearAllFilters: jest.fn()
};

describe("TableHeader", () => {
    it("renders the title", () => {
        render(<TableHeader {...baseProps} />);
        expect(screen.getByRole("heading", { name: /Test Header/i })).toBeInTheDocument();
    });

    describe("Add Button", () => {
        it("renders the add button when href is provided", () => {
            render(<TableHeader {...baseProps} addButtonHref="/add" addButtonLabel="Add New" />);
            const button = screen.getByRole("link", { name: /Add New/i });
            expect(button).toBeInTheDocument();
            expect(button).toHaveAttribute("href", "/add");
        });

        it("does not render the add button when href is not provided", () => {
            render(<TableHeader {...baseProps} />);
            expect(screen.queryByRole("link", { name: /Add New/i })).not.toBeInTheDocument();
        });
    });

    describe("Page Size Selector", () => {
        it("renders page size selector when props are provided", () => {
            render(
                <TableHeader
                    {...baseProps}
                    pageSize={10}
                    onPageSizeChange={jest.fn()}
                    pageSizeOptions={[{ id: "10", label: "10" }]}
                />
            );
            expect(screen.getByText("SingleSelect")).toBeInTheDocument();
        });

        it("does not render page size selector when props are missing", () => {
            render(<TableHeader {...baseProps} />);
            expect(screen.queryByText("SingleSelect")).not.toBeInTheDocument();
        });
    });

    describe("Filtering", () => {
        it("renders filter components when there are filterable columns", () => {
            render(<TableHeader {...baseProps} columns={mockColumns} />);
            expect(screen.getByText("FilterDrawer")).toBeInTheDocument();
            expect(screen.getByText("ActiveFilters")).toBeInTheDocument();
        });

        it("does not render filter components when no columns are filterable", () => {
            render(<TableHeader {...baseProps} columns={mockColumnsNotFilterable} />);
            expect(screen.queryByText("FilterDrawer")).not.toBeInTheDocument();
            expect(screen.queryByText("ActiveFilters")).not.toBeInTheDocument();
        });
    });
});
