import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useFormContext } from "react-hook-form";

import { FilterDrawer } from "@/components/table/filter/filter-drawer";
import { AdminTableColumn, FilterValue } from "@/components/table/types";

// ---- Mocks ---- //

// Stub out Sheet-related UI to avoid portal complexity
jest.mock("@/components/ui/sheet", () => {
    const React = require("react");
    return {
        Sheet: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
        SheetTrigger: ({ children }: { children: React.ReactElement }) => children,
        SheetContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
        SheetHeader: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
        SheetTitle: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
    };
});

// Simple stub for ShortText (text input)
jest.mock("@/components/forms/fields/short-text", () => ({
    ShortText: ({ name, placeholder }: { name: string; placeholder?: string }) => {
        const { register } = useFormContext();
        return <input data-testid={name} placeholder={placeholder} {...register(name)} />;
    }
}));

// Stub for NumberRange (two numeric inputs: min / max)
jest.mock("@/components/forms/fields/number-range", () => ({
    NumberRange: ({ name }: { name: string }) => {
        const { register } = useFormContext();
        return (
            <div>
                <input data-testid={`${name}-min`} type="number" {...register(`${name}.min`)} />
                <input data-testid={`${name}-max`} type="number" {...register(`${name}.max`)} />
            </div>
        );
    }
}));

// Stub for DateRange (two date inputs: startDate / endDate)
jest.mock("@/components/forms/fields/date-range", () => ({
    DateRange: ({ name }: { name: string }) => {
        const { register } = useFormContext();
        return (
            <div>
                <input data-testid={`${name}-start`} type="date" {...register(`${name}.startDate`)} />
                <input data-testid={`${name}-end`} type="date" {...register(`${name}.endDate`)} />
            </div>
        );
    }
}));

// We don't use MultiSelect in these tests, but stub to avoid unexpected errors
jest.mock("@/components/forms/fields/multi-select", () => ({
    MultiSelect: ({ name }: { name: string }) => {
        const { register } = useFormContext();
        return <select data-testid={name} multiple {...register(name)} />;
    }
}));

// ---- Tests ---- //

describe("FilterDrawer component", () => {
    type Row = { id: string };

    const createColumns = (): AdminTableColumn<Row>[] => [
        {
            key: "name",
            label: "Name",
            filterable: true,
            filterType: "text"
        },
        {
            key: "created_at",
            label: "Created At",
            filterable: true,
            filterType: "date"
        },
        {
            key: "score",
            label: "Score",
            filterable: true,
            filterType: "number"
        }
    ];

    it("returns null when there are no filterable columns", () => {
        const columns: AdminTableColumn<Row>[] = [
            {
                key: "id",
                label: "ID",
                filterable: false,
                filterType: "text"
            }
        ];
        const { container } = render(<FilterDrawer columns={columns} activeFilters={{}} onApplyFilters={jest.fn()} />);
        expect(container.firstChild).toBeNull();
    });

    it("submits processed filters via onApplyFilters when Apply is clicked", async () => {
        const onApplyFilters = jest.fn();

        render(<FilterDrawer columns={createColumns()} activeFilters={{}} onApplyFilters={onApplyFilters} />);

        // Fill text filter
        fireEvent.change(screen.getByTestId("name"), { target: { value: "משה" } });

        // Fill number range filter
        fireEvent.change(screen.getByTestId("score-min"), { target: { value: "5" } });
        fireEvent.change(screen.getByTestId("score-max"), { target: { value: "10" } });

        // Fill date range filter
        fireEvent.change(screen.getByTestId("created_at-start"), { target: { value: "2024-01-01" } });
        fireEvent.change(screen.getByTestId("created_at-end"), { target: { value: "2024-01-31" } });

        // Click Apply ("החל")
        fireEvent.click(screen.getByText("החל"));

        await waitFor(() => expect(onApplyFilters).toHaveBeenCalledTimes(1));

        const expected: Record<string, FilterValue> = {
            name: "משה",
            score: { min: 5, max: 10 },
            created_at: {
                startDate: new Date("2024-01-01"),
                endDate: new Date("2024-01-31")
            } as unknown as FilterValue
        };

        const received = onApplyFilters.mock.calls[0][0];

        // Verify text filter
        expect(received.name).toBe(expected.name);

        // Verify number filter
        expect(received.score).toEqual(expected.score);

        // Verify date filter (compare timestamps)
        const receivedDateRange = received.created_at as unknown as {
            startDate: Date | string;
            endDate: Date | string;
        };

        const expectedDateRange = expected.created_at as unknown as {
            startDate: Date;
            endDate: Date;
        };

        expect(new Date(receivedDateRange.startDate).getTime()).toBe(expectedDateRange.startDate.getTime());
        expect(new Date(receivedDateRange.endDate).getTime()).toBe(expectedDateRange.endDate.getTime());
    });

    it("resets filters and sends empty object when Reset is clicked", async () => {
        const onApplyFilters = jest.fn();
        const activeFilters: Record<string, FilterValue> = { name: "abc" };

        render(
            <FilterDrawer columns={createColumns()} activeFilters={activeFilters} onApplyFilters={onApplyFilters} />
        );

        // Click Reset ("נקה הכל")
        fireEvent.click(screen.getByText("נקה הכל"));

        await waitFor(() => expect(onApplyFilters).toHaveBeenCalledWith({}));
    });
});
