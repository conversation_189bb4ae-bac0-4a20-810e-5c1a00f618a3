import { render, screen } from "@testing-library/react";
import React from "react";

import { OnboardingContext } from "@/app/onboarding/onboarding-context";
import { PersonalDetailsStep } from "@/app/onboarding/steps/personal-details-step";

// Mock Clerk
jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

// Mock signup preferences dependencies
jest.mock("@/app/actions/signup-actions", () => ({
    saveSignupPreferences: jest.fn()
}));

jest.mock("@/utils/user-claims-client", () => ({
    getUserClaim: jest.fn(),
    setUserClaim: jest.fn(),
    UserClaimKey: {
        ACCEPTED_TERMS: "accepted_terms",
        SUBSCRIBED_TO_UPDATES: "subscribed_to_updates"
    }
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({}))
}));

// Mock signup preferences context
jest.mock("@/contexts/signup-preferences-context", () => ({
    useSignupPreferences: jest.fn(() => ({
        state: {
            preferences: { acceptedTerms: false, subscribeNewsletter: true },
            isSet: false
        },
        setAcceptedTerms: jest.fn(),
        setSubscribeNewsletter: jest.fn(),
        setPreferences: jest.fn(),
        clearPreferences: jest.fn(),
        hasPreferences: jest.fn(() => false)
    }))
}));

jest.mock("@/components/forms/dynamic-questions-form/dynamic-questions-form", () => {
    return {
        DynamicQuestionsForm: React.forwardRef(
            ({ onSubmitEnd }: { onSubmitEnd: (success: boolean) => void }, ref: React.ForwardedRef<unknown>) => {
                React.useImperativeHandle(ref, () => ({}));
                React.useEffect(() => {
                    onSubmitEnd(true);
                }, [onSubmitEnd]);
                return <div data-testid="dynamic-form" />;
            }
        )
    };
});

const mockDispatch = jest.fn();
const mockUseUser = require("@clerk/nextjs").useUser;
const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;
const mockGetUserClaim = require("@/utils/user-claims-client").getUserClaim;
const mockSetUserClaim = require("@/utils/user-claims-client").setUserClaim;

const prefetchedQuestions = {
    questions: [],
    questionGroups: {},
    conditions: [],
    questionConditionLinks: [],
    answers: []
};

describe("PersonalDetailsStep", () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Default mocks
        mockUseUser.mockReturnValue({ user: null });
        mockSaveSignupPreferences.mockResolvedValue({ success: true });
        mockGetUserClaim.mockResolvedValue(null);
        mockSetUserClaim.mockResolvedValue(undefined);
    });

    it("renders title and triggers dispatch when form completes", () => {
        render(
            <OnboardingContext.Provider
                value={{
                    step: 0,
                    selectedPlanId: null,
                    isSubmitting: false,
                    error: "",
                    showEligibility: false,
                    validatedCoupon: null,
                    personalDetailsFormRef: { current: null },
                    isStepLoading: false,
                    dispatch: mockDispatch,
                    userId: "user_1",
                    supabase: null
                }}
            >
                <PersonalDetailsStep prefetchedQuestions={prefetchedQuestions} />
            </OnboardingContext.Provider>
        );

        expect(screen.getByText(/ברוך הבא/i)).toBeInTheDocument();
        expect(screen.getByTestId("dynamic-form")).toBeInTheDocument();
        expect(mockDispatch).toHaveBeenCalledWith({ type: "END_SUBMIT", payload: { success: true } });
    });
});
