import { render, screen } from "@testing-library/react";
import React from "react";

import { OnboardingContext } from "@/app/onboarding/onboarding-context";
import { PersonalDetailsStep } from "@/app/onboarding/steps/personal-details-step";

jest.mock("@/components/forms/dynamic-questions-form/dynamic-questions-form", () => {
    return {
        DynamicQuestionsForm: React.forwardRef(
            ({ onSubmitEnd }: { onSubmitEnd: (success: boolean) => void }, ref: React.ForwardedRef<unknown>) => {
                React.useImperativeHandle(ref, () => ({}));
                React.useEffect(() => {
                    onSubmitEnd(true);
                }, [onSubmitEnd]);
                return <div data-testid="dynamic-form" />;
            }
        )
    };
});

const mockDispatch = jest.fn();

const prefetchedQuestions = {
    questions: [],
    questionGroups: {},
    conditions: [],
    questionConditionLinks: [],
    answers: []
};

describe("PersonalDetailsStep", () => {
    it("renders title and triggers dispatch when form completes", () => {
        render(
            <OnboardingContext.Provider
                value={{
                    step: 0,
                    selectedPlanId: null,
                    isSubmitting: false,
                    error: "",
                    showEligibility: false,
                    validatedCoupon: null,
                    personalDetailsFormRef: { current: null },
                    isStepLoading: false,
                    dispatch: mockDispatch,
                    userId: "user_1",
                    supabase: null
                }}
            >
                <PersonalDetailsStep prefetchedQuestions={prefetchedQuestions} />
            </OnboardingContext.Provider>
        );

        expect(screen.getByText(/ברוך הבא/i)).toBeInTheDocument();
        expect(screen.getByTestId("dynamic-form")).toBeInTheDocument();
        expect(mockDispatch).toHaveBeenCalledWith({ type: "END_SUBMIT", payload: { success: true } });
    });
});
