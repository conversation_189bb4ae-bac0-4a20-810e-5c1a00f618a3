import { render, screen } from "@testing-library/react";
import React from "react";

import { OnboardingContext } from "@/app/onboarding/onboarding-context";
import { EligibilityStep } from "@/app/onboarding/steps/eligibility-step";

jest.mock("@/components/onboarding/onboarding-loader", () => {
    return {
        OnboardingLoader: ({ onComplete }: { onComplete?: () => void }) => {
            React.useEffect(() => {
                if (onComplete) onComplete();
            }, [onComplete]);
            return <div data-testid="onboarding-loader" />;
        }
    };
});

jest.mock("@/components/scholarship/eligible-scholarships-summary-banner", () => ({
    EligibleScholarshipsSummaryBanner: () => <div data-testid="summary-banner" />
}));

jest.mock("@/components/ui/fireworks", () => ({
    Fireworks: () => <div data-testid="fireworks" />
}));

const mockUseScholarshipEligibility = jest.fn();
jest.mock("@/hooks/use-scholarship-eligibility", () => ({
    useScholarshipEligibility: () => mockUseScholarshipEligibility()
}));

const baseContext = {
    step: 1,
    selectedPlanId: null,
    isSubmitting: false,
    error: "",
    showEligibility: true,
    validatedCoupon: null,
    personalDetailsFormRef: { current: null },
    isStepLoading: false,
    dispatch: jest.fn(),
    userId: "user_1",
    supabase: null
};

let mockMatchMedia: jest.Mock;

beforeAll(() => {
    mockMatchMedia = jest.fn().mockImplementation((query: string) => ({
        matches: false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
    }));

    Object.defineProperty(window, "matchMedia", {
        writable: true,
        value: mockMatchMedia
    });
});

beforeEach(() => {
    mockMatchMedia.mockClear();
    mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-reduced-motion: reduce)",
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
    }));
});

describe("EligibilityStep", () => {
    it("shows loader while eligibility is loading", () => {
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [],
            totalMinAmount: 0,
            totalMaxAmount: 0,
            loading: true
        });

        render(
            <OnboardingContext.Provider value={baseContext}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(screen.getByTestId("onboarding-loader")).toBeInTheDocument();
        expect(screen.queryByTestId("summary-banner")).not.toBeInTheDocument();
    });

    it("shows summary banner when loading completes", () => {
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [{ id: 1 }],
            totalMinAmount: 1000,
            totalMaxAmount: 2000,
            loading: false
        });

        render(
            <OnboardingContext.Provider value={baseContext}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(screen.getByTestId("summary-banner")).toBeInTheDocument();
        expect(screen.queryByTestId("onboarding-loader")).not.toBeInTheDocument();
    });

    it("dispatches SET_STEP_LOADING to true when eligibility loading starts", () => {
        const dispatch = jest.fn();
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [],
            totalMinAmount: 0,
            totalMaxAmount: 0,
            loading: true
        });

        render(
            <OnboardingContext.Provider value={{ ...baseContext, dispatch }}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(dispatch).toHaveBeenCalledWith({ type: "SET_STEP_LOADING", payload: true });
    });

    it("dispatches SET_STEP_LOADING to false when eligibility loading completes", () => {
        const dispatch = jest.fn();
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [{ id: 1 }],
            totalMinAmount: 1000,
            totalMaxAmount: 2000,
            loading: false
        });

        render(
            <OnboardingContext.Provider value={{ ...baseContext, dispatch }}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(dispatch).toHaveBeenCalledWith({ type: "SET_STEP_LOADING", payload: false });
    });

    it("shows fireworks when loading completes and user does not prefer reduced motion", () => {
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [{ id: 1 }],
            totalMinAmount: 1000,
            totalMaxAmount: 2000,
            loading: false
        });

        mockMatchMedia.mockImplementation((query: string) => ({
            matches: query === "(prefers-reduced-motion: reduce)" ? false : false,
            media: query,
            addListener: jest.fn(),
            removeListener: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            dispatchEvent: jest.fn()
        }));

        render(
            <OnboardingContext.Provider value={baseContext}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(screen.getByTestId("fireworks")).toBeInTheDocument();
    });

    it("does not show fireworks when loading completes and user prefers reduced motion", () => {
        mockUseScholarshipEligibility.mockReturnValue({
            eligibleScholarships: [{ id: 1 }],
            totalMinAmount: 1000,
            totalMaxAmount: 2000,
            loading: false
        });

        mockMatchMedia.mockImplementation((query: string) => ({
            matches: query === "(prefers-reduced-motion: reduce)" ? true : false,
            media: query,
            addListener: jest.fn(),
            removeListener: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            dispatchEvent: jest.fn()
        }));

        render(
            <OnboardingContext.Provider value={baseContext}>
                <EligibilityStep />
            </OnboardingContext.Provider>
        );

        expect(screen.queryByTestId("fireworks")).not.toBeInTheDocument();
    });
});
