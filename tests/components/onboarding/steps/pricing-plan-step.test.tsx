import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";

import { PRICING_PLANS } from "@/config/subscriptions";

import { OnboardingContext } from "@/app/onboarding/onboarding-context";
import { PricingPlanStep } from "@/app/onboarding/steps/pricing-plan-step";

jest.mock("@/components/subscriptions/coupon-section", () => ({
    CouponSection: () => <div data-testid="coupon-section" />
}));

jest.mock("@/components/subscriptions/skeleton/subscriptions-skeleton", () => ({
    SubscriptionsSkeleton: ({ cardCount }: { cardCount: number }) => (
        <div data-testid="subscriptions-skeleton" data-count={cardCount} />
    )
}));

jest.mock("@/components/subscriptions/onboarding-subscription-card", () => ({
    OnboardingSubscriptionCard: ({
        item,
        onSelect
    }: {
        item: { id: string; title: string };
        onSelect: (id: string) => void;
    }) => (
        <button type="button" data-testid={`card-${item.id}`} onClick={() => onSelect(item.id)}>
            {item.title}
        </button>
    )
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    __esModule: true,
    calculateFinalPrice: jest.fn((price: number) => Promise.resolve(price))
}));

const baseContext = {
    step: 2,
    selectedPlanId: null,
    isSubmitting: false,
    error: "",
    showEligibility: true,
    validatedCoupon: null,
    personalDetailsFormRef: { current: null },
    isStepLoading: false,
    dispatch: jest.fn(),
    userId: "user_1",
    supabase: null
};

describe("PricingPlanStep", () => {
    it("renders loading skeleton initially and then shows cards", async () => {
        render(
            <OnboardingContext.Provider value={baseContext}>
                <PricingPlanStep />
            </OnboardingContext.Provider>
        );

        expect(screen.getByTestId("subscriptions-skeleton")).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.queryByTestId("subscriptions-skeleton")).not.toBeInTheDocument();
        });

        PRICING_PLANS.forEach((plan) => {
            expect(screen.getByTestId(`card-${plan.id}`)).toBeInTheDocument();
        });
    });

    it("dispatches SELECT_PLAN when a card is clicked", async () => {
        const user = userEvent.setup();
        const dispatchMock = jest.fn();
        render(
            <OnboardingContext.Provider value={{ ...baseContext, dispatch: dispatchMock }}>
                <PricingPlanStep />
            </OnboardingContext.Provider>
        );

        await waitFor(() => screen.getByTestId(`card-${PRICING_PLANS[0].id}`));

        await user.click(screen.getByTestId(`card-${PRICING_PLANS[0].id}`));

        expect(dispatchMock).toHaveBeenCalledWith({ type: "SELECT_PLAN", payload: PRICING_PLANS[0].id });
    });
});
