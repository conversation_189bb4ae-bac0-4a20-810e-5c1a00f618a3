import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

import { CouponAppliedInfo } from "@/lib/subscription-constants";

import { OnboardingProvider, useOnboarding } from "@/app/onboarding/onboarding-context";
import { OnboardingNavigation } from "@/app/onboarding/components/onboarding-navigation";

const TEXTS = {
    back: "חזור",
    next: "המשך",
    finish: "סיום"
};

jest.mock("@/app/onboarding/onboarding-context", () => ({
    ...jest.requireActual("@/app/onboarding/onboarding-context"),
    useOnboarding: jest.fn()
}));

const mockDispatch = jest.fn();
const mockHandleFinalSubmit = jest.fn();

jest.mock("@/app/onboarding/hooks/use-onboarding-submit", () => ({
    useOnboardingSubmit: () => ({
        handleFinalSubmit: mockHandleFinalSubmit
    })
}));

const mockUseOnboarding = useOnboarding as jest.Mock;

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({
        from: jest.fn(() => ({
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({ data: {}, error: null })
        }))
    }))
}));

const renderWithContext = (contextValue: Partial<ReturnType<typeof useOnboarding>>) => {
    mockUseOnboarding.mockReturnValue({
        step: 0,
        isSubmitting: false,
        selectedPlanId: null,
        personalDetailsFormRef: { current: null },
        validatedCoupon: null,
        dispatch: mockDispatch,
        isStepLoading: false,
        ...contextValue
    });

    return render(
        <OnboardingProvider>
            <OnboardingNavigation />
        </OnboardingProvider>
    );
};

describe("OnboardingNavigation", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders Back and Next buttons on the first step", () => {
        renderWithContext({ step: 0 });
        expect(screen.getByRole("button", { name: TEXTS.back })).toBeInTheDocument();
        expect(screen.getByRole("button", { name: TEXTS.next })).toBeInTheDocument();
    });

    it("disables the Back button on the first step", () => {
        renderWithContext({ step: 0 });
        expect(screen.getByRole("button", { name: TEXTS.back })).toBeDisabled();
    });

    it("enables the Back button on subsequent steps", () => {
        renderWithContext({ step: 1 });
        expect(screen.getByRole("button", { name: TEXTS.back })).toBeEnabled();
    });

    it("calls dispatch with PREVIOUS_STEP when Back button is clicked", async () => {
        renderWithContext({ step: 1 });
        await userEvent.click(screen.getByRole("button", { name: TEXTS.back }));
        expect(mockDispatch).toHaveBeenCalledWith({ type: "PREVIOUS_STEP" });
    });

    it("renders Finish button on the last step", () => {
        renderWithContext({ step: 2 });
        expect(screen.getByRole("button", { name: TEXTS.finish })).toBeInTheDocument();
        expect(screen.queryByRole("button", { name: TEXTS.next })).not.toBeInTheDocument();
    });

    it("disables the Finish button if no plan is selected", () => {
        renderWithContext({ step: 2, selectedPlanId: null });
        expect(screen.getByRole("button", { name: TEXTS.finish })).toBeDisabled();
    });

    it("enables the Finish button when a plan is selected", () => {
        renderWithContext({ step: 2, selectedPlanId: "plan_123" });
        expect(screen.getByRole("button", { name: TEXTS.finish })).toBeEnabled();
    });

    it("calls handleFinalSubmit when Finish button is clicked", async () => {
        const selectedPlanId = "plan_123";
        const validatedCoupon: CouponAppliedInfo = {
            couponCode: "SAVE10",
            couponType: "percentage",
            discountValue: 10,
            discountApplied: 10,
            finalAmount: 90
        };
        renderWithContext({ step: 2, selectedPlanId, validatedCoupon });

        await userEvent.click(screen.getByRole("button", { name: TEXTS.finish }));
        expect(mockHandleFinalSubmit).toHaveBeenCalledWith({ selectedPlanId, validatedCoupon });
    });

    it("shows a spinner when isSubmitting is true", () => {
        renderWithContext({ step: 2, selectedPlanId: "plan_123", isSubmitting: true });
        expect(screen.getByRole("button", { name: TEXTS.finish })).toBeDisabled();
        expect(document.querySelector("svg.animate-spin")).toBeInTheDocument();
    });

    it("disables the Next button when isStepLoading is true", () => {
        renderWithContext({ step: 1, isStepLoading: true });
        expect(screen.getByRole("button", { name: TEXTS.next })).toBeDisabled();
    });

    it("submits the personal details form on the first step next click", async () => {
        const submitMock = jest.fn();
        const personalDetailsFormRef = { current: { submit: submitMock } as unknown as HTMLFormElement };
        renderWithContext({ step: 0, personalDetailsFormRef });

        await userEvent.click(screen.getByRole("button", { name: TEXTS.next }));
        expect(mockDispatch).toHaveBeenCalledWith({ type: "START_SUBMIT" });
        expect(submitMock).toHaveBeenCalled();
    });

    it("calls dispatch with NEXT_STEP on intermediate steps", async () => {
        renderWithContext({ step: 1 });
        await userEvent.click(screen.getByRole("button", { name: TEXTS.next }));
        expect(mockDispatch).toHaveBeenCalledWith({ type: "NEXT_STEP" });
    });
});
