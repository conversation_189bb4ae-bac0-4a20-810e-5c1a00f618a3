import type { SupabaseClient } from "@supabase/supabase-js";
import { render, screen } from "@testing-library/react";
import React from "react";

import type { Database } from "@/types/database.types";

import { OnboardingClientView } from "@/app/onboarding/onboarding-client";
import { initialState, OnboardingContext } from "@/app/onboarding/onboarding-context";

jest.mock("@/app/onboarding/components/onboarding-navigation", () => ({
    OnboardingNavigation: () => <div data-testid="onboarding-navigation" />
}));
jest.mock("@/app/onboarding/steps/personal-details-step", () => ({
    PersonalDetailsStep: () => <div data-testid="personal-details-step" />
}));
jest.mock("@/app/onboarding/steps/eligibility-step", () => ({
    EligibilityStep: () => <div data-testid="eligibility-step" />
}));
jest.mock("@/app/onboarding/steps/pricing-plan-step", () => ({
    PricingPlanStep: () => <div data-testid="pricing-plan-step" />
}));
jest.mock("@/components/onboarding/onboarding-loader", () => ({
    OnboardingLoader: () => <div data-testid="onboarding-loader" />
}));

const mockPrefetchedQuestions = {
    questions: [],
    questionGroups: {},
    conditions: [],
    questionConditionLinks: [],
    answers: []
};

type ContextValue = Partial<typeof initialState> & {
    dispatch: jest.Mock;
    userId?: string | null;
    supabase?: SupabaseClient<Database> | null;
};

const renderWithContext = (contextValue: ContextValue) => {
    return render(
        <OnboardingContext.Provider value={contextValue as unknown as React.ContextType<typeof OnboardingContext>}>
            <OnboardingClientView prefetchedQuestions={mockPrefetchedQuestions} />
        </OnboardingContext.Provider>
    );
};

describe("OnboardingClientView", () => {
    const baseContext = {
        step: 0,
        isSubmitting: false,
        selectedPlanId: null,
        personalDetailsFormRef: { current: null },
        validatedCoupon: null,
        dispatch: jest.fn(),
        error: "",
        isStepLoading: false
    };

    it("renders nothing for step -1 (no visible step component)", () => {
        const { container } = renderWithContext({ ...baseContext, step: -1 });
        expect(container.querySelector('[data-testid="personal-details-step"]')).not.toBeInTheDocument();
        expect(container.querySelector('[data-testid="eligibility-step"]')).not.toBeInTheDocument();
        expect(container.querySelector('[data-testid="pricing-plan-step"]')).not.toBeInTheDocument();
    });

    it("renders PersonalDetailsStep for step 0", () => {
        renderWithContext({ ...baseContext, step: 0 });
        expect(screen.getByTestId("personal-details-step")).toBeInTheDocument();
        expect(screen.getByTestId("onboarding-navigation")).toBeInTheDocument();
    });

    it("renders EligibilityStep for step 1", () => {
        renderWithContext({ ...baseContext, step: 1, showEligibility: true });
        expect(screen.getByTestId("eligibility-step")).toBeInTheDocument();
        expect(screen.getByTestId("onboarding-navigation")).toBeInTheDocument();
    });

    it("renders PricingPlanStep for step 2", () => {
        renderWithContext({ ...baseContext, step: 2 });
        expect(screen.getByTestId("pricing-plan-step")).toBeInTheDocument();
        expect(screen.getByTestId("onboarding-navigation")).toBeInTheDocument();
    });

    it("renders navigation but no step component for an invalid step", () => {
        const { container } = renderWithContext({ ...baseContext, step: 99 });
        expect(container.querySelector('[data-testid="personal-details-step"]')).not.toBeInTheDocument();
        expect(container.querySelector('[data-testid="eligibility-step"]')).not.toBeInTheDocument();
        expect(container.querySelector('[data-testid="pricing-plan-step"]')).not.toBeInTheDocument();
        expect(container.querySelector('[data-testid="onboarding-navigation"]')).toBeInTheDocument();
    });

    it("displays an error message when an error is present", () => {
        const error = "Something went wrong";
        renderWithContext({ ...baseContext, error });
        expect(screen.getByText(error)).toBeInTheDocument();
    });

    it("does not render a loader when isStepLoading is true at the root level", () => {
        const { container } = renderWithContext({ ...baseContext, isStepLoading: true });
        expect(container.querySelector('[data-testid="onboarding-loader"]')).not.toBeInTheDocument();
    });
});
