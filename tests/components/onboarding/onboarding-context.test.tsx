import { CouponAppliedInfo } from "@/lib/subscription-constants";

import { initialState, onboardingReducer } from "@/app/onboarding/onboarding-context";

describe("onboardingReducer", () => {
    it("should return the initial state for unknown actions", () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect(onboardingReducer(initialState, { type: "UNKNOWN_ACTION" } as any)).toEqual(initialState);
    });

    it("should handle NEXT_STEP", () => {
        const state = onboardingReducer(initialState, { type: "NEXT_STEP" });
        expect(state.step).toBe(1);
    });

    it("should handle PREVIOUS_STEP", () => {
        const stateWithStep = { ...initialState, step: 2 };
        const state = onboardingReducer(stateWithStep, { type: "PREVIOUS_STEP" });
        expect(state.step).toBe(1);
    });

    it("should not go below step 0 on PREVIOUS_STEP", () => {
        const state = onboardingReducer(initialState, { type: "PREVIOUS_STEP" });
        expect(state.step).toBe(0);
    });

    it("should handle SET_STEP", () => {
        const state = onboardingReducer(initialState, { type: "SET_STEP", payload: 2 });
        expect(state.step).toBe(2);
    });

    it("should handle START_SUBMIT", () => {
        const state = onboardingReducer(initialState, { type: "START_SUBMIT" });
        expect(state.isSubmitting).toBe(true);
    });

    it("should handle END_SUBMIT on success", () => {
        const submittingState = { ...initialState, isSubmitting: true, step: 0 };
        const state = onboardingReducer(submittingState, { type: "END_SUBMIT", payload: { success: true } });
        expect(state.isSubmitting).toBe(false);
        expect(state.step).toBe(1);
        expect(state.showEligibility).toBe(true);
    });

    it("should handle END_SUBMIT on failure", () => {
        const submittingState = { ...initialState, isSubmitting: true, step: 0 };
        const state = onboardingReducer(submittingState, { type: "END_SUBMIT", payload: { success: false } });
        expect(state.isSubmitting).toBe(false);
        expect(state.step).toBe(0);
    });

    it("should handle SELECT_PLAN", () => {
        const planId = "plan_premium";
        const state = onboardingReducer(initialState, { type: "SELECT_PLAN", payload: planId });
        expect(state.selectedPlanId).toBe(planId);
    });

    it("should handle SET_COUPON", () => {
        const coupon: CouponAppliedInfo = {
            couponCode: "TEST",
            couponType: "percentage",
            discountValue: 10,
            discountApplied: 5,
            finalAmount: 45
        };
        const state = onboardingReducer(initialState, { type: "SET_COUPON", payload: coupon });
        expect(state.validatedCoupon).toEqual(coupon);
    });

    it("should handle SET_COUPON with null", () => {
        const coupon: CouponAppliedInfo = {
            couponCode: "TEST",
            couponType: "percentage",
            discountValue: 10,
            discountApplied: 5,
            finalAmount: 45
        };
        const stateWithCoupon = { ...initialState, validatedCoupon: coupon };
        const state = onboardingReducer(stateWithCoupon, { type: "SET_COUPON", payload: null });
        expect(state.validatedCoupon).toBeNull();
    });

    it("should handle SET_STEP_LOADING", () => {
        const state = onboardingReducer(initialState, { type: "SET_STEP_LOADING", payload: true });
        expect(state.isStepLoading).toBe(true);
    });
});
