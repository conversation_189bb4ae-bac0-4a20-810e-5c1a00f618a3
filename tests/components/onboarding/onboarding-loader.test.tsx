import React from "react";
import { render, screen, act, cleanup } from "@testing-library/react";
import { OnboardingLoader } from "@/components/onboarding/onboarding-loader";

// Mock next/dynamic to handle the dynamic import of Lottie
jest.mock("next/dynamic", () => ({
    __esModule: true,
    default: () => {
        const MockLottie = () => <div data-testid="lottie-mock" />;
        MockLottie.displayName = "Lottie";
        return MockLottie;
    }
}));

jest.useFakeTimers();

const TEXTS = {
    progressSteps: ["בודקים את הפרטים שלך", "מחפשים מלגות מתאימות", "כמעט סיימנו"]
};

describe("OnboardingLoader", () => {
    afterEach(() => {
        cleanup();
        jest.clearAllTimers();
    });

    it("renders the initial progress text and lottie mock", () => {
        render(<OnboardingLoader />);
        expect(screen.getByText(TEXTS.progressSteps[0], { exact: false })).toBeInTheDocument();
        expect(screen.getByTestId("lottie-mock")).toBeInTheDocument();
    });

    it("cycles through progress texts over time", () => {
        render(<OnboardingLoader />);
        expect(screen.getByText(TEXTS.progressSteps[0], { exact: false })).toBeInTheDocument();

        act(() => {
            jest.advanceTimersByTime(1500);
        });
        expect(screen.getByText(TEXTS.progressSteps[1], { exact: false })).toBeInTheDocument();

        act(() => {
            jest.advanceTimersByTime(1500);
        });
        expect(screen.getByText(TEXTS.progressSteps[2], { exact: false })).toBeInTheDocument();
    });

    it("calls onComplete when the final step is finished", () => {
        const handleComplete = jest.fn();
        render(<OnboardingLoader onComplete={handleComplete} />);

        // Initial render
        expect(screen.getByText(TEXTS.progressSteps[0], { exact: false })).toBeInTheDocument();

        // Advance to step 2
        act(() => {
            jest.advanceTimersByTime(1500);
        });
        expect(screen.getByText(TEXTS.progressSteps[1], { exact: false })).toBeInTheDocument();

        // Advance to step 3
        act(() => {
            jest.advanceTimersByTime(1500);
        });
        expect(screen.getByText(TEXTS.progressSteps[2], { exact: false })).toBeInTheDocument();

        // Advance for the onComplete call
        act(() => {
            jest.advanceTimersByTime(1500);
        });

        expect(handleComplete).toHaveBeenCalledTimes(1);
    });

    it("cleans up the timer on unmount", () => {
        const { unmount } = render(<OnboardingLoader />);
        const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");
        unmount();
        expect(clearTimeoutSpy).toHaveBeenCalled();
        clearTimeoutSpy.mockRestore();
    });
});
