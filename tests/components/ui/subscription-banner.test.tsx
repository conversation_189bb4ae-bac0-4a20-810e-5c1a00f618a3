/**
 * @jest-environment jsdom
 */
import { useUser } from "@clerk/nextjs";
import { render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import "@testing-library/jest-dom";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { SubscriptionBanner } from "@/components/ui/subscription-banner";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    getCurrentUserSubscription: jest.fn()
}));

jest.mock("@/config/dashboard", () => ({
    TEXTS: {
        subscribeBanner: "שדרג את התוכנית שלך עכשיו!"
    }
}));

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="link">
            {children}
        </a>
    );
});

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockGetCurrentUserSubscription = getCurrentUserSubscription as jest.MockedFunction<
    typeof getCurrentUserSubscription
>;

describe("SubscriptionBanner", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("does not render when user is not loaded", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: false
        } as any);

        render(<SubscriptionBanner />);

        expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
    });

    it("does not render when user is not authenticated", async () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true
        } as any);

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
        });
    });

    it("does not render when user has a paid plan", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapro",
            planType: "milgapro",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 280,
            paid_amount: 280,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
        });
    });

    it("renders banner when user is on free plan", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "basic",
            planType: "free",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 0,
            paid_amount: 0,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.getByText("שדרג את התוכנית שלך עכשיו!")).toBeInTheDocument();
            expect(screen.getByText("שדרג עכשיו")).toBeInTheDocument();
        });

        const link = screen.getByTestId("link");
        expect(link).toHaveAttribute("href", "/subscriptions");
    });

    it("renders banner when subscription is null (defaults to free)", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue(null);

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.getByText("שדרג את התוכנית שלך עכשיו!")).toBeInTheDocument();
        });
    });

    it("renders banner when subscription fetch fails (defaults to free)", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockRejectedValue(new Error("Network error"));

        const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.getByText("שדרג את התוכנית שלך עכשיו!")).toBeInTheDocument();
        });
        expect(consoleSpy).toHaveBeenCalledWith("Error fetching subscription:", expect.any(Error));

        consoleSpy.mockRestore();
    });

    it("does not render during loading state", () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        // Don't resolve the promise immediately
        mockGetCurrentUserSubscription.mockReturnValue(new Promise(() => {}));

        render(<SubscriptionBanner />);

        expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
    });

    it("has correct banner styling and structure", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue(null);

        render(<SubscriptionBanner />);

        await waitFor(() => {
            const banner = screen.getByText("שדרג את התוכנית שלך עכשיו!").closest('[dir="rtl"]');
            expect(banner).toHaveAttribute("dir", "rtl");
            expect(banner).toHaveClass(
                "w-full",
                "py-3",
                "px-6",
                "flex",
                "items-center",
                "justify-between",
                "bg-blue-50",
                "text-blue-800",
                "shadow-sm",
                "border-b"
            );
        });
    });

    it("does not render for elite plan users", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapo-elite",
            planType: "elite",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 350,
            paid_amount: 350,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
        });
    });

    it("does not render for VIP plan users", async () => {
        mockUseUser.mockReturnValue({
            user: { id: "user123" },
            isLoaded: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapo-vip",
            planType: "vip",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 400,
            paid_amount: 400,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(<SubscriptionBanner />);

        await waitFor(() => {
            expect(screen.queryByText("שדרג את התוכנית שלך עכשיו!")).not.toBeInTheDocument();
        });
    });
});
