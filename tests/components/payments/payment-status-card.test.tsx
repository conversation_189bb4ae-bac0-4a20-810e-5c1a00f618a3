import React from "react";
import { render, screen, cleanup } from "@testing-library/react";
import { PaymentStatusCard } from "@/components/payments/payment-status-card";
import { Button } from "@/components/ui/button";

jest.mock("next/dynamic", () => ({
    __esModule: true,
    default: () => {
        const MockLottie = ({ animationData }: { animationData: any }) => (
            <div data-testid="lottie-mock" data-animation={JSON.stringify(animationData)} />
        );
        MockLottie.displayName = "Lottie";
        return MockLottie;
    }
}));

const mockProps = {
    title: "Payment Processing",
    status: "loading" as const,
    loadingText: "Please wait...",
    successTitle: "Payment Successful!",
    successDescription: "Thank you for your payment.",
    successDetailsPrefix: "Order ID:",
    successTransactionPrefix: "Transaction ID:",
    errorTitle: "Payment Failed",
    errorDescription: "Something went wrong.",
    details: {
        orderId: "12345",
        transactionId: "abcde"
    },
    actions: [<Button key="1">Go to Dashboard</Button>]
};

describe("PaymentStatusCard", () => {
    afterEach(cleanup);

    it("renders loading state correctly", () => {
        render(<PaymentStatusCard {...mockProps} status="loading" />);
        expect(screen.getByText(mockProps.title)).toBeInTheDocument();
        expect(screen.getByText(mockProps.loadingText)).toBeInTheDocument();
        expect(screen.queryByText(/Order ID:/)).not.toBeInTheDocument();
    });

    it("renders success state correctly with all details", () => {
        render(<PaymentStatusCard {...mockProps} status="success" />);
        expect(screen.getByText(mockProps.successTitle)).toBeInTheDocument();
        expect(screen.getByText(mockProps.successDescription)).toBeInTheDocument();
        expect(screen.getByText(/Order ID: 12345/)).toBeInTheDocument();
        expect(screen.getByText(/Transaction ID: abcde/)).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "Go to Dashboard" })).toBeInTheDocument();
    });

    it("renders error state correctly", () => {
        render(<PaymentStatusCard {...mockProps} status="error" />);
        expect(screen.getByText(mockProps.errorTitle)).toBeInTheDocument();
        expect(screen.getByText(mockProps.errorDescription)).toBeInTheDocument();
        expect(screen.queryByText(/Order ID:/)).not.toBeInTheDocument();
        expect(screen.getByRole("button", { name: "Go to Dashboard" })).toBeInTheDocument();
    });

    it("does not render details section if details are not provided in success state", () => {
        const propsWithoutDetails = { ...mockProps, details: undefined };
        render(<PaymentStatusCard {...propsWithoutDetails} status="success" />);
        expect(screen.queryByText(/Order ID:/)).not.toBeInTheDocument();
        expect(screen.queryByText(/Transaction ID:/)).not.toBeInTheDocument();
    });
});
