import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { toast } from "sonner";

import { UserNoteForm } from "@/components/forms/user-note-form";

// Mock dependencies
jest.mock("sonner", () => ({
    toast: {
        error: jest.fn(),
        success: jest.fn()
    }
}));

jest.mock("@/app/actions/user-notes-actions", () => ({
    createUserNote: jest.fn()
}));

const mockCreateUserNote = require("@/app/actions/user-notes-actions").createUserNote;

describe("UserNoteForm", () => {
    const defaultProps = {
        reportedUserId: "user123"
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders form with note input and submit button", () => {
        render(<UserNoteForm {...defaultProps} />);

        expect(screen.getByPlaceholderText("הזן הערה...")).toBeInTheDocument();
        expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("submits form with valid note", async () => {
        mockCreateUserNote.mockResolvedValue({ success: true });
        const mockOnSuccess = jest.fn();

        render(<UserNoteForm {...defaultProps} onSuccess={mockOnSuccess} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(mockCreateUserNote).toHaveBeenCalledWith("user123", "Test note");
            expect(toast.success).toHaveBeenCalledWith("הערה נוספה בהצלחה");
            expect(mockOnSuccess).toHaveBeenCalled();
        });

        // Check that form is reset
        expect(input).toHaveValue("");
    });

    it("handles submission error", async () => {
        mockCreateUserNote.mockResolvedValue({
            success: false,
            error: "Failed to create note"
        });

        render(<UserNoteForm {...defaultProps} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(toast.error).toHaveBeenCalledWith("Failed to create note");
        });
    });

    it("handles network error", async () => {
        mockCreateUserNote.mockRejectedValue(new Error("Network error"));

        render(<UserNoteForm {...defaultProps} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(toast.error).toHaveBeenCalledWith("שגיאה ביצירת הערה");
        });
    });

    it("submits form on Enter key press", async () => {
        mockCreateUserNote.mockResolvedValue({ success: true });

        render(<UserNoteForm {...defaultProps} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        fireEvent.keyDown(input, { key: "Enter", shiftKey: false });

        await waitFor(() => {
            expect(mockCreateUserNote).toHaveBeenCalledWith("user123", "Test note");
        });
    });

    it("does not submit on Shift+Enter", () => {
        render(<UserNoteForm {...defaultProps} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        fireEvent.keyDown(input, { key: "Enter", shiftKey: true });

        expect(mockCreateUserNote).not.toHaveBeenCalled();
    });

    it("disables submit button while submitting", async () => {
        mockCreateUserNote.mockImplementation(
            () => new Promise((resolve) => setTimeout(() => resolve({ success: true }), 100))
        );

        render(<UserNoteForm {...defaultProps} />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        expect(submitButton).toBeDisabled();

        await waitFor(() => {
            expect(submitButton).not.toBeDisabled();
        });
    });

    it("works without onSuccess callback", async () => {
        mockCreateUserNote.mockResolvedValue({ success: true });

        render(<UserNoteForm reportedUserId="user123" />);

        const input = screen.getByPlaceholderText("הזן הערה...");
        fireEvent.change(input, { target: { value: "Test note" } });

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(mockCreateUserNote).toHaveBeenCalledWith("user123", "Test note");
            expect(toast.success).toHaveBeenCalledWith("הערה נוספה בהצלחה");
        });
    });

    it("handles empty note submission", async () => {
        render(<UserNoteForm {...defaultProps} />);

        const submitButton = screen.getByRole("button");
        fireEvent.click(submitButton);

        // Should not call the action with empty note
        expect(mockCreateUserNote).not.toHaveBeenCalled();
    });
});
