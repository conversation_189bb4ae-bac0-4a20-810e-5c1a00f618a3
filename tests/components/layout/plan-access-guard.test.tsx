/**
 * @jest-environment jsdom
 */
import { useUser } from "@clerk/nextjs";
import { render, screen, waitFor } from "@testing-library/react";
import { usePathname, useRouter } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { PlanAccessGuard } from "@/components/layout/plan-access-guard";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("next/navigation", () => ({
    usePathname: jest.fn(),
    useRouter: jest.fn()
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    getCurrentUserSubscription: jest.fn()
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text: string }) => <div data-testid="loading-icon">{text}</div>
}));

jest.mock("@/components/layout/sidebar/plan-access", () => ({
    hasAccess: jest.fn((userPlan: string, requiredPlan?: string) => {
        if (!requiredPlan || requiredPlan === "free") return true;
        if (userPlan === "free") return requiredPlan === "free";
        if (userPlan === "milgapro") return ["free", "milgapro"].includes(requiredPlan);
        if (userPlan === "elite") return ["free", "milgapro", "elite"].includes(requiredPlan);
        if (userPlan === "vip") return true;
        return false;
    })
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockGetCurrentUserSubscription = getCurrentUserSubscription as jest.MockedFunction<
    typeof getCurrentUserSubscription
>;

const mockDashboardConfig = {
    navMain: [{ name: "Dashboard", url: "/dashboard" }],
    personalDataGroup: {
        title: "Personal Data",
        items: [{ name: "Profile", url: "/profile", requiredPlan: "free" as const }]
    },
    scholarshipTrackingGroup: {
        title: "Scholarships",
        items: [{ name: "Applications", url: "/applications", requiredPlan: "milgapro" as const }]
    },
    navSecondary: [{ name: "Settings", url: "/settings" }]
};

describe("PlanAccessGuard", () => {
    const mockPush = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseRouter.mockReturnValue({
            push: mockPush,
            refresh: jest.fn(),
            back: jest.fn(),
            forward: jest.fn(),
            replace: jest.fn(),
            prefetch: jest.fn()
        } as any);
    });

    it("shows loading state when user is not loaded", () => {
        mockUseUser.mockReturnValue({
            isLoaded: false,
            isSignedIn: false,
            user: null
        } as any);

        mockUsePathname.mockReturnValue("/dashboard");

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div>Test content</div>
            </PlanAccessGuard>
        );

        expect(screen.getByTestId("loading-icon")).toBeInTheDocument();

        expect(screen.getByTestId("loading-icon")).toHaveTextContent("בודק הרשאות...");
    });

    it("shows loading state when subscription is loading", () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/dashboard");

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div>Test content</div>
            </PlanAccessGuard>
        );

        expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
    });

    it("shows access denied when user is not signed in", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: false,
            user: null
        } as any);

        mockUsePathname.mockReturnValue("/dashboard");

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div>Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByText("אין לך גישה")).toBeInTheDocument();

            expect(screen.getByText("עליך להתחבר כדי לגשת לדף זה.")).toBeInTheDocument();

            expect(screen.getByText("התחבר")).toBeInTheDocument();
        });
    });

    it("shows access denied when user plan is insufficient", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/applications");

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "basic",
            planType: "free",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 0,
            paid_amount: 0,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div>Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByText("אין לך גישה")).toBeInTheDocument();

            expect(screen.getByText("התוכנית הנוכחית שלך אינה מאפשרת גישה לדף זה.")).toBeInTheDocument();

            expect(screen.getByText("לשדרוג התוכנית")).toBeInTheDocument();
        });
    });

    it("allows access when user has sufficient plan", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/applications");

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapro",
            planType: "milgapro",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 280,
            paid_amount: 280,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div data-testid="children">Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByTestId("children")).toBeInTheDocument();
            expect(screen.getByText("Test content")).toBeInTheDocument();
        });

        expect(screen.queryByText("אין לך גישה")).not.toBeInTheDocument();
    });

    it("allows access for free pages regardless of plan", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/profile");

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "basic",
            planType: "free",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 0,
            paid_amount: 0,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div data-testid="children">Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByTestId("children")).toBeInTheDocument();
        });
    });

    it("defaults to free plan when subscription fetch fails", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/profile");

        mockGetCurrentUserSubscription.mockRejectedValue(new Error("Network error"));

        const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div data-testid="children">Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByTestId("children")).toBeInTheDocument();
        });

        expect(consoleSpy).toHaveBeenCalledWith("Unexpected error fetching subscription details:", expect.any(Error));

        consoleSpy.mockRestore();
    });

    it("defaults to free plan when subscription is null", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/profile");

        mockGetCurrentUserSubscription.mockResolvedValue(null);

        render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div data-testid="children">Test content</div>
            </PlanAccessGuard>
        );

        await waitFor(() => {
            expect(screen.getByTestId("children")).toBeInTheDocument();
        });
    });

    it("handles cleanup when component unmounts", () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            isSignedIn: true,
            user: { id: "user123" }
        } as any);

        mockUsePathname.mockReturnValue("/dashboard");

        const { unmount } = render(
            <PlanAccessGuard dashboardConfig={mockDashboardConfig}>
                <div>Test content</div>
            </PlanAccessGuard>
        );

        expect(() => unmount()).not.toThrow();
    });
});
