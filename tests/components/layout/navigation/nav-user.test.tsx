/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { NavUser } from "@/components/layout/navigation/nav-user";
import { useSidebar } from "@/components/ui/sidebar";

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

interface MockSidebarContext {
    state: "expanded" | "collapsed";
    open: boolean;
    setOpen: jest.MockedFunction<(open: boolean) => void>;
    openMobile: boolean;
    setOpenMobile: jest.MockedFunction<(open: boolean) => void>;
    isMobile: boolean;
    toggleSidebar: jest.MockedFunction<() => void>;
}

jest.mock("@clerk/nextjs", () => ({
    SignOutButton: ({ children, redirectUrl }: { children: React.ReactNode; redirectUrl: string }) => (
        <div data-testid="sign-out-button" data-redirect={redirectUrl}>
            {children}
        </div>
    ),
    UserProfile: () => <div data-testid="user-profile">User Profile Component</div>
}));

jest.mock("@/components/ui/sidebar", () => ({
    useSidebar: jest.fn(),
    SidebarMenu: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu">{children}</div>,
    SidebarMenuButton: ({ children, ...props }: React.PropsWithChildren) => (
        <button data-testid="sidebar-menu-button" {...props}>
            {children}
        </button>
    ),
    SidebarMenuItem: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu-item">{children}</div>
}));

jest.mock("@/components/ui/dropdown-menu", () => ({
    DropdownMenu: ({ children }: React.PropsWithChildren) => <div data-testid="dropdown-menu">{children}</div>,
    DropdownMenuContent: ({ children }: React.PropsWithChildren) => (
        <div data-testid="dropdown-menu-content">{children}</div>
    ),
    DropdownMenuItem: ({ children, onSelect }: React.PropsWithChildren<{ onSelect?: () => void }>) => (
        <div data-testid="dropdown-menu-item" onClick={onSelect}>
            {children}
        </div>
    ),
    DropdownMenuTrigger: ({ children }: React.PropsWithChildren) => (
        <div data-testid="dropdown-menu-trigger">{children}</div>
    )
}));

jest.mock("@/components/ui/dialog", () => ({
    Dialog: ({ children, open }: React.PropsWithChildren<{ open: boolean }>) =>
        open ? <div data-testid="dialog">{children}</div> : null,
    DialogContent: ({ children }: React.PropsWithChildren) => <div data-testid="dialog-content">{children}</div>,
    DialogTitle: ({ children }: React.PropsWithChildren) => <div data-testid="dialog-title">{children}</div>
}));

jest.mock("@/hooks/use-mobile", () => ({
    useIsMobile: jest.fn(() => false)
}));

const mockUseSidebar = useSidebar as jest.MockedFunction<typeof useSidebar>;

describe("NavUser", () => {
    const mockUser = {
        name: "John Doe",
        email: "<EMAIL>",
        avatar: "https://example.com/avatar.jpg"
    };

    const createMockSidebarContext = (overrides: Partial<MockSidebarContext> = {}): MockSidebarContext => ({
        state: "expanded",
        open: true,
        setOpen: jest.fn(),
        openMobile: false,
        setOpenMobile: jest.fn(),
        isMobile: false,
        toggleSidebar: jest.fn(),
        ...overrides
    });

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseSidebar.mockReturnValue(createMockSidebarContext());
    });

    it("renders user information correctly", () => {
        render(<NavUser user={mockUser} />);

        expect(screen.getByText("John Doe")).toBeInTheDocument();
        expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });

    it("shows only avatar when sidebar is collapsed", () => {
        mockUseSidebar.mockReturnValue(
            createMockSidebarContext({
                state: "collapsed"
            })
        );

        render(<NavUser user={mockUser} />);

        expect(screen.queryByText("John Doe")).not.toBeInTheDocument();
        expect(screen.queryByText("<EMAIL>")).not.toBeInTheDocument();
    });

    it("opens profile dialog when profile menu item is clicked", () => {
        render(<NavUser user={mockUser} />);

        const profileMenuItem = screen.getAllByTestId("dropdown-menu-item")[0];
        fireEvent.click(profileMenuItem);

        expect(screen.getByTestId("dialog")).toBeInTheDocument();
        expect(screen.getByTestId("user-profile")).toBeInTheDocument();
    });

    it("renders logout button with correct redirect", () => {
        render(<NavUser user={mockUser} />);

        const signOutButton = screen.getByTestId("sign-out-button");
        expect(signOutButton).toHaveAttribute("data-redirect", "/login");
    });
});
