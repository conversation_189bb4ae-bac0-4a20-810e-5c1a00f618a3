/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent } from "@testing-library/react";
import { usePathname, useSearchParams } from "next/navigation";
import { Suspense } from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { NavGroup } from "@/components/layout/navigation/nav-group";
import { useSidebar } from "@/components/ui/sidebar";
import { BaseNavItem } from "@/components/layout/navigation/types";
import { PlanType } from "@/lib/subscription-constants";

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

interface MockSearchParams {
    get: (key: string) => string | null;
    [Symbol.iterator]: () => Iterator<[string, string]>;
}

interface MockSidebarContext {
    isMobile: boolean;
    setOpenMobile: jest.MockedFunction<(open: boolean) => void>;
    state: "expanded" | "collapsed";
    open: boolean;
    setOpen: jest.MockedFunction<(open: boolean) => void>;
    openMobile: boolean;
    toggleSidebar: jest.MockedFunction<() => void>;
}

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

type NavGroupItem = BaseNavItem & {
    jsx?: React.ReactNode;
    className?: string;
};

interface TestNavItem {
    href?: string;
    label?: string;
    icon?: MockLucideIcon;
    disabled?: boolean;
    locked?: boolean;
    requiredPlan?: PlanType;
    jsx?: React.ReactNode;
    className?: string;
}

jest.mock("next/navigation", () => ({
    usePathname: jest.fn(),
    useSearchParams: jest.fn()
}));

jest.mock("@/components/ui/sidebar", () => ({
    useSidebar: jest.fn(),
    SidebarGroup: ({ children, ...props }: React.PropsWithChildren) => (
        <div data-testid="sidebar-group" {...props}>
            {children}
        </div>
    ),
    SidebarGroupLabel: ({ children, ...props }: React.PropsWithChildren) => (
        <div data-testid="sidebar-group-label" {...props}>
            {children}
        </div>
    ),
    SidebarMenu: ({ children, ...props }: React.PropsWithChildren) => (
        <div data-testid="sidebar-menu" {...props}>
            {children}
        </div>
    ),
    SidebarMenuButton: ({ children, ...props }: React.PropsWithChildren) => (
        <button data-testid="sidebar-menu-button" {...props}>
            {children}
        </button>
    ),
    SidebarMenuItem: ({ children, ...props }: React.PropsWithChildren) => (
        <div data-testid="sidebar-menu-item" {...props}>
            {children}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-item", () => ({
    NavItem: ({
        item,
        isActive,
        onClick,
        variant,
        collapsed
    }: {
        item: { label: string; href: string };
        isActive: boolean;
        onClick?: () => void;
        variant?: string;
        collapsed?: boolean;
    }) => (
        <div
            data-testid="nav-item"
            data-active={isActive}
            data-variant={variant}
            data-collapsed={collapsed}
            onClick={onClick}
        >
            {item.label}
        </div>
    )
}));

jest.mock("@/lib/plan-utils", () => ({
    getPlanDisplayName: jest.fn((plan: string) => `Plan ${plan}`)
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;
const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>;
const mockUseSidebar = useSidebar as jest.MockedFunction<typeof useSidebar>;

describe("NavGroup", () => {
    const mockSetOpenMobile = jest.fn();

    const createMockIcon = (className?: string): MockLucideIcon => {
        const MockIcon = ({ className: cls }: { className?: string }) => <span className={cls || className}>📊</span>;
        MockIcon.$$typeof = Symbol.for("react.forward_ref");
        return MockIcon as MockLucideIcon;
    };

    const createNavItem = (item: TestNavItem): NavGroupItem => ({
        href: item.href || "#",
        label: item.label || "",
        icon: item.icon as any, // Mock icon compatibility
        disabled: item.disabled,
        locked: item.locked,
        requiredPlan: item.requiredPlan,
        jsx: item.jsx,
        className: item.className
    });

    const mockItems: NavGroupItem[] = [
        createNavItem({
            href: "/dashboard",
            label: "Dashboard",
            icon: createMockIcon()
        }),
        createNavItem({
            href: "/profile",
            label: "Profile",
            icon: createMockIcon()
        })
    ];

    const createMockSearchParams = (query = ""): MockSearchParams => {
        const params = new URLSearchParams(query);
        return {
            get: (key: string) => params.get(key),
            [Symbol.iterator]: () => params[Symbol.iterator]()
        };
    };

    const createMockSidebarContext = (overrides: Partial<MockSidebarContext> = {}): MockSidebarContext => ({
        isMobile: false,
        setOpenMobile: mockSetOpenMobile,
        state: "expanded",
        open: true,
        setOpen: jest.fn(),
        openMobile: false,
        toggleSidebar: jest.fn(),
        ...overrides
    });

    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/dashboard");
        mockUseSearchParams.mockReturnValue(createMockSearchParams() as any);
        mockUseSidebar.mockReturnValue(createMockSidebarContext());
    });

    it("renders with default props", () => {
        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        expect(screen.getByTestId("sidebar-group")).toBeInTheDocument();
        expect(screen.getByTestId("sidebar-group-label")).toBeInTheDocument();
        expect(screen.getByText("Navigation")).toBeInTheDocument();
    });

    it("renders navigation items correctly", () => {
        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        expect(screen.getByText("Dashboard")).toBeInTheDocument();
        expect(screen.getByText("Profile")).toBeInTheDocument();
    });

    it("marks active item correctly", () => {
        mockUsePathname.mockReturnValue("/dashboard");

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        const navItems = screen.getAllByTestId("nav-item");
        expect(navItems[0]).toHaveAttribute("data-active", "true");
        expect(navItems[1]).toHaveAttribute("data-active", "false");
    });

    it("handles query parameters in active state detection", () => {
        mockUsePathname.mockReturnValue("/dashboard");
        mockUseSearchParams.mockReturnValue(createMockSearchParams("tab=analytics") as any);

        const itemsWithQuery: NavGroupItem[] = [
            createNavItem({ href: "/dashboard?tab=analytics", label: "Analytics" }),
            createNavItem({ href: "/dashboard?tab=reports", label: "Reports" })
        ];

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={itemsWithQuery} />
            </Suspense>
        );

        const navItems = screen.getAllByTestId("nav-item");
        expect(navItems[0]).toHaveAttribute("data-active", "true");
        expect(navItems[1]).toHaveAttribute("data-active", "false");
    });

    it("closes mobile sidebar when clicking nav item", () => {
        mockUseSidebar.mockReturnValue(
            createMockSidebarContext({
                isMobile: true,
                state: "expanded"
            })
        );

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        fireEvent.click(screen.getAllByTestId("nav-item")[0]);
        expect(mockSetOpenMobile).toHaveBeenCalledWith(false);
    });

    it("renders locked items with tooltip", () => {
        const lockedItems: NavGroupItem[] = [
            createNavItem({
                href: "/premium",
                label: "Premium Feature",
                disabled: true,
                locked: true,
                requiredPlan: "milgapro"
            })
        ];

        mockUseSidebar.mockReturnValue(
            createMockSidebarContext({
                state: "collapsed"
            })
        );

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={lockedItems} />
            </Suspense>
        );

        const button = screen.getByTestId("sidebar-menu-button");
        expect(button).toHaveAttribute("disabled", "");
    });

    it("renders custom JSX items", () => {
        const customItems: NavGroupItem[] = [
            createNavItem({
                href: "/custom",
                label: "Custom",
                jsx: <div data-testid="custom-jsx">Custom JSX Content</div>
            })
        ];

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={customItems} />
            </Suspense>
        );

        expect(screen.getByTestId("custom-jsx")).toBeInTheDocument();
        expect(screen.getByText("Custom JSX Content")).toBeInTheDocument();
    });

    it("hides group label when sidebar is collapsed", () => {
        mockUseSidebar.mockReturnValue(
            createMockSidebarContext({
                state: "collapsed"
            })
        );

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        expect(screen.queryByTestId("sidebar-group-label")).not.toBeInTheDocument();
    });

    it("shows fallback content during suspense", () => {
        render(<NavGroup label="Navigation" items={mockItems} />);

        expect(screen.getByTestId("sidebar-group")).toBeInTheDocument();
    });

    it("ignores hash-only URLs in active detection", () => {
        const hashItems: NavGroupItem[] = [createNavItem({ href: "#", label: "Hash Link" })];

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={hashItems} />
            </Suspense>
        );

        const navItem = screen.getByTestId("nav-item");
        expect(navItem).toHaveAttribute("data-active", "false");
    });

    it("handles trailing slashes in URL matching", () => {
        mockUsePathname.mockReturnValue("/dashboard/");

        render(
            <Suspense fallback={<div>Loading...</div>}>
                <NavGroup label="Navigation" items={mockItems} />
            </Suspense>
        );

        const navItem = screen.getAllByTestId("nav-item")[0];
        expect(navItem).toHaveAttribute("data-active", "true");
    });
});
