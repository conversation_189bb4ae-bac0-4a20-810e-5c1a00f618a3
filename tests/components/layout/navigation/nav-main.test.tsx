/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent } from "@testing-library/react";
import { usePathname } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { NavMain } from "@/components/layout/navigation/nav-main";
import { useSidebar } from "@/components/ui/sidebar";
import { BaseNavItem } from "@/components/layout/navigation/types";
import { PlanType } from "@/lib/subscription-constants";

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

interface MockSidebarContext {
    isMobile: boolean;
    setOpenMobile: jest.MockedFunction<(open: boolean) => void>;
    state: "expanded" | "collapsed";
    open: boolean;
    setOpen: jest.MockedFunction<(open: boolean) => void>;
    openMobile: boolean;
    toggleSidebar: jest.MockedFunction<() => void>;
}

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

type NavMainItem = BaseNavItem & {
    title?: string;
    url?: string;
};

interface TestNavItem {
    href?: string;
    url?: string;
    label?: string;
    title?: string;
    icon?: MockLucideIcon;
    disabled?: boolean;
    locked?: boolean;
    requiredPlan?: PlanType;
}

jest.mock("next/navigation", () => ({
    usePathname: jest.fn()
}));

jest.mock("@/components/ui/sidebar", () => ({
    useSidebar: jest.fn(),
    SidebarGroup: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-group">{children}</div>,
    SidebarGroupContent: ({ children }: React.PropsWithChildren) => (
        <div data-testid="sidebar-group-content">{children}</div>
    ),
    SidebarMenu: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu">{children}</div>,
    SidebarMenuButton: ({
        children,
        disabled,
        tooltip,
        ...props
    }: React.PropsWithChildren<{ disabled?: boolean; tooltip?: unknown }>) => (
        <button data-testid="sidebar-menu-button" disabled={disabled} {...props}>
            {children}
        </button>
    ),
    SidebarMenuItem: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu-item">{children}</div>
}));

jest.mock("@/components/layout/navigation/nav-item", () => ({
    NavItem: ({
        item,
        isActive,
        onClick,
        variant,
        collapsed
    }: {
        item: { label: string; href: string };
        isActive: boolean;
        onClick?: () => void;
        variant?: string;
        collapsed?: boolean;
    }) => (
        <div
            data-testid="nav-item"
            data-active={isActive}
            data-variant={variant}
            data-collapsed={collapsed}
            onClick={onClick}
        >
            {item.label}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-utils", () => ({
    isNavItemActive: jest.fn((pathname: string, href: string) => pathname === href)
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;
const mockUseSidebar = useSidebar as jest.MockedFunction<typeof useSidebar>;

describe("NavMain", () => {
    const mockSetOpenMobile = jest.fn();

    const createMockIcon = (): MockLucideIcon => {
        const MockIcon = ({ className }: { className?: string }) => (
            <span data-testid="mock-icon" className={className}>
                📊
            </span>
        );
        MockIcon.$$typeof = Symbol.for("react.forward_ref");
        return MockIcon as MockLucideIcon;
    };

    const createMockSidebarContext = (overrides: Partial<MockSidebarContext> = {}): MockSidebarContext => ({
        isMobile: false,
        setOpenMobile: mockSetOpenMobile,
        state: "expanded",
        open: true,
        setOpen: jest.fn(),
        openMobile: false,
        toggleSidebar: jest.fn(),
        ...overrides
    });

    const createNavItem = (item: TestNavItem): NavMainItem => ({
        href: item.href || "#",
        label: item.label || "",
        icon: item.icon as any, // Mock icon compatibility
        disabled: item.disabled,
        locked: item.locked,
        requiredPlan: item.requiredPlan,
        title: item.title,
        url: item.url
    });

    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/dashboard");
        mockUseSidebar.mockReturnValue(createMockSidebarContext());
    });

    describe("Item normalization", () => {
        it("normalizes items with href property", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
        });

        it("normalizes items with url property (fallback to href)", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/profile", label: "Profile", icon: createMockIcon(), url: "/profile" })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByText("Profile")).toBeInTheDocument();
        });

        it("normalizes items with title property (fallback to label)", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/settings", label: "Settings", icon: createMockIcon(), title: "Settings" })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByText("Settings")).toBeInTheDocument();
        });

        it("defaults to # for missing href/url", () => {
            const items: NavMainItem[] = [createNavItem({ label: "No Link", icon: createMockIcon() })];

            render(<NavMain items={items} />);

            expect(screen.getByText("No Link")).toBeInTheDocument();
        });

        it("defaults to empty string for missing label/title", () => {
            const items: NavMainItem[] = [createNavItem({ href: "/empty", icon: createMockIcon() })];

            render(<NavMain items={items} />);

            const menuItem = screen.getByTestId("sidebar-menu-item");
            expect(menuItem).toBeInTheDocument();
        });
    });

    describe("Sidebar states", () => {
        it("renders correctly when sidebar is expanded", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
            expect(screen.getByTestId("nav-item")).toBeInTheDocument();
        });

        it("renders correctly when sidebar is collapsed", () => {
            mockUseSidebar.mockReturnValue(
                createMockSidebarContext({
                    state: "collapsed"
                })
            );

            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            const navItem = screen.getByTestId("nav-item");
            expect(navItem).toHaveAttribute("data-collapsed", "true");
        });

        it("does not collapse when on mobile", () => {
            mockUseSidebar.mockReturnValue(
                createMockSidebarContext({
                    isMobile: true,
                    state: "collapsed"
                })
            );

            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            const navItem = screen.getByTestId("nav-item");
            expect(navItem).toHaveAttribute("data-collapsed", "false");
        });
    });

    describe("Click handling", () => {
        it("closes mobile sidebar when item is clicked", () => {
            mockUseSidebar.mockReturnValue(
                createMockSidebarContext({
                    isMobile: true,
                    state: "expanded"
                })
            );

            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            fireEvent.click(screen.getByTestId("nav-item"));
            expect(mockSetOpenMobile).toHaveBeenCalledWith(false);
        });

        it("does not close sidebar on desktop", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            fireEvent.click(screen.getByTestId("nav-item"));
            expect(mockSetOpenMobile).not.toHaveBeenCalled();
        });
    });

    describe("Disabled items", () => {
        it("renders disabled items without NavItem", () => {
            const items: NavMainItem[] = [
                createNavItem({
                    href: "/premium",
                    label: "Premium",
                    icon: createMockIcon(),
                    disabled: true
                })
            ];

            render(<NavMain items={items} />);

            expect(screen.queryByTestId("nav-item")).not.toBeInTheDocument();
            expect(screen.getByText("Premium")).toBeInTheDocument();
        });

        it("renders hash links without NavItem", () => {
            const items: NavMainItem[] = [createNavItem({ href: "#", label: "Hash Link", icon: createMockIcon() })];

            render(<NavMain items={items} />);

            expect(screen.queryByTestId("nav-item")).not.toBeInTheDocument();
            expect(screen.getByText("Hash Link")).toBeInTheDocument();
        });
    });

    describe("Locked items", () => {
        it("renders lock icon for locked items when collapsed", () => {
            mockUseSidebar.mockReturnValue(
                createMockSidebarContext({
                    state: "collapsed"
                })
            );

            const items: NavMainItem[] = [
                createNavItem({
                    href: "/premium",
                    label: "Premium",
                    icon: createMockIcon(),
                    disabled: true,
                    locked: true
                })
            ];

            render(<NavMain items={items} />);

            const button = screen.getByTestId("sidebar-menu-button");
            expect(button).toHaveAttribute("disabled", "");
        });

        it("renders lock icon for locked items when expanded", () => {
            const items: NavMainItem[] = [
                createNavItem({
                    href: "/premium",
                    label: "Premium",
                    icon: createMockIcon(),
                    disabled: true,
                    locked: true
                })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByText("Premium")).toBeInTheDocument();
        });
    });

    describe("Active state", () => {
        it("marks active item correctly", () => {
            const items: NavMainItem[] = [
                createNavItem({ href: "/dashboard", label: "Dashboard", icon: createMockIcon() }),
                createNavItem({ href: "/profile", label: "Profile", icon: createMockIcon() })
            ];

            render(<NavMain items={items} />);

            const navItems = screen.getAllByTestId("nav-item");
            expect(navItems[0]).toHaveAttribute("data-active", "true");
            expect(navItems[1]).toHaveAttribute("data-active", "false");
        });
    });

    describe("Icon rendering", () => {
        it("renders icons correctly when expanded", () => {
            const items: NavMainItem[] = [
                createNavItem({
                    href: "/dashboard",
                    label: "Dashboard",
                    icon: createMockIcon(),
                    disabled: true
                })
            ];

            render(<NavMain items={items} />);

            expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
        });

        it("centers icon when collapsed", () => {
            mockUseSidebar.mockReturnValue(
                createMockSidebarContext({
                    state: "collapsed"
                })
            );

            const items: NavMainItem[] = [
                createNavItem({
                    href: "/dashboard",
                    label: "Dashboard",
                    icon: createMockIcon(),
                    disabled: true
                })
            ];

            render(<NavMain items={items} />);

            const icon = screen.getByTestId("mock-icon");
            expect(icon).toHaveClass("mx-auto");
        });
    });

    describe("Without icons", () => {
        it("renders correctly without icons", () => {
            const items: NavMainItem[] = [createNavItem({ href: "/dashboard", label: "Dashboard" })];

            render(<NavMain items={items} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
            expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument();
        });
    });
});
