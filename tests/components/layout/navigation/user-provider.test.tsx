/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { useUser } from "@clerk/nextjs";

import { UserProvider } from "@/components/layout/navigation/user-provider";

interface MockClerkUser {
    fullName?: string | null;
    emailAddresses?: Array<{ emailAddress: string }>;
    username?: string | null;
    imageUrl?: string;
}

interface MockUseUserReturn {
    user: MockClerkUser | null | undefined;
    isLoaded: boolean;
    isSignedIn: boolean | undefined;
}

const createMockUserReturn = (user: MockClerkUser | null, isLoaded: boolean = true): MockUseUserReturn => {
    if (!isLoaded) {
        return {
            user: undefined,
            isLoaded: false,
            isSignedIn: undefined
        };
    }

    if (!user) {
        return {
            user: null,
            isLoaded: true,
            isSignedIn: false
        };
    }

    return {
        user,
        isLoaded: true,
        isSignedIn: true
    };
};

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("@/components/layout/navigation/nav-user", () => ({
    NavUser: ({ user }: { user: { name: string; email: string; avatar: string } }) => (
        <div data-testid="nav-user">
            <span data-testid="user-name">{user.name}</span>
            <span data-testid="user-email">{user.email}</span>
            <span data-testid="user-avatar">{user.avatar}</span>
        </div>
    )
}));

jest.mock("@/components/ui/sidebar", () => ({
    SidebarMenu: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu">{children}</div>,
    SidebarMenuButton: ({ children }: React.PropsWithChildren) => (
        <div data-testid="sidebar-menu-button">{children}</div>
    ),
    SidebarMenuItem: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu-item">{children}</div>
}));

jest.mock("@/components/ui/skeleton", () => ({
    Skeleton: ({ className }: { className?: string }) => (
        <div data-testid="skeleton" className={className}>
            Loading...
        </div>
    )
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

describe("UserProvider", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("shows loading skeleton when user is not loaded", () => {
        mockUseUser.mockReturnValue(createMockUserReturn(null, false) as any);

        render(<UserProvider />);

        expect(screen.getAllByTestId("skeleton")).toHaveLength(4);
        expect(screen.getByTestId("sidebar-menu")).toBeTruthy();
    });

    it("returns null when user is loaded but not signed in", () => {
        mockUseUser.mockReturnValue(createMockUserReturn(null, true) as any);

        const { container } = render(<UserProvider />);
        expect(container.firstChild).toBeNull();
    });

    it("renders NavUser when user is signed in", () => {
        const mockUser: MockClerkUser = {
            fullName: "John Doe",
            emailAddresses: [{ emailAddress: "<EMAIL>" }],
            imageUrl: "https://example.com/avatar.jpg"
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("nav-user")).toBeTruthy();
        expect(screen.getByTestId("user-name").textContent).toBe("John Doe");
        expect(screen.getByTestId("user-email").textContent).toBe("<EMAIL>");
        expect(screen.getByTestId("user-avatar").textContent).toBe("https://example.com/avatar.jpg");
    });

    it("falls back to email as name when fullName is missing", () => {
        const mockUser: MockClerkUser = {
            fullName: null,
            emailAddresses: [{ emailAddress: "<EMAIL>" }],
            imageUrl: "https://example.com/avatar.jpg"
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("user-name").textContent).toBe("<EMAIL>");
    });

    it("falls back to username when fullName and email are missing", () => {
        const mockUser: MockClerkUser = {
            fullName: null,
            emailAddresses: [],
            username: "johndoe",
            imageUrl: "https://example.com/avatar.jpg"
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("user-name").textContent).toBe("johndoe");
    });

    it("falls back to 'User' when all name fields are missing", () => {
        const mockUser: MockClerkUser = {
            fullName: null,
            emailAddresses: [],
            username: null,
            imageUrl: "https://example.com/avatar.jpg"
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("user-name").textContent).toBe("User");
    });

    it("handles missing email gracefully", () => {
        const mockUser: MockClerkUser = {
            fullName: "John Doe",
            emailAddresses: [],
            imageUrl: "https://example.com/avatar.jpg"
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("user-email").textContent).toBe("");
    });

    it("handles missing avatar gracefully", () => {
        const mockUser: MockClerkUser = {
            fullName: "John Doe",
            emailAddresses: [{ emailAddress: "<EMAIL>" }],
            imageUrl: ""
        };

        mockUseUser.mockReturnValue(createMockUserReturn(mockUser, true) as any);

        render(<UserProvider />);

        expect(screen.getByTestId("user-avatar").textContent).toBe("");
    });
});
