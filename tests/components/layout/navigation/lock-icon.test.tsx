/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { LockIcon } from "@/components/layout/navigation/lock-icon";

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

describe("LockIcon", () => {
    beforeEach(() => {
        document.body.innerHTML = "";
    });

    it("renders with default props", () => {
        render(<LockIcon />);

        const icon = document.querySelector("svg");
        expect(icon).toBeTruthy();
        expect(icon?.getAttribute("width")).toBe("16");
        expect(icon?.getAttribute("height")).toBe("16");
        expect(icon?.getAttribute("fill")).toBe("none");
        expect(icon?.getAttribute("viewBox")).toBe("0 0 24 24");
        expect(icon?.getAttribute("stroke")).toBe("currentColor");
        expect(icon?.classList.contains("text-muted-foreground")).toBe(true);
    });

    it("applies custom className and size", () => {
        render(<LockIcon className="text-red-500 custom-class" size={24} />);

        const icon = document.querySelector("svg");
        expect(icon?.classList.contains("text-red-500")).toBe(true);
        expect(icon?.classList.contains("custom-class")).toBe(true);
        expect(icon?.classList.contains("text-muted-foreground")).toBe(false);
    });

    it("renders with custom size", () => {
        render(<LockIcon size={24} />);

        const icon = document.querySelector("svg");
        expect(icon?.getAttribute("width")).toBe("24");
        expect(icon?.getAttribute("height")).toBe("24");
    });

    it("applies custom className and size together", () => {
        const customClassName = "custom-lock-icon";
        render(<LockIcon className={customClassName} size={32} />);

        const icon = document.querySelector("svg");
        expect(icon?.classList.contains(customClassName)).toBe(true);
        expect(icon?.getAttribute("width")).toBe("32");
        expect(icon?.getAttribute("height")).toBe("32");
    });

    it("renders path with correct attributes", () => {
        render(<LockIcon />);

        const path = document.querySelector("svg path");
        expect(path).toBeTruthy();
        expect(path?.getAttribute("stroke-linecap")).toBe("round");
        expect(path?.getAttribute("stroke-linejoin")).toBe("round");
        expect(path?.getAttribute("stroke-width")).toBe("2");
        expect(path?.getAttribute("d")).toBe(
            "M17 11V7a5 5 0 00-10 0v4M5 11h14a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2z"
        );
    });

    it("handles zero size", () => {
        render(<LockIcon size={0} />);

        const icon = document.querySelector("svg");
        expect(icon?.getAttribute("width")).toBe("0");
        expect(icon?.getAttribute("height")).toBe("0");
    });

    it("handles empty className", () => {
        render(<LockIcon className="" />);

        const icon = document.querySelector("svg");
        expect(icon?.getAttribute("class")).toBe("");
        expect(icon?.classList.contains("text-muted-foreground")).toBe(false);
    });
});
