/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent } from "@testing-library/react";
import { usePathname } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { NavSecondary } from "@/components/layout/navigation/nav-secondary";
import { useSidebar } from "@/components/ui/sidebar";
import { BaseNavItem } from "@/components/layout/navigation/types";
import { PlanType } from "@/lib/subscription-constants";

interface MockSidebarContext {
    isMobile: boolean;
    setOpenMobile: jest.MockedFunction<(open: boolean) => void>;
    state: "expanded" | "collapsed";
    open: boolean;
    setOpen: jest.MockedFunction<(open: boolean) => void>;
    openMobile: boolean;
    toggleSidebar: jest.MockedFunction<() => void>;
}

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

type NavSecondaryItem = BaseNavItem & {
    title?: string;
    url?: string;
};

interface TestNavItem {
    href?: string;
    label?: string;
    title?: string;
    url?: string;
    icon?: MockLucideIcon;
    disabled?: boolean;
    locked?: boolean;
    requiredPlan?: PlanType;
}

jest.mock("next/navigation", () => ({
    usePathname: jest.fn()
}));

jest.mock("@/components/ui/sidebar", () => ({
    useSidebar: jest.fn(),
    SidebarGroup: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-group">{children}</div>,
    SidebarGroupContent: ({ children }: React.PropsWithChildren) => (
        <div data-testid="sidebar-group-content">{children}</div>
    ),
    SidebarMenu: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu">{children}</div>,
    SidebarMenuButton: ({ children, disabled }: React.PropsWithChildren<{ disabled?: boolean }>) => (
        <button data-testid="sidebar-menu-button" disabled={disabled}>
            {children}
        </button>
    ),
    SidebarMenuItem: ({ children }: React.PropsWithChildren) => <div data-testid="sidebar-menu-item">{children}</div>
}));

jest.mock("@/components/layout/navigation/nav-item", () => ({
    NavItem: ({
        item,
        isActive,
        onClick
    }: {
        item: { label: string; href: string };
        isActive: boolean;
        onClick?: () => void;
    }) => (
        <div data-testid="nav-item" data-active={isActive} onClick={onClick}>
            {item.label}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-utils", () => ({
    isNavItemActive: jest.fn((pathname: string, href: string, exact: boolean) => pathname === href)
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;
const mockUseSidebar = useSidebar as jest.MockedFunction<typeof useSidebar>;

describe("NavSecondary", () => {
    const mockSetOpenMobile = jest.fn();

    const createNavItem = (item: TestNavItem): NavSecondaryItem => ({
        href: item.href || "#",
        label: item.label || "",
        icon: item.icon as any, // Mock icon compatibility
        disabled: item.disabled,
        locked: item.locked,
        requiredPlan: item.requiredPlan,
        title: item.title,
        url: item.url
    });

    const mockItems: NavSecondaryItem[] = [
        createNavItem({ href: "/settings", label: "Settings" }),
        createNavItem({ href: "/help", title: "Help", label: "Help" })
    ];

    const createMockSidebarContext = (overrides: Partial<MockSidebarContext> = {}): MockSidebarContext => ({
        isMobile: false,
        setOpenMobile: mockSetOpenMobile,
        state: "expanded",
        open: true,
        setOpen: jest.fn(),
        openMobile: false,
        toggleSidebar: jest.fn(),
        ...overrides
    });

    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/settings");
        mockUseSidebar.mockReturnValue(createMockSidebarContext());
    });

    it("renders items correctly", () => {
        render(<NavSecondary items={mockItems} />);

        expect(screen.getByText("Settings")).toBeInTheDocument();
        expect(screen.getByText("Help")).toBeInTheDocument();
    });

    it("closes mobile sidebar when item is clicked", () => {
        mockUseSidebar.mockReturnValue(
            createMockSidebarContext({
                isMobile: true,
                state: "expanded"
            })
        );

        render(<NavSecondary items={mockItems} />);

        fireEvent.click(screen.getAllByTestId("nav-item")[0]);
        expect(mockSetOpenMobile).toHaveBeenCalledWith(false);
    });

    it("handles disabled items correctly", () => {
        const disabledItems: NavSecondaryItem[] = [
            createNavItem({ href: "/premium", label: "Premium", disabled: true })
        ];

        render(<NavSecondary items={disabledItems} />);

        const button = screen.getByTestId("sidebar-menu-button");
        expect(button).toHaveAttribute("disabled", "");
    });
});
