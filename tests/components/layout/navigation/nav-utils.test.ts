import { isNavItemActive, isNavItemActiveWithQuery, navigateToSection } from "@/components/layout/navigation/nav-utils";

describe("nav-utils", () => {
    describe("isNavItemActive", () => {
        it("returns false for href '#'", () => {
            expect(isNavItemActive("/test", "#")).toBe(false);
            expect(isNavItemActive("/test", "#", false)).toBe(false);
        });

        it("handles exact matches with exact=true (default)", () => {
            expect(isNavItemActive("/test", "/test")).toBe(true);
            expect(isNavItemActive("/test", "/test", true)).toBe(true);
            expect(isNavItemActive("/test", "/different")).toBe(false);
        });

        it("handles prefix matches with exact=false", () => {
            expect(isNavItemActive("/test/sub", "/test", false)).toBe(true);
            expect(isNavItemActive("/test/sub/deep", "/test", false)).toBe(true);
            expect(isNavItemActive("/different", "/test", false)).toBe(false);
        });

        it("normalizes trailing slashes from pathname", () => {
            expect(isNavItemActive("/test/", "/test")).toBe(true);
            expect(isNavItemActive("/test", "/test/")).toBe(true);
            expect(isNavItemActive("/test/", "/test/")).toBe(true);
        });

        it("handles root path correctly", () => {
            expect(isNavItemActive("/", "/")).toBe(true);
            expect(isNavItemActive("/", "", true)).toBe(true); // Both normalize to empty string
            expect(isNavItemActive("", "/", true)).toBe(true); // Both normalize to empty string
        });

        it("handles empty strings", () => {
            expect(isNavItemActive("", "")).toBe(true);
            expect(isNavItemActive("", "", false)).toBe(true);
        });

        it("handles complex paths", () => {
            expect(isNavItemActive("/admin/users", "/admin/users")).toBe(true);
            expect(isNavItemActive("/admin/users/123", "/admin/users", false)).toBe(true);
            expect(isNavItemActive("/admin/users/123", "/admin/users", true)).toBe(false);
        });
    });

    describe("isNavItemActiveWithQuery", () => {
        it("returns false for itemHref '#'", () => {
            const searchParams = new URLSearchParams("?tab=test");
            expect(isNavItemActiveWithQuery("/test", searchParams, "#")).toBe(false);
            expect(isNavItemActiveWithQuery("/test", searchParams, "#", false)).toBe(false);
        });

        it("returns false when path doesn't match", () => {
            const searchParams = new URLSearchParams("?tab=test");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/different")).toBe(false);
            expect(isNavItemActiveWithQuery("/test", searchParams, "/different?tab=test")).toBe(false);
        });

        it("returns true when path matches and no query params in itemHref", () => {
            const searchParams = new URLSearchParams("?tab=test");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test")).toBe(true);
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test", true)).toBe(true);
        });

        it("returns true when path and query params match", () => {
            const searchParams = new URLSearchParams("?tab=test&view=grid");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?tab=test")).toBe(true);
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?tab=test&view=grid")).toBe(true);
        });

        it("returns false when query params don't match", () => {
            const searchParams = new URLSearchParams("?tab=different");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?tab=test")).toBe(false);
        });

        it("returns false when some query params are missing", () => {
            const searchParams = new URLSearchParams("?tab=test");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?tab=test&view=grid")).toBe(false);
        });

        it("returns true when current params include all required params", () => {
            const searchParams = new URLSearchParams("?tab=test&view=grid&extra=value");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?tab=test&view=grid")).toBe(true);
        });

        it("handles prefix path matching with exactPathMatch=false", () => {
            const searchParams = new URLSearchParams("?tab=test");
            expect(isNavItemActiveWithQuery("/test/sub", searchParams, "/test", false)).toBe(true);
            expect(isNavItemActiveWithQuery("/test/sub", searchParams, "/test?tab=test", false)).toBe(true);
        });

        it("handles empty query params", () => {
            const searchParams = new URLSearchParams("");
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test")).toBe(true);
            expect(isNavItemActiveWithQuery("/test", searchParams, "/test?")).toBe(true);
        });

        it("handles complex query parameter scenarios", () => {
            const searchParams = new URLSearchParams("?filter=active&sort=date&page=1");

            expect(isNavItemActiveWithQuery("/admin", searchParams, "/admin?filter=active")).toBe(true);
            expect(isNavItemActiveWithQuery("/admin", searchParams, "/admin?filter=active&sort=date")).toBe(true);
            expect(isNavItemActiveWithQuery("/admin", searchParams, "/admin?filter=inactive")).toBe(false);
            expect(isNavItemActiveWithQuery("/admin", searchParams, "/admin?filter=active&sort=name")).toBe(false);
        });
    });

    describe("navigateToSection", () => {
        const originalLocation = window.location;
        const originalScrollTo = window.scrollTo;
        let mockGetElementById: jest.Mock;

        beforeEach(() => {
            delete (window as any).location;
            window.location = { href: "" } as any;
            window.scrollTo = jest.fn();

            mockGetElementById = jest.fn();
            document.getElementById = mockGetElementById;
        });

        afterEach(() => {
            (window as any).location = originalLocation;
            window.scrollTo = originalScrollTo;
            jest.clearAllMocks();
        });

        it("prevents default when event is provided", () => {
            const mockEvent = {
                preventDefault: jest.fn()
            } as any;

            navigateToSection("test", "/", mockEvent);

            expect(mockEvent.preventDefault).toHaveBeenCalled();
        });

        it("redirects to home page with hash when not on root path", () => {
            navigateToSection("test-section", "/different-page");

            expect(window.location.pathname).toBe("/");
            expect(window.location.hash).toBe("#test-section");
        });

        it("redirects to home page with hash when event is provided and not on root", () => {
            const mockEvent = {
                preventDefault: jest.fn()
            } as any;

            navigateToSection("test-section", "/admin", mockEvent);

            expect(mockEvent.preventDefault).toHaveBeenCalled();
            expect(window.location.pathname).toBe("/");
            expect(window.location.hash).toBe("#test-section");
        });

        it("scrolls to element when on root path and element exists", () => {
            const mockElement = {
                getBoundingClientRect: jest.fn().mockReturnValue({ top: 100 })
            };

            mockGetElementById.mockReturnValue(mockElement);
            window.scrollY = 50;

            navigateToSection("test-section", "/");

            expect(mockGetElementById).toHaveBeenCalledWith("test-section");
            expect(window.scrollTo).toHaveBeenCalledWith({
                top: 70, // 100 (element position) + 50 (scrollY) - 80 (navbar offset)
                behavior: "smooth"
            });
        });

        it("handles missing element gracefully when on root path", () => {
            mockGetElementById.mockReturnValue(null);

            navigateToSection("non-existent-section", "/");

            expect(mockGetElementById).toHaveBeenCalledWith("non-existent-section");
            expect(window.scrollTo).not.toHaveBeenCalled();
        });

        it("calculates scroll position correctly with different element positions", () => {
            const mockElement = {
                getBoundingClientRect: jest.fn().mockReturnValue({ top: 200 })
            };

            mockGetElementById.mockReturnValue(mockElement);
            window.scrollY = 100;

            navigateToSection("test-section", "/");

            expect(window.scrollTo).toHaveBeenCalledWith({
                top: 220, // 200 (element position) + 100 (scrollY) - 80 (navbar offset)
                behavior: "smooth"
            });
        });

        it("handles zero element position", () => {
            const mockElement = {
                getBoundingClientRect: jest.fn().mockReturnValue({ top: 0 })
            };

            mockGetElementById.mockReturnValue(mockElement);
            window.scrollY = 0;

            navigateToSection("test-section", "/");

            expect(window.scrollTo).toHaveBeenCalledWith({
                top: -80, // 0 (element position) + 0 (scrollY) - 80 (navbar offset)
                behavior: "smooth"
            });
        });

        it("handles negative calculated scroll position", () => {
            const mockElement = {
                getBoundingClientRect: jest.fn().mockReturnValue({ top: -50 })
            };

            mockGetElementById.mockReturnValue(mockElement);
            window.scrollY = 20;

            navigateToSection("test-section", "/");

            expect(window.scrollTo).toHaveBeenCalledWith({
                top: -110, // -50 (element position) + 20 (scrollY) - 80 (navbar offset)
                behavior: "smooth"
            });
        });
    });
});
