/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { NavItem } from "@/components/layout/navigation/nav-item";
import { BaseNavItem } from "@/components/layout/navigation/types";

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

interface TestNavItem extends Omit<BaseNavItem, "icon"> {
    icon?: MockLucideIcon;
    title?: string;
    url?: string;
}

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

jest.mock("@/components/ui/tooltip", () => ({
    Tooltip: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip">{children}</div>,
    TooltipContent: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="tooltip-content">{children}</div>
    ),
    TooltipTrigger: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-trigger">{children}</div>
}));

jest.mock("@/lib/plan-utils", () => ({
    getPlanDisplayName: jest.fn((plan: string) => `Plan ${plan}`)
}));

const createMockIcon = (): MockLucideIcon => {
    const MockIcon = ({ className }: { className?: string }) => (
        <span data-testid="mock-icon" className={className}>
            📊
        </span>
    );
    MockIcon.$$typeof = Symbol.for("react.forward_ref");
    return MockIcon as MockLucideIcon;
};

describe("NavItem", () => {
    const defaultItem: TestNavItem = {
        href: "/dashboard",
        label: "Dashboard",
        icon: createMockIcon()
    };

    const mockOnClick = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Basic rendering", () => {
        it("renders nav item with default props", () => {
            render(<NavItem item={defaultItem as any} isActive={false} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
            expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
        });

        it("renders as link when not disabled", () => {
            render(<NavItem item={defaultItem as any} isActive={false} />);

            const link = screen.getByRole("link");
            expect(link).toHaveAttribute("href", "/dashboard");
        });

        it("renders as link with disabled semantics when disabled", () => {
            const disabledItem: TestNavItem = { ...defaultItem, disabled: true };
            render(<NavItem item={disabledItem as any} isActive={false} />);

            const element = screen.getByRole("link");
            expect(element).toHaveAttribute("aria-disabled", "true");
        });

        it("calls onClick when clicked", () => {
            render(<NavItem item={defaultItem as any} isActive={false} onClick={mockOnClick} />);

            fireEvent.click(screen.getByRole("link"));
            expect(mockOnClick).toHaveBeenCalledTimes(1);
        });
    });

    describe("Active state", () => {
        it("applies active styles when isActive is true", () => {
            render(<NavItem item={defaultItem as any} isActive={true} />);

            const text = screen.getByText("Dashboard");
            expect(text).toHaveClass("font-semibold");
        });

        it("does not apply active styles when isActive is false", () => {
            render(<NavItem item={defaultItem as any} isActive={false} />);

            const text = screen.getByText("Dashboard");
            expect(text).not.toHaveClass("font-semibold");
        });
    });

    describe("Variants", () => {
        it("renders navbar variant correctly", () => {
            render(<NavItem item={defaultItem as any} isActive={false} variant="navbar" />);

            const container = screen.getByRole("link").closest("div");
            expect(container).toBeInTheDocument();
        });

        it("renders mobile variant correctly", () => {
            render(<NavItem item={defaultItem as any} isActive={false} variant="mobile" />);

            const container = screen.getByRole("link").closest("div");
            expect(container).toBeInTheDocument();
        });

        it("renders sidebar variant correctly", () => {
            render(<NavItem item={defaultItem as any} isActive={false} variant="sidebar" />);

            const container = screen.getByRole("link").closest("div");
            expect(container).toBeInTheDocument();
        });
    });

    describe("Collapsed state", () => {
        it("hides label when collapsed", () => {
            render(<NavItem item={defaultItem as any} isActive={false} collapsed={true} />);

            expect(screen.queryByText("Dashboard")).not.toBeInTheDocument();
            expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
        });

        it("shows label when not collapsed", () => {
            render(<NavItem item={defaultItem as any} isActive={false} collapsed={false} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
            expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
        });
    });

    describe("Locked items", () => {
        const lockedItem: TestNavItem = {
            ...defaultItem,
            locked: true,
            requiredPlan: "milgapro" as any
        };

        it("renders lock icon for locked items", () => {
            render(<NavItem item={lockedItem as any} isActive={false} />);

            expect(screen.getByTestId("tooltip")).toBeInTheDocument();
        });

        it("does not render lock icon when collapsed", () => {
            render(<NavItem item={lockedItem as any} isActive={false} collapsed={true} />);

            expect(screen.queryByTestId("tooltip")).not.toBeInTheDocument();
        });
    });

    describe("Active indicators", () => {
        it("renders active indicator for navbar variant", () => {
            render(<NavItem item={defaultItem as any} isActive={true} variant="navbar" />);

            const activeIndicator = screen.getByRole("link").querySelector("div:last-child");
            expect(activeIndicator).toBeInTheDocument();
        });

        it("renders active indicator for mobile variant", () => {
            render(<NavItem item={defaultItem as any} isActive={true} variant="mobile" />);

            const activeIndicator = screen.getByRole("link").querySelector("div:last-child");
            expect(activeIndicator).toBeInTheDocument();
        });

        it("does not render active indicator for sidebar variant", () => {
            render(<NavItem item={defaultItem as any} isActive={true} variant="sidebar" />);

            const link = screen.getByRole("link");
            expect(link).toBeInTheDocument();
        });
    });

    describe("Icon rendering", () => {
        it("renders icon with correct className", () => {
            render(<NavItem item={defaultItem as any} isActive={false} />);

            const icon = screen.getByTestId("mock-icon");
            expect(icon).toHaveClass("h-5", "w-5", "shrink-0", "transition-colors", "duration-200");
        });

        it("applies active icon styles when active", () => {
            render(<NavItem item={defaultItem as any} isActive={true} />);

            const icon = screen.getByTestId("mock-icon");
            expect(icon).toHaveClass("text-secondary-foreground");
        });

        it("centers icon when collapsed", () => {
            render(<NavItem item={defaultItem as any} isActive={false} collapsed={true} />);

            const icon = screen.getByTestId("mock-icon");
            expect(icon).toHaveClass("mx-auto");
        });
    });

    describe("Without icon", () => {
        const itemWithoutIcon: TestNavItem = {
            href: "/dashboard",
            label: "Dashboard"
        };

        it("renders correctly without icon", () => {
            render(<NavItem item={itemWithoutIcon as any} isActive={false} />);

            expect(screen.getByText("Dashboard")).toBeInTheDocument();
            expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has correct aria attributes for disabled items", () => {
            const disabledItem: TestNavItem = { ...defaultItem, disabled: true };
            render(<NavItem item={disabledItem as any} isActive={false} />);

            const element = screen.getByRole("link");
            expect(element).toHaveAttribute("aria-disabled", "true");
        });

        it("maintains link semantics for enabled items", () => {
            render(<NavItem item={defaultItem as any} isActive={false} />);

            const link = screen.getByRole("link");
            expect(link).toHaveAttribute("href", "/dashboard");
        });
    });
});
