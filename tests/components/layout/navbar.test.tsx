/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";

import { Navbar } from "@/components/layout/navbar";
import { useUserOrgRole } from "@/hooks/use-user-org-role";
import { TEXTS } from "@/lib/auth-constants";

// Mock dependencies
jest.mock("@clerk/nextjs", () => ({
    useAuth: jest.fn()
}));

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("@/hooks/use-user-org-role", () => ({
    useUserOrgRole: jest.fn()
}));

// Mock types to satisfy TypeScript
const createMockAuthReturn = (overrides: Partial<any> = {}) => ({
    userId: null,
    isLoaded: true,
    isSignedIn: false,
    sessionId: null,
    sessionClaims: null,
    actor: null,
    orgId: null,
    orgRole: null,
    orgSlug: null,
    has: jest.fn(),
    signOut: jest.fn(),
    getToken: jest.fn(),
    ...overrides
});

const createMockUserOrgRole = (overrides: Partial<any> = {}) => ({
    role: "user" as const,
    orgId: undefined,
    isLoaded: true,
    ...overrides
});

jest.mock("@/components/layout/navbar/desktop-nav", () => ({
    DesktopNav: () => <div data-testid="desktop-nav">Desktop Nav</div>
}));

jest.mock("@/components/layout/navbar/mobile-menu", () => ({
    MobileMenu: () => <div data-testid="mobile-menu">Mobile Menu</div>
}));

jest.mock("@/components/layout/site-logo", () => ({
    SiteLogo: ({ href, size }: { href: string; size?: string }) => (
        <div data-testid="site-logo" data-href={href} data-size={size}>
            Milgapo Logo
        </div>
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({
        children,
        onClick,
        disabled,
        variant,
        size,
        className,
        "aria-label": ariaLabel
    }: {
        children: React.ReactNode;
        onClick?: (e: React.MouseEvent) => void;
        disabled?: boolean;
        variant?: string;
        size?: string;
        className?: string;
        "aria-label"?: string;
    }) => (
        <button
            onClick={onClick}
            disabled={disabled}
            data-variant={variant}
            data-size={size}
            className={className}
            aria-label={ariaLabel}
            data-testid="account-button"
        >
            {children}
        </button>
    )
}));

jest.mock("lucide-react", () => ({
    User: ({ className }: { className?: string }) => (
        <span data-testid="user-icon" className={className}>
            👤
        </span>
    )
}));

const mockUseAuth = useAuth as jest.MockedFunction<any>;
const mockUseRouter = useRouter as jest.MockedFunction<any>;
const mockUseUserOrgRole = useUserOrgRole as jest.MockedFunction<any>;

describe("Navbar", () => {
    const mockPush = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseRouter.mockReturnValue({
            push: mockPush,
            replace: jest.fn(),
            back: jest.fn(),
            forward: jest.fn(),
            refresh: jest.fn(),
            prefetch: jest.fn()
        });

        // Mock scroll behavior
        Object.defineProperty(window, "scrollY", {
            value: 0,
            writable: true
        });

        // Mock addEventListener/removeEventListener
        const mockAddEventListener = jest.fn();
        const mockRemoveEventListener = jest.fn();
        Object.defineProperty(window, "addEventListener", {
            value: mockAddEventListener,
            writable: true
        });
        Object.defineProperty(window, "removeEventListener", {
            value: mockRemoveEventListener,
            writable: true
        });
    });

    describe("Basic rendering", () => {
        it("renders navbar with all components", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            expect(screen.getByTestId("mobile-menu")).toBeInTheDocument();
            expect(screen.getAllByTestId("site-logo")).toHaveLength(2); // Desktop and mobile
            expect(screen.getByTestId("desktop-nav")).toBeInTheDocument();
            expect(screen.getByTestId("account-button")).toBeInTheDocument();
        });

        it("renders correct logo sizes for desktop and mobile", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const logos = screen.getAllByTestId("site-logo");
            expect(logos[0]).toHaveAttribute("data-size", "lg"); // Desktop
            expect(logos[1]).not.toHaveAttribute("data-size"); // Mobile (default)
        });
    });

    describe("Authentication states", () => {
        it("shows signup button when not authenticated", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).toHaveTextContent(TEXTS.signup);
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleSignup);
        });

        it("shows account button when authenticated", () => {
            mockUseAuth.mockReturnValue(
                createMockAuthReturn({
                    userId: "user123",
                    isSignedIn: true
                })
            );
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).toHaveTextContent(TEXTS.account);
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleAccount);
        });

        it("does not render account button text when loading", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn({ isLoaded: false }));
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole({ isLoaded: false }));

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).toBeInTheDocument();
        });
    });

    describe("Navigation behavior", () => {
        it("navigates to signup when not authenticated", async () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            fireEvent.click(button);

            await waitFor(() => {
                expect(mockPush).toHaveBeenCalledWith("/signup");
            });
        });

        it("navigates to admin when user is admin", async () => {
            mockUseAuth.mockReturnValue(
                createMockAuthReturn({
                    userId: "admin123",
                    isSignedIn: true
                })
            );
            mockUseUserOrgRole.mockReturnValue(
                createMockUserOrgRole({
                    role: "admin",
                    orgId: "org123"
                })
            );

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            fireEvent.click(button);

            await waitFor(() => {
                expect(mockPush).toHaveBeenCalledWith("/admin");
            });
        });

        it("navigates to admin when user is employee", async () => {
            mockUseAuth.mockReturnValue(
                createMockAuthReturn({
                    userId: "employee123",
                    isSignedIn: true
                })
            );
            mockUseUserOrgRole.mockReturnValue(
                createMockUserOrgRole({
                    role: "employee",
                    orgId: "org123"
                })
            );

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            fireEvent.click(button);

            await waitFor(() => {
                expect(mockPush).toHaveBeenCalledWith("/admin");
            });
        });

        it("navigates to dashboard when user is regular user", async () => {
            mockUseAuth.mockReturnValue(
                createMockAuthReturn({
                    userId: "user123",
                    isSignedIn: true
                })
            );
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            fireEvent.click(button);

            await waitFor(() => {
                expect(mockPush).toHaveBeenCalledWith("/dashboard");
            });
        });

        it("does not navigate when not loaded", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn({ isLoaded: false }));
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole({ isLoaded: false }));

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            fireEvent.click(button);

            expect(mockPush).not.toHaveBeenCalled();
        });
    });

    describe("Button state management", () => {
        it("disables button after click", async () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).not.toBeDisabled();

            fireEvent.click(button);

            expect(button).toBeDisabled();
        });

        it("prevents default on click", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            const clickEvent = new MouseEvent("click", { bubbles: true });
            const preventDefaultSpy = jest.spyOn(clickEvent, "preventDefault");

            fireEvent(button, clickEvent);

            expect(preventDefaultSpy).toHaveBeenCalled();
        });
    });

    describe("Scroll behavior", () => {
        it("adds scroll event listener on mount", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            expect(window.addEventListener).toHaveBeenCalledWith("scroll", expect.any(Function));
        });

        it("removes scroll event listener on unmount", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            const { unmount } = render(<Navbar />);
            unmount();

            expect(window.removeEventListener).toHaveBeenCalledWith("scroll", expect.any(Function));
        });

        it("applies scrolled styles when scrolled", async () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const nav = screen.getByRole("navigation");
            // Initially should not have scrolled styles
            expect(nav).toHaveClass("bg-white");
            expect(nav).not.toHaveClass("shadow-sm");

            // Mock scrollY to be greater than 20
            Object.defineProperty(window, "scrollY", {
                value: 50,
                writable: true
            });

            // Simulate scroll event with act to handle state updates
            const scrollCall = (window.addEventListener as jest.Mock).mock.calls.find(
                (call: any[]) => call[0] === "scroll"
            );
            if (scrollCall && typeof scrollCall[1] === "function") {
                await act(async () => {
                    (scrollCall[1] as Function)();
                });
            }

            // Should now have scrolled styles
            await waitFor(() => {
                expect(nav).toHaveClass("bg-white", "shadow-sm", "border-b", "border-border/40");
            });
        });
    });

    describe("Responsive design", () => {
        it("has correct responsive classes", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            // Check that it has the responsive classes from the component
            expect(button).toHaveClass("md:flex"); // Desktop override
            expect(button).toHaveClass("md:h-10"); // Desktop height
            expect(button).toHaveClass("md:w-auto"); // Desktop width
            expect(button).toHaveClass("text-white"); // Text color
        });

        it("renders mobile menu for small screens", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            expect(screen.getByTestId("mobile-menu")).toBeInTheDocument();
        });

        it("renders desktop nav for large screens", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            expect(screen.getByTestId("desktop-nav")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper ARIA labels", () => {
            mockUseAuth.mockReturnValue(createMockAuthReturn());
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleSignup);
        });

        it("updates ARIA label based on auth state", () => {
            mockUseAuth.mockReturnValue(
                createMockAuthReturn({
                    userId: "user123",
                    isSignedIn: true
                })
            );
            mockUseUserOrgRole.mockReturnValue(createMockUserOrgRole());

            render(<Navbar />);

            const button = screen.getByTestId("account-button");
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleAccount);
        });
    });
});
