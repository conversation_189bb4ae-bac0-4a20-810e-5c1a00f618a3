/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";

import { SiteLogo } from "@/components/layout/site-logo";

// Mock Next.js components
jest.mock("next/image", () => ({
    __esModule: true,
    default: ({
        src,
        alt,
        width,
        height,
        priority,
        className
    }: {
        src: string;
        alt: string;
        width: number;
        height: number;
        priority?: boolean;
        className?: string;
    }) => (
        <img
            src={src}
            alt={alt}
            width={width}
            height={height}
            data-priority={priority}
            className={className}
            data-testid="logo-image"
        />
    )
}));

jest.mock("next/link", () => ({
    __esModule: true,
    default: ({ href, children, className }: { href: string; children: React.ReactNode; className?: string }) => (
        <a href={href} className={className} data-testid="logo-link">
            {children}
        </a>
    )
}));

describe("SiteLogo", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Basic rendering", () => {
        it("renders logo with required props", () => {
            render(<SiteLogo href="/" />);

            expect(screen.getByTestId("logo-link")).toBeInTheDocument();
            expect(screen.getByTestId("logo-image")).toBeInTheDocument();
        });

        it("has correct href attribute", () => {
            render(<SiteLogo href="/test-page" />);

            const link = screen.getByTestId("logo-link");
            expect(link).toHaveAttribute("href", "/test-page");
        });

        it("has correct alt text", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("alt", "Milgapo Logo");
        });

        it("has correct src attribute", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("src", "/logo.svg");
        });

        it("has priority attribute set to true", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("data-priority", "true");
        });
    });

    describe("Size calculations", () => {
        it("uses default size (md) when no size prop provided", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("width", "85"); // Math.round(32 * (122.03 / 45.69))
            expect(image).toHaveAttribute("height", "32");
        });

        it("calculates correct dimensions for sm size", () => {
            render(<SiteLogo href="/" size="sm" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("width", "64"); // Math.round(24 * (122.03 / 45.69))
            expect(image).toHaveAttribute("height", "24");
        });

        it("calculates correct dimensions for md size", () => {
            render(<SiteLogo href="/" size="md" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("width", "85"); // Math.round(32 * (122.03 / 45.69))
            expect(image).toHaveAttribute("height", "32");
        });

        it("calculates correct dimensions for lg size", () => {
            render(<SiteLogo href="/" size="lg" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("width", "107"); // Math.round(40 * (122.03 / 45.69))
            expect(image).toHaveAttribute("height", "40");
        });

        it("calculates correct dimensions for xl size", () => {
            render(<SiteLogo href="/" size="xl" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("width", "128"); // Math.round(48 * (122.03 / 45.69))
            expect(image).toHaveAttribute("height", "48");
        });

        it("maintains aspect ratio for all sizes", () => {
            const sizes = ["sm", "md", "lg", "xl"] as const;
            const expectedRatio = 122.03 / 45.69;

            sizes.forEach((size) => {
                const { unmount } = render(<SiteLogo href="/" size={size} />);

                const image = screen.getByTestId("logo-image");
                const width = parseInt(image.getAttribute("width") || "0");
                const height = parseInt(image.getAttribute("height") || "0");

                const actualRatio = width / height;
                expect(actualRatio).toBeCloseTo(expectedRatio, 1);

                unmount();
            });
        });
    });

    describe("Styling and classes", () => {
        it("has default link classes", () => {
            render(<SiteLogo href="/" />);

            const link = screen.getByTestId("logo-link");
            expect(link).toHaveClass("flex", "items-center", "transition-transform", "duration-200", "ease-in-out");
        });

        it("applies custom className to link", () => {
            render(<SiteLogo href="/" className="custom-class" />);

            const link = screen.getByTestId("logo-link");
            expect(link).toHaveClass("custom-class");
        });

        it("has correct container div styling", () => {
            render(<SiteLogo href="/" />);

            const container = screen.getByTestId("logo-link").querySelector("div");
            expect(container).toHaveClass("relative", "transition-colors", "duration-200");
        });

        it("has correct image styling", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveClass("transition-colors", "duration-200");
        });

        it("sets correct inline styles for container", () => {
            render(<SiteLogo href="/" size="lg" />);

            const container = screen.getByTestId("logo-link").querySelector("div");
            expect(container).toHaveStyle({
                height: "40px",
                width: "107px"
            });
        });
    });

    describe("Props handling", () => {
        it("handles all size options", () => {
            const sizes = ["sm", "md", "lg", "xl"] as const;

            sizes.forEach((size) => {
                const { unmount } = render(<SiteLogo href="/" size={size} />);
                expect(screen.getByTestId("logo-image")).toBeInTheDocument();
                unmount();
            });
        });

        it("handles different href values", () => {
            const hrefs = ["/", "/home", "/about", "https://example.com"];

            hrefs.forEach((href) => {
                const { unmount } = render(<SiteLogo href={href} />);
                const link = screen.getByTestId("logo-link");
                expect(link).toHaveAttribute("href", href);
                unmount();
            });
        });

        it("handles color prop (even though not used in current implementation)", () => {
            // This test documents that color prop exists but isn't currently used
            render(<SiteLogo href="/" color="red" />);
            expect(screen.getByTestId("logo-image")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper semantic structure", () => {
            render(<SiteLogo href="/" />);

            const link = screen.getByRole("link");
            expect(link).toBeInTheDocument();
        });

        it("has descriptive alt text for screen readers", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByAltText("Milgapo Logo");
            expect(image).toBeInTheDocument();
        });

        it("is keyboard accessible", () => {
            render(<SiteLogo href="/" />);

            const link = screen.getByRole("link");
            expect(link).toHaveAttribute("href");
        });
    });

    describe("Performance", () => {
        it("sets priority loading for performance", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image).toHaveAttribute("data-priority", "true");
        });

        it("uses SVG format for scalability", () => {
            render(<SiteLogo href="/" />);

            const image = screen.getByTestId("logo-image");
            expect(image.getAttribute("src")).toMatch(/\.svg$/);
        });
    });

    describe("Responsive behavior", () => {
        it("maintains consistent aspect ratio across all sizes", () => {
            const sizes = ["sm", "md", "lg", "xl"] as const;

            sizes.forEach((size) => {
                const { unmount } = render(<SiteLogo href="/" size={size} />);

                const image = screen.getByTestId("logo-image");
                const width = parseInt(image.getAttribute("width") || "0");
                const height = parseInt(image.getAttribute("height") || "0");

                // Should maintain the logo's aspect ratio
                expect(width).toBeGreaterThan(height);
                expect(width / height).toBeGreaterThan(2); // Logo is wider than it is tall

                unmount();
            });
        });

        it("provides appropriate sizes for different use cases", () => {
            // Small size for compact areas
            const { unmount: unmountSm } = render(<SiteLogo href="/" size="sm" />);
            let image = screen.getByTestId("logo-image");
            expect(parseInt(image.getAttribute("height") || "0")).toBe(24);
            unmountSm();

            // Large size for headers
            const { unmount: unmountLg } = render(<SiteLogo href="/" size="lg" />);
            image = screen.getByTestId("logo-image");
            expect(parseInt(image.getAttribute("height") || "0")).toBe(40);
            unmountLg();

            // Extra large for hero sections
            const { unmount: unmountXl } = render(<SiteLogo href="/" size="xl" />);
            image = screen.getByTestId("logo-image");
            expect(parseInt(image.getAttribute("height") || "0")).toBe(48);
            unmountXl();
        });
    });

    describe("Edge cases", () => {
        it("handles empty href", () => {
            render(<SiteLogo href="" />);

            const link = screen.getByTestId("logo-link");
            expect(link).toHaveAttribute("href", "");
        });

        it("handles undefined className", () => {
            render(<SiteLogo href="/" className={undefined} />);

            expect(screen.getByTestId("logo-link")).toBeInTheDocument();
        });

        it("handles className with multiple classes", () => {
            render(<SiteLogo href="/" className="class1 class2 class3" />);

            const link = screen.getByTestId("logo-link");
            expect(link).toHaveClass("class1", "class2", "class3");
        });
    });
});
