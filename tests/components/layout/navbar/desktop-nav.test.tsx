/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { usePathname } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { DesktopNav } from "@/components/layout/navbar/desktop-nav";
import { BaseNavItem } from "@/components/layout/navigation/types";

const TEXTS = {
    homePage: "דף הבית",
    scholarships: "מלגות",
    contact: "צור קשר",
    pricing: "מחירון"
} as const;

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

interface MockNavItemProps {
    item: BaseNavItem;
    isActive: boolean;
    variant: string;
}

interface MockUtilsFunction {
    (pathname: string, href: string): boolean;
}

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

jest.mock("next/navigation", () => ({
    usePathname: jest.fn()
}));

jest.mock("@/components/layout/navigation/nav-item", () => ({
    NavItem: ({ item, isActive, variant }: MockNavItemProps) => (
        <div data-testid="nav-item" data-href={item.href} data-active={isActive} data-variant={variant}>
            {item.label}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-utils", () => ({
    isNavItemActive: jest.fn((pathname: string, href: string) => pathname === href)
}));

const createMockIcon = (name: string): MockLucideIcon => {
    const MockIcon = ({ className }: { className?: string }) => (
        <span data-testid={`${name.toLowerCase()}-icon`} className={className}>
            {name}
        </span>
    );
    MockIcon.$$typeof = Symbol.for("react.forward_ref");
    MockIcon.displayName = name;
    return MockIcon as MockLucideIcon;
};

jest.mock("@/components/layout/navigation/nav-config", () => ({
    NAV_ITEMS: [
        { href: "/", label: "דף הבית", icon: (() => <span>MockHome</span>) as unknown as MockLucideIcon },
        { href: "/scholarships", label: "מלגות", icon: (() => <span>MockSchool2</span>) as unknown as MockLucideIcon },
        { href: "/contact", label: "צור קשר", icon: (() => <span>MockSend</span>) as unknown as MockLucideIcon },
        {
            href: "/subscriptions",
            label: "מחירון",
            icon: (() => <span>MockDollarSign</span>) as unknown as MockLucideIcon
        }
    ]
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe("DesktopNav", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/");
    });

    describe("Basic rendering", () => {
        it("renders desktop navigation container with correct styling", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toHaveClass("hidden", "md:flex", "items-center", "space-x-6", "space-x-reverse", "text-right");
        });

        it("renders all navigation items from NAV_ITEMS config", () => {
            render(<DesktopNav />);

            expect(screen.getByText(TEXTS.homePage)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.scholarships)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.contact)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.pricing)).toBeInTheDocument();
        });

        it("renders correct number of navigation items", () => {
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            expect(navItems).toHaveLength(4);
        });
    });

    describe("Navigation item props", () => {
        it("passes correct props to each NavItem component", () => {
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            const homeItem = navItems[0];
            expect(homeItem).toHaveAttribute("data-href", "/");
            expect(homeItem).toHaveAttribute("data-variant", "navbar");
        });

        it("passes variant='navbar' to all NavItem components", () => {
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            navItems.forEach((item) => {
                expect(item).toHaveAttribute("data-variant", "navbar");
            });
        });

        it("passes correct href for each navigation item", () => {
            render(<DesktopNav />);

            const expectedHrefs = ["/", "/scholarships", "/contact", "/subscriptions"];
            const navItems = screen.getAllByTestId("nav-item");

            navItems.forEach((item, index) => {
                expect(item).toHaveAttribute("data-href", expectedHrefs[index]);
            });
        });
    });

    describe("Active state handling", () => {
        it("marks home item as active when pathname is '/'", () => {
            mockUsePathname.mockReturnValue("/");
            render(<DesktopNav />);

            const homeItem = screen.getByText(TEXTS.homePage).closest('[data-testid="nav-item"]');
            expect(homeItem).toHaveAttribute("data-active", "true");
        });

        it("marks scholarships item as active when pathname is '/scholarships'", () => {
            mockUsePathname.mockReturnValue("/scholarships");
            render(<DesktopNav />);

            const scholarshipsItem = screen.getByText(TEXTS.scholarships).closest('[data-testid="nav-item"]');
            expect(scholarshipsItem).toHaveAttribute("data-active", "true");
        });

        it("marks contact item as active when pathname is '/contact'", () => {
            mockUsePathname.mockReturnValue("/contact");
            render(<DesktopNav />);

            const contactItem = screen.getByText(TEXTS.contact).closest('[data-testid="nav-item"]');
            expect(contactItem).toHaveAttribute("data-active", "true");
        });

        it("marks only one item as active at a time", () => {
            mockUsePathname.mockReturnValue("/scholarships");
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            const activeItems = navItems.filter((item) => item.getAttribute("data-active") === "true");

            expect(activeItems).toHaveLength(1);
            expect(activeItems[0]).toHaveAttribute("data-href", "/scholarships");
        });

        it("marks no items as active for unknown pathname", () => {
            mockUsePathname.mockReturnValue("/unknown-path");
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            const activeItems = navItems.filter((item) => item.getAttribute("data-active") === "true");

            expect(activeItems).toHaveLength(0);
        });
    });

    describe("Responsive behavior", () => {
        it("is hidden on mobile devices", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toHaveClass("hidden");
        });

        it("is visible on medium screens and above", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toHaveClass("md:flex");
        });
    });

    describe("Layout and styling", () => {
        it("applies correct flex layout classes", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toHaveClass("items-center", "space-x-6", "space-x-reverse");
        });

        it("applies RTL text alignment", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toHaveClass("text-right");
        });
    });

    describe("Integration with navigation utilities", () => {
        it("uses isNavItemActive to determine active state", () => {
            const isNavItemActiveMock = require("@/components/layout/navigation/nav-utils").isNavItemActive;
            mockUsePathname.mockReturnValue("/scholarships");

            render(<DesktopNav />);

            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/scholarships");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/contact");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/subscriptions");
        });
    });

    describe("Accessibility", () => {
        it("renders navigation items in correct order", () => {
            render(<DesktopNav />);

            const navItems = screen.getAllByTestId("nav-item");
            const expectedLabels = [TEXTS.homePage, TEXTS.scholarships, TEXTS.contact, TEXTS.pricing];

            navItems.forEach((item, index) => {
                expect(item).toHaveTextContent(expectedLabels[index]);
            });
        });

        it("maintains proper semantic structure", () => {
            const { container } = render(<DesktopNav />);

            const nav = container.firstChild as HTMLElement;
            expect(nav).toBeInTheDocument();
        });
    });
});
