/**
 * @jest-environment jsdom
 */
import { render, screen, fireEvent, act } from "@testing-library/react";
import { usePathname } from "next/navigation";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers";

import { MobileMenu } from "@/components/layout/navbar/mobile-menu";
import { BaseNavItem } from "@/components/layout/navigation/types";

interface MockLucideIcon {
    (props: { className?: string }): JSX.Element;
    $$typeof: symbol;
    displayName?: string;
}

interface MockButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    className?: string;
    size?: string;
    variant?: string;
    [key: string]: unknown;
}

interface MockSheetProps {
    children: React.ReactNode;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

interface MockSheetTriggerProps {
    children: React.ReactNode;
    asChild?: boolean;
}

interface MockSheetContentProps {
    children: React.ReactNode;
    side?: string;
    className?: string;
}

interface MockSheetHeaderProps {
    children: React.ReactNode;
    className?: string;
}

interface MockSheetTitleProps {
    children: React.ReactNode;
    className?: string;
}

interface MockNavItemProps {
    item: BaseNavItem;
    isActive: boolean;
    onClick?: () => void;
    variant: string;
}

interface MockSiteLogoProps {
    href: string;
    size: string;
}

interface MockMenuIconProps {
    className?: string;
}

interface MockUtilsFunction {
    (pathname: string, href: string): boolean;
}

declare module "@jest/expect" {
    interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}

declare global {
    namespace jest {
        interface Matchers<R> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare namespace JSX {
    interface Element {}
}

const TEXTS = {
    toggleMenu: "פתח/סגור תפריט"
};

jest.mock("next/navigation", () => ({
    usePathname: jest.fn()
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ children, onClick, className, size, variant, ...props }: MockButtonProps) => (
        <button onClick={onClick} className={className} data-size={size} data-variant={variant} {...props}>
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/sheet", () => ({
    Sheet: ({ children, open, onOpenChange }: MockSheetProps) => (
        <div data-testid="sheet" data-open={open} onClick={() => onOpenChange?.(!open)}>
            {children}
        </div>
    ),
    SheetTrigger: ({ children, asChild }: MockSheetTriggerProps) => (
        <div data-testid="sheet-trigger" onClick={(e: React.MouseEvent) => e.stopPropagation()}>
            {asChild ? children : <div>{children}</div>}
        </div>
    ),
    SheetContent: ({ children, side, className }: MockSheetContentProps) => (
        <div data-testid="sheet-content" data-side={side} className={className}>
            {children}
        </div>
    ),
    SheetHeader: ({ children, className }: MockSheetHeaderProps) => (
        <div data-testid="sheet-header" className={className}>
            {children}
        </div>
    ),
    SheetTitle: ({ children, className }: MockSheetTitleProps) => (
        <div data-testid="sheet-title" className={className}>
            {children}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-item", () => ({
    NavItem: ({ item, isActive, onClick, variant }: MockNavItemProps) => (
        <div
            data-testid="nav-item"
            data-href={item.href}
            data-active={isActive}
            data-variant={variant}
            onClick={onClick}
        >
            {item.label}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-utils", () => ({
    isNavItemActive: jest.fn((pathname: string, href: string) => pathname === href)
}));

jest.mock("@/components/layout/site-logo", () => ({
    SiteLogo: ({ href, size }: MockSiteLogoProps) => (
        <div data-testid="site-logo" data-href={href} data-size={size}>
            Milgapo Logo
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Menu: ({ className }: MockMenuIconProps) => (
        <span data-testid="menu-icon" className={className}>
            ☰
        </span>
    )
}));

const createMockIcon = (name: string): MockLucideIcon => {
    const MockIcon = ({ className }: { className?: string }) => (
        <span data-testid={`${name.toLowerCase()}-icon`} className={className}>
            {name}
        </span>
    );
    MockIcon.$$typeof = Symbol.for("react.forward_ref");
    MockIcon.displayName = name;
    return MockIcon as MockLucideIcon;
};

jest.mock("@/components/layout/navigation/nav-config", () => ({
    NAV_ITEMS: [
        { href: "/", label: "דף הבית", icon: (() => <span>MockHome</span>) as unknown as MockLucideIcon },
        { href: "/scholarships", label: "מלגות", icon: (() => <span>MockSchool2</span>) as unknown as MockLucideIcon },
        { href: "/contact", label: "צור קשר", icon: (() => <span>MockSend</span>) as unknown as MockLucideIcon },
        {
            href: "/subscriptions",
            label: "מחירון",
            icon: (() => <span>MockDollarSign</span>) as unknown as MockLucideIcon
        }
    ]
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe("MobileMenu", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUsePathname.mockReturnValue("/");
    });

    describe("Basic rendering", () => {
        it("renders mobile menu trigger button", () => {
            render(<MobileMenu />);

            const button = screen.getByRole("button");
            expect(button).toBeInTheDocument();
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleMenu);
        });

        it("renders menu icon inside trigger button", () => {
            render(<MobileMenu />);

            const menuIcon = screen.getByTestId("menu-icon");
            expect(menuIcon).toBeInTheDocument();
            expect(menuIcon).toHaveClass("h-5", "w-5");
        });

        it("applies correct styling to trigger button", () => {
            render(<MobileMenu />);

            const button = screen.getByRole("button");
            expect(button).toHaveAttribute("data-variant", "default");
            expect(button).toHaveAttribute("data-size", "icon");
            expect(button).toHaveClass("md:hidden", "text-white");
        });

        it("renders sheet component for mobile menu", () => {
            render(<MobileMenu />);

            const sheet = screen.getByTestId("sheet");
            expect(sheet).toBeInTheDocument();
            expect(sheet).toHaveAttribute("data-open", "false");
        });
    });

    describe("Sheet content", () => {
        it("renders sheet content with correct props", () => {
            render(<MobileMenu />);

            const sheetContent = screen.getByTestId("sheet-content");
            expect(sheetContent).toBeInTheDocument();
            expect(sheetContent).toHaveAttribute("data-side", "right");
            expect(sheetContent).toHaveClass("w-[85%]", "sm:w-[385px]", "p-0", "rtl");
        });

        it("renders sheet header with site logo", () => {
            render(<MobileMenu />);

            const sheetHeader = screen.getByTestId("sheet-header");
            expect(sheetHeader).toBeInTheDocument();
            expect(sheetHeader).toHaveClass("p-6", "text-right", "border-b");

            const siteLogo = screen.getByTestId("site-logo");
            expect(siteLogo).toBeInTheDocument();
            expect(siteLogo).toHaveAttribute("data-href", "/");
            expect(siteLogo).toHaveAttribute("data-size", "lg");
        });

        it("renders sheet title with correct styling", () => {
            render(<MobileMenu />);

            const sheetTitle = screen.getByTestId("sheet-title");
            expect(sheetTitle).toBeInTheDocument();
            expect(sheetTitle).toHaveClass("text-xl", "font-medium");
        });
    });

    describe("Navigation items", () => {
        it("renders all navigation items from NAV_ITEMS config", () => {
            render(<MobileMenu />);

            expect(screen.getByText("דף הבית")).toBeInTheDocument();
            expect(screen.getByText("מלגות")).toBeInTheDocument();
            expect(screen.getByText("צור קשר")).toBeInTheDocument();
            expect(screen.getByText("מחירון")).toBeInTheDocument();
        });

        it("renders correct number of navigation items", () => {
            render(<MobileMenu />);

            const navItems = screen.getAllByTestId("nav-item");
            expect(navItems).toHaveLength(4);
        });

        it("passes variant='mobile' to all NavItem components", () => {
            render(<MobileMenu />);

            const navItems = screen.getAllByTestId("nav-item");
            navItems.forEach((item) => {
                expect(item).toHaveAttribute("data-variant", "mobile");
            });
        });

        it("passes correct href for each navigation item", () => {
            render(<MobileMenu />);

            const expectedHrefs = ["/", "/scholarships", "/contact", "/subscriptions"];
            const navItems = screen.getAllByTestId("nav-item");

            navItems.forEach((item, index) => {
                expect(item).toHaveAttribute("data-href", expectedHrefs[index]);
            });
        });

        it("renders navigation items in correct container", () => {
            render(<MobileMenu />);

            const container = screen.getByTestId("sheet-content").querySelector(".flex.flex-col.py-2");
            expect(container).toBeInTheDocument();
        });
    });

    describe("Active state handling", () => {
        it("marks home item as active when pathname is '/'", () => {
            mockUsePathname.mockReturnValue("/");
            render(<MobileMenu />);

            const homeItem = screen.getByText("דף הבית").closest('[data-testid="nav-item"]');
            expect(homeItem).toHaveAttribute("data-active", "true");
        });

        it("marks scholarships item as active when pathname is '/scholarships'", () => {
            mockUsePathname.mockReturnValue("/scholarships");
            render(<MobileMenu />);

            const scholarshipsItem = screen.getByText("מלגות").closest('[data-testid="nav-item"]');
            expect(scholarshipsItem).toHaveAttribute("data-active", "true");
        });

        it("marks only one item as active at a time", () => {
            mockUsePathname.mockReturnValue("/contact");
            render(<MobileMenu />);

            const navItems = screen.getAllByTestId("nav-item");
            const activeItems = navItems.filter((item) => item.getAttribute("data-active") === "true");

            expect(activeItems).toHaveLength(1);
            expect(activeItems[0]).toHaveAttribute("data-href", "/contact");
        });
    });

    describe("Sheet state management", () => {
        it("sheet starts in closed state", () => {
            render(<MobileMenu />);

            const sheet = screen.getByTestId("sheet");
            expect(sheet).toHaveAttribute("data-open", "false");
        });

        it("closes mobile menu when navigation item is clicked", () => {
            render(<MobileMenu />);

            // Initially closed
            const sheet = screen.getByTestId("sheet");
            expect(sheet).toHaveAttribute("data-open", "false");

            // Click nav item (this would normally set open to false via onClick)
            const navItem = screen.getAllByTestId("nav-item")[0];
            fireEvent.click(navItem);

            // The onClick handler should have been called
            expect(navItem).toBeInTheDocument();
        });
    });

    describe("Pathname change effects", () => {
        it("closes menu when pathname changes", () => {
            const { rerender } = render(<MobileMenu />);

            // Change pathname
            mockUsePathname.mockReturnValue("/scholarships");

            // Re-render with new pathname
            rerender(<MobileMenu />);

            // Menu should be closed (initial state is closed anyway, but this tests the useEffect)
            const sheet = screen.getByTestId("sheet");
            expect(sheet).toHaveAttribute("data-open", "false");
        });

        it("updates active navigation item when pathname changes", () => {
            const { rerender } = render(<MobileMenu />);

            // Initially home is active
            let homeItem = screen.getByText("דף הבית").closest('[data-testid="nav-item"]');
            expect(homeItem).toHaveAttribute("data-active", "true");

            // Change pathname to scholarships
            mockUsePathname.mockReturnValue("/scholarships");
            rerender(<MobileMenu />);

            // Now scholarships should be active
            const scholarshipsItem = screen.getByText("מלגות").closest('[data-testid="nav-item"]');
            homeItem = screen.getByText("דף הבית").closest('[data-testid="nav-item"]');
            expect(scholarshipsItem).toHaveAttribute("data-active", "true");
            expect(homeItem).toHaveAttribute("data-active", "false");
        });
    });

    describe("Responsive behavior", () => {
        it("trigger button is hidden on desktop", () => {
            render(<MobileMenu />);

            const button = screen.getByRole("button");
            expect(button).toHaveClass("md:hidden");
        });

        it("sheet content has responsive width classes", () => {
            render(<MobileMenu />);

            const sheetContent = screen.getByTestId("sheet-content");
            expect(sheetContent).toHaveClass("w-[85%]", "sm:w-[385px]");
        });
    });

    describe("Accessibility", () => {
        it("trigger button has accessible label", () => {
            render(<MobileMenu />);

            const button = screen.getByRole("button");
            expect(button).toHaveAttribute("aria-label", TEXTS.toggleMenu);
        });

        it("maintains proper semantic structure with headings", () => {
            render(<MobileMenu />);

            const sheetTitle = screen.getByTestId("sheet-title");
            expect(sheetTitle).toBeInTheDocument();
        });

        it("navigation items are properly accessible", () => {
            render(<MobileMenu />);

            const navItems = screen.getAllByTestId("nav-item");
            navItems.forEach((item) => {
                expect(item).toBeInTheDocument();
            });
        });
    });

    describe("RTL support", () => {
        it("sheet opens from right side for RTL layout", () => {
            render(<MobileMenu />);

            const sheetContent = screen.getByTestId("sheet-content");
            expect(sheetContent).toHaveAttribute("data-side", "right");
            expect(sheetContent).toHaveClass("rtl");
        });

        it("header has right text alignment", () => {
            render(<MobileMenu />);

            const sheetHeader = screen.getByTestId("sheet-header");
            expect(sheetHeader).toHaveClass("text-right");
        });
    });

    describe("Integration with navigation utilities", () => {
        it("uses isNavItemActive to determine active state", () => {
            const isNavItemActiveMock = require("@/components/layout/navigation/nav-utils").isNavItemActive;
            mockUsePathname.mockReturnValue("/scholarships");

            render(<MobileMenu />);

            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/scholarships");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/contact");
            expect(isNavItemActiveMock).toHaveBeenCalledWith("/scholarships", "/subscriptions");
        });

        it("passes onClick handler to close menu to all nav items", () => {
            render(<MobileMenu />);

            const navItems = screen.getAllByTestId("nav-item");
            navItems.forEach((item) => {
                // Each nav item should have an onClick handler
                expect(item).toBeInTheDocument();
            });
        });
    });

    describe("Component constants", () => {
        it("uses correct Hebrew text for accessibility label", () => {
            render(<MobileMenu />);

            const button = screen.getByRole("button");
            expect(button).toHaveAttribute("aria-label", "פתח/סגור תפריט");
        });
    });
});
