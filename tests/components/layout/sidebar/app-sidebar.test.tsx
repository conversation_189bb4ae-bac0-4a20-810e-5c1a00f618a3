/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";

import { AppSidebar } from "@/components/layout/sidebar/app-sidebar";

// Mock Lucide React icons
const createMockIcon = (name: string) => {
    const MockIcon = ({ className }: { className?: string }) => (
        <span data-testid={`${name.toLowerCase()}-icon`} className={className}>
            {name}
        </span>
    );
    MockIcon.displayName = name;
    return MockIcon;
};

jest.mock("lucide-react", () => ({
    Home: createMockIcon("Home"),
    User: createMockIcon("User"),
    Settings: createMockIcon("Settings"),
    FileText: createMockIcon("FileText"),
    HelpCircle: createMockIcon("HelpCircle")
}));

// Mock UI components
jest.mock("@/components/ui/sidebar", () => ({
    Sidebar: ({
        children,
        collapsible,
        side,
        dir,
        className,
        ...props
    }: {
        children: React.ReactNode;
        collapsible?: string;
        side?: string;
        dir?: string;
        className?: string;
        [key: string]: any;
    }) => (
        <div
            data-testid="sidebar"
            data-collapsible={collapsible}
            data-side={side}
            data-dir={dir}
            className={className}
            {...props}
        >
            {children}
        </div>
    ),
    SidebarContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="sidebar-content" className={className}>
            {children}
        </div>
    ),
    SidebarHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="sidebar-header">{children}</div>,
    SidebarFooter: ({ children }: { children: React.ReactNode }) => <div data-testid="sidebar-footer">{children}</div>,
    SidebarSeparator: ({ className }: { className?: string }) => (
        <div data-testid="sidebar-separator" className={className} />
    ),
    SidebarTrigger: ({ className }: { className?: string }) => (
        <button data-testid="sidebar-trigger" className={className}>
            Toggle
        </button>
    )
}));

// Mock navigation components
jest.mock("@/components/layout/navigation/nav-main", () => ({
    NavMain: ({ items }: { items: any[] }) => (
        <div data-testid="nav-main">
            {items.map((item, index) => (
                <div key={index} data-testid="nav-main-item" data-href={item.href}>
                    {item.label}
                </div>
            ))}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-group", () => ({
    NavGroup: ({ items, label }: { items: any[]; label?: string }) => (
        <div data-testid="nav-group" data-label={label}>
            {items.map((item, index) => (
                <div key={index} data-testid="nav-group-item" data-href={item.href}>
                    {item.label}
                </div>
            ))}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/nav-secondary", () => ({
    NavSecondary: ({ items, className }: { items: any[]; className?: string }) => (
        <div data-testid="nav-secondary" className={className}>
            {items.map((item, index) => (
                <div key={index} data-testid="nav-secondary-item" data-href={item.href}>
                    {item.label}
                </div>
            ))}
        </div>
    )
}));

jest.mock("@/components/layout/navigation/user-provider", () => ({
    UserProvider: () => <div data-testid="user-provider">User Provider</div>
}));

// Mock icons for testing
const HomeIcon = createMockIcon("Home");
const UserIcon = createMockIcon("User");
const SettingsIcon = createMockIcon("Settings");
const FileTextIcon = createMockIcon("FileText");
const HelpCircleIcon = createMockIcon("HelpCircle");

describe("AppSidebar", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Basic rendering", () => {
        it("renders sidebar with basic structure", () => {
            render(<AppSidebar />);

            expect(screen.getByTestId("sidebar")).toBeInTheDocument();
            expect(screen.getByTestId("sidebar-header")).toBeInTheDocument();
            expect(screen.getByTestId("sidebar-content")).toBeInTheDocument();
            expect(screen.getByTestId("sidebar-footer")).toBeInTheDocument();
            expect(screen.getByTestId("sidebar-trigger")).toBeInTheDocument();
            expect(screen.getByTestId("user-provider")).toBeInTheDocument();
        });

        it("has correct sidebar attributes", () => {
            render(<AppSidebar />);

            const sidebar = screen.getByTestId("sidebar");
            expect(sidebar).toHaveAttribute("data-collapsible", "icon");
            expect(sidebar).toHaveAttribute("data-side", "right");
            expect(sidebar).toHaveAttribute("data-dir", "rtl");
            expect(sidebar).toHaveClass("border-l");
        });

        it("has correct sidebar content classes", () => {
            render(<AppSidebar />);

            const content = screen.getByTestId("sidebar-content");
            expect(content).toHaveClass("text-right");
        });

        it("has correct trigger classes", () => {
            render(<AppSidebar />);

            const trigger = screen.getByTestId("sidebar-trigger");
            expect(trigger).toHaveClass("mb-2");
        });
    });

    describe("Navigation main", () => {
        const mockNavMain = [
            { title: "Home", url: "/", icon: HomeIcon as any },
            { title: "Profile", url: "/profile", icon: UserIcon as any }
        ];

        it("renders nav main when provided", () => {
            render(<AppSidebar navMain={mockNavMain} />);

            expect(screen.getByTestId("nav-main")).toBeInTheDocument();
            expect(screen.getAllByTestId("nav-main-item")).toHaveLength(2);
        });

        it("converts main items correctly", () => {
            render(<AppSidebar navMain={mockNavMain} />);

            const items = screen.getAllByTestId("nav-main-item");
            expect(items[0]).toHaveAttribute("data-href", "/");
            expect(items[0]).toHaveTextContent("Home");
            expect(items[1]).toHaveAttribute("data-href", "/profile");
            expect(items[1]).toHaveTextContent("Profile");
        });

        it("does not render nav main when not provided", () => {
            render(<AppSidebar />);

            expect(screen.queryByTestId("nav-main")).not.toBeInTheDocument();
        });

        it("does not render nav main when empty array", () => {
            render(<AppSidebar navMain={[]} />);

            expect(screen.queryByTestId("nav-main")).not.toBeInTheDocument();
        });
    });

    describe("Navigation groups", () => {
        const mockNavGroups = [
            {
                title: "Content",
                icon: FileTextIcon as any,
                items: [
                    { name: "Posts", url: "/posts", icon: FileTextIcon as any },
                    { name: "Pages", url: "/pages", icon: FileTextIcon as any }
                ]
            },
            {
                title: "Settings",
                icon: SettingsIcon as any,
                items: [
                    { name: "General", url: "/settings/general", icon: SettingsIcon as any },
                    { name: "Users", url: "/settings/users", icon: UserIcon as any }
                ]
            }
        ];

        it("renders nav groups when provided", () => {
            render(<AppSidebar navGroups={mockNavGroups} />);

            expect(screen.getAllByTestId("nav-group")).toHaveLength(2);
        });

        it("converts group items correctly", () => {
            render(<AppSidebar navGroups={mockNavGroups} />);

            const groups = screen.getAllByTestId("nav-group");
            expect(groups[0]).toHaveAttribute("data-label", "Content");
            expect(groups[1]).toHaveAttribute("data-label", "Settings");

            const firstGroupItems = groups[0].querySelectorAll('[data-testid="nav-group-item"]');
            expect(firstGroupItems[0]).toHaveAttribute("data-href", "/posts");
            expect(firstGroupItems[0]).toHaveTextContent("Posts");
            expect(firstGroupItems[1]).toHaveAttribute("data-href", "/pages");
            expect(firstGroupItems[1]).toHaveTextContent("Pages");
        });

        it("handles groups with special properties", () => {
            const mockGroupsWithProps = [
                {
                    title: "Admin",
                    items: [
                        {
                            name: "Dashboard",
                            url: "/admin/dashboard",
                            disabled: true,
                            locked: true,
                            requiredPlan: "premium" as any
                        }
                    ]
                }
            ] as any;

            render(<AppSidebar navGroups={mockGroupsWithProps} />);

            const group = screen.getByTestId("nav-group");
            expect(group).toHaveAttribute("data-label", "Admin");
        });

        it("does not render nav groups when not provided", () => {
            render(<AppSidebar />);

            expect(screen.queryByTestId("nav-group")).not.toBeInTheDocument();
        });
    });

    describe("Navigation secondary", () => {
        const mockNavSecondary = [
            { title: "Help", url: "/help", icon: HelpCircleIcon as any },
            { title: "Settings", url: "/settings", icon: SettingsIcon as any }
        ] as any;

        it("renders nav secondary when provided", () => {
            render(<AppSidebar navSecondary={mockNavSecondary} />);

            expect(screen.getByTestId("nav-secondary")).toBeInTheDocument();
            expect(screen.getAllByTestId("nav-secondary-item")).toHaveLength(2);
        });

        it("converts secondary items correctly", () => {
            render(<AppSidebar navSecondary={mockNavSecondary} />);

            const items = screen.getAllByTestId("nav-secondary-item");
            expect(items[0]).toHaveAttribute("data-href", "/help");
            expect(items[0]).toHaveTextContent("Help");
            expect(items[1]).toHaveAttribute("data-href", "/settings");
            expect(items[1]).toHaveTextContent("Settings");
        });

        it("has correct secondary nav classes", () => {
            render(<AppSidebar navSecondary={mockNavSecondary} />);

            const navSecondary = screen.getByTestId("nav-secondary");
            expect(navSecondary).toHaveClass("mt-auto");
        });

        it("does not render nav secondary when not provided", () => {
            render(<AppSidebar />);

            expect(screen.queryByTestId("nav-secondary")).not.toBeInTheDocument();
        });
    });

    describe("Separators", () => {
        it("renders separator between main and groups", () => {
            const mockNavMain = [{ title: "Home", url: "/" }];
            const mockNavGroups = [{ title: "Content", items: [{ name: "Posts", url: "/posts" }] }];

            render(<AppSidebar navMain={mockNavMain} navGroups={mockNavGroups} />);

            const separators = screen.getAllByTestId("sidebar-separator");
            expect(separators.length).toBeGreaterThan(0);
        });

        it("renders separator between groups", () => {
            const mockNavGroups = [
                { title: "Content", items: [{ name: "Posts", url: "/posts" }] },
                { title: "Settings", items: [{ name: "General", url: "/settings" }] }
            ];

            render(<AppSidebar navGroups={mockNavGroups} />);

            const separators = screen.getAllByTestId("sidebar-separator");
            expect(separators.length).toBeGreaterThan(0);
        });

        it("renders separator before secondary nav", () => {
            const mockNavGroups = [{ title: "Content", items: [{ name: "Posts", url: "/posts" }] }];
            const mockNavSecondary = [{ title: "Help", url: "/help" }];

            render(<AppSidebar navGroups={mockNavGroups} navSecondary={mockNavSecondary} />);

            const separators = screen.getAllByTestId("sidebar-separator");
            expect(separators.length).toBeGreaterThan(0);
        });

        it("has correct separator classes", () => {
            const mockNavMain = [{ title: "Home", url: "/" }];
            const mockNavGroups = [{ title: "Content", items: [{ name: "Posts", url: "/posts" }] }];

            render(<AppSidebar navMain={mockNavMain} navGroups={mockNavGroups} />);

            const separator = screen.getByTestId("sidebar-separator");
            expect(separator).toHaveClass("my-0");
        });
    });

    describe("Data transformation", () => {
        it("transforms group items to base nav items", () => {
            const mockNavGroups = [
                {
                    title: "Test Group",
                    items: [
                        {
                            name: "Test Item",
                            url: "/test",
                            icon: HomeIcon as any,
                            disabled: true,
                            locked: true,
                            requiredPlan: "premium" as any
                        }
                    ]
                }
            ] as any;

            render(<AppSidebar navGroups={mockNavGroups} />);

            const group = screen.getByTestId("nav-group");
            const item = group.querySelector('[data-testid="nav-group-item"]');
            expect(item).toHaveAttribute("data-href", "/test");
            expect(item).toHaveTextContent("Test Item");
        });

        it("transforms main items to base nav items", () => {
            const mockNavMain = [
                {
                    title: "Test Main",
                    url: "/main",
                    icon: UserIcon as any
                }
            ] as any;

            render(<AppSidebar navMain={mockNavMain} />);

            const item = screen.getByTestId("nav-main-item");
            expect(item).toHaveAttribute("data-href", "/main");
            expect(item).toHaveTextContent("Test Main");
        });

        it("preserves all properties in transformation", () => {
            const mockNavMain = [
                {
                    title: "Complex Item",
                    url: "/complex"
                }
            ];

            render(<AppSidebar navMain={mockNavMain} />);

            const item = screen.getByTestId("nav-main-item");
            expect(item).toHaveAttribute("data-href", "/complex");
            expect(item).toHaveTextContent("Complex Item");
        });
    });

    describe("Props forwarding", () => {
        it("forwards additional props to Sidebar", () => {
            render(<AppSidebar data-testprop="test-value" />);

            const sidebar = screen.getByTestId("sidebar");
            expect(sidebar).toHaveAttribute("data-testprop", "test-value");
        });

        it("maintains default sidebar props", () => {
            render(<AppSidebar />);

            const sidebar = screen.getByTestId("sidebar");
            expect(sidebar).toHaveAttribute("data-collapsible", "icon");
            expect(sidebar).toHaveAttribute("data-side", "right");
            expect(sidebar).toHaveAttribute("data-dir", "rtl");
        });
    });

    describe("Complex scenarios", () => {
        it("renders all navigation types together", () => {
            const mockNavMain = [{ title: "Home", url: "/" }];
            const mockNavGroups = [{ title: "Content", items: [{ name: "Posts", url: "/posts" }] }];
            const mockNavSecondary = [{ title: "Help", url: "/help" }];

            render(<AppSidebar navMain={mockNavMain} navGroups={mockNavGroups} navSecondary={mockNavSecondary} />);

            expect(screen.getByTestId("nav-main")).toBeInTheDocument();
            expect(screen.getByTestId("nav-group")).toBeInTheDocument();
            expect(screen.getByTestId("nav-secondary")).toBeInTheDocument();
        });

        it("handles empty navigation arrays", () => {
            render(<AppSidebar navMain={[]} navGroups={[]} navSecondary={[]} />);

            expect(screen.queryByTestId("nav-main")).not.toBeInTheDocument();
            expect(screen.queryByTestId("nav-group")).not.toBeInTheDocument();
            expect(screen.queryByTestId("nav-secondary")).not.toBeInTheDocument();
        });

        it("handles mixed empty and populated navigation", () => {
            const mockNavGroups = [{ title: "Content", items: [{ name: "Posts", url: "/posts" }] }];

            render(<AppSidebar navMain={[]} navGroups={mockNavGroups} navSecondary={[]} />);

            expect(screen.queryByTestId("nav-main")).not.toBeInTheDocument();
            expect(screen.getByTestId("nav-group")).toBeInTheDocument();
            expect(screen.queryByTestId("nav-secondary")).not.toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("has proper semantic structure", () => {
            render(<AppSidebar />);

            const sidebar = screen.getByTestId("sidebar");
            expect(sidebar).toBeInTheDocument();
        });

        it("has sidebar trigger for keyboard navigation", () => {
            render(<AppSidebar />);

            const trigger = screen.getByTestId("sidebar-trigger");
            expect(trigger).toBeInTheDocument();
        });

        it("maintains RTL direction for Hebrew content", () => {
            render(<AppSidebar />);

            const sidebar = screen.getByTestId("sidebar");
            expect(sidebar).toHaveAttribute("data-dir", "rtl");
        });

        it("has proper content alignment for RTL", () => {
            render(<AppSidebar />);

            const content = screen.getByTestId("sidebar-content");
            expect(content).toHaveClass("text-right");
        });
    });
});
