/**
 * @jest-environment jsdom
 */
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";

import { SiteFooter } from "@/components/layout/site-footer";

// Mock the icons
jest.mock("react-icons/fa", () => ({
    FaFacebookF: ({ className }: { className?: string }) => (
        <span data-testid="facebook-icon" className={className}>
            FB
        </span>
    ),
    FaYoutube: ({ className }: { className?: string }) => (
        <span data-testid="youtube-icon" className={className}>
            YT
        </span>
    ),
    FaLinkedin: ({ className }: { className?: string }) => (
        <span data-testid="linkedin-icon" className={className}>
            LI
        </span>
    ),
    FaTiktok: ({ className }: { className?: string }) => (
        <span data-testid="tiktok-icon" className={className}>
            TT
        </span>
    )
}));

jest.mock("react-icons/ri", () => ({
    RiInstagramFill: ({ className }: { className?: string }) => (
        <span data-testid="instagram-icon" className={className}>
            IG
        </span>
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({
        children,
        variant,
        size,
        className,
        asChild,
        "aria-label": ariaLabel
    }: {
        children: React.ReactNode;
        variant?: string;
        size?: string;
        className?: string;
        asChild?: boolean;
        "aria-label"?: string;
    }) => (
        <button
            data-variant={variant}
            data-size={size}
            className={className}
            aria-label={ariaLabel}
            data-as-child={asChild}
        >
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/footer", () => ({
    Footer: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <footer data-testid="footer" className={className}>
            {children}
        </footer>
    ),
    FooterContent: ({ children }: { children: React.ReactNode }) => <div data-testid="footer-content">{children}</div>,
    FooterColumn: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="footer-column" className={className}>
            {children}
        </div>
    )
}));

jest.mock("next/link", () => ({
    __esModule: true,
    default: ({
        href,
        children,
        target,
        rel,
        className
    }: {
        href: string;
        children: React.ReactNode;
        target?: string;
        rel?: string;
        className?: string;
    }) => (
        <a href={href} target={target} rel={rel} className={className} data-testid="link">
            {children}
        </a>
    )
}));

describe("SiteFooter", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock the current year to be consistent
        jest.spyOn(Date.prototype, "getFullYear").mockReturnValue(2024);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe("Basic rendering", () => {
        it("renders footer with correct structure", () => {
            render(<SiteFooter />);

            expect(screen.getByTestId("footer")).toBeInTheDocument();
            expect(screen.getByTestId("footer-content")).toBeInTheDocument();
            expect(screen.getAllByTestId("footer-column")).toHaveLength(3);
        });

        it("has correct footer classes", () => {
            render(<SiteFooter />);

            const footer = screen.getByTestId("footer");
            expect(footer).toHaveClass("border-t");
        });
    });

    describe("Navigation links", () => {
        it("renders all navigation links", () => {
            render(<SiteFooter />);

            const links = screen.getAllByTestId("link");
            const navLinks = links.filter((link) => link.getAttribute("href")?.startsWith("/"));

            expect(navLinks).toHaveLength(3);

            // Check specific links
            expect(screen.getByText("תקנון")).toBeInTheDocument();
            expect(screen.getByText("מדיניות הפרטיות")).toBeInTheDocument();
            expect(screen.getByText("צור קשר")).toBeInTheDocument();
        });

        it("has correct href attributes for navigation links", () => {
            render(<SiteFooter />);

            const termsLink = screen.getByText("תקנון").closest("a");
            const privacyLink = screen.getByText("מדיניות הפרטיות").closest("a");
            const contactLink = screen.getByText("צור קשר").closest("a");

            expect(termsLink).toHaveAttribute("href", "/terms-of-use");
            expect(privacyLink).toHaveAttribute("href", "/privacy-policy");
            expect(contactLink).toHaveAttribute("href", "/contact");
        });

        it("has correct styling for navigation links", () => {
            render(<SiteFooter />);

            const termsLink = screen.getByText("תקנון").closest("a");
            expect(termsLink).toHaveClass("text-muted-foreground", "transition-colors", "hover:text-primary");
        });
    });

    describe("Social media links", () => {
        it("renders all social media icons", () => {
            render(<SiteFooter />);

            expect(screen.getByTestId("youtube-icon")).toBeInTheDocument();
            expect(screen.getByTestId("instagram-icon")).toBeInTheDocument();
            expect(screen.getByTestId("tiktok-icon")).toBeInTheDocument();
            expect(screen.getByTestId("facebook-icon")).toBeInTheDocument();
            expect(screen.getByTestId("linkedin-icon")).toBeInTheDocument();
        });

        it("has correct href attributes for social links", () => {
            render(<SiteFooter />);

            const socialLinks = screen
                .getAllByTestId("link")
                .filter((link) => link.getAttribute("href")?.startsWith("https://"));

            expect(socialLinks).toHaveLength(5);

            const youtubeLink = screen.getByTestId("youtube-icon").closest("a");
            const instagramLink = screen.getByTestId("instagram-icon").closest("a");
            const tiktokLink = screen.getByTestId("tiktok-icon").closest("a");
            const facebookLink = screen.getByTestId("facebook-icon").closest("a");
            const linkedinLink = screen.getByTestId("linkedin-icon").closest("a");

            expect(youtubeLink).toHaveAttribute("href", "https://www.youtube.com/watch?v=uhNEtEFRq5M");
            expect(instagramLink).toHaveAttribute("href", "https://www.instagram.com/milgapo/?ref=badge");
            expect(tiktokLink).toHaveAttribute("href", "https://www.tiktok.com/@milgapo_");
            expect(facebookLink).toHaveAttribute("href", "https://www.facebook.com/milgapo/");
            expect(linkedinLink).toHaveAttribute("href", "https://www.linkedin.com/in/milgapo");
        });

        it("has correct attributes for external links", () => {
            render(<SiteFooter />);

            const socialLinks = screen
                .getAllByTestId("link")
                .filter((link) => link.getAttribute("href")?.startsWith("https://"));

            socialLinks.forEach((link) => {
                expect(link).toHaveAttribute("target", "_blank");
                expect(link).toHaveAttribute("rel", "noopener noreferrer");
            });
        });

        it("has correct ARIA labels for social buttons", () => {
            render(<SiteFooter />);

            const buttons = screen.getAllByRole("button");
            const socialButtons = buttons.filter((button) => button.getAttribute("aria-label"));

            expect(socialButtons).toHaveLength(5);

            const ariaLabels = socialButtons.map((button) => button.getAttribute("aria-label"));
            expect(ariaLabels).toContain("YouTube");
            expect(ariaLabels).toContain("Instagram");
            expect(ariaLabels).toContain("TikTok");
            expect(ariaLabels).toContain("Facebook");
            expect(ariaLabels).toContain("LinkedIn");
        });

        it("has correct button styling", () => {
            render(<SiteFooter />);

            const buttons = screen.getAllByRole("button");
            const socialButtons = buttons.filter((button) => button.getAttribute("aria-label"));

            socialButtons.forEach((button) => {
                expect(button).toHaveAttribute("data-variant", "ghost");
                expect(button).toHaveAttribute("data-size", "icon");
                expect(button).toHaveClass("h-8", "w-8", "rounded-md", "transition-transform", "hover:scale-110");
            });
        });

        it("has screen reader text for social links", () => {
            render(<SiteFooter />);

            expect(screen.getByText("YouTube")).toHaveClass("sr-only");
            expect(screen.getByText("Instagram")).toHaveClass("sr-only");
            expect(screen.getByText("TikTok")).toHaveClass("sr-only");
            expect(screen.getByText("Facebook")).toHaveClass("sr-only");
            expect(screen.getByText("LinkedIn")).toHaveClass("sr-only");
        });
    });

    describe("Copyright section", () => {
        it("displays current year", () => {
            render(<SiteFooter />);

            expect(screen.getByText("© 2024 כל הזכויות שמורות")).toBeInTheDocument();
        });

        it("has correct styling for copyright text", () => {
            render(<SiteFooter />);

            const copyrightText = screen.getByText("© 2024 כל הזכויות שמורות");
            expect(copyrightText).toHaveClass("text-sm", "text-muted-foreground", "text-center");
        });

        it("updates year dynamically", () => {
            // Test with different year
            jest.spyOn(Date.prototype, "getFullYear").mockReturnValue(2025);

            render(<SiteFooter />);

            expect(screen.getByText("© 2025 כל הזכויות שמורות")).toBeInTheDocument();
        });
    });

    describe("Layout and responsive design", () => {
        it("has correct container classes", () => {
            render(<SiteFooter />);

            const container = screen.getByTestId("footer").querySelector(".container");
            expect(container).toHaveClass("py-4", "md:py-6");
        });

        it("has correct column classes", () => {
            render(<SiteFooter />);

            const columns = screen.getAllByTestId("footer-column");

            // Navigation links column
            expect(columns[0]).toHaveClass("col-span-full", "mb-2", "md:mb-3");

            // Social links column
            expect(columns[1]).toHaveClass("col-span-full", "mb-2", "md:mb-3");

            // Copyright column
            expect(columns[2]).toHaveClass("col-span-full");
        });

        it("has correct flex layouts", () => {
            render(<SiteFooter />);

            const navLinksContainer = screen.getByText("תקנון").closest("div");
            expect(navLinksContainer).toHaveClass("flex", "flex-wrap", "justify-center", "gap-4", "text-sm");

            const socialLinksContainer = screen.getByTestId("youtube-icon").closest("div")?.closest("div");
            expect(socialLinksContainer).toHaveClass("flex", "flex-wrap", "justify-center", "gap-4");
        });
    });

    describe("Accessibility", () => {
        it("has proper semantic structure", () => {
            render(<SiteFooter />);

            const footer = screen.getByRole("contentinfo");
            expect(footer).toBeInTheDocument();
        });

        it("has proper link accessibility", () => {
            render(<SiteFooter />);

            const allLinks = screen.getAllByRole("link");
            allLinks.forEach((link) => {
                expect(link).toHaveAttribute("href");
            });
        });

        it("has proper button accessibility", () => {
            render(<SiteFooter />);

            const buttons = screen.getAllByRole("button");
            buttons.forEach((button) => {
                expect(button).toHaveAttribute("aria-label");
            });
        });
    });
});
