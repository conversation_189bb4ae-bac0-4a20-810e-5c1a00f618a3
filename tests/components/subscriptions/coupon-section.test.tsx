import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "sonner";

import { CouponSection } from "@/components/subscriptions/coupon-section";
import { TEXTS } from "@/lib/subscription-constants";

// Mock sonner
jest.mock("sonner");

// Mock fetch
global.fetch = jest.fn();

describe("CouponSection", () => {
    const onCouponApplied = jest.fn();
    const onCouponInvalid = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the component with title, input, and button", () => {
        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        expect(screen.getByText(TEXTS.COUPON_TITLE)).toBeInTheDocument();
        expect(screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER)).toBeInTheDocument();
        expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("does not render the title when hideTitle is true", () => {
        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} hideTitle />);
        expect(screen.queryByText(TEXTS.COUPON_TITLE)).not.toBeInTheDocument();
    });

    it("updates coupon code on input change", async () => {
        const user = userEvent.setup();
        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        const input = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        await user.type(input, "TESTCOUPON");
        expect(input).toHaveValue("TESTCOUPON");
    });

    it("shows an error toast if coupon code is empty on apply", async () => {
        const user = userEvent.setup();
        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        const button = screen.getByRole("button");
        await user.click(button);
        expect(toast.error).toHaveBeenCalledWith(TEXTS.ENTER_COUPON_CODE);
        expect(fetch).not.toHaveBeenCalled();
    });

    it("handles successful coupon validation", async () => {
        const user = userEvent.setup();
        const mockResponse = {
            success: true,
            couponCode: "VALID",
            couponType: "percentage",
            discountValue: 10,
            discountApplied: 10,
            finalAmount: 90
        };
        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => mockResponse
        });

        render(
            <CouponSection
                onCouponApplied={onCouponApplied}
                onCouponInvalid={onCouponInvalid}
                currentTotalAmount={100}
            />
        );

        const input = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        const button = screen.getByRole("button");

        await user.type(input, "VALID");
        await user.click(button);

        await waitFor(() => {
            expect(screen.getByRole("button")).not.toBeDisabled();
        });

        expect(fetch).toHaveBeenCalledWith("/api/validate-coupon", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                coupon_code: "VALID",
                total_amount: 100
            })
        });

        expect(onCouponApplied).toHaveBeenCalledWith(mockResponse);
        expect(toast.success).toHaveBeenCalledWith(TEXTS.COUPON_APPLIED_SUCCESS("VALID"));
    });

    it("handles invalid coupon", async () => {
        const user = userEvent.setup();
        const mockResponse = { success: false, error: "Invalid coupon" };
        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: false,
            json: async () => mockResponse
        });

        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        const input = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        const button = screen.getByRole("button");

        await user.type(input, "INVALID");
        await user.click(button);

        await waitFor(() => {
            expect(screen.getByRole("button")).not.toBeDisabled();
        });

        expect(onCouponInvalid).toHaveBeenCalledWith("Invalid coupon");
        expect(toast.error).toHaveBeenCalledWith("Invalid coupon");
    });

    it("handles API fetch error", async () => {
        const user = userEvent.setup();
        (fetch as jest.Mock).mockRejectedValueOnce(new Error("Network error"));

        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        const input = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        const button = screen.getByRole("button");

        await user.type(input, "ANYCOUPON");
        await user.click(button);

        await waitFor(() => {
            expect(screen.getByRole("button")).not.toBeDisabled();
        });

        expect(onCouponInvalid).toHaveBeenCalledWith("Network error");
        expect(toast.error).toHaveBeenCalledWith("An error occurred while validating the coupon.");
    });

    it("triggers coupon application on Enter key press", async () => {
        const user = userEvent.setup();
        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({ success: true, couponCode: "ENTER" })
        });
        render(<CouponSection onCouponApplied={onCouponApplied} onCouponInvalid={onCouponInvalid} />);
        const input = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        await user.type(input, "ENTER{enter}");
        await waitFor(() => {
            expect(screen.getByRole("button")).not.toBeDisabled();
        });
        expect(fetch).toHaveBeenCalled();
    });
});
