import { render, screen, fireEvent, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

import { OnboardingSubscriptionCard } from "@/components/subscriptions/onboarding-subscription-card";
import { Feature, SubscriptionsItem } from "@/lib/subscription-constants";

const mockFeatures: Feature[] = [
    { id: "f1", name: "Feature One" },
    { id: "f2", name: "Feature Two" }
];

const getFeatureByKey = (featureId: string) => mockFeatures.find((f) => f.id === featureId)!;

const mockItem: SubscriptionsItem = {
    id: "plan1",
    planType: "milgapro",
    title: "Onboarding Plan",
    description: "An onboarding plan",
    price: 50,
    originalPrice: 75,
    duration_days: 30,
    isFeatured: false,
    colors: {
        cardGradient: "bg-white",
        text: "text-black",
        bg: "bg-gray-100",
        bg10: "bg-gray-50",
        bar: "bg-gray-300"
    },
    features: [
        { featureId: "f1", isApplicable: true },
        { featureId: "f2", isApplicable: false }
    ]
};

describe("OnboardingSubscriptionCard", () => {
    const onSelect = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders basic card details", () => {
        render(<OnboardingSubscriptionCard item={mockItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />);
        expect(screen.getByText("Onboarding Plan")).toBeInTheDocument();
        expect(screen.getByText("An onboarding plan")).toBeInTheDocument();
        expect(screen.getByText("₪50")).toBeInTheDocument();
    });

    it("calls onSelect when clicked", async () => {
        const user = userEvent.setup();
        render(<OnboardingSubscriptionCard item={mockItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />);
        await user.click(screen.getByRole("button"));
        expect(onSelect).toHaveBeenCalledWith("plan1");
    });

    it("calls onSelect on Enter key press", () => {
        render(<OnboardingSubscriptionCard item={mockItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />);
        fireEvent.keyDown(screen.getByRole("button"), { key: "Enter", code: "Enter" });
        expect(onSelect).toHaveBeenCalledWith("plan1");
    });

    it("calls onSelect on Space key press", () => {
        render(<OnboardingSubscriptionCard item={mockItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />);
        fireEvent.keyDown(screen.getByRole("button"), { key: " ", code: "Space" });
        expect(onSelect).toHaveBeenCalledWith("plan1");
    });

    it("shows selected state with checkmark and styles", () => {
        render(
            <OnboardingSubscriptionCard
                item={mockItem}
                onSelect={onSelect}
                getFeatureByKey={getFeatureByKey}
                selected
            />
        );
        expect(screen.getByRole("button")).toHaveClass("ring-2 ring-primary");
        expect(screen.getByRole("button").querySelector("svg")).toBeInTheDocument(); // Check for checkmark svg
    });

    it("shows featured state with badge and styles", () => {
        const featuredItem = { ...mockItem, isFeatured: true };
        render(
            <OnboardingSubscriptionCard item={featuredItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />
        );
        expect(screen.getByText("מומלץ")).toBeInTheDocument();
        expect(screen.getByRole("button")).toHaveClass("border-primary border-2");
    });

    it("shows discounted price", () => {
        render(
            <OnboardingSubscriptionCard
                item={mockItem}
                onSelect={onSelect}
                getFeatureByKey={getFeatureByKey}
                appliedDiscount={25}
            />
        );
        const originalPrice = screen.getByText("₪75");
        expect(originalPrice).toBeInTheDocument();
        expect(originalPrice).toHaveClass("line-through");
    });

    it("renders features with correct icons", () => {
        render(<OnboardingSubscriptionCard item={mockItem} onSelect={onSelect} getFeatureByKey={getFeatureByKey} />);
        const feature1 = screen.getByText("Feature One");
        const feature2 = screen.getByText("Feature Two");

        // Use within to scope queries to the specific feature list item
        const feature1Container = feature1.closest("li");
        const feature2Container = feature2.closest("li");

        if (feature1Container && feature2Container) {
            expect(within(feature1Container).getByText("✔")).toBeInTheDocument();
            expect(within(feature2Container).getByText("✖")).toBeInTheDocument();
        } else {
            throw new Error("Feature containers not found in the DOM");
        }
        expect(feature2).toHaveClass("line-through");
    });
});
