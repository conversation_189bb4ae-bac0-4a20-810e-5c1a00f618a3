import { render, screen } from "@testing-library/react";

import { FeatureComparisonTable } from "@/components/subscriptions/feature-comparison-table";
import { FeatureComparison, SubscriptionsItem } from "@/lib/subscription-constants";

// Correctly generate planNames from the actual mocked config
const mockedPricingPlans = jest.requireMock("@/config/subscriptions").PRICING_PLANS;
const planNames = mockedPricingPlans.map((plan: SubscriptionsItem) => plan.title);

const comparisonData: FeatureComparison[] = [
    {
        name: "התאמת מלגות אישית",
        plans: {
            "חבילת Basic": true,
            "Milga-Pro": true,
            "Elite Student": true,
            VIP: true
        }
    },
    {
        name: "מנגנון הגשות אוטומטיות",
        plans: {
            "חבילת Basic": false,
            "Milga-Pro": true,
            "Elite Student": true,
            VIP: true
        }
    },
    {
        name: "כמות הגשות",
        plans: {
            "חבילת Basic": "מוגבל",
            "Milga-Pro": "ללא הגבלה",
            "Elite Student": "ללא הגבלה",
            VIP: "ללא הגבלה"
        }
    }
];

// Mock a featured plan for testing styling
jest.mock("@/config/subscriptions", () => {
    const originalConfig = jest.requireActual("@/config/subscriptions");
    return {
        ...originalConfig,
        PRICING_PLANS: originalConfig.PRICING_PLANS.map((plan: SubscriptionsItem) =>
            plan.planType === "milgapro"
                ? { ...plan, title: "Milga-Pro", isFeatured: true, colors: { bg10: "bg-blue-50" } }
                : plan
        )
    };
});

describe("FeatureComparisonTable", () => {
    it("renders table headers with correct plan names", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        planNames.forEach((planName: string) => {
            expect(screen.getByRole("columnheader", { name: planName })).toBeInTheDocument();
        });
    });

    it("renders feature rows with correct names", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        comparisonData.forEach((feature) => {
            expect(screen.getByText(feature.name)).toBeInTheDocument();
        });
    });

    it("displays a checkmark for true values", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        const featureRow = screen.getByText("התאמת מלגות אישית").closest("tr");
        const checkIcons = featureRow?.querySelectorAll("svg.lucide-check");
        expect(checkIcons?.length).toBe(4);
    });

    it("displays an X for false values", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        const featureRow = screen.getByText("מנגנון הגשות אוטומטיות").closest("tr");
        const xIcon = featureRow?.querySelector("svg.lucide-x");
        expect(xIcon).toBeInTheDocument();
    });

    it("displays string values correctly", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        expect(screen.getByText("מוגבל")).toBeInTheDocument();
        expect(screen.getAllByText("ללא הגבלה")).toHaveLength(3);
    });

    it("applies featured plan styling correctly", () => {
        render(<FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />);
        const featuredPlan = mockedPricingPlans.find((p: SubscriptionsItem) => p.planType === "milgapro")!;
        const featuredHeader = screen.getByRole("columnheader", { name: featuredPlan.title });
        expect(featuredHeader).toHaveClass("bg-blue-50");
    });
});
