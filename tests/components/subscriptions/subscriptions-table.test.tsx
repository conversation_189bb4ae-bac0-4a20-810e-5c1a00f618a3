import { cleanup, render, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import * as React from "react";

import { applyFreePlan } from "@/app/actions/subscriptions-actions";
import { SubscriptionsTable } from "@/components/subscriptions/subscriptions-table";
import { PRICING_PLANS } from "@/config/subscriptions";
import { TEXTS, type UserSubscriptionWithPlan, type SubscriptionsItem } from "@/lib/subscription-constants";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    applyFreePlan: jest.fn()
}));

global.fetch = jest.fn();

const mockUseUser = useUser as jest.Mock;
const mockUseRouter = useRouter as jest.Mock;
const mockApplyFreePlan = applyFreePlan as jest.Mock;
const mockToastSuccess = toast.success as jest.Mock;

const mockUser = {
    id: "user_123",
    fullName: "Test User",
    emailAddresses: [{ emailAddress: "<EMAIL>" }],
    phoneNumbers: [{ phoneNumber: "1234567890" }]
};

describe("SubscriptionsTable", () => {
    let mockPush: jest.Mock;

    beforeEach(() => {
        mockPush = jest.fn();
        mockUseRouter.mockReturnValue({ push: mockPush });
        mockUseUser.mockReturnValue({ user: null, isLoaded: true, isSignedIn: false });
        jest.clearAllMocks();
    });

    afterEach(cleanup);

    it("renders skeleton when user is not loaded", () => {
        mockUseUser.mockReturnValue({ user: null, isLoaded: false });
        render(<SubscriptionsTable />);
        const firstPlanTitle = PRICING_PLANS[0]?.title ?? "";
        expect(screen.queryByText(firstPlanTitle)).toBeNull();
    });

    it("renders subscription cards and coupon section for a guest user", () => {
        render(<SubscriptionsTable />);
        PRICING_PLANS.forEach((plan) => {
            expect(screen.getByText(plan.title)).toBeInTheDocument();
        });
        expect(screen.getByText(TEXTS.COUPON_TITLE)).toBeInTheDocument();
    });

    it("redirects to login when an unauthenticated user tries to checkout", async () => {
        const user = userEvent.setup();
        render(<SubscriptionsTable />);

        const proPlan = PRICING_PLANS.find((p) => p.planType === "milgapro")!;
        const proCard = screen.getByText(proPlan.title).closest("div[class*='text-card-foreground']") as HTMLElement;
        const checkoutButton = within(proCard).getByRole("button");

        await user.click(checkoutButton);

        expect(mockPush).toHaveBeenCalledWith("/login");
    });

    it("handles paid checkout correctly for an authenticated user", async () => {
        const user = userEvent.setup();
        mockUseUser.mockReturnValue({ user: mockUser, isLoaded: true });

        const mockSignedUrl = "https://example.com/checkout";
        (global.fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({ success: true, signedUrl: mockSignedUrl })
        });

        // We can't truly mock window.location.href assignment in jsdom without a setup file.
        // The component will call it, but we can't assert the result.
        // The important part is that the fetch call was made.
        render(<SubscriptionsTable />);

        const proPlan = PRICING_PLANS.find((p) => p.planType === "milgapro")!;
        const proCard = screen.getByText(proPlan.title).closest("div[class*='text-card-foreground']") as HTMLElement;
        const checkoutButton = within(proCard).getByRole("button");
        await user.click(checkoutButton);

        await waitFor(() => {
            expect(global.fetch).toHaveBeenCalledWith("/api/generate-checkout-url", expect.any(Object));
        });
    });

    it("handles free plan application when final amount is zero", async () => {
        const user = userEvent.setup();
        mockUseUser.mockReturnValue({ user: mockUser, isLoaded: true });
        mockApplyFreePlan.mockResolvedValue({ success: true });

        const milgaproPlan = PRICING_PLANS.find((p) => p.planType === "milgapro")!;
        const couponCode = "FREEBIE";

        render(<SubscriptionsTable />);

        const couponInfo = {
            couponCode,
            couponType: "fixed_amount",
            discountValue: milgaproPlan.price
        };
        (global.fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({ success: true, ...couponInfo })
        });

        const couponInput = screen.getByPlaceholderText(TEXTS.COUPON_PLACEHOLDER);
        const couponSection = screen.getByText(TEXTS.COUPON_TITLE).closest("div[class*='text-center']") as HTMLElement;
        const applyButton = within(couponSection).getByRole("button");
        await user.type(couponInput, couponCode);
        await user.click(applyButton);

        const planCard = screen
            .getByText(milgaproPlan.title)
            .closest("div[class*='text-card-foreground']") as HTMLElement;

        await waitFor(() => {
            const priceDisplay = planCard.querySelector(".text-4xl");
            // Using a regex to match "0" and "₪" inside the container,
            // which is more robust than checking for a single combined string.
            expect(priceDisplay).toHaveTextContent(/^0\s*₪$/);
        });

        const checkoutButton = within(planCard).getByRole("button");

        await user.click(checkoutButton);

        await waitFor(() => {
            expect(mockApplyFreePlan).toHaveBeenCalledWith(mockUser.id, milgaproPlan.id, couponCode);
        });

        expect(mockToastSuccess).toHaveBeenCalledWith(TEXTS.PLAN_ADDED_FREE_SUCCESS(milgaproPlan.title));
    });

    it("displays current plan and disables CTA for lower-tier plans", () => {
        const proPlan = PRICING_PLANS.find((p: SubscriptionsItem) => p.planType === "milgapro")!;
        const currentSubscription: UserSubscriptionWithPlan = {
            id: "sub_1",
            user_id: "user_123",
            plan_id: "plan_pro",
            start_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            planType: "milgapro",
            coupon_id: null,
            expiration_date: null,
            is_active: true,
            order_id: "order_123",
            paid_amount: 99,
            payment_details: {},
            plan_price: 99,
            transaction_id: "txn_123",
            updated_at: new Date().toISOString()
        };

        render(<SubscriptionsTable currentSubscription={currentSubscription} />);

        const proCard = screen.getByText(proPlan.title).closest("div[class*='text-card-foreground']") as HTMLElement;
        expect(within(proCard).getByRole("button")).toBeDisabled();

        const freePlan = PRICING_PLANS.find((p: SubscriptionsItem) => p.planType === "free")!;
        const freeCard = screen.getByText(freePlan.title).closest("div[class*='text-card-foreground']") as HTMLElement;
        expect(within(freeCard).getByText(TEXTS.INCLUDED_IN_CURRENT_PLAN)).toBeInTheDocument();
    });

    it("renders FeatureComparisonTable when showFeaturesTable is true", () => {
        render(<SubscriptionsTable showFeaturesTable={true} />);
        expect(screen.getByText(TEXTS.FEATURE_COMPARISON)).toBeInTheDocument();
    });

    it("does not render FeatureComparisonTable by default", () => {
        render(<SubscriptionsTable />);
        expect(screen.queryByText(TEXTS.FEATURE_COMPARISON)).not.toBeInTheDocument();
    });
});
