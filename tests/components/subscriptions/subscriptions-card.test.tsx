import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

import { SubscriptionsCard } from "@/components/subscriptions/subscriptions-card";
import { Feature, PlanFeature, SubscriptionsItem, TEXTS, UserSubscriptionWithPlan } from "@/lib/subscription-constants";

const mockFeatures: Feature[] = [
    { id: "f1", name: "Feature One" },
    { id: "f2", name: "Feature Two" },
    { id: "f3", name: "Feature Three (Valued)" }
];

const getFeatureByKey = (featureId: string) => mockFeatures.find((f) => f.id === featureId)!;

const mockItem: SubscriptionsItem = {
    id: "plan1",
    planType: "milgapro",
    title: "Basic Plan",
    description: "A basic plan",
    price: 100,
    duration_days: 30,
    isFeatured: false,
    colors: {
        cardGradient: "bg-white",
        text: "text-black",
        bg: "bg-gray-100",
        bg10: "bg-gray-50",
        bar: "bg-gray-300"
    },
    features: [
        { featureId: "f1", isApplicable: true },
        { featureId: "f2", isApplicable: false },
        { featureId: "f3", isApplicable: true, value: "10 units" }
    ]
};

const mockFeaturedItem: SubscriptionsItem = {
    ...mockItem,
    id: "plan2",
    planType: "elite",
    title: "Featured Plan",
    isFeatured: true
};

const mockCurrentSubscription: UserSubscriptionWithPlan = {
    planType: "milgapro",
    is_active: true,
    id: "sub1",
    user_id: "user1",
    plan_id: "plan1",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    start_date: new Date().toISOString(),
    expiration_date: null,
    coupon_id: null,
    order_id: null,
    paid_amount: null,
    payment_details: null,
    plan_price: null,
    transaction_id: null
};

describe("SubscriptionsCard", () => {
    const handleCtaClick = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders basic card details correctly", () => {
        render(<SubscriptionsCard item={mockItem} handleCtaClick={handleCtaClick} getFeatureByKey={getFeatureByKey} />);
        expect(screen.getByText("Basic Plan")).toBeInTheDocument();
        expect(screen.getByText("A basic plan")).toBeInTheDocument();
        expect(screen.getByText("100")).toBeInTheDocument();
    });

    it("renders featured badge and styles for featured plans", () => {
        render(
            <SubscriptionsCard
                item={mockFeaturedItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
            />
        );
        expect(screen.getByText(TEXTS.MOST_POPULAR)).toBeInTheDocument();
    });

    it("renders features correctly with check, x, and values", () => {
        render(<SubscriptionsCard item={mockItem} handleCtaClick={handleCtaClick} getFeatureByKey={getFeatureByKey} />);
        const feature1 = screen.getByText("Feature One");
        const feature2 = screen.getByText("Feature Two");
        const feature3 = screen.getByText("Feature Three (Valued)");

        expect(feature1.previousElementSibling).toHaveClass("lucide-check");
        expect(feature2.previousElementSibling).toHaveClass("lucide-x");
        expect(feature2).toHaveClass("line-through");
        expect(screen.getByText("(10 units)")).toBeInTheDocument();
    });

    it("displays discounted price when discount is applied", () => {
        render(
            <SubscriptionsCard
                item={mockItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                appliedDiscount={20}
            />
        );
        expect(screen.getByText("80")).toBeInTheDocument();
        const originalPrice = screen.getByText("100");
        expect(originalPrice.parentElement).toHaveClass("line-through");
    });

    it("shows 'Register' button for guests", () => {
        render(
            <SubscriptionsCard
                item={mockItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                isAuthenticated={false}
            />
        );
        expect(screen.getByRole("button", { name: TEXTS.REGISTER })).toBeInTheDocument();
    });

    it("shows 'Payment' button for authenticated users", () => {
        render(
            <SubscriptionsCard
                item={mockItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                isAuthenticated={true}
            />
        );
        expect(screen.getByRole("button", { name: TEXTS.PAYMENT })).toBeInTheDocument();
    });

    it("shows 'Active Plan' button and is disabled for the current plan", () => {
        render(
            <SubscriptionsCard
                item={mockItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                isCurrentPlan={true}
                currentSubscription={mockCurrentSubscription}
            />
        );
        const button = screen.getByRole("button", { name: TEXTS.ACTIVE_PLAN });
        expect(button).toBeInTheDocument();
        expect(button).toBeDisabled();
    });

    it("shows 'Included in current plan' button and is disabled for included plans", () => {
        const higherPlan: SubscriptionsItem = { ...mockFeaturedItem, planType: "vip" };
        render(
            <SubscriptionsCard
                item={higherPlan}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                isCurrentPlan={true}
                currentSubscription={mockCurrentSubscription}
            />
        );
        const button = screen.getByRole("button", { name: TEXTS.INCLUDED_IN_CURRENT_PLAN });
        expect(button).toBeInTheDocument();
        expect(button).toBeDisabled();
    });

    it("disables button when loading", () => {
        render(
            <SubscriptionsCard
                item={mockItem}
                handleCtaClick={handleCtaClick}
                getFeatureByKey={getFeatureByKey}
                isLoading={true}
            />
        );
        expect(screen.getByRole("button")).toBeDisabled();
    });

    it("calls handleCtaClick with item id on button click", async () => {
        const user = userEvent.setup();
        render(<SubscriptionsCard item={mockItem} handleCtaClick={handleCtaClick} getFeatureByKey={getFeatureByKey} />);
        await user.click(screen.getByRole("button"));
        expect(handleCtaClick).toHaveBeenCalledWith("plan1");
    });
});
