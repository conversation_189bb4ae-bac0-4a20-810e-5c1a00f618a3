import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import FaqPage from "@/app/(admin)/admin/faq/page";
import { deleteFaq, getAllFaqs, reorderFaqs } from "@/app/actions/faq-actions";
import { TEXTS } from "@/lib/faq-constants";
import { toast } from "sonner";

jest.mock("@/app/actions/faq-actions", () => ({
    getAllFaqs: jest.fn(),
    deleteFaq: jest.fn(),
    reorderFaqs: jest.fn()
}));

jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn(),
        loading: jest.fn()
    }
}));

describe("FaqPage Component", () => {
    const mockFaqs = [
        {
            id: "1",
            question: "Question 1",
            answer: "Answer 1",
            order_index: 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        },
        {
            id: "2",
            question: "Question 2",
            answer: "Answer 2",
            order_index: 2,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }
    ];

    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();
        (getAllFaqs as jest.Mock).mockResolvedValue({
            success: true,
            data: [...mockFaqs]
        });
        (deleteFaq as jest.Mock).mockResolvedValue({ success: true });

        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    it("should render loading state and then display FAQ items", async () => {
        render(<FaqPage />);

        expect(screen.getByText(TEXTS.LOADING)).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.queryByText(TEXTS.LOADING)).not.toBeInTheDocument();
        });

        expect(getAllFaqs).toHaveBeenCalledTimes(1);
        expect(screen.getByRole("heading", { name: TEXTS.PAGE_TITLE })).toBeInTheDocument();
        expect(screen.getByText("Question 1")).toBeInTheDocument();
        expect(screen.getByText("Question 2")).toBeInTheDocument();
    });

    it("should display an error message if fetching fails", async () => {
        (getAllFaqs as jest.Mock).mockResolvedValueOnce({
            success: false,
            error: "Failed to fetch"
        });

        render(<FaqPage />);

        await waitFor(() => {
            expect(screen.getByText(/Failed to fetch/i)).toBeInTheDocument();
        });
    });

    it("should delete an FAQ item after confirmation", async () => {
        const user = userEvent.setup();

        (getAllFaqs as jest.Mock)
            .mockResolvedValueOnce({ success: true, data: [...mockFaqs] })
            .mockResolvedValueOnce({ success: true, data: [mockFaqs[1]] });

        render(<FaqPage />);

        await waitFor(() => {
            expect(screen.getByText("Question 1")).toBeInTheDocument();
        });

        const row = screen.getByText("Question 1").closest("tr");
        const deleteButton = row!.querySelector("button.h-8.w-8 svg.lucide-trash-2");
        expect(deleteButton).toBeInTheDocument();

        await user.click(deleteButton!.parentElement!);

        const confirmButton = await screen.findByRole("button", { name: TEXTS.CONFIRM_DELETE });
        await user.click(confirmButton);

        await waitFor(() => {
            expect(deleteFaq).toHaveBeenCalledWith("1");
            expect(toast.success).toHaveBeenCalledWith(TEXTS.DELETE_SUCCESS, expect.any(Object));
        });

        await waitFor(() => {
            expect(getAllFaqs).toHaveBeenCalledTimes(2);
        });

        await waitFor(() => {
            expect(screen.queryByText("Question 1")).not.toBeInTheDocument();
            expect(screen.getByText("Question 2")).toBeInTheDocument();
        });
    });

    it("should show an error toast if deletion fails", async () => {
        const user = userEvent.setup();
        (deleteFaq as jest.Mock).mockResolvedValueOnce({
            success: false,
            error: "Failed to delete"
        });

        render(<FaqPage />);

        await waitFor(() => {
            expect(screen.getByText("Question 1")).toBeInTheDocument();
        });

        const row = screen.getByText("Question 1").closest("tr");
        const deleteButton = row!.querySelector("button.h-8.w-8 svg.lucide-trash-2");
        await user.click(deleteButton!.parentElement!);

        const confirmButton = await screen.findByRole("button", { name: TEXTS.CONFIRM_DELETE });
        await user.click(confirmButton);

        await waitFor(() => {
            expect(deleteFaq).toHaveBeenCalledWith("1");
            expect(toast.error).toHaveBeenCalledWith(TEXTS.DELETE_ERROR, {
                description: "Failed to delete",
                id: undefined
            });
        });

        expect(screen.getByText("Question 1")).toBeInTheDocument();
    });

    it("should show a generic error if delete action throws", async () => {
        const user = userEvent.setup();
        (deleteFaq as jest.Mock).mockRejectedValueOnce(new Error("Network error"));

        render(<FaqPage />);
        await waitFor(() => {
            expect(screen.getByText("Question 1")).toBeInTheDocument();
        });

        const row = screen.getByText("Question 1").closest("tr");
        const deleteButton = row!.querySelector("button.h-8.w-8 svg.lucide-trash-2");
        await user.click(deleteButton!.parentElement!);

        const confirmButton = await screen.findByRole("button", { name: TEXTS.CONFIRM_DELETE });
        await user.click(confirmButton);

        await waitFor(() => {
            expect(toast.error).toHaveBeenCalledWith(TEXTS.DELETE_ERROR, {
                description: "Network error",
                id: undefined
            });
        });
        expect(screen.getByText("Question 1")).toBeInTheDocument();
    });
});
