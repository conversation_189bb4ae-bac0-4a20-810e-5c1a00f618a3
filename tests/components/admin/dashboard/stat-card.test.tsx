import React from "react";
import { render, screen, cleanup } from "@testing-library/react";
import { StatCard } from "@/components/admin/dashboard/stat-card";
import { Users } from "lucide-react";

const mockProps = {
    title: "Total Users",
    value: "1,234",
    icon: <Users data-testid="icon" />,
    loading: false,
    error: null
};

describe("StatCard", () => {
    afterEach(cleanup);

    it("renders the value when not loading and no error", () => {
        render(<StatCard {...mockProps} />);
        expect(screen.getByText(mockProps.title)).toBeInTheDocument();
        expect(screen.getByText(mockProps.value)).toBeInTheDocument();
        expect(screen.getByTestId("icon")).toBeInTheDocument();
        expect(screen.queryByText("Error")).not.toBeInTheDocument();
    });

    it("renders a skeleton when loading", () => {
        render(<StatCard {...mockProps} loading={true} />);
        expect(screen.getByText(mockProps.title)).toBeInTheDocument();
        expect(screen.queryByText(mockProps.value)).not.toBeInTheDocument();
        // Skeleton has no text, so we check for its presence via other means if needed,
        // but checking for the absence of value is sufficient here.
        expect(screen.queryByText("Error")).not.toBeInTheDocument();
    });

    it("renders an error message when there is an error", () => {
        render(<StatCard {...mockProps} error={new Error("Failed to load")} />);
        expect(screen.getByText(mockProps.title)).toBeInTheDocument();
        expect(screen.getByText("Error")).toBeInTheDocument();
        expect(screen.queryByText(mockProps.value)).not.toBeInTheDocument();
    });
});
