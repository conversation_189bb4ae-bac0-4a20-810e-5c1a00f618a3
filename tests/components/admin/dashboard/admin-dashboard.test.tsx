import React from "react";
import { render, screen, cleanup } from "@testing-library/react";

const TEXTS = {
    dashboardTitle: "לוח בקרה",
    totalUsers: "סה״כ משתמשים"
};

interface MockUserData {
    total?: number;
}

interface SubscriptionData {
    planId: string;
    planName: string;
    count: number;
    percentage: number;
    newLast24h: number;
}

interface MockStats {
    users: {
        data: MockUserData | null | undefined;
        loading: boolean;
        error: Error | null;
    };
    subscriptions: {
        data: SubscriptionData[] | null;
        loading: boolean;
        error: Error | null;
    };
}

interface StatCardProps {
    title: string;
    value: number | string;
    loading: boolean;
    error: Error | null;
}

interface SubscriptionsCardProps {
    data: SubscriptionData[] | null;
    loading: boolean;
    error: Error | null;
}

const mockStats: MockStats = {
    users: {
        data: { total: 150 },
        loading: false,
        error: null
    },
    subscriptions: {
        data: [
            { planId: "plan_1", planName: "Free Plan", count: 100, percentage: 50, newLast24h: 5 },
            { planId: "plan_2", planName: "Premium Plan", count: 100, percentage: 50, newLast24h: 2 }
        ],
        loading: false,
        error: null
    }
};

const mockUseAdminDashboard = jest.fn(() => ({ stats: mockStats }));

jest.mock("@/hooks/use-admin-dashboard", () => ({
    useAdminDashboard: mockUseAdminDashboard
}));

jest.mock("@/components/admin/dashboard/stat-card", () => ({
    StatCard: ({ title, value, loading, error }: StatCardProps) => (
        <div data-testid="stat-card">
            <div data-testid="stat-card-title">{title}</div>
            <div data-testid="stat-card-value">{loading ? "Loading..." : error ? "Error" : value}</div>
        </div>
    )
}));

jest.mock("@/components/admin/dashboard/subscriptions-card", () => ({
    SubscriptionsCard: ({ data, loading, error }: SubscriptionsCardProps) => (
        <div data-testid="subscriptions-card">
            <div data-testid="subscriptions-card-state">
                {loading ? "Loading..." : error ? "Error" : `Data: ${JSON.stringify(data)}`}
            </div>
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Users: () => <div data-testid="users-icon" />
}));

import { AdminDashboard } from "@/components/admin/dashboard/admin-dashboard";

describe("AdminDashboard", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockUseAdminDashboard.mockReturnValue({ stats: mockStats });
    });

    afterEach(cleanup);

    it("renders the dashboard title correctly", () => {
        render(<AdminDashboard />);

        expect(screen.getByText(TEXTS.dashboardTitle)).toBeInTheDocument();
        expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(TEXTS.dashboardTitle);
    });

    it("renders StatCard with correct props when data is loaded", () => {
        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card")).toBeInTheDocument();
        expect(screen.getByTestId("stat-card-title")).toHaveTextContent(TEXTS.totalUsers);
        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("150");
    });

    it("renders StatCard with loading state", () => {
        const loadingStats: MockStats = {
            ...mockStats,
            users: {
                data: null,
                loading: true,
                error: null
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: loadingStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("Loading...");
    });

    it("renders StatCard with error state", () => {
        const errorStats: MockStats = {
            ...mockStats,
            users: {
                data: null,
                loading: false,
                error: new Error("Failed to load users")
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: errorStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("Error");
    });

    it("renders StatCard with zero value when data is null", () => {
        const nullDataStats: MockStats = {
            ...mockStats,
            users: {
                data: null,
                loading: false,
                error: null
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: nullDataStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("0");
    });

    it("renders SubscriptionsCard with correct props when data is loaded", () => {
        render(<AdminDashboard />);

        expect(screen.getByTestId("subscriptions-card")).toBeInTheDocument();
        expect(screen.getByTestId("subscriptions-card-state")).toHaveTextContent(
            JSON.stringify(mockStats.subscriptions.data)
        );
    });

    it("renders SubscriptionsCard with loading state", () => {
        const loadingStats: MockStats = {
            ...mockStats,
            subscriptions: {
                data: null,
                loading: true,
                error: null
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: loadingStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("subscriptions-card-state")).toHaveTextContent("Loading...");
    });

    it("renders SubscriptionsCard with error state", () => {
        const errorStats: MockStats = {
            ...mockStats,
            subscriptions: {
                data: null,
                loading: false,
                error: new Error("Failed to load subscriptions")
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: errorStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("subscriptions-card-state")).toHaveTextContent("Error");
    });

    it("calls useAdminDashboard hook", () => {
        render(<AdminDashboard />);

        expect(mockUseAdminDashboard).toHaveBeenCalledTimes(1);
    });

    it("handles undefined total in users data", () => {
        const undefinedTotalStats: MockStats = {
            ...mockStats,
            users: {
                data: { total: undefined },
                loading: false,
                error: null
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: undefinedTotalStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("0");
    });

    it("handles missing data object in users", () => {
        const missingDataStats: MockStats = {
            ...mockStats,
            users: {
                data: undefined,
                loading: false,
                error: null
            }
        };

        mockUseAdminDashboard.mockReturnValue({ stats: missingDataStats });

        render(<AdminDashboard />);

        expect(screen.getByTestId("stat-card-value")).toHaveTextContent("0");
    });
});
