import React from "react";
import { render, screen, within, cleanup } from "@testing-library/react";
import { SubscriptionsCard } from "@/components/admin/dashboard/subscriptions-card";
import { SubscriptionStat } from "@/lib/admin-dashboard-constants";

// Mock constants and dependencies
jest.mock("@/config/subscriptions", () => ({
    ...jest.requireActual("@/config/subscriptions"),
    PRICING_PLANS: [
        { id: "plan_1", planType: "free" },
        { id: "plan_2", planType: "premium" }
    ]
}));

const mockData: SubscriptionStat[] = [
    {
        planId: "plan_1",
        planName: "Free Plan",
        count: 100,
        percentage: 50,
        newLast24h: 5
    },
    {
        planId: "plan_2",
        planName: "Premium Plan",
        count: 100,
        percentage: 50,
        newLast24h: 2
    }
];

const mockProps = {
    data: [],
    loading: false,
    error: null
};

describe("SubscriptionsCard", () => {
    afterEach(cleanup);

    it("renders skeletons when loading", () => {
        render(<SubscriptionsCard {...mockProps} loading={true} />);
        const rows = screen.getAllByRole("row");
        // Header row + 4 skeleton rows
        expect(rows).toHaveLength(5);
        expect(screen.queryByText("No Data Available")).not.toBeInTheDocument();
    });

    it("renders an error message on error", () => {
        render(<SubscriptionsCard {...mockProps} error={new Error("Failed to load")} />);
        expect(screen.getByText("שגיאה בטעינת נתונים")).toBeInTheDocument();
    });

    it('renders "no data" message when data is empty and not loading', () => {
        render(<SubscriptionsCard {...mockProps} data={[]} />);
        expect(screen.getByText("אין נתונים להצגה")).toBeInTheDocument();
    });

    it("renders the table with data correctly", () => {
        render(<SubscriptionsCard {...mockProps} data={mockData} />);

        // Check for header
        expect(screen.getByRole("columnheader", { name: "תכנית" })).toBeInTheDocument();
        expect(screen.getByRole("columnheader", { name: "מנויים פעילים" })).toBeInTheDocument();

        // Check for data rows
        const freePlanRow = screen.getByText("Free Plan").closest("tr");
        const premiumPlanRow = screen.getByText("Premium Plan").closest("tr");

        expect(freePlanRow).not.toBeNull();
        expect(premiumPlanRow).not.toBeNull();

        // Verify content within a specific row
        const { getByText } = within(freePlanRow!);
        expect(getByText("100")).toBeInTheDocument();
        expect(getByText("50%")).toBeInTheDocument();
        expect(getByText("5")).toBeInTheDocument();
    });
});
