jest.mock("@/app/actions/user-actions", () => ({
    getUserSubscriptionByUserId: jest.fn()
}));

jest.mock("@/app/actions/user-notes-actions", () => ({
    deleteUserNote: jest.fn(),
    createUserNote: jest.fn(),
    getUserNotes: jest.fn()
}));

jest.mock("@/hooks/use-user-notes", () => ({
    useUserNotes: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: ({ items, loading, error }: { items: any[]; loading: boolean; error: any }) => (
        <div data-testid="admin-table">
            {loading && <div data-testid="admin-table-loading">Loading...</div>}
            {error && <div data-testid="admin-table-error">Error: {error.message}</div>}
            {!loading && !error && (
                <div data-testid="admin-table-items">
                    {items.length === 0 ? (
                        <div>No items</div>
                    ) : (
                        items.map((item, index) => (
                            <div key={index} data-testid="admin-table-item">
                                {item.note}
                            </div>
                        ))
                    )}
                </div>
            )}
        </div>
    )
}));

jest.mock("@/components/common/empty-state", () => ({
    EmptyState: ({ message }: { message: string }) => <div data-testid="empty-state">{message}</div>
}));

jest.mock("@/components/forms/user-note-form", () => ({
    UserNoteForm: ({ reportedUserId, onSuccess }: { reportedUserId: string; onSuccess?: () => void }) => (
        <div data-testid="user-note-form" data-reported-user-id={reportedUserId}>
            User Note Form for: {reportedUserId}
            <button onClick={() => onSuccess?.()} data-testid="mock-note-submit">
                Add Note
            </button>
        </div>
    )
}));

import React from "react";
import { render, screen, waitFor } from "@testing-library/react";

import { UserQuestionsTab } from "@/app/(admin)/admin/users/[id]/components/user-questions-tab";
import { UserDocumentsTab } from "@/app/(admin)/admin/users/[id]/components/user-documents-tab";
import { UserPackageTab } from "@/app/(admin)/admin/users/[id]/components/user-package-tab";
import { UserNotesTab } from "@/app/(admin)/admin/users/[id]/components/user-notes-tab";
import { getUserSubscriptionByUserId } from "@/app/actions/user-actions";
import { useUserNotes } from "@/hooks/use-user-notes";
import { TEXTS } from "@/lib/user-constants";
import type { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

jest.mock("@/components/subscriptions/admin-subscriptions-table", () => ({
    AdminSubscriptionsTable: ({
        targetUserId,
        currentSubscription
    }: {
        targetUserId: string;
        currentSubscription?: UserSubscriptionWithPlan | null;
    }) => (
        <div data-testid="admin-subscriptions-table">
            <div>מצב אדמין - כל התוכניות ללא עלות</div>
            <div>Admin Subscriptions Table for user: {targetUserId}</div>
            {currentSubscription && <div>Current subscription: {currentSubscription.planType}</div>}
        </div>
    )
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text?: string }) => (
        <div data-testid="loading-icon">{text && <span data-testid="loading-text">{text}</span>}</div>
    )
}));

const mockGetUserSubscriptionByUserId = getUserSubscriptionByUserId as jest.MockedFunction<
    typeof getUserSubscriptionByUserId
>;

const mockUseUserNotes = useUserNotes as jest.MockedFunction<typeof useUserNotes>;

jest.mock("@/components/forms/dynamic-questions-form/dynamic-questions-form", () => ({
    DynamicQuestionsForm: ({
        overrideUserId,
        onSubmitEnd
    }: {
        overrideUserId: string;
        onSubmitEnd: (success: boolean) => void;
    }) => (
        <div data-testid="dynamic-questions-form" data-user-id={overrideUserId}>
            Dynamic Questions Form for user: {overrideUserId}
            <button onClick={() => onSubmitEnd(true)} data-testid="mock-submit">
                Submit
            </button>
        </div>
    )
}));

jest.mock("@/components/forms/dynamic-document-upload-form", () => ({
    DynamicDocumentUploadForm: ({
        overrideUserId,
        onUploadComplete
    }: {
        overrideUserId: string;
        onUploadComplete: () => void;
    }) => (
        <div data-testid="dynamic-document-upload-form" data-user-id={overrideUserId}>
            Dynamic Document Upload Form for user: {overrideUserId}
            <button onClick={() => onUploadComplete()} data-testid="mock-upload">
                Upload
            </button>
        </div>
    )
}));

describe("User Tab Components", () => {
    const mockUserId = "user-123";

    describe("UserQuestionsTab", () => {
        it("renders the questions tab title", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            expect(screen.getByText(TEXTS.questionsTab)).toBeInTheDocument();
        });

        it("renders the dynamic questions form", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            expect(screen.getByTestId("dynamic-questions-form")).toBeInTheDocument();
            expect(screen.getByText(`Dynamic Questions Form for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("passes correct user ID to the form", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            const form = screen.getByTestId("dynamic-questions-form");
            expect(form).toHaveAttribute("data-user-id", mockUserId);
        });

        it("renders within a card component", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            const title = screen.getByText(TEXTS.questionsTab);
            const card = title.closest('[class*="card"]');
            expect(card).toBeInTheDocument();
        });

        it("uses small muted styling for user ID", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            expect(screen.getByTestId("dynamic-questions-form")).toBeInTheDocument();
        });

        it("accepts different user IDs and passes them to form", () => {
            const differentUserId = "different-user-456";
            render(<UserQuestionsTab userId={differentUserId} />);

            const form = screen.getByTestId("dynamic-questions-form");
            expect(form).toHaveAttribute("data-user-id", differentUserId);
        });

        it("renders form submit functionality", () => {
            render(<UserQuestionsTab userId={mockUserId} />);
            expect(screen.getByTestId("mock-submit")).toBeInTheDocument();
        });
    });

    describe("UserDocumentsTab", () => {
        it("renders the documents tab title", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            expect(screen.getByText(TEXTS.documentsTab)).toBeInTheDocument();
        });

        it("renders the dynamic document upload form", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            expect(screen.getByTestId("dynamic-document-upload-form")).toBeInTheDocument();
            expect(screen.getByText(`Dynamic Document Upload Form for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("passes correct user ID to the form", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            const form = screen.getByTestId("dynamic-document-upload-form");
            expect(form).toHaveAttribute("data-user-id", mockUserId);
        });

        it("shows the user ID in form content", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            expect(screen.getByText(`Dynamic Document Upload Form for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("renders within a card component", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            const title = screen.getByText(TEXTS.documentsTab);
            const card = title.closest('[class*="card"]');
            expect(card).toBeInTheDocument();
        });

        it("displays user ID within form component", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            const formElement = screen.getByTestId("dynamic-document-upload-form");
            expect(formElement).toHaveAttribute("data-user-id", mockUserId);
        });

        it("accepts different user IDs and passes them to form", () => {
            const differentUserId = "different-user-456";
            render(<UserDocumentsTab userId={differentUserId} />);

            const form = screen.getByTestId("dynamic-document-upload-form");
            expect(form).toHaveAttribute("data-user-id", differentUserId);
        });

        it("renders form upload functionality", () => {
            render(<UserDocumentsTab userId={mockUserId} />);
            expect(screen.getByTestId("mock-upload")).toBeInTheDocument();
        });
    });

    describe("UserPackageTab", () => {
        const mockUserId = "user-123";

        beforeEach(() => {
            jest.clearAllMocks();
        });

        it("renders with loading state initially", () => {
            render(<UserPackageTab userId={mockUserId} />);

            expect(screen.getByText(TEXTS.packageTab)).toBeInTheDocument();
            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByTestId("loading-text")).toHaveTextContent("טוען פרטי תוכנית...");
        });

        it("displays error state when subscription fetch fails", async () => {
            mockGetUserSubscriptionByUserId.mockResolvedValue({
                success: false,
                error: "Failed to fetch subscription"
            });

            render(<UserPackageTab userId={mockUserId} />);

            await waitFor(() => {
                expect(screen.getByText("Failed to fetch subscription")).toBeInTheDocument();
            });
        });

        it("renders AdminSubscriptionsTable when subscription loads successfully", async () => {
            const mockSubscription = {
                id: "sub-123",
                user_id: mockUserId,
                plan_id: "milgapro",
                planType: "milgapro" as const,
                is_active: true,
                start_date: "2023-01-01",
                expiration_date: "2024-01-01",
                created_at: "2023-01-01",
                updated_at: "2023-01-01",
                coupon_id: null,
                plan_price: 100,
                paid_amount: 100,
                payment_details: null,
                transaction_id: null,
                order_id: null
            };

            mockGetUserSubscriptionByUserId.mockResolvedValue({
                success: true,
                subscription: mockSubscription
            });

            render(<UserPackageTab userId={mockUserId} />);

            await waitFor(() => {
                expect(screen.getByText("מצב אדמין - כל התוכניות ללא עלות")).toBeInTheDocument();
            });
        });

        it("handles null subscription (no active subscription)", async () => {
            mockGetUserSubscriptionByUserId.mockResolvedValue({
                success: true,
                subscription: null
            });

            render(<UserPackageTab userId={mockUserId} />);

            await waitFor(() => {
                expect(screen.getByText("מצב אדמין - כל התוכניות ללא עלות")).toBeInTheDocument();
            });
        });

        it("handles unexpected errors gracefully", async () => {
            mockGetUserSubscriptionByUserId.mockRejectedValue(new Error("Network error"));

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            render(<UserPackageTab userId={mockUserId} />);

            await waitFor(() => {
                expect(screen.getByText("An unexpected error occurred")).toBeInTheDocument();
            });

            expect(consoleSpy).toHaveBeenCalledWith("Error fetching user subscription:", expect.any(Error));

            consoleSpy.mockRestore();
        });

        it("refetches subscription when userId changes", async () => {
            mockGetUserSubscriptionByUserId.mockResolvedValue({
                success: true,
                subscription: null
            });

            const { rerender } = render(<UserPackageTab userId="user-1" />);

            await waitFor(() => {
                expect(mockGetUserSubscriptionByUserId).toHaveBeenCalledWith("user-1");
            });

            rerender(<UserPackageTab userId="user-2" />);

            await waitFor(() => {
                expect(mockGetUserSubscriptionByUserId).toHaveBeenCalledWith("user-2");
            });

            expect(mockGetUserSubscriptionByUserId).toHaveBeenCalledTimes(2);
        });

        it("does not fetch when userId is empty", () => {
            render(<UserPackageTab userId="" />);

            expect(mockGetUserSubscriptionByUserId).not.toHaveBeenCalled();
        });
    });

    describe("UserNotesTab", () => {
        beforeEach(() => {
            jest.clearAllMocks();
            mockUseUserNotes.mockReturnValue({
                notes: [],
                loading: false,
                error: null,
                refetch: jest.fn()
            });
        });

        it("renders the notes page title", () => {
            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByText("הערות משתמש")).toBeInTheDocument();
        });

        it("renders the user note form", () => {
            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByTestId("user-note-form")).toBeInTheDocument();
            expect(screen.getByText(`User Note Form for: ${mockUserId}`)).toBeInTheDocument();
        });

        it("shows empty state when no notes exist", () => {
            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
            expect(screen.getByText("לא נמצאו הערות")).toBeInTheDocument();
        });

        it("renders admin table when notes exist", () => {
            const mockNotes = [
                {
                    id: "note-1",
                    user_id: "reporter-1",
                    reported_user_id: mockUserId,
                    note: "Test note 1",
                    created_at: "2023-01-01T00:00:00Z",
                    updated_at: "2023-01-01T00:00:00Z",
                    reporter_email: "<EMAIL>"
                }
            ];

            mockUseUserNotes.mockReturnValue({
                notes: mockNotes,
                loading: false,
                error: null,
                refetch: jest.fn()
            });

            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByTestId("admin-table")).toBeInTheDocument();
            expect(screen.getByText("Test note 1")).toBeInTheDocument();
        });

        it("shows loading state when notes are loading", () => {
            mockUseUserNotes.mockReturnValue({
                notes: [],
                loading: true,
                error: null,
                refetch: jest.fn()
            });

            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByTestId("admin-table-loading")).toBeInTheDocument();
        });

        it("shows error state when there's an error", () => {
            const mockError = new Error("Failed to load notes");
            mockUseUserNotes.mockReturnValue({
                notes: [],
                loading: false,
                error: mockError,
                refetch: jest.fn()
            });

            render(<UserNotesTab userId={mockUserId} />);
            expect(screen.getByTestId("admin-table-error")).toBeInTheDocument();
            expect(screen.getByText("Error: Failed to load notes")).toBeInTheDocument();
        });

        it("passes correct userId to useUserNotes hook", () => {
            render(<UserNotesTab userId={mockUserId} />);
            expect(mockUseUserNotes).toHaveBeenCalledWith(mockUserId);
        });

        it("renders within a card component", () => {
            render(<UserNotesTab userId={mockUserId} />);
            const title = screen.getByText("הערות משתמש");
            const card = title.closest('[class*="card"]');
            expect(card).toBeInTheDocument();
        });
    });

    describe("Tab Components Constants Integration", () => {
        it("all tabs use centralized constants for titles", () => {
            render(
                <div>
                    <UserQuestionsTab userId={mockUserId} />
                    <UserDocumentsTab userId={mockUserId} />
                    <UserPackageTab userId={mockUserId} />
                    <UserNotesTab userId={mockUserId} />
                </div>
            );

            expect(screen.getByText(TEXTS.questionsTab)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.documentsTab)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.packageTab)).toBeInTheDocument();
            expect(screen.getByText("הערות משתמש")).toBeInTheDocument();
        });

        it("package tab uses AdminSubscriptionsTable and notes tab uses form and table", () => {
            render(
                <div>
                    <UserPackageTab userId={mockUserId} />
                    <UserNotesTab userId={mockUserId} />
                </div>
            );

            expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
            expect(screen.getByTestId("user-note-form")).toBeInTheDocument();
            expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        });

        it("documents tab uses dynamic form instead of placeholder content", () => {
            render(<UserDocumentsTab userId={mockUserId} />);

            expect(screen.getByTestId("dynamic-document-upload-form")).toBeInTheDocument();
            expect(screen.queryByText(TEXTS.documentsContent)).not.toBeInTheDocument();
        });

        it("questions tab uses dynamic form instead of placeholder content", () => {
            render(<UserQuestionsTab userId={mockUserId} />);

            expect(screen.getByTestId("dynamic-questions-form")).toBeInTheDocument();
            expect(screen.queryByText(TEXTS.questionsContent)).not.toBeInTheDocument();
        });
    });

    describe("Component Props", () => {
        it("questions, documents, and notes components handle empty user ID", () => {
            const emptyUserId = "";

            render(
                <div>
                    <UserQuestionsTab userId={emptyUserId} />
                    <UserDocumentsTab userId={emptyUserId} />
                    <UserNotesTab userId={emptyUserId} />
                </div>
            );
        });

        it("questions, documents, and notes components handle long user IDs", () => {
            const longUserId = "very-long-user-id-that-should-still-display-correctly-123456789";

            render(
                <div>
                    <UserQuestionsTab userId={longUserId} />
                    <UserDocumentsTab userId={longUserId} />
                    <UserNotesTab userId={longUserId} />
                </div>
            );
        });

        it("questions, documents, and notes components handle special characters in user ID", () => {
            const specialUserId = "user-123@domain.com_test";

            render(
                <div>
                    <UserQuestionsTab userId={specialUserId} />
                    <UserDocumentsTab userId={specialUserId} />
                    <UserNotesTab userId={specialUserId} />
                </div>
            );
        });
    });

    describe("Component Structure", () => {
        it("all components have consistent card structure", () => {
            render(
                <div>
                    <div data-testid="questions-container">
                        <UserQuestionsTab userId={mockUserId} />
                    </div>
                    <div data-testid="documents-container">
                        <UserDocumentsTab userId={mockUserId} />
                    </div>
                    <div data-testid="package-container">
                        <UserPackageTab userId={mockUserId} />
                    </div>
                    <div data-testid="notes-container">
                        <UserNotesTab userId={mockUserId} />
                    </div>
                </div>
            );

            const containers = ["questions", "documents", "package", "notes"];
            containers.forEach((container) => {
                const containerElement = screen.getByTestId(`${container}-container`);
                const cards = containerElement.querySelectorAll('[class*="card"]');
                expect(cards.length).toBeGreaterThan(0);
            });
        });
    });
});
