import React from "react";
import { render, screen, fireEvent, cleanup, waitFor, act } from "@testing-library/react";
import { useRouter } from "next/navigation";

import { AuthLayout } from "@/components/auth/auth-layout";

jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

jest.mock("@/components/auth/auth-footer", () => ({
    AuthFooter: () => <div data-testid="auth-footer">Auth Footer</div>
}));

const mockPush = jest.fn();
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe("AuthLayout", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
        mockUseRouter.mockReturnValue({
            push: mockPush,
            back: jest.fn(),
            forward: jest.fn(),
            refresh: jest.fn(),
            replace: jest.fn(),
            prefetch: jest.fn()
        } as any);
    });

    afterEach(() => {
        cleanup();
        jest.runOnlyPendingTimers();
        jest.useRealTimers();
    });

    it("should render children correctly", () => {
        render(
            <AuthLayout>
                <div data-testid="test-child">Test Child</div>
            </AuthLayout>
        );

        expect(screen.getByTestId("test-child")).toBeInTheDocument();
        expect(screen.getByTestId("auth-footer")).toBeInTheDocument();
    });

    it("should render an error message when the error prop is provided", () => {
        const errorMessage = "Something went wrong";
        render(
            <AuthLayout error={errorMessage}>
                <div>Test</div>
            </AuthLayout>
        );

        expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it("should not render an error message when the error prop is not provided", () => {
        render(
            <AuthLayout>
                <div>Test</div>
            </AuthLayout>
        );

        expect(screen.queryByRole("alert")).not.toBeInTheDocument();
    });

    it("should render the back button and navigate on click when backButtonHref is provided", () => {
        render(
            <AuthLayout backButtonHref="/home">
                <div>Test</div>
            </AuthLayout>
        );

        const backButton = screen.getByRole("button", { name: /back/i });
        expect(backButton).toBeInTheDocument();

        fireEvent.click(backButton);
        expect(mockPush).toHaveBeenCalledWith("/home");
    });

    it("should render the back button and call the callback on click when backButtonCallback is provided", () => {
        const callback = jest.fn();
        render(
            <AuthLayout backButtonCallback={callback}>
                <div>Test</div>
            </AuthLayout>
        );

        const backButton = screen.getByRole("button", { name: /back/i });
        expect(backButton).toBeInTheDocument();

        fireEvent.click(backButton);
        expect(callback).toHaveBeenCalled();
    });

    it("should not render the back button when neither href nor callback is provided", () => {
        render(
            <AuthLayout>
                <div>Test</div>
            </AuthLayout>
        );

        expect(screen.queryByRole("button", { name: /back/i })).not.toBeInTheDocument();
    });

    it("should disable the back button when isSubmitting is true", () => {
        render(
            <AuthLayout backButtonHref="/home" isSubmitting={true}>
                <div>Test</div>
            </AuthLayout>
        );

        const backButton = screen.getByRole("button", { name: /back/i });
        expect(backButton).toBeDisabled();
    });

    describe("Skeleton Loading", () => {
        it("should show skeleton when showSkeleton is true", () => {
            render(
                <AuthLayout showSkeleton={true}>
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Should show skeleton initially
            expect(screen.queryByTestId("test-child")).not.toBeInTheDocument();
            expect(screen.queryByTestId("auth-footer")).not.toBeInTheDocument();
        });

        it("should show signin skeleton by default", () => {
            const { container } = render(
                <AuthLayout showSkeleton={true}>
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Signin skeleton should not have signup-specific elements
            const skeletonElements = container.querySelectorAll(".animate-pulse");
            expect(skeletonElements.length).toBeGreaterThan(0);
        });

        it("should show signup skeleton when skeletonMode is signup", () => {
            const { container } = render(
                <AuthLayout showSkeleton={true} skeletonMode="signup">
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Signup skeleton should have more elements (terms checkboxes)
            const skeletonElements = container.querySelectorAll(".animate-pulse");
            expect(skeletonElements.length).toBeGreaterThan(0);
        });

        it("should hide skeleton and show content after timeout", async () => {
            render(
                <AuthLayout showSkeleton={true}>
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Initially should show skeleton
            expect(screen.queryByTestId("test-child")).not.toBeInTheDocument();

            // Fast-forward through the loading timer
            act(() => {
                jest.advanceTimersByTime(500);
            });

            // Wait for the component to update
            await waitFor(() => {
                expect(screen.getByTestId("test-child")).toBeInTheDocument();
            });

            expect(screen.getByTestId("auth-footer")).toBeInTheDocument();
        });

        it("should not show back button during skeleton loading", () => {
            render(
                <AuthLayout showSkeleton={true} backButtonHref="/home">
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            expect(screen.queryByRole("button", { name: /back/i })).not.toBeInTheDocument();
        });

        it("should show back button after skeleton loading completes", async () => {
            render(
                <AuthLayout showSkeleton={true} backButtonHref="/home">
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Fast-forward through the loading timer
            act(() => {
                jest.advanceTimersByTime(500);
            });

            await waitFor(() => {
                expect(screen.getByRole("button", { name: /back/i })).toBeInTheDocument();
            });
        });

        it("should not show error during skeleton loading", () => {
            render(
                <AuthLayout showSkeleton={true} error="Test error">
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            expect(screen.queryByText("Test error")).not.toBeInTheDocument();
        });

        it("should show error after skeleton loading completes", async () => {
            const { container } = render(
                <AuthLayout showSkeleton={true} error="Test error">
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Error should not show during skeleton loading
            expect(screen.queryByText("Test error")).toBeNull();

            // Wait for skeleton to complete
            act(() => {
                jest.advanceTimersByTime(500);
            });
            await waitFor(() => {
                expect(screen.getByText("Test error")).toBeInTheDocument();
            });
        });

        it("should render skeleton logo when showSkeleton is true", () => {
            const { container } = render(
                <AuthLayout showSkeleton={true}>
                    <div data-testid="test-child">Test Child</div>
                </AuthLayout>
            );

            // Should have logo skeleton (h-12 w-32 class combination is unique to logo skeleton)
            const logoSkeleton = container.querySelector(".h-12.w-32");
            expect(logoSkeleton).toBeInTheDocument();
            expect(logoSkeleton).toHaveClass("animate-pulse");
        });
    });
});
