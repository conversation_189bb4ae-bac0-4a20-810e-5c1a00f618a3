import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { OTPVerification } from "@/components/auth/otp-verification";
import { TEXTS } from "@/lib/auth-constants";

jest.mock("@clerk/elements/common", () => ({
    Field: ({ children }: { children: React.ReactNode }) => <div data-testid="clerk-field">{children}</div>,
    Label: ({ children }: { children: React.ReactNode }) => <label data-testid="clerk-label">{children}</label>,
    Input: ({
        render,
        ...props
    }: {
        render: (args: any) => React.ReactNode;
        type?: string;
        autoSubmit?: boolean;
        className?: string;
    }) => {
        const mockValue = "123456";
        const mockStatus = "idle";

        if (render) {
            return <div data-testid="clerk-input">{render({ value: mockValue, status: mockStatus })}</div>;
        }

        return <input data-testid="clerk-input" {...props} />;
    },
    FieldError: ({ className }: { className?: string }) => (
        <div data-testid="clerk-field-error" className={className} />
    ),
    Loading: ({ children }: { children: (isLoading: boolean) => React.ReactNode }) => {
        return <div data-testid="clerk-loading">{children(false)}</div>;
    }
}));

jest.mock("@clerk/elements/sign-in", () => ({
    Action: ({
        children,
        asChild,
        submit,
        resend,
        fallback,
        className
    }: {
        children: React.ReactNode;
        asChild?: boolean;
        submit?: boolean;
        resend?: boolean;
        fallback?: (args: any) => React.ReactNode;
        className?: string;
    }) => {
        if (resend && fallback) {
            return (
                <div data-testid="signin-action-resend" className={className}>
                    {fallback({ resendableAfter: 30 })}
                </div>
            );
        }
        return <button data-testid="signin-action">{children}</button>;
    }
}));

jest.mock("@clerk/elements/sign-up", () => ({
    Action: ({
        children,
        asChild,
        submit,
        resend,
        fallback,
        className
    }: {
        children: React.ReactNode;
        asChild?: boolean;
        submit?: boolean;
        resend?: boolean;
        fallback?: (args: any) => React.ReactNode;
        className?: string;
    }) => {
        if (resend && fallback) {
            return (
                <div data-testid="signup-action-resend" className={className}>
                    {fallback({ resendableAfter: 30 })}
                </div>
            );
        }
        return <button data-testid="signup-action">{children}</button>;
    }
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({
        children,
        disabled,
        variant,
        size,
        className,
        ...props
    }: {
        children: React.ReactNode;
        disabled?: boolean;
        variant?: string;
        size?: string;
        className?: string;
    }) => {
        const testId = variant === "link" ? "resend-button" : "verify-button";
        return (
            <button data-testid={testId} disabled={disabled} className={className} {...props}>
                {children}
            </button>
        );
    }
}));

jest.mock("@/components/ui/card", () => ({
    Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="card" className={className}>
            {children}
        </div>
    ),
    CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="card-content" className={className}>
            {children}
        </div>
    ),
    CardDescription: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="card-description" className={className}>
            {children}
        </div>
    ),
    CardFooter: ({ children }: { children: React.ReactNode }) => <div data-testid="card-footer">{children}</div>,
    CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="card-header" className={className}>
            {children}
        </div>
    ),
    CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
        <div data-testid="card-title" className={className}>
            {children}
        </div>
    )
}));

describe("OTPVerification", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    const defaultProps = {
        type: "signin" as const,
        title: "Test Title",
        description: "Test Description",
        verifyButtonText: TEXTS.verify,
        resendText: TEXTS.resendCode,
        resendCountdownText: TEXTS.resendCodeCountdown,
        isGlobalLoading: false,
        customErrors: <div data-testid="custom-errors"></div>
    };

    describe("Component Rendering", () => {
        it("should render OTPVerification component correctly", () => {
            render(<OTPVerification {...defaultProps} />);

            expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
            expect(screen.getByText(defaultProps.description)).toBeInTheDocument();
            expect(screen.getByTestId("clerk-field")).toBeInTheDocument();
            expect(screen.getByTestId("clerk-label")).toBeInTheDocument();
            expect(screen.getByTestId("clerk-input")).toBeInTheDocument();
            expect(screen.getByTestId("clerk-field-error")).toBeInTheDocument();
            expect(screen.getByTestId("custom-errors")).toBeInTheDocument();
        });

        it("should render card structure correctly", () => {
            render(<OTPVerification {...defaultProps} />);

            expect(screen.getByTestId("card")).toBeInTheDocument();
            expect(screen.getByTestId("card-header")).toBeInTheDocument();
            expect(screen.getByTestId("card-title")).toBeInTheDocument();
            expect(screen.getByTestId("card-description")).toBeInTheDocument();
            expect(screen.getByTestId("card-content")).toBeInTheDocument();
            expect(screen.getByTestId("card-footer")).toBeInTheDocument();
        });

        it("should render OTP input with correct structure", () => {
            render(<OTPVerification {...defaultProps} />);

            const input = screen.getByTestId("clerk-input");
            expect(input).toBeInTheDocument();
            expect(input).toHaveTextContent("123456");
        });

        it("should render label with correct text", () => {
            render(<OTPVerification {...defaultProps} />);

            const label = screen.getByTestId("clerk-label");
            expect(label).toBeInTheDocument();
            expect(label).toHaveTextContent(TEXTS.emailOtpLabel);
        });

        it("should render custom errors when provided", () => {
            const customErrors = <div data-testid="custom-errors">Custom error message</div>;
            render(<OTPVerification {...defaultProps} customErrors={customErrors} />);

            expect(screen.getByTestId("custom-errors")).toBeInTheDocument();
            expect(screen.getByText("Custom error message")).toBeInTheDocument();
        });

        it("should not render custom errors when not provided", () => {
            const { customErrors, ...propsWithoutErrors } = defaultProps;
            render(<OTPVerification {...propsWithoutErrors} />);

            expect(screen.queryByTestId("custom-errors")).not.toBeInTheDocument();
        });
    });

    describe("Sign In Mode", () => {
        it("should use signin action component for signin type", () => {
            render(<OTPVerification {...defaultProps} type="signin" />);

            expect(screen.getByTestId("signin-action")).toBeInTheDocument();
            expect(screen.queryByTestId("signup-action")).not.toBeInTheDocument();
        });

        it("should display correct loading state for signin", () => {
            render(<OTPVerification {...defaultProps} type="signin" />);

            expect(screen.getByTestId("clerk-loading")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.verify)).toBeInTheDocument();
        });

        it("should display resend functionality for signin", () => {
            render(<OTPVerification {...defaultProps} type="signin" />);

            expect(screen.getByTestId("signin-action-resend")).toBeInTheDocument();
            expect(screen.getByText(/לא קיבלת קוד/)).toBeInTheDocument();
        });
    });

    describe("Sign Up Mode", () => {
        it("should use signup action component for signup type", () => {
            render(<OTPVerification {...defaultProps} type="signup" />);

            expect(screen.getByTestId("signup-action")).toBeInTheDocument();
            expect(screen.queryByTestId("signin-action")).not.toBeInTheDocument();
        });

        it("should display correct loading state for signup", () => {
            render(<OTPVerification {...defaultProps} type="signup" />);

            expect(screen.getByTestId("clerk-loading")).toBeInTheDocument();
            expect(screen.getByText(TEXTS.verify)).toBeInTheDocument();
        });

        it("should display resend functionality for signup", () => {
            render(<OTPVerification {...defaultProps} type="signup" />);

            expect(screen.getByTestId("signup-action-resend")).toBeInTheDocument();
            expect(screen.getByText(/לא קיבלת קוד/)).toBeInTheDocument();
        });
    });

    describe("Loading States", () => {
        it("should disable verify button when isGlobalLoading is true", () => {
            render(<OTPVerification {...defaultProps} isGlobalLoading={true} />);

            const verifyButton = screen.getByTestId("verify-button");
            expect(verifyButton).toHaveAttribute("disabled");
        });

        it("should enable verify button when isGlobalLoading is false", () => {
            render(<OTPVerification {...defaultProps} isGlobalLoading={false} />);

            const verifyButton = screen.getByTestId("verify-button");
            expect(verifyButton).not.toHaveAttribute("disabled");
        });

        it("should show verify button text when not loading", () => {
            render(<OTPVerification {...defaultProps} />);

            expect(screen.getByText(TEXTS.verify)).toBeInTheDocument();
        });
    });

    describe("Resend Functionality", () => {
        it("should show resend countdown with correct time", () => {
            render(<OTPVerification {...defaultProps} />);

            const resendContainer = screen.getByTestId(`${defaultProps.type}-action-resend`);
            expect(resendContainer).toBeInTheDocument();
            expect(screen.getByText("30")).toBeInTheDocument();
        });

        it("should display resend countdown structure", () => {
            render(<OTPVerification {...defaultProps} />);

            expect(screen.getByText(/לא קיבלת קוד/)).toBeInTheDocument();
            expect(screen.getByText("30")).toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("should have proper accessibility structure", () => {
            render(<OTPVerification {...defaultProps} />);

            const label = screen.getByTestId("clerk-label");
            expect(label).toBeInTheDocument();
            expect(label).toHaveTextContent(TEXTS.emailOtpLabel);
        });

        it("should have proper card structure for screen readers", () => {
            render(<OTPVerification {...defaultProps} />);

            const header = screen.getByTestId("card-header");
            const title = screen.getByTestId("card-title");
            const description = screen.getByTestId("card-description");

            expect(header).toHaveClass("text-center");
            expect(title).toHaveClass("text-right");
            expect(description).toHaveClass("text-right");
        });
    });

    describe("CSS Classes", () => {
        it("should apply correct CSS classes to card", () => {
            render(<OTPVerification {...defaultProps} />);

            const card = screen.getByTestId("card");
            expect(card).toHaveClass("w-full", "sm:w-96");
        });

        it("should apply correct CSS classes to content", () => {
            render(<OTPVerification {...defaultProps} />);

            const content = screen.getByTestId("card-content");
            expect(content).toHaveClass("grid", "gap-y-4");
        });

        it("should apply correct CSS classes to field error", () => {
            render(<OTPVerification {...defaultProps} />);

            const fieldError = screen.getByTestId("clerk-field-error");
            expect(fieldError).toHaveClass("block", "text-sm", "text-destructive", "text-center");
        });
    });
});
