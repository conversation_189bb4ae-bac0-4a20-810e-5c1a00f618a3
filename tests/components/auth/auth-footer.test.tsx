import React from "react";
import { render, screen, cleanup } from "@testing-library/react";

import { AuthFooter } from "@/components/auth/auth-footer";

describe("AuthFooter", () => {
    afterEach(cleanup);

    it("should render copyright text correctly", () => {
        render(<AuthFooter />);

        const copyrightText = screen.getByText(/כל הזכויות שמורות Mi<PERSON>po בע״מ/);
        expect(copyrightText).toBeInTheDocument();
    });

    it("should display the current year dynamically", () => {
        const currentYear = new Date().getFullYear();
        render(<AuthFooter />);

        const yearText = screen.getByText(new RegExp(currentYear.toString()));
        expect(yearText).toBeInTheDocument();
    });

    it("should render the complete copyright message with year", () => {
        const currentYear = new Date().getFullYear();
        render(<AuthFooter />);

        const completeText = screen.getByText(`כל הזכויות שמורות Milgapo בע״מ ${currentYear}`);
        expect(completeText).toBeInTheDocument();
    });

    it("should have proper styling classes", () => {
        const { container } = render(<AuthFooter />);

        const footerDiv = container.querySelector("div");
        expect(footerDiv).toHaveClass("text-center", "text-xs", "text-muted-foreground");
    });
});
