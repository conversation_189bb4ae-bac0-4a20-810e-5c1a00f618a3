import { render, screen, fireEvent } from "@testing-library/react";
import { toast } from "sonner";
import { SocialSignInButton } from "@/components/auth/social-signin-button";

jest.mock("sonner", () => ({
    toast: {
        error: jest.fn()
    }
}));

jest.mock("@clerk/elements/common", () => ({
    Connection: ({ children, name }: { children: React.ReactNode; name: string }) => (
        <div data-testid={`clerk-connection-${name}`}>{children}</div>
    ),
    Loading: ({ children, scope }: { children: (loading: boolean) => React.ReactNode; scope: string }) => (
        <div data-testid={`clerk-loading-${scope}`}>{children(false)}</div>
    )
}));

describe("SocialSignInButton", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Google Provider", () => {
        it("should render Google button with correct styling", () => {
            render(<SocialSignInButton provider="google">Sign in with Google</SocialSignInButton>);

            expect(screen.getByText("Sign in with Google")).toBeInTheDocument();
            expect(screen.getByTestId("clerk-connection-google")).toBeInTheDocument();

            const button = screen.getByRole("button");
            expect(button).toHaveClass("bg-white", "border-gray-300", "text-gray-700");
        });

        it("should show loading state for Google", () => {
            render(
                <SocialSignInButton provider="google" loading>
                    Sign in with Google
                </SocialSignInButton>
            );

            expect(screen.getByTestId("clerk-loading-provider:google")).toBeInTheDocument();
        });
    });

    describe("Facebook Provider", () => {
        it("should render Facebook button with correct styling", () => {
            render(<SocialSignInButton provider="facebook">Sign in with Facebook</SocialSignInButton>);

            expect(screen.getByText("Sign in with Facebook")).toBeInTheDocument();
            expect(screen.getByTestId("clerk-connection-facebook")).toBeInTheDocument();

            const button = screen.getByRole("button");
            expect(button).toHaveClass("bg-[#1877F2]", "border-[#1877F2]", "text-white");
        });

        it("should show loading state for Facebook", () => {
            render(
                <SocialSignInButton provider="facebook" loading>
                    Sign in with Facebook
                </SocialSignInButton>
            );

            expect(screen.getByTestId("clerk-loading-provider:facebook")).toBeInTheDocument();
        });
    });

    describe("Terms Validation", () => {
        it("should render normal button without terms validation (handled by parent)", () => {
            render(<SocialSignInButton provider="google">Sign up with Google</SocialSignInButton>);

            expect(screen.getByTestId("clerk-connection-google")).toBeInTheDocument();

            const button = screen.getByRole("button");
            expect(button).not.toHaveClass("opacity-50", "cursor-not-allowed");
        });

        it("should render normal button for Facebook without terms validation", () => {
            render(<SocialSignInButton provider="facebook">Sign up with Facebook</SocialSignInButton>);

            expect(screen.getByTestId("clerk-connection-facebook")).toBeInTheDocument();

            const button = screen.getByRole("button");
            expect(button).not.toHaveClass("opacity-50", "cursor-not-allowed");
        });
    });

    describe("Disabled State", () => {
        it("should pass disabled prop to Clerk.Connection button", () => {
            render(
                <SocialSignInButton provider="google" disabled={true}>
                    Sign in with Google
                </SocialSignInButton>
            );

            const button = screen.getByRole("button");
            expect(button).toBeDisabled();
        });

        it("should not show disabled styling when terms are required but accepted", () => {
            render(
                <SocialSignInButton provider="google" disabled={false}>
                    Sign up with Google
                </SocialSignInButton>
            );

            const button = screen.getByRole("button");
            expect(button).not.toHaveClass("opacity-50", "cursor-not-allowed");
            expect(button).not.toBeDisabled();
        });
    });

    describe("Custom Styling", () => {
        it("should apply custom className", () => {
            render(
                <SocialSignInButton provider="google" className="custom-class">
                    Sign in with Google
                </SocialSignInButton>
            );

            const button = screen.getByRole("button");
            expect(button).toHaveClass("custom-class");
        });
    });
});
