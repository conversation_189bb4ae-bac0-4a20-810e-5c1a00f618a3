import React from "react";
import { render, screen, fireEvent, cleanup, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "sonner";

import { AuthForm, TEXTS } from "@/components/auth/auth-form";

jest.mock("@clerk/nextjs", () => ({
    useSignIn: jest.fn(),
    useSignUp: jest.fn(),
    useUser: jest.fn()
}));

jest.mock("@clerk/elements/common", () => ({
    Loading: ({ children }: { children: (arg: any) => React.ReactNode }) => (
        <div data-testid="clerk-loading">{children(false)}</div>
    ),
    Field: ({ children, name }: { children: React.ReactNode; name: string }) => (
        <div data-testid="clerk-field">{children}</div>
    ),
    Label: ({ children }: { children: React.ReactNode }) => <label data-testid="clerk-label">{children}</label>,
    Input: ({ children }: { children: React.ReactNode }) => <input data-testid="clerk-input" />,
    FieldError: () => <div data-testid="clerk-field-error" />,
    Connection: ({ children, name }: { children: React.ReactNode; name: string }) => (
        <div data-testid={`clerk-connection-${name}`}>{children}</div>
    )
}));

jest.mock("@clerk/elements/sign-in", () => ({
    Root: ({ children }: { children: React.ReactNode }) => <div data-testid="signin-root">{children}</div>,
    Step: ({ children, name }: { children: React.ReactNode; name: string }) => (
        <div data-testid={`signin-step-${name}`}>{children}</div>
    ),
    Action: ({ children }: { children: React.ReactNode }) => <button data-testid="signin-action">{children}</button>,
    Strategy: ({ children }: { children: React.ReactNode }) => <div data-testid="signin-strategy">{children}</div>
}));

jest.mock("@clerk/elements/sign-up", () => ({
    Root: ({ children }: { children: React.ReactNode }) => <div data-testid="signup-root">{children}</div>,
    Step: ({ children, name }: { children: React.ReactNode; name: string }) => (
        <div data-testid={`signup-step-${name}`}>{children}</div>
    ),
    Action: ({ children }: { children: React.ReactNode }) => <button data-testid="signup-action">{children}</button>,
    Strategy: ({ children }: { children: React.ReactNode }) => <div data-testid="signup-strategy">{children}</div>
}));

jest.mock("@/app/actions/signup-actions", () => ({
    saveSignupPreferences: jest.fn().mockResolvedValue({ success: true })
}));

jest.mock("@/contexts/signup-preferences-context", () => ({
    useSignupPreferences: jest.fn()
}));

jest.mock("@/components/auth/otp-verification", () => ({
    OTPVerification: ({ type }: { type: string }) => (
        <div data-testid={`otp-verification-${type}`}>OTP Verification</div>
    )
}));

jest.mock("@/components/auth/social-signin-button", () => ({
    SocialSignInButton: ({ children, provider, onClick, disabled, loading }: any) => (
        <button
            data-testid={`social-button-${provider}`}
            onClick={onClick}
            disabled={disabled}
            className={loading ? "loading" : ""}
        >
            {children}
        </button>
    )
}));

jest.mock("sonner", () => ({
    toast: {
        error: jest.fn()
    }
}));

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => <a href={href}>{children}</a>;
});

const mockUseSignIn = require("@clerk/nextjs").useSignIn;
const mockUseSignUp = require("@clerk/nextjs").useSignUp;
const mockUseUser = require("@clerk/nextjs").useUser;
const mockUseSignupPreferences = require("@/contexts/signup-preferences-context").useSignupPreferences;

const mockSignInAuthenticateWithRedirect = jest.fn();
const mockSignUpAuthenticateWithRedirect = jest.fn();

// Mock DOM methods
Object.defineProperty(document, "querySelectorAll", {
    writable: true,
    value: jest.fn().mockReturnValue([])
});

Object.defineProperty(window, "MutationObserver", {
    writable: true,
    value: jest.fn().mockImplementation(() => ({
        observe: jest.fn(),
        disconnect: jest.fn()
    }))
});

describe("AuthForm", () => {
    beforeEach(() => {
        jest.clearAllMocks();

        mockUseSignIn.mockReturnValue({
            signIn: {
                errors: [],
                authenticateWithRedirect: mockSignInAuthenticateWithRedirect
            }
        });

        mockUseSignUp.mockReturnValue({
            signUp: {
                errors: [],
                authenticateWithRedirect: mockSignUpAuthenticateWithRedirect
            }
        });

        mockUseUser.mockReturnValue({
            user: null
        });

        mockUseSignupPreferences.mockReturnValue({
            state: {
                preferences: { acceptedTerms: false, subscribeNewsletter: true },
                isSet: false
            },
            setAcceptedTerms: jest.fn(),
            setSubscribeNewsletter: jest.fn(),
            setPreferences: jest.fn(),
            clearPreferences: jest.fn(),
            hasPreferences: jest.fn(() => false)
        });
    });

    afterEach(() => {
        cleanup();
    });

    describe("Sign In Mode", () => {
        it("should render sign in form with correct elements", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.getByTestId("signin-root")).toBeInTheDocument();
            expect(screen.getByTestId("signin-step-start")).toBeInTheDocument();
            expect(screen.getByTestId("signin-action")).toBeInTheDocument();
            expect(screen.getByTestId("social-button-google")).toBeInTheDocument();
            expect(screen.getByTestId("social-button-facebook")).toBeInTheDocument();
        });

        it("should not render signup-specific elements in signin mode", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.queryByText(TEXTS.termsLabel.before)).not.toBeInTheDocument();
            expect(screen.queryByText(TEXTS.newsletterLabel)).not.toBeInTheDocument();
        });

        it("should render correct button text for sign in", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.getByText(TEXTS.signInTitle)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signInWithGoogle)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signInWithFacebook)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signIn)).toBeInTheDocument();
        });

        it("should have correct navigation links for sign in", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.getByText(`${TEXTS.noAccount} ${TEXTS.signUpLink}`)).toBeInTheDocument();
            const link = screen.getByText(`${TEXTS.noAccount} ${TEXTS.signUpLink}`).closest("a");
            expect(link).toHaveAttribute("href", "/signup");
        });

        it("should handle social button clicks in signin mode", () => {
            render(<AuthForm mode="signin" />);

            const googleButton = screen.getByTestId("social-button-google");
            const facebookButton = screen.getByTestId("social-button-facebook");

            fireEvent.click(googleButton);
            fireEvent.click(facebookButton);

            expect(toast.error).not.toHaveBeenCalled();
        });
    });

    describe("Sign Up Mode", () => {
        it("should render sign up form with all elements", () => {
            render(<AuthForm mode="signup" />);

            expect(screen.getByText(TEXTS.createAccount)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signUpWithGoogle)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signUpWithFacebook)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.email)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.orDivider)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signUp)).toBeInTheDocument();
            expect(screen.getByText(`${TEXTS.alreadyHaveAccount} ${TEXTS.signIn}`)).toBeInTheDocument();
        });

        it("should render sign up checkboxes", () => {
            render(<AuthForm mode="signup" />);

            expect(screen.getByText(new RegExp(TEXTS.termsLabel.before))).toBeInTheDocument();
            expect(screen.getByText(new RegExp(TEXTS.newsletterLabel))).toBeInTheDocument();
        });

        it("should have 1 form field in signup mode (email only)", () => {
            render(<AuthForm mode="signup" />);

            const fields = screen.getAllByTestId("clerk-field");
            expect(fields).toHaveLength(1);
        });

        it("should show social buttons with overlay when terms not accepted", () => {
            render(<AuthForm mode="signup" />);

            const googleButton = screen.getByText(TEXTS.signUpWithGoogle).closest("button") as HTMLButtonElement;
            const facebookButton = screen.getByText(TEXTS.signUpWithFacebook).closest("button") as HTMLButtonElement;

            expect(googleButton).not.toHaveClass("opacity-50");
            expect(googleButton).not.toHaveClass("cursor-not-allowed");
            expect(facebookButton).not.toHaveClass("opacity-50");
            expect(facebookButton).not.toHaveClass("cursor-not-allowed");

            expect(screen.getByTestId("social-button-google")).toBeInTheDocument();
            expect(screen.getByTestId("social-button-facebook")).toBeInTheDocument();
        });

        it("should show toast error when clicking social buttons without terms", () => {
            render(<AuthForm mode="signup" />);

            const googleButton = screen.getByTestId("social-button-google");
            const facebookButton = screen.getByTestId("social-button-facebook");

            fireEvent.click(googleButton);
            expect(toast.error).toHaveBeenCalledWith(TEXTS.termsRequired);

            fireEvent.click(facebookButton);
            expect(toast.error).toHaveBeenCalledWith(TEXTS.termsRequired);
        });

        it("should enable social buttons when terms are accepted", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            expect(screen.getByTestId("social-button-google")).toBeInTheDocument();
            expect(screen.getByTestId("social-button-facebook")).toBeInTheDocument();
        });

        it("should show terms required error when submitting without accepting terms", () => {
            render(<AuthForm mode="signup" />);

            const submitButton = screen.getByTestId("signup-action").querySelector("button");
            if (submitButton) {
                fireEvent.click(submitButton);
            } else {
                fireEvent.click(screen.getByTestId("signup-action"));
            }

            expect(toast.error).toHaveBeenCalledWith(TEXTS.termsRequired);
        });

        it("should have correct navigation links for sign up", () => {
            render(<AuthForm mode="signup" />);

            expect(screen.getByText(`${TEXTS.alreadyHaveAccount} ${TEXTS.signIn}`)).toBeInTheDocument();
            const link = screen.getByText(`${TEXTS.alreadyHaveAccount} ${TEXTS.signIn}`).closest("a");
            expect(link).toHaveAttribute("href", "/login");
        });

        it("should handle newsletter checkbox changes", () => {
            render(<AuthForm mode="signup" />);

            const newsletterCheckbox = screen.getByRole("checkbox", { name: TEXTS.newsletterLabel });

            // Should be checked by default
            expect(newsletterCheckbox).toBeChecked();

            // Uncheck it
            fireEvent.click(newsletterCheckbox);
            expect(newsletterCheckbox).not.toBeChecked();

            // Check it again
            fireEvent.click(newsletterCheckbox);
            expect(newsletterCheckbox).toBeChecked();
        });

        it("should handle terms checkbox changes", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });

            // Should be unchecked by default
            expect(termsCheckbox).not.toBeChecked();

            // Check it
            fireEvent.click(termsCheckbox);
            expect(termsCheckbox).toBeChecked();

            // Uncheck it
            fireEvent.click(termsCheckbox);
            expect(termsCheckbox).not.toBeChecked();
        });
    });

    describe("Loading States", () => {
        it("should show loading state for Google button", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const googleButton = screen.getByTestId("social-button-google");
            fireEvent.click(googleButton);

            expect(googleButton).toHaveClass("loading");
        });

        it("should show loading state for Facebook button", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const facebookButton = screen.getByTestId("social-button-facebook");
            fireEvent.click(facebookButton);

            expect(facebookButton).toHaveClass("loading");
        });

        it("should disable other buttons when one is loading", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const googleButton = screen.getByTestId("social-button-google");
            const facebookButton = screen.getByTestId("social-button-facebook");

            fireEvent.click(googleButton);

            expect(facebookButton).toBeDisabled();
        });

        it("should reset loading states when user is authenticated", () => {
            const { rerender } = render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const googleButton = screen.getByTestId("social-button-google");
            fireEvent.click(googleButton);

            expect(googleButton).toHaveClass("loading");

            // Simulate user authentication by updating the mock
            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            // Rerender the component with the new user state
            rerender(<AuthForm mode="signup" />);

            // Get the button again after rerender
            const updatedGoogleButton = screen.getByTestId("social-button-google");
            expect(updatedGoogleButton).not.toHaveClass("loading");
        });
    });

    describe("Form Submission", () => {
        it("should handle form submission with terms accepted", async () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const submitButton = screen.getByTestId("signup-action");
            fireEvent.click(submitButton);

            expect(toast.error).not.toHaveBeenCalled();
        });

        it("should set loading state on form submission", async () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const submitButton = screen.getByTestId("signup-action");
            fireEvent.click(submitButton);

            // The loading state should be set internally
            expect(toast.error).not.toHaveBeenCalled();
        });

        it("should handle form submission in signin mode", async () => {
            render(<AuthForm mode="signin" />);

            const submitButton = screen.getByTestId("signin-action");
            fireEvent.click(submitButton);

            expect(toast.error).not.toHaveBeenCalled();
        });
    });

    describe("Preference Saving", () => {
        it("should save preferences when user is authenticated in signup mode", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            render(<AuthForm mode="signup" />);

            await waitFor(() => {
                expect(mockSaveSignupPreferences).toHaveBeenCalledWith({
                    acceptedTerms: false,
                    subscribeNewsletter: true
                });
            });
        });

        it("should save updated preferences when checkboxes change", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            const newsletterCheckbox = screen.getByRole("checkbox", { name: TEXTS.newsletterLabel });

            fireEvent.click(termsCheckbox);
            fireEvent.click(newsletterCheckbox);

            await waitFor(() => {
                expect(mockSaveSignupPreferences).toHaveBeenCalledWith({
                    acceptedTerms: true,
                    subscribeNewsletter: false
                });
            });
        });

        it("should handle preference saving errors", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;
            mockSaveSignupPreferences.mockResolvedValue({
                success: false,
                error: "Test error"
            });

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            render(<AuthForm mode="signup" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith("Test error");
            });
        });

        it("should handle preference saving exceptions", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;
            mockSaveSignupPreferences.mockRejectedValue(new Error("Network error"));

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            render(<AuthForm mode="signup" />);

            await waitFor(() => {
                expect(toast.error).toHaveBeenCalledWith(TEXTS.savePreferencesError);
            });
        });

        it("should not save preferences in signin mode", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            render(<AuthForm mode="signin" />);

            await waitFor(() => {
                expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
            });
        });
    });

    describe("Error Handling", () => {
        it("should setup MutationObserver for error handling", () => {
            render(<AuthForm mode="signup" />);

            expect(window.MutationObserver).toHaveBeenCalled();
        });

        it("should handle Hebrew error message translation", () => {
            // Mock error elements
            const mockErrorElement = {
                textContent: "email address is taken",
                className: "text-left",
                setAttribute: jest.fn()
            };

            Object.defineProperty(mockErrorElement, "textContent", {
                value: "email address is taken",
                writable: true
            });

            Object.defineProperty(mockErrorElement, "className", {
                value: "text-left",
                writable: true
            });

            (document.querySelectorAll as jest.Mock).mockReturnValue([mockErrorElement]);

            render(<AuthForm mode="signup" />);

            // MutationObserver should have been called
            expect(window.MutationObserver).toHaveBeenCalled();
        });

        it("should handle signin-specific error messages", () => {
            const mockErrorElement = {
                textContent: "password is incorrect",
                className: "text-left"
            };

            Object.defineProperty(mockErrorElement, "textContent", {
                value: "password is incorrect",
                writable: true
            });

            Object.defineProperty(mockErrorElement, "className", {
                value: "text-left",
                writable: true
            });

            (document.querySelectorAll as jest.Mock).mockReturnValue([mockErrorElement]);

            render(<AuthForm mode="signin" />);

            expect(window.MutationObserver).toHaveBeenCalled();
        });
    });

    describe("Email Field", () => {
        it("should render email field with correct attributes", () => {
            render(<AuthForm mode="signup" />);

            const emailInput = screen.getByTestId("clerk-input");
            expect(emailInput).toBeInTheDocument();
        });

        it("should use correct field names for signin and signup", () => {
            const { rerender } = render(<AuthForm mode="signin" />);
            expect(screen.getByTestId("clerk-field")).toBeInTheDocument();

            rerender(<AuthForm mode="signup" />);
            expect(screen.getByTestId("clerk-field")).toBeInTheDocument();
        });
    });

    describe("Checkbox Interactions", () => {
        it("should handle checkbox state changes correctly", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            const newsletterCheckbox = screen.getByRole("checkbox", { name: TEXTS.newsletterLabel });

            // Initial states
            expect(termsCheckbox).not.toBeChecked();
            expect(newsletterCheckbox).toBeChecked();

            // Change terms
            fireEvent.click(termsCheckbox);
            expect(termsCheckbox).toBeChecked();

            // Change newsletter
            fireEvent.click(newsletterCheckbox);
            expect(newsletterCheckbox).not.toBeChecked();
        });

        it("should handle checkbox onChange events", () => {
            render(<AuthForm mode="signup" />);

            const newsletterCheckbox = screen.getByRole("checkbox", { name: TEXTS.newsletterLabel });

            // Test onCheckedChange with true
            fireEvent.click(newsletterCheckbox);
            expect(newsletterCheckbox).not.toBeChecked();

            // Test onCheckedChange with false
            fireEvent.click(newsletterCheckbox);
            expect(newsletterCheckbox).toBeChecked();
        });
    });

    describe("Social Button Interactions", () => {
        it("should handle social button clicks without terms in signup", () => {
            render(<AuthForm mode="signup" />);

            const googleButton = screen.getByTestId("social-button-google");
            const facebookButton = screen.getByTestId("social-button-facebook");

            fireEvent.click(googleButton);
            expect(toast.error).toHaveBeenCalledWith(TEXTS.termsRequired);

            fireEvent.click(facebookButton);
            expect(toast.error).toHaveBeenCalledWith(TEXTS.termsRequired);
        });

        it("should allow social button clicks with terms accepted", () => {
            render(<AuthForm mode="signup" />);

            const termsCheckbox = screen.getByRole("checkbox", { name: new RegExp(TEXTS.termsLabel.before) });
            fireEvent.click(termsCheckbox);

            const googleButton = screen.getByTestId("social-button-google");
            const facebookButton = screen.getByTestId("social-button-facebook");

            fireEvent.click(googleButton);
            fireEvent.click(facebookButton);

            expect(toast.error).not.toHaveBeenCalledWith(TEXTS.termsRequired);
        });
    });

    describe("Common Functionality", () => {
        it("should render social sign in buttons for both modes", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.getByText(TEXTS.signInWithGoogle)).toBeInTheDocument();
            expect(screen.getByText(TEXTS.signInWithFacebook)).toBeInTheDocument();
        });

        it("should render email field for both modes", () => {
            render(<AuthForm mode="signin" />);

            const fields = screen.getAllByTestId("clerk-field");
            const inputs = screen.getAllByTestId("clerk-input");

            expect(fields).toHaveLength(1);
            expect(inputs).toHaveLength(1);
        });

        it("should render both email and password fields for sign-up mode", () => {
            render(<AuthForm mode="signup" />);

            const fields = screen.getAllByTestId("clerk-field");
            const inputs = screen.getAllByTestId("clerk-input");

            expect(fields).toHaveLength(1);
            expect(inputs).toHaveLength(1);
        });

        it("should render the divider between social and email authentication", () => {
            render(<AuthForm mode="signin" />);

            expect(screen.getByText(TEXTS.orDivider)).toBeInTheDocument();
        });

        it("should render the captcha container", () => {
            render(<AuthForm mode="signin" />);

            expect(document.querySelector("#clerk-captcha")).toBeInTheDocument();
        });

        it("should render verification steps for both modes", () => {
            render(<AuthForm mode="signin" />);
            expect(screen.getByTestId("signin-step-verifications")).toBeInTheDocument();
            expect(screen.getByTestId("otp-verification-signin")).toBeInTheDocument();
        });
    });

    describe("Cleanup and User Validation", () => {
        it("should handle user object becoming null without errors", () => {
            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            const { rerender } = render(<AuthForm mode="signup" />);

            mockUseUser.mockReturnValue({
                user: null
            });

            expect(() => {
                rerender(<AuthForm mode="signup" />);
            }).not.toThrow();
        });

        it("should validate user object properly before saving preferences", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockUseUser.mockReturnValue({
                user: { id: "" }
            });

            render(<AuthForm mode="signup" />);

            await waitFor(() => {
                expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
            });
        });

        it("should validate user object with valid string ID", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockUseUser.mockReturnValue({
                user: { id: "valid-user-id" }
            });

            render(<AuthForm mode="signup" />);

            await waitFor(() => {
                expect(mockSaveSignupPreferences).toHaveBeenCalledWith({
                    acceptedTerms: false,
                    subscribeNewsletter: true
                });
            });
        });

        it("should not save preferences for invalid user objects", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            const testCases = [
                { user: null },
                { user: undefined },
                { user: { id: null } },
                { user: { id: undefined } },
                { user: { id: "" } },
                { user: { id: 123 } },
                { user: {} }
            ];

            for (const testCase of testCases) {
                mockUseUser.mockReturnValue(testCase);
                const { unmount } = render(<AuthForm mode="signup" />);

                await waitFor(() => {
                    expect(mockSaveSignupPreferences).not.toHaveBeenCalled();
                });

                unmount();
                mockSaveSignupPreferences.mockClear();
            }
        });

        it("should handle component unmounting during async operations", async () => {
            const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;

            mockSaveSignupPreferences.mockImplementation(
                () => new Promise((resolve) => setTimeout(() => resolve({ success: false, error: "Test error" }), 100))
            );

            mockUseUser.mockReturnValue({
                user: { id: "test-user-id" }
            });

            const { unmount } = render(<AuthForm mode="signup" />);

            unmount();

            await waitFor(() => {
                expect(toast.error).not.toHaveBeenCalled();
            });
        });

        it("should cleanup MutationObserver on unmount", () => {
            const mockDisconnect = jest.fn();
            (window.MutationObserver as jest.Mock).mockImplementation(() => ({
                observe: jest.fn(),
                disconnect: mockDisconnect
            }));

            const { unmount } = render(<AuthForm mode="signup" />);
            unmount();

            expect(mockDisconnect).toHaveBeenCalled();
        });
    });
});
