import { type RenderResult, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useClerk } from "@clerk/nextjs";

import { LogoutButton } from "@/components/auth/logout-button";

jest.mock("@clerk/nextjs", () => ({
    useClerk: jest.fn()
}));

const mockSignOut = jest.fn();
const useClerkMock = useClerk as jest.Mock;

describe("LogoutButton", () => {
    let renderResult: RenderResult;

    beforeEach(() => {
        jest.clearAllMocks();
        useClerkMock.mockReturnValue({ signOut: mockSignOut });
        renderResult = render(<LogoutButton />);
    });

    it("should render the logout button correctly", () => {
        const logoutButton = screen.getByRole("button", { name: "התנתק" });
        expect(logoutButton).toBeInTheDocument();
        expect(logoutButton).toHaveClass(
            "text-sm text-gray-500 hover:text-primary hover:border-primary border border-gray-200 rounded-lg px-4 py-1 transition"
        );
    });

    it("should call signOut when the button is clicked", async () => {
        const user = userEvent.setup();
        const logoutButton = screen.getByRole("button", { name: "התנתק" });
        await user.click(logoutButton);
        expect(mockSignOut).toHaveBeenCalledTimes(1);
    });
});
