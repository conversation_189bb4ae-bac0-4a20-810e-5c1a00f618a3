import { render, screen } from "@testing-library/react";
import { useUser } from "@clerk/nextjs";
import FinalCTA from "@/components/home/<USER>";

// Type definitions for mocked Clerk user
interface MockUser {
    id: string;
    [key: string]: unknown;
}

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="link" data-href={href}>
            {children}
        </a>
    );
});

describe("FinalCTA", () => {
    beforeEach(() => {
        mockUseUser.mockClear();
    });

    it("renders with default props when user is not loaded", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: false,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        expect(screen.getByText("מוכנים להתחיל את המסע לקבלת מלגה?")).toBeInTheDocument();
        expect(screen.getByText("הרשמו עכשיו וקבלו גישה למאות מלגות מותאמות אישית עבורכם")).toBeInTheDocument();
        expect(screen.getByText("לחצו וקבלו רשימת מלגות מותאמת אישית")).toBeInTheDocument();
    });

    it("renders with custom props", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        const customTitle = "התחילו היום";
        const customDescription = "מלגות מחכות לכם";
        const customButtonText = "לחצו כאן";

        render(<FinalCTA title={customTitle} description={customDescription} buttonText={customButtonText} />);

        expect(screen.getByText(customTitle)).toBeInTheDocument();
        expect(screen.getByText(customDescription)).toBeInTheDocument();
        expect(screen.getByText("לחצו לקבלת רשימת מלגות אישית")).toBeInTheDocument();
    });

    it("shows guest text and login link when user is not authenticated", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        expect(screen.getByText("לחצו לקבלת רשימת מלגות אישית")).toBeInTheDocument();

        const link = screen.getByTestId("link");
        expect(link).toHaveAttribute("data-href", "/login");
    });

    it("shows subscription link when user is authenticated", () => {
        mockUseUser.mockReturnValue({
            user: { id: "test-user" } as MockUser,
            isLoaded: true,
            isSignedIn: true
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const link = screen.getByTestId("link");
        expect(link).toHaveAttribute("data-href", "/subscriptions");
    });

    it("shows submission-specific text when scholarship type is submission", () => {
        mockUseUser.mockReturnValue({
            user: { id: "test-user" } as MockUser,
            isLoaded: true,
            isSignedIn: true
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA scholarshipType="submission" />);

        expect(screen.getByText("להרשמה וקבלת הגשה אוטומטית")).toBeInTheDocument();
    });

    it("shows guidance-specific text when scholarship type is guidance", () => {
        mockUseUser.mockReturnValue({
            user: { id: "test-user" } as MockUser,
            isLoaded: true,
            isSignedIn: true
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA scholarshipType="guidance" />);

        expect(screen.getByText("לקבלת סיוע אישי בהגשת המלגה")).toBeInTheDocument();
    });

    it("applies correct styling to container", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const container = document.querySelector(".text-center");
        expect(container).toHaveClass(
            "bg-gradient-to-r",
            "from-blue-50",
            "to-primary/10",
            "p-10",
            "rounded-xl",
            "shadow-sm"
        );
    });

    it("applies correct styling to title", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const title = screen.getByText("מוכנים להתחיל את המסע לקבלת מלגה?");
        expect(title).toHaveClass("text-2xl", "font-bold", "mb-4", "text-gray-800");
    });

    it("applies correct styling to description", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const description = screen.getByText("הרשמו עכשיו וקבלו גישה למאות מלגות מותאמות אישית עבורכם");
        expect(description).toHaveClass("text-gray-600", "mb-6", "max-w-xl", "mx-auto");
    });

    it("applies correct styling to button", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const button = screen.getByRole("button");
        expect(button).toHaveClass(
            "bg-primary",
            "text-white",
            "px-10",
            "py-4",
            "rounded-lg",
            "text-lg",
            "font-semibold",
            "hover:bg-primary/90",
            "transition-all",
            "shadow-md",
            "hover:shadow-lg"
        );
    });

    it("shows default button text when user is authenticated but no scholarship type", () => {
        mockUseUser.mockReturnValue({
            user: { id: "test-user" } as MockUser,
            isLoaded: true,
            isSignedIn: true
        } as unknown as ReturnType<typeof useUser>);

        const customButtonText = "התחילו כאן";
        render(<FinalCTA buttonText={customButtonText} />);

        expect(screen.getByText(customButtonText)).toBeInTheDocument();
    });

    it("handles loading state properly", () => {
        mockUseUser.mockReturnValue({
            user: { id: "test-user" } as MockUser,
            isLoaded: false,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        const customButtonText = "טעינה...";
        render(<FinalCTA buttonText={customButtonText} />);

        expect(screen.getByText(customButtonText)).toBeInTheDocument();
    });

    it("renders button as child of link", () => {
        mockUseUser.mockReturnValue({
            user: null,
            isLoaded: true,
            isSignedIn: false
        } as unknown as ReturnType<typeof useUser>);

        render(<FinalCTA />);

        const link = screen.getByTestId("link");
        const button = screen.getByRole("button");

        expect(link).toContainElement(button);
    });
});
