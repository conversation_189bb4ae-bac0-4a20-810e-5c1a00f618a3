import { render, screen } from "@testing-library/react";
import Scholarships from "@/components/home/<USER>";
import { useScholarships } from "@/hooks/use-scholarships";
import { Database } from "@/types/database.types";

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"];

jest.mock("@/hooks/use-scholarships");
const mockUseScholarships = useScholarships as jest.MockedFunction<typeof useScholarships>;

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="scholarship-link" data-href={href}>
            {children}
        </a>
    );
});

describe("Scholarships", () => {
    const mockScholarships: Scholarship[] = [
        {
            id: "1",
            title: "מלגת מצוינות",
            short_description: "מלגה לסטודנטים מצטיינים",
            slug: "excellence-scholarship",
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            description: "מלגה מלאה לסטודנטים מצטיינים",
            scholarship_type: "submission" as const,
            end_date: "2024-12-31T23:59:59Z",
            start_date: "2024-01-01T00:00:00Z",
            is_active: true,
            is_public: true,
            min_amount: 5000,
            max_amount: 10000,
            requirements: ["ממוצע גבוה", "המלצות"],
            benefits: ["מימון מלא", "הנחיות אישיות"],
            url: "https://example.com/apply",
            target_audience: "undergraduate",
            image_url: null,
            internal_notes: null,
            response_date: null,
            contact_email: null,
            contact_person: null,
            contact_phone: null,
            volunteer_hours: 0
        },
        {
            id: "2",
            title: "מלגת מחקר",
            short_description: "מלגה לסטודנטים חוקרים",
            slug: "research-scholarship",
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            description: "מלגה לסטודנטים העוסקים במחקר",
            scholarship_type: "guidance" as const,
            end_date: "2024-11-30T23:59:59Z",
            start_date: "2024-01-01T00:00:00Z",
            is_active: true,
            is_public: true,
            min_amount: 10000,
            max_amount: 15000,
            requirements: ["פרויקט מחקר", "פרסום"],
            benefits: ["מימון מחקר", "ייעוץ מקצועי"],
            url: "https://example.com/apply-research",
            target_audience: "graduate",
            image_url: null,
            internal_notes: null,
            response_date: null,
            contact_email: null,
            contact_person: null,
            contact_phone: null,
            volunteer_hours: 0
        }
    ];

    const createMockReturn = (overrides: any = {}) => ({
        loadMore: jest.fn(),
        scholarships: [],
        displayedScholarships: [],
        loading: false,
        error: null,
        hasMore: false,
        totalCount: 0,
        ...overrides
    });

    beforeEach(() => {
        mockUseScholarships.mockClear();
    });

    it("renders section title", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        expect(screen.getByText("מלגות")).toBeInTheDocument();
    });

    it("renders 'view all' link", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const viewAllLink = screen.getByText("צפה בכל המלגות").closest("a");
        expect(viewAllLink).toHaveAttribute("data-href", "/scholarships");
        expect(screen.getByText("צפה בכל המלגות")).toBeInTheDocument();
    });

    it("renders scholarship items when loaded successfully", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        expect(screen.getByText("מלגת מצוינות")).toBeInTheDocument();
        expect(screen.getByText("מלגת מחקר")).toBeInTheDocument();
    });

    it("renders scholarship descriptions", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        expect(screen.getByText("מלגה לסטודנטים מצטיינים")).toBeInTheDocument();
        expect(screen.getByText("מלגה לסטודנטים חוקרים")).toBeInTheDocument();
    });

    it("renders correct links for scholarships", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const scholarshipLinks = screen.getAllByTestId("scholarship-link");

        expect(scholarshipLinks).toHaveLength(3);

        expect(scholarshipLinks[1]).toHaveAttribute("data-href", "/scholarships/excellence-scholarship");
        expect(scholarshipLinks[2]).toHaveAttribute("data-href", "/scholarships/research-scholarship");
    });

    it("renders 'discover more' text for each scholarship", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const discoverMoreElements = screen.getAllByText("גלה עוד");
        expect(discoverMoreElements).toHaveLength(2);
    });

    it("renders responsive grid layout", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const gridContainer = document.querySelector(".grid");
        expect(gridContainer).toHaveClass("grid-cols-1", "sm:grid-cols-2", "lg:grid-cols-3", "xl:grid-cols-4");
    });

    it("applies correct styling to cards", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const scholarshipLinks = screen.getAllByTestId("scholarship-link");

        const cardLinks = scholarshipLinks.filter((link) => link.getAttribute("data-href")?.includes("/scholarships/"));
        expect(cardLinks.length).toBeGreaterThan(0);

        cardLinks.forEach((link) => {
            expect(link).toBeInTheDocument();
        });
    });

    it("applies correct styling to titles", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const scholarshipTitles = [screen.getByText("מלגת מצוינות"), screen.getByText("מלגת מחקר")];

        scholarshipTitles.forEach((title) => {
            expect(title).toHaveClass("font-semibold", "text-lg", "text-gray-900", "group-hover:text-primary");
        });
    });

    it("applies correct styling to descriptions", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const descriptions = [screen.getByText("מלגה לסטודנטים מצטיינים"), screen.getByText("מלגה לסטודנטים חוקרים")];

        descriptions.forEach((description) => {
            expect(description).toHaveClass("text-sm", "text-gray-600", "line-clamp-3");
        });
    });

    it("renders icon containers with correct styling", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const iconContainers = document.querySelectorAll(".w-12.h-12");
        expect(iconContainers).toHaveLength(2);

        iconContainers.forEach((container) => {
            expect(container).toHaveClass(
                "rounded-lg",
                "bg-primary/10",
                "flex",
                "items-center",
                "justify-center",
                "text-primary"
            );
        });
    });

    it("renders section with correct container classes", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const section = document.querySelector("section");
        expect(section).toHaveClass("max-w-4xl", "mx-auto", "w-full");
    });

    it("renders section title with correct styling", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const title = screen.getByText("מלגות");
        expect(title).toHaveClass("text-2xl", "md:text-3xl", "font-bold");
    });

    it("renders header with correct layout", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const header = document.querySelector(".flex.items-center.justify-between");
        expect(header).toBeInTheDocument();
        expect(header).toHaveClass("mb-6");
    });

    it("handles empty scholarships list", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: []
            })
        );

        render(<Scholarships />);

        expect(screen.getByText("מלגות")).toBeInTheDocument();
        expect(screen.getByText("צפה בכל המלגות")).toBeInTheDocument();

        const scholarshipTitles = screen.queryByText("מלגת מצוינות");
        expect(scholarshipTitles).not.toBeInTheDocument();
    });

    it("renders view all link with arrow icon", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const viewAllLink = screen.getByText("צפה בכל המלגות").closest("a");
        expect(viewAllLink).toBeInTheDocument();
        expect(viewAllLink).toHaveAttribute("data-href", "/scholarships");
    });

    it("applies hover effects to cards", () => {
        mockUseScholarships.mockReturnValue(
            createMockReturn({
                displayedScholarships: mockScholarships
            })
        );

        render(<Scholarships />);

        const cards = document.querySelectorAll("[class*='hover:shadow-md']");
        expect(cards.length).toBeGreaterThan(0);

        cards.forEach((card) => {
            expect(card).toHaveClass("transition-all", "duration-200", "hover:border-primary/20", "hover:scale-[1.02]");
        });
    });
});
