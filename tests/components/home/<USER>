import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Contact from "@/components/home/<USER>";

const mockFetch = jest.fn();
global.fetch = mockFetch;

describe("Contact", () => {
    beforeEach(() => {
        mockFetch.mockClear();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders with default props", () => {
        render(<Contact />);

        expect(screen.getByPlaceholderText("כתובת אימייל")).toBeInTheDocument();
        expect(screen.getByRole("combobox")).toBeInTheDocument();
        expect(screen.getByPlaceholderText("תוכן ההודעה")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "שלח טופס" })).toBeInTheDocument();
    });

    it("renders with custom title and subtitle", () => {
        const title = "צור קשר";
        const subtitle = "נשמח לעזור לכם";

        render(<Contact title={title} subtitle={subtitle} />);

        expect(screen.getByText(title)).toBeInTheDocument();
        expect(screen.getByText(subtitle)).toBeInTheDocument();
    });

    it("displays subject options in select field", async () => {
        const user = userEvent.setup();
        render(<Contact />);

        const subjectField = screen.getByRole("combobox");
        await user.click(subjectField);

        expect(screen.getAllByText("שאלה כללית")[0]).toBeInTheDocument();
        expect(screen.getAllByText("מידע על מלגה ספציפית")[0]).toBeInTheDocument();
        expect(screen.getAllByText("עזרה בהגשת בקשה")[0]).toBeInTheDocument();
        expect(screen.getAllByText("הצעה לשיפור האתר")[0]).toBeInTheDocument();
        expect(screen.getByText("אחר")).toBeInTheDocument();
    });

    it("validates required fields", async () => {
        const user = userEvent.setup();
        render(<Contact />);

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        expect(screen.getByText("אימייל הוא שדה חובה")).toBeInTheDocument();
        expect(screen.getByText("נושא הוא שדה חובה")).toBeInTheDocument();
        expect(screen.getByText("הודעה היא שדה חובה")).toBeInTheDocument();
    });

    it("validates email format", async () => {
        const user = userEvent.setup();
        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        await user.type(emailField, "invalid-email");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        expect(screen.getByText("אימייל אינו תקין")).toBeInTheDocument();
    });

    it("submits form successfully", async () => {
        const user = userEvent.setup();
        mockFetch.mockResolvedValueOnce({
            ok: true,
            json: async () => ({ success: true })
        });

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        await waitFor(() => {
            expect(mockFetch).toHaveBeenCalledWith("/api/email", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    email: "<EMAIL>",
                    subject: { id: "שאלה כללית", label: "שאלה כללית" },
                    message: "Test message"
                })
            });
        });

        await waitFor(() => {
            expect(screen.getByText("הודעה נשלחה בהצלחה")).toBeInTheDocument();
        });
    });

    it("displays error message on submission failure", async () => {
        const user = userEvent.setup();
        mockFetch.mockResolvedValueOnce({
            ok: false,
            json: async () => ({ error: "Server error" })
        });

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText("Server error")).toBeInTheDocument();
        });
    });

    it("displays generic error message on network failure", async () => {
        const user = userEvent.setup();
        mockFetch.mockRejectedValueOnce(new Error("Network error"));

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText("Network error")).toBeInTheDocument();
        });
    });

    it("resets form after successful submission", async () => {
        const user = userEvent.setup();
        mockFetch.mockResolvedValueOnce({
            ok: true,
            json: async () => ({ success: true })
        });

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText("הודעה נשלחה בהצלחה")).toBeInTheDocument();
        });

        expect((emailField as HTMLInputElement).value).toBe("");
        expect((messageField as HTMLTextAreaElement).value).toBe("");
    });

    it("disables submit button during submission", async () => {
        const user = userEvent.setup();
        mockFetch.mockImplementationOnce(() => new Promise((resolve) => setTimeout(resolve, 1000)));

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        expect(submitButton).toBeDisabled();
    });

    it("clears success message when starting new submission", async () => {
        const user = userEvent.setup();
        mockFetch.mockResolvedValueOnce({
            ok: true,
            json: async () => ({ success: true })
        });

        render(<Contact />);

        const emailField = screen.getByPlaceholderText("כתובת אימייל");
        const messageField = screen.getByPlaceholderText("תוכן ההודעה");
        const subjectField = screen.getByRole("combobox");

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);

        await waitFor(() => {
            const options = screen.getAllByText("שאלה כללית");
            expect(options.length).toBeGreaterThan(0);
        });
        await user.click(screen.getAllByText("שאלה כללית")[0]);

        await user.type(messageField, "Test message");

        const submitButton = screen.getByRole("button", { name: "שלח טופס" });
        await user.click(submitButton);

        await waitFor(() => {
            expect(screen.getByText("הודעה נשלחה בהצלחה")).toBeInTheDocument();
        });

        mockFetch.mockImplementationOnce(() => new Promise((resolve) => setTimeout(resolve, 1000)));

        await user.type(emailField, "<EMAIL>");
        await user.click(subjectField);
        await user.click(screen.getByText("אחר"));
        await user.type(messageField, "Another test message");
        await user.click(submitButton);

        expect(screen.queryByText("הודעה נשלחה בהצלחה")).not.toBeInTheDocument();
    });
});
