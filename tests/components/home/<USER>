import { render, screen } from "@testing-library/react";
import Press from "@/components/home/<USER>";

// Type definitions for mocked components
interface ImageProps {
    src: string;
    alt: string;
    width: number | string;
    height: number | string;
    className?: string;
    onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    [key: string]: unknown;
}

interface LinkProps {
    children: React.ReactNode;
    href: string;
    target?: string;
    rel?: string;
    [key: string]: unknown;
}

jest.mock("next/link", () => {
    return ({ children, href, target, rel }: LinkProps) => (
        <a href={href} target={target} rel={rel} data-testid="press-link" data-href={href}>
            {children}
        </a>
    );
});

jest.mock("next/image", () => {
    return ({ src, alt, width, height, className, onError }: ImageProps) => (
        <img
            src={src}
            alt={alt}
            width={width}
            height={height}
            className={className}
            onError={onError}
            data-testid="press-image"
        />
    );
});

describe("Press", () => {
    it("renders with default title", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");
        expect(pressLinks.length).toBeGreaterThan(0);
        expect(pressLinks[0]).toBeInTheDocument();
    });

    it("renders with custom title", () => {
        const title = "עיתונות ותקשורת";
        render(<Press title={title} />);

        expect(screen.getByText(title)).toBeInTheDocument();
    });

    it("renders all press items correctly", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");
        expect(pressLinks).toHaveLength(4);

        expect(screen.getByText("Ynet")).toBeInTheDocument();
        expect(screen.getByText("Ysrael Hayom")).toBeInTheDocument();
        expect(screen.getByText("Mako")).toBeInTheDocument();
        expect(screen.getByText("Geektime")).toBeInTheDocument();
    });

    it("renders press items with correct links", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");

        expect(pressLinks[0]).toHaveAttribute("data-href", "http://www.ynet.co.il/articles/0,7340,L-4845735,00.html");
        expect(pressLinks[1]).toHaveAttribute("data-href", "http://www.israelhayom.co.il/article/407455");
        expect(pressLinks[2]).toHaveAttribute(
            "data-href",
            "http://www.mako.co.il/study-career-study/articles/Article-27988298f42b651006.htm"
        );
        expect(pressLinks[3]).toHaveAttribute("data-href", "http://www.geektime.co.il/new-scholarship-site/");
    });

    it("renders press items with correct link attributes", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");

        pressLinks.forEach((link) => {
            expect(link).toHaveAttribute("target", "_blank");
            expect(link).toHaveAttribute("rel", "noopener noreferrer");
        });
    });

    it("renders press images with correct attributes", () => {
        render(<Press />);

        const pressImages = screen.getAllByTestId("press-image");
        expect(pressImages).toHaveLength(4);

        expect(pressImages[0]).toHaveAttribute("src", "/press/Ynet.svg");
        expect(pressImages[0]).toHaveAttribute("alt", "Ynet");
        expect(pressImages[0]).toHaveAttribute("width", "160");
        expect(pressImages[0]).toHaveAttribute("height", "96");
    });

    it("applies correct styling to images", () => {
        render(<Press />);

        const pressImages = screen.getAllByTestId("press-image");

        pressImages.forEach((image) => {
            expect(image).toHaveClass(
                "w-full",
                "h-full",
                "object-contain",
                "opacity-90",
                "group-hover:opacity-100",
                "transition-all"
            );
        });
    });

    it("applies correct styling to title", () => {
        const title = "עיתונות ותקשורת";
        render(<Press title={title} />);

        const titleElement = screen.getByText(title);
        expect(titleElement).toHaveClass(
            "text-3xl",
            "md:text-4xl",
            "font-bold",
            "bg-gradient-to-l",
            "from-gray-900",
            "to-gray-600",
            "bg-clip-text",
            "text-transparent",
            "mb-10"
        );
    });

    it("applies correct styling to container", () => {
        render(<Press />);

        const section = document.querySelector("section");
        expect(section).toHaveClass("bg-background", "py-12");

        const container = document.querySelector(".container");
        expect(container).toHaveClass("mx-auto", "text-center");
    });

    it("applies correct styling to press items container", () => {
        render(<Press />);

        const itemsContainer = document.querySelector(".flex.flex-wrap");
        expect(itemsContainer).toHaveClass("justify-center", "gap-8", "mx-auto");
    });

    it("applies correct styling to individual press items", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");

        pressLinks.forEach((link) => {
            expect(link).toBeInTheDocument();
            expect(link).toHaveAttribute("target", "_blank");
            expect(link).toHaveAttribute("rel", "noopener noreferrer");
        });
    });

    it("applies correct styling to image containers", () => {
        render(<Press />);

        const imageContainers = document.querySelectorAll(".relative.w-40.h-24");
        expect(imageContainers).toHaveLength(4);

        imageContainers.forEach((container) => {
            expect(container).toHaveClass("flex", "items-center", "justify-center");
        });
    });

    it("handles image error correctly", () => {
        render(<Press />);

        const pressImages = screen.getAllByTestId("press-image");
        const firstImage = pressImages[0];

        expect(firstImage).toBeInTheDocument();
        expect(firstImage).toHaveAttribute("src");
        expect(firstImage).toHaveAttribute("alt");
    });

    it("renders fallback content for broken images", () => {
        render(<Press />);

        const fallbackElements = document.querySelectorAll(".hidden");
        expect(fallbackElements.length).toBeGreaterThan(0);

        const fallbackElement = fallbackElements[0];
        expect(fallbackElement).toHaveClass(
            "w-full",
            "h-full",
            "flex",
            "items-center",
            "justify-center",
            "text-sm",
            "text-gray-500",
            "border",
            "border-gray-200",
            "rounded"
        );
    });

    it("renders correct number of press items", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");
        expect(pressLinks).toHaveLength(4);

        const pressImages = screen.getAllByTestId("press-image");
        expect(pressImages).toHaveLength(4);
    });

    it("renders press items with unique keys", () => {
        render(<Press />);

        const pressLinks = screen.getAllByTestId("press-link");
        expect(pressLinks).toHaveLength(4);
    });
});
