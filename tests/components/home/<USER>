import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ScholarshipGroups from "@/components/home/<USER>";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";

// Type definitions for mocked components
interface MotionComponentProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: unknown;
}

interface LucideIconProps {
    size?: number | string;
    className?: string;
    strokeWidth?: number;
    [key: string]: unknown;
}

interface ButtonProps {
    children?: React.ReactNode;
    className?: string;
    onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
    variant?: string;
    [key: string]: unknown;
}

interface CardComponentProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: unknown;
}

interface LinkProps {
    children: React.ReactNode;
    href: string;
}

jest.mock("@/hooks/use-scholarship-groups");
const mockUseScholarshipGroups = useScholarshipGroups as jest.MockedFunction<typeof useScholarshipGroups>;

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, className, ...props }: MotionComponentProps) => (
            <div className={className} {...props}>
                {children}
            </div>
        ),
        h3: ({ children, className, ...props }: MotionComponentProps) => (
            <h3 className={className} {...props}>
                {children}
            </h3>
        ),
        p: ({ children, className, ...props }: MotionComponentProps) => (
            <p className={className} {...props}>
                {children}
            </p>
        )
    }
}));

jest.mock("lucide-react", () => ({
    ArrowDown: ({ size, className, ...props }: LucideIconProps) => (
        <div data-testid="arrow-down-icon" className={className} {...props}>
            ↓
        </div>
    ),
    Award: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="award-icon" className={className} {...props}>
            🏆
        </div>
    ),
    BookOpen: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="book-open-icon" className={className} {...props}>
            📖
        </div>
    ),
    Building2: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="building2-icon" className={className} {...props}>
            🏢
        </div>
    ),
    GraduationCap: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="graduation-cap-icon" className={className} {...props}>
            🎓
        </div>
    ),
    MapPin: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="map-pin-icon" className={className} {...props}>
            📍
        </div>
    ),
    Medal: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="medal-icon" className={className} {...props}>
            🏅
        </div>
    ),
    Shield: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="shield-icon" className={className} {...props}>
            🛡️
        </div>
    ),
    Star: ({ size, className, strokeWidth, ...props }: LucideIconProps) => (
        <div data-testid="star-icon" className={className} {...props}>
            ⭐
        </div>
    )
}));

jest.mock("next/link", () => {
    return ({ children, href }: LinkProps) => (
        <a href={href} data-testid="scholarship-group-link" data-href={href}>
            {children}
        </a>
    );
});

jest.mock("@/components/scholarship-groups/scholarship-group-skeleton", () => ({
    ScholarshipGroupSkeletonList: () => (
        <div data-testid="scholarship-groups-skeleton">Loading scholarship groups...</div>
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ children, className, onClick, variant, ...props }: ButtonProps) => (
        <button className={className} onClick={onClick} {...props}>
            {children}
        </button>
    )
}));

jest.mock("@/components/ui/card", () => ({
    Card: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} {...props}>
            {children}
        </div>
    ),
    CardContent: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} {...props}>
            {children}
        </div>
    ),
    CardFooter: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} {...props}>
            {children}
        </div>
    ),
    CardHeader: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} {...props}>
            {children}
        </div>
    )
}));

describe("ScholarshipGroups", () => {
    const mockScholarshipGroups = [
        {
            id: "1",
            title: "מלגות לתואר ראשון",
            description: "מלגות מיועדות לסטודנטים לתואר ראשון",
            slug: "bachelor-degree",
            icon: "graduation-cap",
            image_url: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            isNew: false
        },
        {
            id: "2",
            title: "מלגות לתואר שני",
            description: "מלגות מיועדות לסטודנטים לתואר שני",
            slug: "master-degree",
            icon: "certificate",
            image_url: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            isNew: false
        },
        {
            id: "3",
            title: "מלגות מצוינות",
            description: "מלגות לסטודנטים מצטיינים",
            slug: "excellence",
            icon: "award",
            image_url: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            isNew: true
        }
    ];

    const createMockReturn = (overrides: Partial<ReturnType<typeof useScholarshipGroups>> = {}) => ({
        displayedGroups: [],
        loading: false,
        error: null,
        hasMore: false,
        loadMore: jest.fn(),
        items: [],
        refetch: jest.fn(),
        ...overrides
    });

    beforeEach(() => {
        mockUseScholarshipGroups.mockClear();
    });

    it("renders loading state", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                loading: true
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.getByTestId("scholarship-groups-skeleton")).toBeInTheDocument();
    });

    it("renders error state", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                error: new Error("שגיאה בטעינת המלגות")
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.getByText("אירעה שגיאה בטעינת המלגות. אנא נסו שוב מאוחר יותר.")).toBeInTheDocument();
        expect(screen.getByText("אירעה שגיאה בטעינת המלגות. אנא נסו שוב מאוחר יותר.")).toHaveClass("text-red-600");
    });

    it("renders scholarship groups when loaded successfully", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.getByText("מלגות לתואר ראשון")).toBeInTheDocument();
        expect(screen.getByText("מלגות לתואר שני")).toBeInTheDocument();
        expect(screen.getByText("מלגות מצוינות")).toBeInTheDocument();
    });

    it("renders scholarship group descriptions", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.getByText("מלגות מיועדות לסטודנטים לתואר ראשון")).toBeInTheDocument();
        expect(screen.getByText("מלגות מיועדות לסטודנטים לתואר שני")).toBeInTheDocument();
        expect(screen.getByText("מלגות לסטודנטים מצטיינים")).toBeInTheDocument();
    });

    it("renders correct links for scholarship groups", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const links = screen.getAllByTestId("scholarship-group-link");
        expect(links).toHaveLength(3);

        expect(links[0]).toHaveAttribute("data-href", "/scholarships/groups/bachelor-degree");
        expect(links[1]).toHaveAttribute("data-href", "/scholarships/groups/master-degree");
        expect(links[2]).toHaveAttribute("data-href", "/scholarships/groups/excellence");
    });

    it("renders 'discover more' text for each group", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const discoverMoreElements = screen.getAllByText("גלה עוד");
        expect(discoverMoreElements).toHaveLength(3);
    });

    it("renders load more button when hasMore is true", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups,
                hasMore: true
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.getByText("טען עוד")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "טען עוד" })).toBeInTheDocument();
    });

    it("does not render load more button when hasMore is false", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups,
                hasMore: false
            })
        );

        render(<ScholarshipGroups />);

        expect(screen.queryByText("טען עוד")).not.toBeInTheDocument();
    });

    it("calls loadMore when load more button is clicked", async () => {
        const mockLoadMore = jest.fn();
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups,
                hasMore: true,
                loadMore: mockLoadMore
            })
        );

        const user = userEvent.setup();
        render(<ScholarshipGroups />);

        const loadMoreButton = screen.getByRole("button", { name: "טען עוד" });
        await user.click(loadMoreButton);

        expect(mockLoadMore).toHaveBeenCalledTimes(1);
    });

    it("renders responsive grid layout", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const gridContainer = document.querySelector(".grid");
        expect(gridContainer).toHaveClass("grid-cols-1", "sm:grid-cols-2", "lg:grid-cols-3", "xl:grid-cols-4");
    });

    it("applies correct styling to cards", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const links = screen.getAllByTestId("scholarship-group-link");
        expect(links.length).toBeGreaterThan(0);

        links.forEach((link) => {
            expect(link).toBeInTheDocument();
        });
    });

    it("applies correct styling to titles", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const title1 = screen.getByText("מלגות לתואר ראשון");
        const title2 = screen.getByText("מלגות לתואר שני");
        const title3 = screen.getByText("מלגות מצוינות");

        [title1, title2, title3].forEach((title) => {
            expect(title).toHaveClass("font-semibold", "text-lg", "text-gray-900", "group-hover:text-primary");
        });
    });

    it("applies correct styling to descriptions", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const descriptions = screen.getAllByText(/מיועדות/);
        descriptions.forEach((description) => {
            expect(description).toHaveClass("text-sm", "text-gray-600", "line-clamp-3");
        });
    });

    it("applies correct styling to load more button", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups,
                hasMore: true
            })
        );

        render(<ScholarshipGroups />);

        const loadMoreButton = screen.getByRole("button", { name: "טען עוד" });
        expect(loadMoreButton).toHaveClass("border-primary", "text-primary", "hover:bg-primary/5");
    });

    it("renders icon containers with correct styling", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const iconContainers = document.querySelectorAll(".w-12.h-12");
        expect(iconContainers).toHaveLength(3);

        iconContainers.forEach((container) => {
            expect(container).toHaveClass(
                "rounded-lg",
                "bg-primary/10",
                "flex",
                "items-center",
                "justify-center",
                "text-primary"
            );
        });
    });

    it("handles empty state correctly", () => {
        mockUseScholarshipGroups.mockReturnValue(createMockReturn({}));

        render(<ScholarshipGroups />);

        const links = screen.queryAllByTestId("scholarship-group-link");
        expect(links).toHaveLength(0);
    });

    it("renders section with correct container classes", () => {
        mockUseScholarshipGroups.mockReturnValue(
            createMockReturn({
                displayedGroups: mockScholarshipGroups,
                items: mockScholarshipGroups
            })
        );

        render(<ScholarshipGroups />);

        const section = document.querySelector("section");
        expect(section).toHaveClass("w-full");

        const container = document.querySelector(".w-full");
        expect(container).toBeInTheDocument();
    });
});
