import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import FAQ from "@/components/home/<USER>";
import { useFAQ } from "@/hooks/use-faq";

jest.mock("@/hooks/use-faq");
const mockUseFAQ = useFAQ as jest.MockedFunction<typeof useFAQ>;

jest.mock("@/components/home/<USER>/faq-skeleton", () => {
    return function FAQSkeleton() {
        return <div data-testid="faq-skeleton">Loading FAQ...</div>;
    };
});

describe("FAQ", () => {
    const mockFAQItems = [
        {
            id: "1",
            question: "מה זה מלגה?",
            answer: "מלגה היא סכום כסף שניתן לסטודנטים לסיוע בלימודים",
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            order_index: 1
        },
        {
            id: "2",
            question: "כיצד אפשר להגיש בקשה למלגה?",
            answer: "ההגשה מתבצעת דרך האתר שלנו",
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            order_index: 2
        },
        {
            id: "3",
            question: "מתי יוצאות התוצאות?",
            answer: "התוצאות יוצאות בתוך חודש מההגשה",
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            order_index: 3
        }
    ];

    beforeEach(() => {
        mockUseFAQ.mockClear();
    });

    it("renders with default props when loading", () => {
        mockUseFAQ.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        expect(screen.getByTestId("faq-skeleton")).toBeInTheDocument();
    });

    it("renders with custom title and subtitle", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        const title = "שאלות נפוצות";
        const subtitle = "כל מה שרציתם לדעת";

        render(<FAQ title={title} subtitle={subtitle} />);

        expect(screen.getByText(title)).toBeInTheDocument();
        expect(screen.getByText(subtitle)).toBeInTheDocument();
    });

    it("renders FAQ items when loaded successfully", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        expect(screen.getByText("מה זה מלגה?")).toBeInTheDocument();
        expect(screen.getByText("כיצד אפשר להגיש בקשה למלגה?")).toBeInTheDocument();
        expect(screen.getByText("מתי יוצאות התוצאות?")).toBeInTheDocument();
    });

    it("displays error message when there's an error", () => {
        const errorMessage = "שגיאה בטעינת השאלות";
        mockUseFAQ.mockReturnValue({
            items: [],
            loading: false,
            error: errorMessage,
            refetch: jest.fn()
        });

        render(<FAQ />);

        expect(screen.getByText(errorMessage)).toBeInTheDocument();
        expect(screen.getByText(errorMessage)).toHaveClass("text-red-500");
    });

    it("opens first FAQ item by default when items are loaded", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        expect(screen.getByText("מלגה היא סכום כסף שניתן לסטודנטים לסיוע בלימודים")).toBeInTheDocument();
    });

    it("expands and collapses FAQ items when clicked", async () => {
        const user = userEvent.setup();
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const secondQuestion = screen.getByText("כיצד אפשר להגיש בקשה למלגה?");
        await user.click(secondQuestion);

        await waitFor(() => {
            expect(screen.getByText("ההגשה מתבצעת דרך האתר שלנו")).toBeInTheDocument();
        });
    });

    it("has proper accessibility attributes for accordion", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const firstQuestion = screen.getByText("מה זה מלגה?");
        const button = firstQuestion.closest("button");

        expect(button).toHaveAttribute("aria-label", "מה זה מלגה?");
    });

    it("renders correct styling for questions", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const firstQuestion = screen.getByText("מה זה מלגה?");
        expect(firstQuestion).toHaveClass("font-medium", "text-xl", "md:text-2xl", "text-right");
    });

    it("renders answers with correct styling", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const answer = screen.getByText("מלגה היא סכום כסף שניתן לסטודנטים לסיוע בלימודים");
        expect(answer).toHaveClass("bg-secondary/50", "p-6", "rounded-[calc(var(--radius)-4px)]");
    });

    it("renders proper card structure", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const cardContainer = document.querySelector(".backdrop-blur-md");
        expect(cardContainer).toHaveClass(
            "bg-card",
            "rounded-[var(--radius)]",
            "shadow-[0_10px_30px_rgba(0,0,0,0.08)]"
        );
    });

    it("applies border between items correctly", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const items = document.querySelectorAll("[class*='border-t']");
        expect(items).toHaveLength(2);
    });

    it("handles empty FAQ items gracefully", () => {
        mockUseFAQ.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const outerContainer = document.querySelector(".w-full.bg-background");
        expect(outerContainer).toBeInTheDocument();

        const faqItems = screen.queryAllByText(/מה זה|כיצד|מתי/);
        expect(faqItems).toHaveLength(0);
    });

    it("applies hover effects correctly", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const firstTrigger = screen.getByText("מה זה מלגה?").closest("button");
        expect(firstTrigger).toHaveClass("hover:bg-accent/30", "group-hover:text-primary");
    });

    it("renders responsive text sizes", () => {
        mockUseFAQ.mockReturnValue({
            items: mockFAQItems,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<FAQ />);

        const titles = screen.queryAllByText(/.+/);
        const titleWithTextXl = titles.find((el) => el.classList.contains("text-3xl"));
        if (titleWithTextXl) {
            expect(titleWithTextXl).toHaveClass("text-3xl", "md:text-4xl");
        }
    });
});
