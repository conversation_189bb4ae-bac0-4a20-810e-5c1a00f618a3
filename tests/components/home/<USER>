import { render, screen } from "@testing-library/react";
import <PERSON> from "@/components/home/<USER>";

interface MotionComponentProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: unknown;
}

interface LottieComponentProps {
    animationData?: {
        v?: string;
        fr?: number;
        ip?: number;
        op?: number;
        w?: number;
        h?: number;
        nm?: string;
        ddd?: number;
        assets?: unknown[];
        layers?: unknown[];
        [key: string]: unknown;
    };
    loop?: boolean;
    autoplay?: boolean;
    className?: string;
    style?: React.CSSProperties;
    width?: number | string;
    height?: number | string;
    [key: string]: unknown;
}

interface LinkProps {
    children: React.ReactNode;
    href: string;
    target?: string;
    rel?: string;
    className?: string;
    [key: string]: unknown;
}

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, className, ...props }: MotionComponentProps) => (
            <div className={className} {...props}>
                {children}
            </div>
        ),
        h1: ({ children, className, ...props }: MotionComponentProps) => (
            <h1 className={className} {...props}>
                {children}
            </h1>
        ),
        p: ({ children, className, ...props }: MotionComponentProps) => (
            <p className={className} {...props}>
                {children}
            </p>
        )
    }
}));

jest.mock("next/dynamic", () => () => {
    const MockLottie = ({ animationData, loop }: LottieComponentProps) => (
        <div data-testid="lottie-animation" data-loop={loop}>
            Mock Hero Animation
        </div>
    );
    return MockLottie;
});

jest.mock("next/link", () => {
    return ({ children, href }: LinkProps) => (
        <a href={href} data-testid="hero-link" data-href={href}>
            {children}
        </a>
    );
});

jest.mock("@/components/home/<USER>/hero-skeleton", () => {
    return function HeroSkeleton() {
        return <div data-testid="hero-skeleton">Loading Hero...</div>;
    };
});

jest.mock("@/public/home/<USER>", () => ({
    v: "5.5.7",
    fr: 29.9700012207031,
    ip: 0,
    op: 90.0000036657751,
    w: 800,
    h: 600,
    nm: "hero-animation",
    ddd: 0,
    assets: [],
    layers: []
}));

describe("Hero", () => {
    it("renders all text content correctly", () => {
        render(<Hero />);

        expect(screen.getByText("מלגות לסטודנטים לכל התארים")).toBeInTheDocument();
        expect(screen.getByText(/מצאו את כל המלגות הרלוונטיות עבורכם בקלות/)).toBeInTheDocument();
        expect(screen.getByText("לחצו וקבלו רשימת מלגות המותאמת לכם אישית")).toBeInTheDocument();
    });

    it("renders the lottie animation", () => {
        render(<Hero />);

        const animation = screen.getByTestId("lottie-animation");
        expect(animation).toBeInTheDocument();
        expect(animation).toHaveAttribute("data-loop", "true");
    });

    it("renders the CTA button with correct link", () => {
        render(<Hero />);

        const link = screen.getByTestId("hero-link");
        expect(link).toHaveAttribute("data-href", "/#scholarship-groups");

        const button = screen.getByRole("button");
        expect(button).toHaveAttribute("aria-label", "לחצו וקבלו רשימת מלגות המותאמת לכם אישית");
    });

    it("applies correct styling to title", () => {
        render(<Hero />);

        const title = screen.getByText("מלגות לסטודנטים לכל התארים");
        expect(title).toHaveClass(
            "text-3xl",
            "sm:text-4xl",
            "md:text-5xl",
            "lg:text-6xl",
            "font-bold",
            "bg-clip-text",
            "text-transparent",
            "bg-gradient-to-l",
            "from-blue-900",
            "to-primary"
        );
    });

    it("applies correct styling to description", () => {
        render(<Hero />);

        const description = screen.getByText(/מצאו את כל המלגות הרלוונטיות/);
        expect(description).toHaveClass("text-base", "sm:text-lg", "text-gray-700", "leading-relaxed");
    });

    it("applies correct styling to button", () => {
        render(<Hero />);

        const button = screen.getByRole("button");
        expect(button).toHaveClass(
            "bg-primary",
            "hover:bg-primary/90",
            "text-white",
            "font-medium",
            "transition-all",
            "shadow-md",
            "hover:shadow-lg"
        );
    });

    it("renders responsive layout classes", () => {
        render(<Hero />);

        const layoutContainer = document.querySelector(".flex-col-reverse");
        expect(layoutContainer).toHaveClass("md:flex-row");
    });

    it("renders background pattern", () => {
        render(<Hero />);

        const patternElement = document.querySelector("[style*='radial-gradient']");
        expect(patternElement).toBeInTheDocument();
        expect(patternElement).toHaveClass("opacity-5");
    });

    it("renders decorative blur elements", () => {
        render(<Hero />);

        const blurElements = document.querySelectorAll(".blur-3xl");
        expect(blurElements).toHaveLength(2);

        const leftBlur = document.querySelector(".-bottom-16.-left-16");
        const rightBlur = document.querySelector(".-top-16.-right-16");

        expect(leftBlur).toBeInTheDocument();
        expect(rightBlur).toBeInTheDocument();
    });

    it("renders card with proper styling", () => {
        render(<Hero />);

        const card = document.querySelector(".shadow-lg");
        expect(card).toHaveClass("border-0", "bg-white/80", "backdrop-blur-sm");
    });

    it("renders animation container with proper aspect ratio", () => {
        render(<Hero />);

        const aspectContainer = document.querySelector(".aspect-\\[600\\/450\\]");
        expect(aspectContainer).toBeInTheDocument();
        expect(aspectContainer).toHaveClass("rounded-2xl", "overflow-hidden");
    });

    it("renders responsive text container width", () => {
        render(<Hero />);

        const textContainer = document.querySelector(".w-full");
        if (textContainer && textContainer.classList.contains("md:w-[45%]")) {
            expect(textContainer).toHaveClass("md:w-[45%]");
        }
    });

    it("renders animation container with correct width", () => {
        render(<Hero />);

        const animationContainer = document.querySelector(".md\\:w-\\[55\\%\\]");
        expect(animationContainer).toBeInTheDocument();
    });

    it("has arrow icon in button", () => {
        render(<Hero />);

        const button = screen.getByRole("button");
        expect(button).toBeInTheDocument();

        const iconContainer = document.querySelector(".rtl\\:rotate-180");
        expect(iconContainer).toBeInTheDocument();
    });

    it("applies proper gradient backgrounds", () => {
        render(<Hero />);

        const mainGradient = document.querySelector(".bg-gradient-to-br");
        expect(mainGradient).toHaveClass("from-blue-50", "via-white", "to-primary/5");

        const overlayGradient = document.querySelector(".bg-gradient-to-tr");
        expect(overlayGradient).toHaveClass("from-primary/10");
    });

    it("renders text content with proper RTL alignment", () => {
        render(<Hero />);

        const textContent = document.querySelector(".text-right");
        expect(textContent).toBeInTheDocument();
    });

    it("has proper responsive padding", () => {
        render(<Hero />);

        const container = document.querySelector(".py-12");
        expect(container).toHaveClass("sm:py-16");
    });
});
