import { render, screen } from "@testing-library/react";
import ContentWithImage from "@/components/home/<USER>";

// Type definitions for mocked components
interface MotionDivProps extends React.HTMLAttributes<HTMLDivElement> {
    children?: React.ReactNode;
}

interface MotionPProps extends React.HTMLAttributes<HTMLParagraphElement> {
    children?: React.ReactNode;
}

interface LottieAnimationData {
    v?: string;
    fr?: number;
    ip?: number;
    op?: number;
    w?: number;
    h?: number;
    nm?: string;
    ddd?: number;
    assets?: unknown[];
    layers?: unknown[];
    [key: string]: unknown;
}

interface LottieComponentProps {
    animationData?: LottieAnimationData;
    loop?: boolean;
    className?: string;
    autoplay?: boolean;
    rendererSettings?: {
        preserveAspectRatio?: string;
        clearCanvas?: boolean;
        progressiveLoad?: boolean;
        hideOnTransparent?: boolean;
    };
}

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, className, ...props }: MotionDivProps) => (
            <div className={className} {...props}>
                {children}
            </div>
        ),
        p: ({ children, className, ...props }: MotionPProps) => (
            <p className={className} {...props}>
                {children}
            </p>
        )
    }
}));

jest.mock("next/dynamic", () => () => {
    const MockLottie = ({ animationData, loop, className }: LottieComponentProps) => (
        <div className={className} data-testid="lottie-animation" data-loop={loop}>
            Mock Lottie Animation
        </div>
    );
    return MockLottie;
});

jest.mock("@/public/home/<USER>", () => ({
    v: "5.5.7",
    fr: 29.9700012207031,
    ip: 0,
    op: 90.0000036657751,
    w: 800,
    h: 600,
    nm: "test-animation",
    ddd: 0,
    assets: [],
    layers: []
}));

describe("ContentWithImage", () => {
    it("renders all text content correctly", () => {
        render(<ContentWithImage />);

        expect(screen.getByText("קצת עלינו")).toBeInTheDocument();
        expect(screen.getByText(/המלגות שלנו מאפשרות לכם לקבל מימון עבור הלימודים האקדמיים שלכם/)).toBeInTheDocument();
        expect(screen.getByRole("button", { name: "למד עוד" })).toBeInTheDocument();
    });

    it("renders the lottie animation", () => {
        render(<ContentWithImage />);

        const animation = screen.getByTestId("lottie-animation");
        expect(animation).toBeInTheDocument();
        expect(animation).toHaveAttribute("data-loop", "true");
        expect(animation).toHaveClass("w-full", "max-w-xs", "rounded-3xl", "overflow-hidden");
    });

    it("renders card structure correctly", () => {
        render(<ContentWithImage />);

        const card =
            screen.getByRole("button", { name: "למד עוד" }).closest("[class*='Card']") ||
            document.querySelector("[class*='shadow-xl']");
        expect(card).toBeInTheDocument();
    });

    it("renders responsive grid layout", () => {
        render(<ContentWithImage />);

        const gridContainer = document.querySelector(".grid");
        expect(gridContainer).toHaveClass("grid-cols-1", "md:grid-cols-2");
    });

    it("applies correct styling to title", () => {
        render(<ContentWithImage />);

        const title = screen.getByText("קצת עלינו");

        expect(title).toBeInTheDocument();

        const titleContainer = title.closest("h2") || title.parentElement;
        expect(titleContainer).toBeInTheDocument();
    });

    it("applies correct styling to description", () => {
        render(<ContentWithImage />);

        const description = screen.getByText(/המלגות שלנו מאפשרות לכם/);
        expect(description).toHaveClass("text-base", "md:text-lg", "text-gray-700", "text-right");
    });

    it("applies correct styling to button", () => {
        render(<ContentWithImage />);

        const button = screen.getByRole("button", { name: "למד עוד" });
        expect(button).toHaveClass("bg-primary", "hover:bg-primary/90", "text-white");
    });

    it("has proper accessibility attributes", () => {
        render(<ContentWithImage />);

        const button = screen.getByRole("button", { name: "למד עוד" });
        expect(button).toHaveAttribute("aria-label", "למד עוד");
    });

    it("renders gradient background correctly", () => {
        render(<ContentWithImage />);

        const gradientBackground = document.querySelector(".bg-gradient-to-br");
        expect(gradientBackground).toHaveClass("from-blue-50", "to-primary/5");
    });

    it("renders decorative pattern", () => {
        render(<ContentWithImage />);

        const patternElement = document.querySelector("[style*='radial-gradient']");
        expect(patternElement).toBeInTheDocument();
        expect(patternElement).toHaveClass("opacity-5");
    });

    it("renders decorative blur element", () => {
        render(<ContentWithImage />);

        const blurElement = document.querySelector(".bg-primary\\/10.rounded-full.blur-xl");
        expect(blurElement).toBeInTheDocument();
    });

    it("has responsive padding classes", () => {
        render(<ContentWithImage />);

        const contentContainer = document.querySelector(".p-8");
        expect(contentContainer).toHaveClass("md:p-10", "lg:p-12");
    });

    it("renders card with proper shadow and border", () => {
        render(<ContentWithImage />);

        const card = document.querySelector(".shadow-xl");
        expect(card).toHaveClass("rounded-3xl", "border-0");
    });

    it("renders image container with proper aspect ratio", () => {
        render(<ContentWithImage />);

        const imageContainer = document.querySelector(".aspect-square");
        expect(imageContainer).toHaveClass("md:aspect-auto");
    });
});
