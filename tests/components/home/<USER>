import { render, screen } from "@testing-library/react";
import Testimonials from "@/components/home/<USER>";
import { useTestimonials } from "@/hooks/use-testimonials";

// Type definitions for mocked components
interface MotionComponentProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: unknown;
}

interface CardComponentProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: unknown;
}

interface IconProps {
    className?: string;
    fill?: string;
    size?: number | string;
    [key: string]: unknown;
}

jest.mock("@/hooks/use-testimonials");
const mockUseTestimonials = useTestimonials as jest.MockedFunction<typeof useTestimonials>;

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, className, ...props }: MotionComponentProps) => (
            <div className={className} {...props}>
                {children}
            </div>
        )
    }
}));

jest.mock("@/components/ui/card", () => ({
    Card: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} {...props}>
            {children}
        </div>
    ),
    CardHeader: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} data-testid="card-header" {...props}>
            {children}
        </div>
    ),
    CardContent: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} data-testid="card-content" {...props}>
            {children}
        </div>
    ),
    CardFooter: ({ children, className, ...props }: CardComponentProps) => (
        <div className={className} data-testid="card-footer" {...props}>
            {children}
        </div>
    )
}));

jest.mock("lucide-react", () => ({
    Star: ({ className, fill, ...props }: IconProps) => (
        <div data-testid="star-icon" className={className} {...props}>
            ⭐
        </div>
    ),
    User: ({ className, ...props }: IconProps) => (
        <div data-testid="user-icon" className={className} {...props}>
            👤
        </div>
    ),
    Building: ({ className, ...props }: IconProps) => (
        <div data-testid="building-icon" className={className} {...props}>
            🏢
        </div>
    )
}));

jest.mock("@/components/home/<USER>/testimonials-skeleton", () => {
    return function TestimonialsSkeleton() {
        return <div data-testid="testimonials-skeleton">Loading testimonials...</div>;
    };
});

describe("Testimonials", () => {
    const mockTestimonials = [
        {
            id: "1",
            name: "יוסי כהן",
            text: "המלגה עזרה לי להשלים את התואר",
            type: "personal" as const,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z"
        },
        {
            id: "2",
            name: "אוניברסיטת תל אביב",
            text: "שירות מעולה לסטודנטים",
            type: "institution" as const,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z"
        }
    ];

    beforeEach(() => {
        mockUseTestimonials.mockClear();
    });

    it("renders loading state", () => {
        mockUseTestimonials.mockReturnValue({
            items: [],
            loading: true,
            error: null
        });

        render(<Testimonials />);

        expect(screen.getByTestId("testimonials-skeleton")).toBeInTheDocument();
    });

    it("renders error state", () => {
        const errorMessage = "שגיאה בטעינת העדויות";
        mockUseTestimonials.mockReturnValue({
            items: [],
            loading: false,
            error: errorMessage
        });

        render(<Testimonials />);

        expect(screen.getByText(errorMessage)).toBeInTheDocument();
        expect(screen.getByText(errorMessage)).toHaveClass("text-red-500");
    });

    it("renders testimonials when loaded successfully", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        expect(screen.getByText("יוסי כהן")).toBeInTheDocument();
        expect(screen.getByText("אוניברסיטת תל אביב")).toBeInTheDocument();

        expect(screen.getByText(/המלגה עזרה לי/)).toBeInTheDocument();
        expect(screen.getByText(/שירות מעולה/)).toBeInTheDocument();
    });

    it("renders with specific testimonial IDs", () => {
        mockUseTestimonials.mockReturnValue({
            items: [mockTestimonials[0]],
            loading: false,
            error: null
        });

        render(<Testimonials ids={["1"]} />);

        expect(mockUseTestimonials).toHaveBeenCalledWith(["1"]);
        expect(screen.getByText("יוסי כהן")).toBeInTheDocument();
    });

    it("renders default title and subtitle", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        expect(screen.getByText("סטודנטים מספרים עלינו")).toBeInTheDocument();
        expect(screen.getByText("מה הסטודנטים אומרים על הסיוע שקיבלו מאיתנו")).toBeInTheDocument();
    });

    it("renders star ratings for testimonials", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const stars = document.querySelectorAll("[class*='fill-primary']");
        expect(stars.length).toBeGreaterThan(0);
    });

    it("renders correct icons for different testimonial types", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const userIcons = document.querySelectorAll(".h-5.w-5.text-primary.opacity-70");
        expect(userIcons).toHaveLength(2);
    });

    it("applies correct styling to testimonial cards", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const cards = document.querySelectorAll("[class*='bg-white']");
        expect(cards.length).toBeGreaterThan(0);

        expect(screen.getByText("יוסי כהן")).toBeInTheDocument();
        expect(screen.getByText("אוניברסיטת תל אביב")).toBeInTheDocument();
    });

    it("applies correct styling to testimonial text", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const testimonialText1 = screen.getByText(/המלגה עזרה לי/);
        const testimonialText2 = screen.getByText(/שירות מעולה/);

        expect(testimonialText1).toBeInTheDocument();
        expect(testimonialText2).toBeInTheDocument();

        expect(testimonialText1).toHaveClass("italic");
        expect(testimonialText2).toHaveClass("italic");
    });

    it("applies different styling for institution vs personal names", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const personalName = screen.getByText("יוסי כהן");
        const institutionName = screen.getByText("אוניברסיטת תל אביב");

        expect(personalName).toHaveClass("text-primary");
        expect(institutionName).toHaveClass("text-blue-700");
    });

    it("renders responsive grid layout", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const gridContainer = document.querySelector(".grid");
        expect(gridContainer).toHaveClass("grid-cols-1", "md:grid-cols-2", "lg:grid-cols-3");
    });

    it("renders background pattern", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const patternElement = document.querySelector("[style*='radial-gradient']");
        expect(patternElement).toBeInTheDocument();
        expect(patternElement).toHaveClass("opacity-5");
    });

    it("renders decorative blur elements", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const blurElements = document.querySelectorAll(".blur-3xl");
        expect(blurElements).toHaveLength(2);
    });

    it("handles empty testimonials list", () => {
        mockUseTestimonials.mockReturnValue({
            items: [],
            loading: false,
            error: null
        });

        render(<Testimonials />);

        expect(screen.getByText("סטודנטים מספרים עלינו")).toBeInTheDocument();

        const testimonialTexts = screen.queryByText("המלגה עזרה לי");
        expect(testimonialTexts).not.toBeInTheDocument();
    });

    it("applies hover effects to cards", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const cards = document.querySelectorAll("[class*='hover:shadow-lg']");
        expect(cards.length).toBeGreaterThan(0);

        cards.forEach((card) => {
            expect(card).toHaveClass("hover:-translate-y-2");
        });
    });

    it("renders proper card structure with header, content, and footer", () => {
        mockUseTestimonials.mockReturnValue({
            items: mockTestimonials,
            loading: false,
            error: null
        });

        render(<Testimonials />);

        const cardHeaders = screen.getAllByTestId("card-header");
        const cardContents = screen.getAllByTestId("card-content");
        const cardFooters = screen.getAllByTestId("card-footer");

        expect(cardHeaders.length).toBeGreaterThan(0);
        expect(cardContents.length).toBeGreaterThan(0);
        expect(cardFooters.length).toBeGreaterThan(0);

        expect(cardHeaders.length).toBe(cardContents.length);
        expect(cardContents.length).toBe(cardFooters.length);
    });
});
