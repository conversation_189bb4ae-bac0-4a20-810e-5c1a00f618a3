import { render, screen } from "@testing-library/react";
import ScholarshipGroupContent from "@/components/scholarship/scholarship-group-content";
import { useScholarships } from "@/hooks/use-scholarships";

jest.mock("@/hooks/use-scholarships");
const mockUseScholarships = useScholarships as jest.Mock;

// Mock heavy child components
jest.mock("@/components/scholarship/scholarship-group-hero", () => () => <div data-testid="group-hero" />);

type ListProps = { scholarships: unknown[] };
jest.mock("@/components/scholarship/scholarship-list", () => ({ scholarships }: ListProps) => (
    <div data-testid="scholarship-list">{scholarships.length}</div>
));

jest.mock("@/components/scholarship/scholarship-list-skeleton", () => ({
    ScholarshipListSkeleton: () => <div data-testid="scholarship-list-skeleton" />
}));

jest.mock("@/components/home/<USER>", () => () => <div data-testid="final-cta" />);
jest.mock("@/components/home/<USER>", () => () => <div data-testid="testimonials" />);

describe("ScholarshipGroupContent", () => {
    const group = {
        title: "מלגות מצוינות",
        description: "תיאור",
        slug: "excellent",
        image_url: undefined
    };

    it("shows skeleton when loading", () => {
        mockUseScholarships.mockReturnValue({
            displayedScholarships: [],
            loading: true,
            error: null,
            hasMore: false,
            loadMore: jest.fn()
        });

        render(<ScholarshipGroupContent group={group} />);

        expect(screen.getByTestId("scholarship-list-skeleton")).toBeInTheDocument();
    });

    it("shows error message when error", () => {
        mockUseScholarships.mockReturnValue({
            displayedScholarships: [],
            loading: false,
            error: "error",
            hasMore: false,
            loadMore: jest.fn()
        });

        render(<ScholarshipGroupContent group={group} />);

        expect(screen.getByText("אירעה שגיאה בטעינת המלגות. אנא נסו שוב מאוחר יותר.")).toBeInTheDocument();
    });

    it("shows empty message when no scholarships", () => {
        mockUseScholarships.mockReturnValue({
            displayedScholarships: [],
            loading: false,
            error: null,
            hasMore: false,
            loadMore: jest.fn()
        });

        render(<ScholarshipGroupContent group={group} />);

        expect(screen.getByText("אין כרגע מלגות זמינות בקטגוריה זו. אנא בדקו שוב בקרוב.")).toBeInTheDocument();
    });

    it("renders scholarship list when data available", () => {
        mockUseScholarships.mockReturnValue({
            displayedScholarships: [{ id: "1" }],
            loading: false,
            error: null,
            hasMore: false,
            loadMore: jest.fn()
        });

        render(<ScholarshipGroupContent group={group} />);

        expect(screen.getByTestId("scholarship-list")).toBeInTheDocument();
        expect(screen.getByTestId("scholarship-list")).toHaveTextContent("1");
    });
});
