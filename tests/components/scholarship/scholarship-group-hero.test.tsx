import { render, screen } from "@testing-library/react";
import ScholarshipGroupHero from "@/components/scholarship/scholarship-group-hero";
import { useUser } from "@clerk/nextjs";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

const mockUseUser = useUser as jest.Mock;

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
        h1: ({ children, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => <h1 {...props}>{children}</h1>,
        p: ({ children, ...props }: React.HTMLAttributes<HTMLParagraphElement>) => <p {...props}>{children}</p>
    }
}));

jest.mock("next/image", () => {
    return ({
        src,
        alt,
        onError,
        ...props
    }: {
        src: string;
        alt: string;
        onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
        [key: string]: any;
    }) => <img src={src} alt={alt} onError={onError} {...props} />;
});

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="group-hero-link">
            {children}
        </a>
    );
});

describe("ScholarshipGroupHero", () => {
    const baseProps = {
        title: "מלגות מצוינות",
        description: "תיאור",
        imageUrl: undefined
    };

    it("shows loading text when user not loaded", () => {
        mockUseUser.mockReturnValue({ user: null, isLoaded: false });

        render(<ScholarshipGroupHero {...baseProps} />);

        expect(screen.getByText("לחצו וקבלו רשימת מלגות מותאמת אישית")).toBeInTheDocument();
    });

    it("shows guest text and login link when user unauthenticated", () => {
        mockUseUser.mockReturnValue({ user: null, isLoaded: true });

        render(<ScholarshipGroupHero {...baseProps} />);

        expect(screen.getByText("להרשמה וקבלת הגשה אוטומטית")).toBeInTheDocument();
        expect(screen.getByTestId("group-hero-link")).toHaveAttribute("href", "/login");
    });

    it("shows authenticated text and subscriptions link when user authenticated", () => {
        mockUseUser.mockReturnValue({ user: { id: "u1" }, isLoaded: true });

        render(<ScholarshipGroupHero {...baseProps} />);

        expect(screen.getByText("רוצה שנגיש אותך בצורה אוטומטית?"));
        expect(screen.getByTestId("group-hero-link")).toHaveAttribute("href", "/subscriptions");
    });
});
