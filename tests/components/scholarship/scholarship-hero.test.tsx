import { render, screen } from "@testing-library/react";
import ScholarshipHero from "@/components/scholarship/scholarship-hero";
import { useUser } from "@clerk/nextjs";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

const mockUseUser = useUser as jest.Mock;

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
        h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
        p: ({ children, ...props }: any) => <p {...props}>{children}</p>
    }
}));

jest.mock("next/image", () => {
    return ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />;
});

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="hero-link">
            {children}
        </a>
    );
});

describe("ScholarshipHero", () => {
    const baseProps = {
        title: "מלגת מצטיינים",
        shortDescription: "תיאור קצר",
        applyButtonText: "CTA",
        scholarshipType: "submission" as const
    };

    it("shows guest text and login link when user not authenticated", () => {
        mockUseUser.mockReturnValue({ user: null, isLoaded: true });

        render(<ScholarshipHero {...baseProps} />);

        expect(screen.getByText("לחצו לקבלת רשימת מלגות אישית")).toBeInTheDocument();
        expect(screen.getByTestId("hero-link")).toHaveAttribute("href", "/login");
    });

    it("shows submission text and subscriptions link when user authenticated", () => {
        mockUseUser.mockReturnValue({ user: { id: "123" }, isLoaded: true });

        render(<ScholarshipHero {...baseProps} scholarshipType="submission" />);

        expect(screen.getByText("להרשמה וקבלת הגשה אוטומטית")).toBeInTheDocument();
        expect(screen.getByTestId("hero-link")).toHaveAttribute("href", "/subscriptions");
    });

    it("shows guidance text when type guidance", () => {
        mockUseUser.mockReturnValue({ user: { id: "123" }, isLoaded: true });

        render(<ScholarshipHero {...baseProps} scholarshipType="guidance" />);

        expect(screen.getByText("לקבלת סיוע אישי בהגשת המלגה")).toBeInTheDocument();
    });
});
