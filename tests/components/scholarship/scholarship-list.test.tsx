import { render, screen, fireEvent } from "@testing-library/react";
import ScholarshipList from "@/components/scholarship/scholarship-list";

// Simple mocks for external libraries and heavy components
jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
        button: ({ children, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
            <button {...props}>{children}</button>
        )
    }
}));

jest.mock("@/components/ui/accordion", () => {
    const Accordion = ({ children }: { children: React.ReactNode }) => <div data-testid="accordion">{children}</div>;
    const AccordionItem = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
        <div {...props}>{children}</div>
    );
    const AccordionTrigger = ({ children, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
        <button {...props}>{children}</button>
    );
    const AccordionContent = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
    return { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
});

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="link">
            {children}
        </a>
    );
});

jest.mock("lucide-react", () => ({
    CalendarIcon: () => <span data-testid="calendar-icon" />,
    Clock: () => <span data-testid="clock-icon" />,
    Coins: () => <span data-testid="coins-icon" />
}));

// Helper to create a minimal Scholarship object
import { Tables } from "@/types/database.types";

const createScholarship = (overrides: Partial<Tables<"scholarships">> = {}) =>
    ({
        id: "1",
        title: "מלגה א",
        short_description: "תיאור קצר",
        description: "תיאור מלא",
        volunteer_hours: 10,
        min_amount: 1000,
        max_amount: 2000,
        start_date: "2025-01-01",
        end_date: "2025-06-30",
        slug: "scholarship-a",
        requirements: [],
        benefits: [],
        created_at: "",
        updated_at: "",
        is_active: true,
        is_public: true,
        scholarship_type: "submission",
        target_audience: "",
        image_url: undefined,
        contact_email: undefined,
        contact_person: undefined,
        contact_phone: undefined,
        internal_notes: undefined,
        response_date: undefined,
        url: undefined,
        ...overrides
    }) as Tables<"scholarships">;

describe("ScholarshipList", () => {
    it("renders scholarship items", () => {
        const scholarships = [createScholarship()];
        render(<ScholarshipList scholarships={scholarships} />);

        expect(screen.getByText("מלגה א")).toBeInTheDocument();
        expect(screen.getByText(/תיאור קצר/)).toBeInTheDocument();
    });

    it("triggers load more callback", () => {
        const scholarships = [createScholarship()];
        const onLoadMore = jest.fn();
        render(<ScholarshipList scholarships={scholarships} hasMore onLoadMore={onLoadMore} />);

        fireEvent.click(screen.getByRole("button", { name: "טען עוד" }));
        expect(onLoadMore).toHaveBeenCalledTimes(1);
    });

    it("does not render load more button when hasMore is false", () => {
        const scholarships = [createScholarship()];
        render(<ScholarshipList scholarships={scholarships} hasMore={false} />);

        expect(screen.queryByRole("button", { name: "טען עוד" })).not.toBeInTheDocument();
    });
});
