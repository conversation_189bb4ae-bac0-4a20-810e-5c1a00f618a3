import { render, screen } from "@testing-library/react";
import { EligibleScholarshipCardWithSkeleton } from "@/components/scholarship/eligible-scholarship-card";
import { TEXTS as CARD_TEXTS } from "@/lib/eligible-scholarship-card-constants";

// --- mocks ---

jest.mock("framer-motion", () => ({
    motion: {
        create: (tag: string | React.ComponentType) => tag,
        div: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>
    }
}));

jest.mock("@/components/ui/card", () => ({
    Card: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
    CardContent: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
    CardFooter: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
    CardHeader: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>,
    CardTitle: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => <div {...props}>{children}</div>
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ children, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
        <button {...props}>{children}</button>
    )
}));

jest.mock("@/components/forms/fields/single-select", () => ({
    SingleSelect: ({
        label,
        placeholder,
        options,
        name
    }: {
        label: string;
        placeholder?: string;
        options: Array<{ id: string; label: string }>;
        name: string;
    }) => (
        <div data-testid="single-select">
            <label>{label}</label>
            <select data-testid={`select-${name}`}>
                <option value="">{placeholder}</option>
                {options.map((option) => (
                    <option key={option.id} value={option.id}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    )
}));

jest.mock("@/components/forms/dynamic-questions-form/dynamic-questions-form", () => () => (
    <div data-testid="dynamic-questions-form" />
));

jest.mock("@/components/common/copy-to-clipboard", () => ({
    CopyToClipboard: () => <div data-testid="copy" />
}));

jest.mock("@/components/common/empty-state", () => ({
    EmptyState: ({ message, children, variant }: { message: string; children?: React.ReactNode; variant?: string }) => (
        <div data-testid="empty-state" data-variant={variant}>
            <h1>{message}</h1>
            {children}
        </div>
    )
}));

let mockApplicationQuestion: any = null;
let mockLoading = false;
let mockError: Error | null = null;
let mockRefetch = jest.fn();

jest.mock("@/hooks/use-application-questions", () => ({
    useApplicationQuestions: () => ({
        applicationQuestion: mockApplicationQuestion,
        loading: mockLoading,
        error: mockError,
        refetch: mockRefetch
    })
}));

jest.mock(
    "lucide-react",
    () =>
        new Proxy(
            {},
            {
                get: () => () => <span />
            }
        )
);

jest.mock("next/link", () => {
    return ({ children, href, ...props }: { children: React.ReactNode; href: string }) => (
        <a href={href} {...props} data-testid="link">
            {children}
        </a>
    );
});

// --- helpers ---
import { Tables } from "@/types/database.types";

const createScholarship = (overrides: Partial<Tables<"scholarships">> = {}) =>
    ({
        id: "1",
        scholarshipId: "1",
        title: "Test Scholarship",
        description: "A short description",
        start_date: "2025-01-01",
        end_date: "2025-02-01",
        min_amount: 1000,
        max_amount: 2000,
        volunteer_hours: 5,
        url: "https://example.com",
        short_description: "A short description",
        scholarship_type: "submission",
        isEligible: true,
        benefits: [],
        requirements: [],
        created_at: "",
        updated_at: "",
        is_active: true,
        is_public: true,
        target_audience: "",
        image_url: undefined,
        contact_email: undefined,
        contact_person: undefined,
        contact_phone: undefined,
        internal_notes: undefined,
        response_date: undefined,
        slug: "test-scholarship",
        ...overrides
    }) as Tables<"scholarships"> & { scholarshipId: string; isEligible: boolean; url: string | undefined };

// --- tests ---
describe("EligibleScholarshipCardWithSkeleton", () => {
    beforeEach(() => {
        mockApplicationQuestion = null;
        mockLoading = false;
        mockError = null;
        mockRefetch.mockClear();
    });

    it("renders skeleton when scholarship is undefined", () => {
        render(<EligibleScholarshipCardWithSkeleton userPlan="free" />);
        // Expect a skeleton element (we can look for background gray skeleton class)
        expect(document.querySelectorAll(".bg-gray-200").length).toBeGreaterThan(0);
    });

    it("renders scholarship content when data is provided", () => {
        const scholarship = createScholarship();
        render(
            <EligibleScholarshipCardWithSkeleton
                scholarship={scholarship}
                isEligible={true}
                shouldApply={undefined}
                onShouldApplyChange={jest.fn()}
                scholarshipType="submission"
            />
        );

        // Title
        expect(screen.getByText("Test Scholarship")).toBeInTheDocument();
        // Labels from TEXTS
        expect(screen.getByText(CARD_TEXTS.dateRangeLabel)).toBeInTheDocument();
        expect(screen.getByText(CARD_TEXTS.amountLabel)).toBeInTheDocument();
        // External link button text
        expect(screen.getByText(CARD_TEXTS.externalLinkLabel)).toBeInTheDocument();
    });

    it("uses database-loaded application options when available", () => {
        mockApplicationQuestion = {
            id: "test-question",
            label: "Custom question from database",
            placeholder: "Select your choice",
            options: [
                { id: "accept", label: "Accept Application" },
                { id: "decline", label: "Decline Application" }
            ]
        };

        const scholarship = createScholarship();
        render(
            <EligibleScholarshipCardWithSkeleton
                scholarship={scholarship}
                isEligible={true}
                shouldApply={undefined}
                onShouldApplyChange={jest.fn()}
                scholarshipType="submission"
                userPlan="premium"
            />
        );

        // Should display the custom label from database
        expect(screen.getByText("Custom question from database")).toBeInTheDocument();

        // Should have the custom options
        expect(screen.getByText("Accept Application")).toBeInTheDocument();
        expect(screen.getByText("Decline Application")).toBeInTheDocument();

        // Should show custom placeholder
        expect(screen.getByText("Select your choice")).toBeInTheDocument();
    });

    it("falls back to default options when no database questions available", () => {
        mockApplicationQuestion = null;

        const scholarship = createScholarship();
        render(
            <EligibleScholarshipCardWithSkeleton
                scholarship={scholarship}
                isEligible={true}
                shouldApply={undefined}
                onShouldApplyChange={jest.fn()}
                scholarshipType="submission"
                userPlan="premium"
            />
        );

        // Should display default options
        expect(screen.getByText(CARD_TEXTS.applicationStatusYes)).toBeInTheDocument();
        expect(screen.getByText(CARD_TEXTS.applicationStatusNo)).toBeInTheDocument();

        // Should show default placeholder
        expect(screen.getByText(CARD_TEXTS.applicationStatusPlaceholder)).toBeInTheDocument();
    });

    it("renders error state when application questions fail to load", () => {
        mockError = new Error("Failed to load application questions");
        mockLoading = false;

        const scholarship = createScholarship();
        render(
            <EligibleScholarshipCardWithSkeleton
                scholarship={scholarship}
                isEligible={true}
                shouldApply={undefined}
                onShouldApplyChange={jest.fn()}
                scholarshipType="submission"
                userPlan="premium"
            />
        );

        // Should show error state
        expect(screen.getByTestId("empty-state")).toBeInTheDocument();
        expect(screen.getByTestId("empty-state")).toHaveAttribute("data-variant", "error");
        expect(screen.getByText("שגיאה בטעינת שאלות הבקשה")).toBeInTheDocument();
        expect(screen.getByText("לא ניתן לטעון את שאלות הבקשה כרגע. אנא נסה לרענן את הדף.")).toBeInTheDocument();

        // Should have retry button
        const retryButton = screen.getByText("נסה שוב");
        expect(retryButton).toBeInTheDocument();

        // Should call refetch when retry button is clicked
        retryButton.click();
        expect(mockRefetch).toHaveBeenCalledTimes(1);
    });

    it("does not render error state when loading", () => {
        mockError = new Error("Failed to load application questions");
        mockLoading = true; // Still loading

        const scholarship = createScholarship();
        render(
            <EligibleScholarshipCardWithSkeleton
                scholarship={scholarship}
                isEligible={true}
                shouldApply={undefined}
                onShouldApplyChange={jest.fn()}
                scholarshipType="submission"
                userPlan="premium"
            />
        );

        // Should not show error state while loading
        expect(screen.queryByTestId("empty-state")).not.toBeInTheDocument();
        expect(screen.getByText("Test Scholarship")).toBeInTheDocument();
    });
});
