import { render, screen } from "@testing-library/react";
import ScholarshipDetail from "@/components/scholarship/scholarship-detail";

// Mock nested heavy components
jest.mock("@/components/scholarship/scholarship-hero", () => ({ title }: { title: string }) => (
    <div data-testid="scholarship-hero">{title}</div>
));

jest.mock("@/components/home/<USER>", () => () => <div data-testid="final-cta" />);
jest.mock("@/components/home/<USER>", () => () => <div data-testid="scholarship-groups" />);
jest.mock("@/components/home/<USER>", () => () => <div data-testid="testimonials" />);

jest.mock("framer-motion", () => ({
    motion: {
        div: ({ children, ...props }: any) => <div {...props}>{children}</div>
    }
}));

jest.mock("lucide-react", () => ({
    CalendarIcon: () => <span />,
    Clock: () => <span />,
    Coins: () => <span />,
    Users: () => <span />
}));

jest.mock("next/image", () => {
    return ({ src, alt, onError, ...props }: any) => <img src={src} alt={alt} onError={onError} {...props} />;
});

jest.mock("next/link", () => {
    return ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href} data-testid="detail-link">
            {children}
        </a>
    );
});

describe("ScholarshipDetail", () => {
    const scholarship = {
        id: "s1",
        title: "מלגת מצטיינים",
        short_description: "תיאור קצר",
        description: "תיאור מלא",
        volunteer_hours: 20,
        min_amount: 5000,
        max_amount: 5000,
        start_date: "2025-01-01",
        end_date: "2025-02-01",
        slug: "excellent",
        requirements: ["Requirement 1", "Requirement 2"],
        benefits: ["Benefit 1"],
        created_at: "",
        updated_at: "",
        is_active: true,
        is_public: true,
        scholarship_type: "submission",
        target_audience: "Students",
        image_url: null,
        contact_email: null,
        contact_person: null,
        contact_phone: null,
        internal_notes: null,
        response_date: null,
        url: null
    } as unknown as Parameters<typeof ScholarshipDetail>[0]["scholarshipData"];

    it("renders scholarship hero and details", () => {
        render(<ScholarshipDetail scholarshipData={scholarship} />);

        // Hero title
        expect(screen.getByTestId("scholarship-hero")).toHaveTextContent("מלגת מצטיינים");
        // Description paragraph
        expect(screen.getByText("תיאור מלא")).toBeInTheDocument();
        // Requirement and benefit items
        expect(screen.getByText("Requirement 1")).toBeInTheDocument();
        expect(screen.getByText("Benefit 1")).toBeInTheDocument();
    });
});
