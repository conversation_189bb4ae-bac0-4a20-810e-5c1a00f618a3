import { render, screen } from "@testing-library/react";
import { EligibleScholarshipsSummaryBanner } from "@/components/scholarship/eligible-scholarships-summary-banner";
import { TEXTS as SUMMARY_TEXTS } from "@/components/scholarship/eligible-scholarships-summary-banner";

// Mocks
jest.mock("framer-motion", () => ({
    motion: {
        create: (tag: string | React.ComponentType) => tag,
        div: ({ children, ...props }: any) => <div {...props}>{children}</div>
    }
}));

jest.mock("react-share", () => ({
    WhatsappShareButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    FacebookShareButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    TwitterShareButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    TelegramShareButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    WhatsappIcon: () => <span data-testid="wa-icon" />,
    FacebookIcon: () => <span data-testid="fb-icon" />,
    XIcon: () => <span data-testid="x-icon" />,
    TelegramIcon: () => <span data-testid="tg-icon" />
}));

// Tests

describe("EligibleScholarshipsSummaryBanner", () => {
    const commonProps = {
        shareUrl: "https://example.com",
        shareTitle: "Check my scholarships"
    } as const;

    it("renders eligible summary and share buttons when count > 0", () => {
        render(
            <EligibleScholarshipsSummaryBanner
                eligibleCount={3}
                totalMinAmount={1000}
                totalMaxAmount={3000}
                {...commonProps}
            />
        );

        const eligibleText = `${SUMMARY_TEXTS.eligibleCountPrefix}3 ${SUMMARY_TEXTS.eligibleCountSuffix}`;
        expect(screen.getByText(eligibleText)).toBeInTheDocument();
        expect(screen.getByText(SUMMARY_TEXTS.subtitle)).toBeInTheDocument();
        expect(screen.getAllByRole("button").length).toBeGreaterThan(0); // share buttons
    });

    it("shows no-scholarships text when count is 0", () => {
        render(
            <EligibleScholarshipsSummaryBanner
                eligibleCount={0}
                totalMinAmount={0}
                totalMaxAmount={0}
                {...commonProps}
            />
        );

        expect(screen.getByText(SUMMARY_TEXTS.noScholarships)).toBeInTheDocument();
    });
});
