import React from "react";
import { render, screen } from "@testing-library/react";

import { DashboardHeader } from "@/components/dashboard/dashboard-header";

describe("DashboardHeader", () => {
    const defaultProps = {
        title: "Test Title",
        description: "Test Description"
    };

    it("renders title and description correctly", () => {
        render(<DashboardHeader {...defaultProps} />);

        expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent("Test Title");
        expect(screen.getByText("Test Description")).toBeInTheDocument();
    });

    it("applies correct styling to header element", () => {
        render(<DashboardHeader {...defaultProps} />);

        const header = screen.getByRole("banner");
        expect(header).toHaveClass("mb-8");
    });

    it("applies correct styling to title", () => {
        render(<DashboardHeader {...defaultProps} />);

        const title = screen.getByRole("heading", { level: 1 });
        expect(title).toHaveClass("text-3xl", "font-bold", "tracking-tight");
    });

    it("applies correct styling to description", () => {
        render(<DashboardHeader {...defaultProps} />);

        const description = screen.getByText("Test Description");
        expect(description).toHaveClass("text-muted-foreground", "mt-1");
    });

    it("renders with empty title and description", () => {
        render(<DashboardHeader title="" description="" />);

        expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent("");
        expect(screen.getByRole("heading", { level: 1 }).nextElementSibling).toHaveTextContent("");
    });

    it("renders with long title and description", () => {
        const longTitle = "Very Long Title That Should Still Render Correctly Without Issues";
        const longDescription =
            "This is a very long description that should also render correctly and maintain proper styling even when it spans multiple lines";

        render(<DashboardHeader title={longTitle} description={longDescription} />);

        expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(longTitle);
        expect(screen.getByText(longDescription)).toBeInTheDocument();
    });

    it("renders with special characters in title and description", () => {
        const titleWithSpecialChars = "Title with 123 & special chars! @#$%";
        const descriptionWithSpecialChars = "Description with עברית & English mixed text";

        render(<DashboardHeader title={titleWithSpecialChars} description={descriptionWithSpecialChars} />);

        expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(titleWithSpecialChars);
        expect(screen.getByText(descriptionWithSpecialChars)).toBeInTheDocument();
    });
});
