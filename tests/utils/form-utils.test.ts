import { mapIdsToOptions, mapIdToOption, mapQuestionIdsToOptions } from "@/utils/form-utils";

describe("Form Utils", () => {
    describe("mapIdsToOptions", () => {
        const mockItems = [
            { id: "item1", name: "First Item", category: "A" },
            { id: "item2", name: "Second Item", category: "B" },
            { id: "item3", name: "Third Item", category: "A" }
        ];

        it("maps IDs to options with labels from source items", () => {
            const ids = ["item1", "item3"];
            const result = mapIdsToOptions(
                ids,
                mockItems,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual([
                { id: "item1", label: "First Item" },
                { id: "item3", label: "Third Item" }
            ]);
        });

        it("uses fallback label for missing items", () => {
            const ids = ["item1", "missing-item", "item2"];
            const result = mapIdsToOptions(
                ids,
                mockItems,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual([
                { id: "item1", label: "First Item" },
                { id: "missing-item", label: "ID: missing-item" },
                { id: "item2", label: "Second Item" }
            ]);
        });

        it("uses custom fallback label function", () => {
            const ids = ["missing-item"];
            const result = mapIdsToOptions(
                ids,
                mockItems,
                (item) => item.id,
                (item) => item.name,
                (id) => `Not found: ${id}`
            );

            expect(result).toEqual([{ id: "missing-item", label: "Not found: missing-item" }]);
        });

        it("handles empty arrays", () => {
            const result = mapIdsToOptions(
                [],
                mockItems,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual([]);
        });

        it("handles undefined labels from source items", () => {
            const itemsWithUndefinedLabels = [
                { id: "item1", name: undefined },
                { id: "item2", name: "Valid Name" }
            ];

            const ids = ["item1", "item2"];
            const result = mapIdsToOptions(
                ids,
                itemsWithUndefinedLabels,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual([
                { id: "item1", label: "ID: item1" },
                { id: "item2", label: "Valid Name" }
            ]);
        });
    });

    describe("mapIdToOption", () => {
        const mockItems = [
            { id: "item1", name: "First Item", category: "A" },
            { id: "item2", name: "Second Item", category: "B" },
            { id: "item3", name: "Third Item", category: "A" }
        ];

        it("maps single ID to option with label from source item", () => {
            const result = mapIdToOption(
                "item2",
                mockItems,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual({ id: "item2", label: "Second Item" });
        });

        it("uses fallback label for missing item", () => {
            const result = mapIdToOption(
                "missing-item",
                mockItems,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual({ id: "missing-item", label: "ID: missing-item" });
        });

        it("uses custom fallback label function", () => {
            const result = mapIdToOption(
                "missing-item",
                mockItems,
                (item) => item.id,
                (item) => item.name,
                (id) => `Not found: ${id}`
            );

            expect(result).toEqual({ id: "missing-item", label: "Not found: missing-item" });
        });

        it("handles undefined label from source item", () => {
            const itemsWithUndefinedLabels = [
                { id: "item1", name: undefined },
                { id: "item2", name: "Valid Name" }
            ];

            const result = mapIdToOption(
                "item1",
                itemsWithUndefinedLabels,
                (item) => item.id,
                (item) => item.name
            );

            expect(result).toEqual({ id: "item1", label: "ID: item1" });
        });

        it("works with different source item properties", () => {
            const users = [
                { userId: "user1", fullName: "John Doe", email: "<EMAIL>" },
                { userId: "user2", fullName: "Jane Smith", email: "<EMAIL>" }
            ];

            const result = mapIdToOption(
                "user2",
                users,
                (user) => user.userId,
                (user) => user.fullName
            );

            expect(result).toEqual({ id: "user2", label: "Jane Smith" });
        });
    });

    describe("mapQuestionIdsToOptions", () => {
        const mockQuestions = [
            {
                id: "question1",
                metadata: {
                    label: "Age Range",
                    placeholder: "Select your age"
                }
            },
            {
                id: "question2",
                metadata: {
                    placeholder: "Enter your name"
                }
            },
            {
                id: "question3",
                metadata: {}
            },
            {
                id: "question4"
                // no metadata
            }
        ];

        it("maps question IDs to options using metadata labels", () => {
            const questionIds = ["question1", "question2"];
            const result = mapQuestionIdsToOptions(questionIds, mockQuestions);

            expect(result).toEqual([
                { id: "question1", label: "Age Range" },
                { id: "question2", label: "Enter your name" }
            ]);
        });

        it("prefers label over placeholder in metadata", () => {
            const questionIds = ["question1"];
            const result = mapQuestionIdsToOptions(questionIds, mockQuestions);

            expect(result).toEqual([{ id: "question1", label: "Age Range" }]);
        });

        it("uses placeholder when label is not available", () => {
            const questionIds = ["question2"];
            const result = mapQuestionIdsToOptions(questionIds, mockQuestions);

            expect(result).toEqual([{ id: "question2", label: "Enter your name" }]);
        });

        it("uses fallback for questions without metadata or with empty metadata", () => {
            const questionIds = ["question3", "question4"];
            const result = mapQuestionIdsToOptions(questionIds, mockQuestions);

            expect(result).toEqual([
                { id: "question3", label: "Question ID: question3" },
                { id: "question4", label: "Question ID: question4" }
            ]);
        });

        it("uses fallback for missing questions", () => {
            const questionIds = ["missing-question"];
            const result = mapQuestionIdsToOptions(questionIds, mockQuestions);

            expect(result).toEqual([{ id: "missing-question", label: "Question ID: missing-question" }]);
        });

        it("handles empty question IDs array", () => {
            const result = mapQuestionIdsToOptions([], mockQuestions);

            expect(result).toEqual([]);
        });

        it("handles empty available questions array", () => {
            const questionIds = ["question1"];
            const result = mapQuestionIdsToOptions(questionIds, []);

            expect(result).toEqual([{ id: "question1", label: "Question ID: question1" }]);
        });
    });
});
