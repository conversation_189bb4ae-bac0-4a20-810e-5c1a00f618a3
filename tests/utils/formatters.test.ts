import { textToArray, formatPhoneToE164 } from "@/utils/formatters";

describe("textToArray", () => {
    it("should convert a multi-line string into an array of strings", () => {
        const text = "Line 1\nLine 2\nLine 3";
        expect(textToArray(text)).toEqual(["Line 1", "Line 2", "Line 3"]);
    });

    it("should trim whitespace from each line", () => {
        const text = "  Line 1  \n\tLine 2\t\nLine 3 ";
        expect(textToArray(text)).toEqual(["Line 1", "Line 2", "Line 3"]);
    });

    it("should remove empty lines and lines consisting only of whitespace", () => {
        const text = "Line 1\n\n  \t \nLine 2";
        expect(textToArray(text)).toEqual(["Line 1", "Line 2"]);
    });

    it("should handle an empty string, returning an empty array", () => {
        expect(textToArray("")).toEqual([]);
    });

    it("should handle a single line string without newlines", () => {
        const text = "Single Line Text";
        expect(textToArray(text)).toEqual(["Single Line Text"]);
    });

    it("should handle strings with leading/trailing newlines and spaces", () => {
        const text = "\n  Line 1  \nLine 2\n ";
        expect(textToArray(text)).toEqual(["Line 1", "Line 2"]);
    });

    it("should handle strings with multiple consecutive newlines", () => {
        const text = "Line 1\n\n\nLine 2";
        expect(textToArray(text)).toEqual(["Line 1", "Line 2"]);
    });
});

describe("formatPhoneToE164", () => {
    it("should convert a '05XXXXXXXX' number to '+972XXXXXXXX'", () => {
        expect(formatPhoneToE164("0501234567")).toBe("+972501234567");
    });

    it("should convert a '972XXXXXXXX' number to '+972XXXXXXXX'", () => {
        expect(formatPhoneToE164("972501234567")).toBe("+972501234567");
    });

    it("should retain leading '+' for numbers already in E.164 format", () => {
        expect(formatPhoneToE164("+972501234567")).toBe("+972501234567");
        expect(formatPhoneToE164("+1234567890")).toBe("+1234567890"); // Non-Israeli E.164
    });

    it("should remove non-digit characters (spaces, dashes, parentheses) before formatting", () => {
        expect(formatPhoneToE164("************")).toBe("+972501234567");
        expect(formatPhoneToE164("(*************")).toBe("+972501234567");
    });

    it("should handle short numbers by returning digits only if no recognized prefix", () => {
        expect(formatPhoneToE164("12345")).toBe("12345");
    });

    it("should return empty string for empty input", () => {
        expect(formatPhoneToE164("")).toBe("");
    });

    it("should return digits only for numbers not starting with 05, 972, or +", () => {
        expect(formatPhoneToE164("************")).toBe("1234567890"); // US-like number
        expect(formatPhoneToE164("0701234567")).toBe("0701234567"); // Other Israeli prefix (e.g., landline)
    });
});
