import { getPaymentErrorMessage, extractCCode } from "@/utils/payment-errors";

describe("payment-errors", () => {
    describe("getPaymentErrorMessage", () => {
        it("should return a default message for undefined or null error codes", () => {
            expect(getPaymentErrorMessage(undefined)).toBe("אירעה שגיאה לא צפויה בתהליך התשלום.");
            expect(getPaymentErrorMessage(null)).toBe("אירעה שגיאה לא צפויה בתהליך התשלום.");
        });

        it("should return the correct error message for a known error code", () => {
            expect(getPaymentErrorMessage("901")).toBe("מסוף לא מורשה לעבודה בשיטה זו: אין הרשאה למסוף");
            expect(getPaymentErrorMessage(901)).toBe("מסוף לא מורשה לעבודה בשיטה זו: אין הרשאה למסוף");
        });

        it("should return the error code itself if it is not found", () => {
            expect(getPaymentErrorMessage("unknown_code")).toBe("unknown_code");
        });
    });

    describe("extractCCode", () => {
        it("should return undefined if the response text is undefined", () => {
            expect(extractCCode(undefined)).toBeUndefined();
        });

        it("should extract the CCode from a URL string", () => {
            const url = "https://example.com?foo=bar&CCode=123&baz=qux";
            expect(extractCCode(url)).toBe("123");
        });

        it("should return undefined if CCode is not found in the string", () => {
            const url = "https://example.com?foo=bar&baz=qux";
            expect(extractCCode(url)).toBeUndefined();
        });
    });
});
