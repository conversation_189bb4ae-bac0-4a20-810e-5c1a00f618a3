import { transformConditionsFromDatabase, transformConditionsToDatabase } from "@/utils/condition-utils";
import type { BackendConditionDependency } from "@/utils/condition-utils";
import type { ConditionDependency, ConditionQuestion } from "@/components/forms/fields/condition-selector";
import type { Option } from "@/components/forms/fields/dropdown-base";

describe("condition-utils", () => {
    const mockQuestions: ConditionQuestion[] = [
        {
            id: "q1",
            type: "single_select",
            metadata: {
                label: "Age",
                options: [
                    { id: "18-25", label: "18-25" },
                    { id: "26-35", label: "26-35" },
                    { id: "36-45", label: "36-45" }
                ]
            }
        },
        {
            id: "q2",
            type: "single_select",
            metadata: {
                label: "Gender",
                options: [
                    { id: "male", label: "Male" },
                    { id: "female", label: "Female" }
                ]
            }
        },
        {
            id: "q3",
            type: "number_input",
            metadata: {
                placeholder: "Income Level"
            }
        }
    ];

    describe("transformConditionsFromDatabase", () => {
        it("should transform basic condition dependencies", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    id: "cond1",
                    question_id: "q1",
                    condition_type: "in",
                    condition_value: ["18-25", "26-35"]
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result).toHaveLength(1);
            expect(result[0].question_id).toEqual({
                id: "q1",
                label: "Age"
            });
            expect(result[0].condition_type).toBe("in");
            expect(result[0].condition_value).toEqual([
                { id: "18-25", label: "18-25" },
                { id: "26-35", label: "26-35" }
            ]);
        });

        it("should handle question with placeholder instead of label", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "q3",
                    condition_type: "range",
                    condition_value: { min: 1000, max: 5000 }
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result[0].question_id).toEqual({
                id: "q3",
                label: "Income Level"
            });
        });

        it("should handle condition values that are already Option objects", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "q1",
                    condition_type: "in",
                    condition_value: [
                        { id: "18-25", label: "18-25" },
                        { id: "26-35", label: "26-35" }
                    ]
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result[0].condition_value).toEqual([
                { id: "18-25", label: "18-25" },
                { id: "26-35", label: "26-35" }
            ]);
        });

        it("should preserve non-'in' condition types as-is", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "q3",
                    condition_type: "range",
                    condition_value: { min: 100, max: 500 }
                },
                {
                    question_id: "q3",
                    condition_type: "date_range",
                    condition_value: { operator: "greater_than", days_from_today: 30 }
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result[0].condition_value).toEqual({ min: 100, max: 500 });
            expect(result[1].condition_value).toEqual({ operator: "greater_than", days_from_today: 30 });
        });

        it("should handle question not found in available questions", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "nonexistent",
                    condition_type: "in",
                    condition_value: ["value1"]
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result[0].question_id).toEqual({
                id: "nonexistent",
                label: "nonexistent"
            });
        });

        it("should handle question without options for 'in' condition", () => {
            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "q3", // q3 has no options
                    condition_type: "in",
                    condition_value: ["value1", "value2"]
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, mockQuestions);

            expect(result[0].condition_value).toEqual(["value1", "value2"]);
        });

        it("should handle empty conditions array", () => {
            const result = transformConditionsFromDatabase([], mockQuestions);
            expect(result).toEqual([]);
        });

        it("should handle question without metadata", () => {
            const questionsWithoutMetadata: ConditionQuestion[] = [
                { id: "q1", type: "short_text", metadata: undefined as any }
            ];

            const dbConditions: BackendConditionDependency[] = [
                {
                    question_id: "q1",
                    condition_type: "in",
                    condition_value: ["value1"]
                }
            ];

            const result = transformConditionsFromDatabase(dbConditions, questionsWithoutMetadata);

            expect(result[0].question_id).toEqual({
                id: "q1",
                label: "q1"
            });
        });
    });

    describe("transformConditionsToDatabase", () => {
        it("should transform form conditions to database format", () => {
            const formConditions: ConditionDependency[] = [
                {
                    question_id: { id: "q1", label: "Age" },
                    condition_type: "in",
                    condition_value: [
                        { id: "18-25", label: "18-25" },
                        { id: "26-35", label: "26-35" }
                    ]
                }
            ];

            const result = transformConditionsToDatabase(formConditions);

            expect(result).toEqual([
                {
                    question_id: "q1",
                    condition_type: "in",
                    condition_value: ["18-25", "26-35"]
                }
            ]);
        });

        it("should preserve non-'in' condition types", () => {
            const formConditions: ConditionDependency[] = [
                {
                    question_id: { id: "q3", label: "Income" },
                    condition_type: "range",
                    condition_value: { min: 100, max: 500 }
                },
                {
                    question_id: { id: "q4", label: "Date" },
                    condition_type: "date_range",
                    condition_value: { operator: "greater_than", days_from_today: 30 }
                }
            ];

            const result = transformConditionsToDatabase(formConditions);

            expect(result).toEqual([
                {
                    question_id: "q3",
                    condition_type: "range",
                    condition_value: { min: 100, max: 500 }
                },
                {
                    question_id: "q4",
                    condition_type: "date_range",
                    condition_value: { operator: "greater_than", days_from_today: 30 }
                }
            ]);
        });

        it("should filter out conditions without valid question ID", () => {
            const formConditions: ConditionDependency[] = [
                {
                    question_id: { id: "", label: "Empty ID" },
                    condition_type: "in",
                    condition_value: [{ id: "value1", label: "value1" }]
                },
                {
                    question_id: { id: "   ", label: "Whitespace ID" },
                    condition_type: "in",
                    condition_value: [{ id: "value2", label: "value2" }]
                },
                {
                    question_id: null as any,
                    condition_type: "in",
                    condition_value: [{ id: "value3", label: "value3" }]
                },
                {
                    question_id: { id: "valid", label: "Valid" },
                    condition_type: "in",
                    condition_value: [{ id: "opt1", label: "Option 1" }]
                }
            ];

            const result = transformConditionsToDatabase(formConditions);

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                question_id: "valid",
                condition_type: "in",
                condition_value: ["opt1"]
            });
        });

        it("should handle empty form conditions array", () => {
            const result = transformConditionsToDatabase([]);
            expect(result).toEqual([]);
        });

        it("should handle empty condition values", () => {
            const formConditions: ConditionDependency[] = [
                {
                    question_id: { id: "q1", label: "Question" },
                    condition_type: "range",
                    condition_value: { min: undefined, max: undefined }
                },
                {
                    question_id: { id: "q2", label: "Question 2" },
                    condition_type: "in",
                    condition_value: []
                }
            ];

            const result = transformConditionsToDatabase(formConditions);

            expect(result).toEqual([
                {
                    question_id: "q1",
                    condition_type: "range",
                    condition_value: { min: undefined, max: undefined }
                },
                {
                    question_id: "q2",
                    condition_type: "in",
                    condition_value: []
                }
            ]);
        });

        it("should handle mixed valid and invalid conditions", () => {
            const formConditions: ConditionDependency[] = [
                {
                    question_id: { id: "valid1", label: "Valid 1" },
                    condition_type: "in",
                    condition_value: [{ id: "opt1", label: "Option 1" }]
                },
                {
                    question_id: { id: "", label: "Invalid" },
                    condition_type: "in",
                    condition_value: [{ id: "value", label: "value" }]
                },
                {
                    question_id: { id: "valid2", label: "Valid 2" },
                    condition_type: "range",
                    condition_value: { min: 1, max: 10 }
                }
            ];

            const result = transformConditionsToDatabase(formConditions);

            expect(result).toHaveLength(2);
            expect(result[0].question_id).toBe("valid1");
            expect(result[1].question_id).toBe("valid2");
        });
    });
});
