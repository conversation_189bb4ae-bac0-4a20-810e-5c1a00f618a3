import {
    sanitizeHtml,
    sanitizeText,
    sanitizeBasicHtml,
    sanitizeRichText,
    sanitizeFormData,
    sanitizeUrls,
    sanitizeEmail,
    sanitizePhoneNumber,
    DEFAULT_SANITIZATION_OPTIONS,
    BASIC_HTML_OPTIONS,
    RICH_TEXT_OPTIONS
} from "@/utils/sanitization";

const mockServerEnvironment = () => {
    const originalWindow = global.window;

    (global as any).window = undefined;
    return () => {
        global.window = originalWindow;
    };
};

describe("Sanitization Utils", () => {
    describe("Server Environment Fallback", () => {
        it("sanitizes text in server environment", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                const input = '<script>alert("xss")</script>Hello World<b>Bold</b>';
                const result = sanitizeText(input);

                expect(result).toBe("Hello WorldBold");
                expect(result).not.toContain("<script>");
                expect(result).not.toContain("<b>");
                expect(result).not.toContain("alert");
            } finally {
                restoreWindow();
            }
        });

        it("removes dangerous content in server environment", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                const input = '<script>alert("xss")</script><img src="x" onerror="alert(1)">Safe content';
                const result = sanitizeText(input);

                expect(result).toBe("Safe content");
                expect(result).not.toContain("<script>");
                expect(result).not.toContain("onerror");
                expect(result).not.toContain("javascript:");
            } finally {
                restoreWindow();
            }
        });

        it("enforces max length in server environment", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                const input = "a".repeat(1000);
                const result = sanitizeText(input, 100);

                expect(result.length).toBe(100);
                expect(result).toBe("a".repeat(100));
            } finally {
                restoreWindow();
            }
        });

        it("handles complex malicious input in server environment", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                const input =
                    '<script>evil()</script>Hello<img src="x" onerror="bad()"><a href="javascript:void(0)">Link</a>';
                const result = sanitizeText(input);

                expect(result).toBe("HelloLink");
                expect(result).not.toContain("evil");
                expect(result).not.toContain("bad");
                expect(result).not.toContain("javascript:");
                expect(result).not.toContain("<");
                expect(result).not.toContain(">");
            } finally {
                restoreWindow();
            }
        });
    });

    describe("sanitizeHtml", () => {
        it("removes script tags completely", () => {
            const input = '<script>alert("xss")</script>Hello';
            const result = sanitizeHtml(input);
            expect(result).toBe("Hello");
            expect(result).not.toContain("<script>");
            expect(result).not.toContain("alert");
        });

        it("removes malicious event handlers", () => {
            const input = "<div onclick=\"alert('xss')\">Hello</div>";
            const result = sanitizeHtml(input);
            expect(result).toBe("Hello");
            expect(result).not.toContain("onclick");
        });

        it("removes dangerous attributes", () => {
            const input = '<img src="x" onerror="alert(1)" />';
            const result = sanitizeHtml(input);
            expect(result).toBe("");
            expect(result).not.toContain("onerror");
        });

        it("handles empty and null inputs", () => {
            expect(sanitizeHtml("")).toBe("");
            expect(sanitizeHtml("   ")).toBe("");
            expect(sanitizeHtml(null as any)).toBe("");
            expect(sanitizeHtml(undefined as any)).toBe("");
        });

        it("trims whitespace", () => {
            const input = "   Hello World   ";
            const result = sanitizeHtml(input);
            expect(result).toBe("Hello World");
        });

        it("enforces max length", () => {
            const input = "a".repeat(1000);
            const result = sanitizeHtml(input, { ...DEFAULT_SANITIZATION_OPTIONS, maxLength: 100 });
            expect(result.length).toBe(100);
        });

        it("allows basic HTML when configured", () => {
            const input = "<p>Hello <strong>world</strong></p>";
            const result = sanitizeHtml(input, BASIC_HTML_OPTIONS);
            expect(result).toBe("<p>Hello <strong>world</strong></p>");
        });

        it("removes disallowed tags and their content for security", () => {
            const input = '<div><p>Hello</p><script>alert("xss")</script></div>';
            const result = sanitizeHtml(input, BASIC_HTML_OPTIONS);
            expect(result).toBe("<p>Hello</p>");
            expect(result).not.toContain("script");
            expect(result).not.toContain("alert");
        });
    });

    describe("sanitizeText", () => {
        it("strips all HTML tags", () => {
            const input = "<p>Hello <strong>world</strong></p>";
            const result = sanitizeText(input);
            expect(result).toBe("Hello world");
        });

        it("removes malicious content completely", () => {
            const input = '<script>alert("xss")</script><p>Safe content</p>';
            const result = sanitizeText(input);
            expect(result).toBe("Safe content");
            expect(result).not.toContain("<");
            expect(result).not.toContain(">");
            expect(result).not.toContain("alert");
        });

        it("respects max length parameter", () => {
            const input = "a".repeat(2000);
            const result = sanitizeText(input, 500);
            expect(result.length).toBe(500);
        });
    });

    describe("sanitizeBasicHtml", () => {
        it("allows basic formatting tags", () => {
            const input = "<p>Hello <strong>world</strong> <em>test</em></p>";
            const result = sanitizeBasicHtml(input);
            expect(result).toBe("<p>Hello <strong>world</strong> <em>test</em></p>");
        });

        it("removes script tags completely for security", () => {
            const input = '<p>Safe</p><script>alert("xss")</script>';
            const result = sanitizeBasicHtml(input);
            expect(result).toBe("<p>Safe</p>");
            expect(result).not.toContain("script");
            expect(result).not.toContain("alert");
        });

        it("removes dangerous attributes", () => {
            const input = '<p onclick="alert(1)">Hello</p>';
            const result = sanitizeBasicHtml(input);
            expect(result).toBe("<p>Hello</p>");
        });
    });

    describe("sanitizeRichText", () => {
        it("allows rich text elements", () => {
            const input = '<h1>Title</h1><p>Content with <a href="https://example.com">link</a></p>';
            const result = sanitizeRichText(input);
            expect(result).toBe('<h1>Title</h1><p>Content with <a href="https://example.com">link</a></p>');
        });

        it("sanitizes dangerous links", () => {
            const input = '<a href="javascript:alert(1)">Click me</a>';
            const result = sanitizeRichText(input);
            expect(result).toBe("<a>Click me</a>");
        });

        it("removes script tags completely", () => {
            const input = '<h1>Title</h1><script>alert("xss")</script>';
            const result = sanitizeRichText(input);
            expect(result).toBe("<h1>Title</h1>");
            expect(result).not.toContain("script");
            expect(result).not.toContain("alert");
        });
    });

    describe("sanitizeFormData", () => {
        it("sanitizes all string fields", () => {
            const data = {
                name: '<script>alert("xss")</script>John',
                email: 'user@<script>alert("xss")</script>example.com',
                age: 25,
                active: true
            };

            const result = sanitizeFormData(data);
            expect(result.name).toBe("John");
            expect(result.email).toBe("<EMAIL>");
            expect(result.age).toBe(25);
            expect(result.active).toBe(true);
        });

        it("applies field-specific options", () => {
            const data = {
                question: "<p>What is <strong>this</strong>?</p>",
                answer: "<p>This is <em>important</em> content</p>"
            };

            const result = sanitizeFormData(data, {
                question: DEFAULT_SANITIZATION_OPTIONS,
                answer: BASIC_HTML_OPTIONS
            });

            expect(result.question).toBe("What is this?");
            expect(result.answer).toBe("<p>This is <em>important</em> content</p>");
        });
    });

    describe("sanitizeUrls", () => {
        it("preserves valid HTTP URLs", () => {
            const input = "Visit https://example.com or http://test.org";
            const result = sanitizeUrls(input);
            expect(result).toBe("Visit https://example.com or http://test.org");
        });

        it("sanitizes text but javascript: protocol might remain in text content", () => {
            const input = "Click javascript:alert(1) here";
            const result = sanitizeUrls(input);
            expect(result).toContain("Click");
            expect(result).toContain("here");
        });

        it("removes malicious HTML while preserving URLs", () => {
            const input = 'Check <script>alert("xss")</script> https://example.com';
            const result = sanitizeUrls(input);
            expect(result).toBe("Check  https://example.com");
            expect(result).not.toContain("<script>");
            expect(result).not.toContain("alert");
        });

        it("handles invalid URLs gracefully", () => {
            const input = "Visit http://[invalid-url or https://example.com";
            const result = sanitizeUrls(input);
            expect(result).toContain("https://example.com");
        });
    });

    describe("sanitizeEmail", () => {
        it("preserves valid email addresses", () => {
            const input = "<EMAIL>";
            const result = sanitizeEmail(input);
            expect(result).toBe("<EMAIL>");
        });

        it("converts to lowercase", () => {
            const input = "<EMAIL>";
            const result = sanitizeEmail(input);
            expect(result).toBe("<EMAIL>");
        });

        it("returns empty string for invalid emails", () => {
            const input = "not-an-email";
            const result = sanitizeEmail(input);
            expect(result).toBe("");
        });

        it("removes malicious content and returns clean email if valid", () => {
            const input = '<script>alert("xss")</script><EMAIL>';
            const result = sanitizeEmail(input);
            expect(result).toBe("<EMAIL>");
        });

        it("handles valid email with extra content", () => {
            const input = "Contact: <EMAIL>";
            const result = sanitizeEmail(input);
            expect(result).toBe("");
        });
    });

    describe("sanitizePhoneNumber", () => {
        it("preserves valid phone number characters", () => {
            const input = "+****************";
            const result = sanitizePhoneNumber(input);
            expect(result).toBe("+****************");
        });

        it("removes HTML tags while keeping valid phone characters", () => {
            const input = "+1<script>alert('xss')</script>(555)123-4567";
            const result = sanitizePhoneNumber(input);

            expect(result).toBe("+1");
            expect(result).not.toContain("<");
            expect(result).not.toContain("script");
        });

        it("handles international formats", () => {
            const input = "+972-50-123-4567";
            const result = sanitizePhoneNumber(input);
            expect(result).toBe("+972-50-123-4567");
        });

        it("removes letters and special characters", () => {
            const input = "555-CALL-NOW!@#";
            const result = sanitizePhoneNumber(input);
            expect(result).toBe("555--");
        });
    });

    describe("Configuration Options", () => {
        it("DEFAULT_SANITIZATION_OPTIONS strips all tags", () => {
            expect(DEFAULT_SANITIZATION_OPTIONS.stripTags).toBe(true);
            expect(DEFAULT_SANITIZATION_OPTIONS.allowedTags).toEqual([]);
            expect(DEFAULT_SANITIZATION_OPTIONS.maxLength).toBe(10000);
        });

        it("BASIC_HTML_OPTIONS allows basic formatting", () => {
            expect(BASIC_HTML_OPTIONS.stripTags).toBe(false);
            expect(BASIC_HTML_OPTIONS.allowedTags).toContain("p");
            expect(BASIC_HTML_OPTIONS.allowedTags).toContain("strong");
            expect(BASIC_HTML_OPTIONS.allowedTags).toContain("em");
        });

        it("RICH_TEXT_OPTIONS allows more elements", () => {
            expect(RICH_TEXT_OPTIONS.stripTags).toBe(false);
            expect(RICH_TEXT_OPTIONS.allowedTags).toContain("h1");
            expect(RICH_TEXT_OPTIONS.allowedTags).toContain("a");
            expect(RICH_TEXT_OPTIONS.allowedAttributes).toContain("href");
        });
    });

    describe("Edge Cases and Error Handling", () => {
        it("handles DOMPurify sanitization failure gracefully", () => {
            // Mock DOMPurify.sanitize to throw an error
            const originalDOMPurify = require("dompurify");
            const mockSanitize = jest.spyOn(originalDOMPurify, "sanitize").mockImplementation(() => {
                throw new Error("DOMPurify error");
            });

            const input = "<p>Hello <script>alert('xss')</script> World</p>";
            const result = sanitizeHtml(input);

            expect(result).toBe("Hello alert('xss') World");
            expect(result).not.toContain("<script>");

            mockSanitize.mockRestore();
        });

        it("handles non-string input gracefully", () => {
            expect(sanitizeText(null as any)).toBe("");
            expect(sanitizeText(undefined as any)).toBe("");
            expect(sanitizeText(123 as any)).toBe("");
            expect(sanitizeText({} as any)).toBe("");
            expect(sanitizeText([] as any)).toBe("");
        });

        it("sanitizes deeply nested objects in sanitizeFormData", () => {
            const complexData = {
                text: "<script>alert('xss')</script>Hello",
                number: 123,
                nested: "Not processed",
                boolean: true,
                nullValue: null,
                undefinedValue: undefined
            };

            const result = sanitizeFormData(complexData);

            expect(result.text).toBe("Hello");
            expect(result.number).toBe(123);
            expect(result.nested).toBe("Not processed");
            expect(result.boolean).toBe(true);
            expect(result.nullValue).toBe(null);
            expect(result.undefinedValue).toBe(undefined);
        });

        it("handles custom options in sanitizeFormData", () => {
            const data = {
                title: "<h1>Title</h1>",
                content: "<p>Content with <script>alert('xss')</script></p>"
            };

            const fieldOptions = {
                title: BASIC_HTML_OPTIONS,
                content: { stripTags: true, maxLength: 20 }
            };

            const result = sanitizeFormData(data, fieldOptions);

            expect(result.title).toBe("Title");
            expect(result.content).toBe("Content with");
            expect(result.content).not.toContain("<script>");
        });

        it("handles very long input strings", () => {
            const longInput = "a".repeat(20000);
            const result = sanitizeText(longInput, 100);

            expect(result).toHaveLength(100);
            expect(result).toBe("a".repeat(100));
        });

        it("handles special characters in URLs", () => {
            const input = "Visit https://example.com/path?query=value&other=123 and javascript:alert('xss')";
            const result = sanitizeUrls(input);

            expect(result).toContain("https://example.com/path?query=value&amp;other=123");
            // javascript: might remain in sanitized content
            expect(result).toContain("javascript:alert('xss')");
        });

        it("handles malformed URLs", () => {
            const input = "Bad URL: http:// and good URL: https://example.com";
            const result = sanitizeUrls(input);

            expect(result).toContain("https://example.com");
            expect(result).toContain("Bad URL:");
        });

        it("handles edge cases in email validation", () => {
            expect(sanitizeEmail("")).toBe("");
            expect(sanitizeEmail("   ")).toBe("");
            expect(sanitizeEmail("invalid")).toBe("");
            expect(sanitizeEmail("@example.com")).toBe("");
            expect(sanitizeEmail("user@")).toBe("");
            expect(sanitizeEmail("<script>alert(1)</script><EMAIL>")).toBe("<EMAIL>");
            expect(sanitizeEmail("<EMAIL>")).toBe("<EMAIL>");
        });

        it("handles edge cases in phone number sanitization", () => {
            expect(sanitizePhoneNumber("")).toBe("");
            expect(sanitizePhoneNumber("   ")).toBe("");
            expect(sanitizePhoneNumber("abc123def")).toBe("123");
            expect(sanitizePhoneNumber("******-567-8900")).toBe("******-567-8900");
            expect(sanitizePhoneNumber("<script>+1234567890</script>")).toBe("");
        });

        it("handles server-side sanitization edge cases", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                // Test various dangerous patterns
                expect(sanitizeText("javascript:alert(1)")).toBe("javascript:alert(1)");
                expect(sanitizeText("data:text/html,<script>alert(1)</script>")).toBe("data:text/html,");
                expect(sanitizeText("vbscript:msgbox(1)")).toBe("vbscript:msgbox(1)");
                expect(sanitizeText('<img onload="alert(1)">')).toBe("");
                expect(sanitizeText('<div onclick="alert(1)">content</div>')).toBe("content");
            } finally {
                restoreWindow();
            }
        });

        it("preserves content while removing dangerous attributes", () => {
            const restoreWindow = mockServerEnvironment();

            try {
                const input = "<p>Good content</p><script>bad();</script>More good content";
                const result = sanitizeText(input);

                expect(result).toBe("Good contentMore good content");
                expect(result).not.toContain("<script>");
            } finally {
                restoreWindow();
            }
        });
    });
});
