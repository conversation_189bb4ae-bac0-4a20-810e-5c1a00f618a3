import { createBrowserSupabaseClient, getSupabaseClient } from "@/utils/supabase/client";

// Mock dependencies
jest.mock("@supabase/ssr", () => ({
    createBrowserClient: jest.fn()
}));

const mockCreateBrowserClient = require("@supabase/ssr").createBrowserClient;

// Mock process.env
const originalEnv = process.env;

// Mock console.error to test error logging
const consoleSpy = jest.spyOn(console, "error").mockImplementation();

// Store original window to restore it
const originalWindow = global.window;

// Mock fetch and Response
(global as any).fetch = jest.fn();
(global as any).Response = class {
    constructor(public body: any) {}
};

describe("Supabase Client Utils", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        consoleSpy.mockClear();

        // Reset the singleton instance
        jest.resetModules();

        process.env = {
            ...originalEnv,
            NEXT_PUBLIC_SUPABASE_URL: "https://test.supabase.co",
            NEXT_PUBLIC_SUPABASE_ANON_KEY: "test-anon-key"
        };
    });

    afterEach(() => {
        process.env = originalEnv;
        consoleSpy.mockRestore();
        // Restore original window
        global.window = originalWindow;
    });

    describe("createBrowserSupabaseClient", () => {
        it("creates browser client with correct configuration", () => {
            const mockClient = { from: jest.fn() };
            mockCreateBrowserClient.mockReturnValue(mockClient);

            const result = createBrowserSupabaseClient();

            expect(mockCreateBrowserClient).toHaveBeenCalledWith(
                "https://test.supabase.co",
                "test-anon-key",
                expect.objectContaining({
                    global: {
                        fetch: expect.any(Function)
                    }
                })
            );
            expect(result).toBe(mockClient);
        });

        it("throws error when SUPABASE_URL is missing", () => {
            delete process.env.NEXT_PUBLIC_SUPABASE_URL;

            expect(() => createBrowserSupabaseClient()).toThrow("Supabase URL and anon key must be provided");
        });

        it("throws error when ANON_KEY is missing", () => {
            delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

            expect(() => createBrowserSupabaseClient()).toThrow("Supabase URL and anon key must be provided");
        });

        it.skip("custom fetch includes Clerk token when available", async () => {
            const mockToken = "test-clerk-token";
            const mockSession = {
                getToken: jest.fn().mockResolvedValue(mockToken)
            };

            // Mock window.Clerk
            (global as any).window = {
                Clerk: {
                    session: mockSession
                }
            };

            const mockFetch = jest.fn().mockResolvedValue(new (global as any).Response("{}"));
            const originalFetch = global.fetch;
            global.fetch = mockFetch;

            let customFetch: Function;
            mockCreateBrowserClient.mockImplementation((_url: string, _key: string, config: any) => {
                customFetch = config.global.fetch;
                return { from: jest.fn() };
            });

            createBrowserSupabaseClient();

            // Test custom fetch
            await customFetch!("https://api.test.com", { method: "GET" });

            expect(mockSession.getToken).toHaveBeenCalledWith({ template: "supabase" });
            expect(mockFetch).toHaveBeenCalledWith(
                "https://api.test.com",
                expect.objectContaining({
                    method: "GET",
                    headers: expect.any(Headers)
                })
            );

            // Check that Authorization header was set
            const callArgs = mockFetch.mock.calls[0];
            const headers = callArgs[1].headers;
            expect(headers.get("Authorization")).toBe(`Bearer ${mockToken}`);
            // Restore
            global.fetch = originalFetch;
        });

        it("custom fetch works without Clerk token", async () => {
            // Mock window without Clerk
            (global as any).window = {};

            const mockFetch = jest.fn().mockResolvedValue(new (global as any).Response("{}"));
            (global as any).fetch = mockFetch;

            let customFetch: Function;
            mockCreateBrowserClient.mockImplementation((_url: string, _key: string, config: any) => {
                customFetch = config.global.fetch;
                return { from: jest.fn() };
            });

            createBrowserSupabaseClient();

            // Test custom fetch without token
            await customFetch!("https://api.test.com", { method: "GET" });

            expect(mockFetch).toHaveBeenCalledWith(
                "https://api.test.com",
                expect.objectContaining({
                    method: "GET",
                    headers: expect.any(Headers)
                })
            );

            // Check that no Authorization header was set
            const callArgs = mockFetch.mock.calls[0];
            const headers = callArgs[1].headers;
            expect(headers.get("Authorization")).toBeNull();
        });

        it.skip("custom fetch handles Clerk token error gracefully", async () => {
            const mockSession = {
                getToken: jest.fn().mockRejectedValue(new Error("Token error"))
            };

            (global as any).window = {
                Clerk: {
                    session: mockSession
                }
            };

            const mockFetch = jest.fn().mockResolvedValue(new (global as any).Response("{}"));
            const originalFetch = global.fetch;
            global.fetch = mockFetch;

            let customFetch: Function;
            mockCreateBrowserClient.mockImplementation((_url: string, _key: string, config: any) => {
                customFetch = config.global.fetch;
                return { from: jest.fn() };
            });

            createBrowserSupabaseClient();

            // Test custom fetch with token error
            await customFetch!("https://api.test.com", {});

            expect(consoleSpy).toHaveBeenCalledWith("Failed to retrieve Clerk token:", expect.any(Error));
            expect(mockFetch).toHaveBeenCalledWith(
                "https://api.test.com",
                expect.objectContaining({
                    headers: expect.any(Headers)
                })
            );
            // Restore
            global.fetch = originalFetch;
        });

        it("custom fetch works in server environment", async () => {
            // Mock server environment (no window)
            (global as any).window = undefined;

            const mockFetch = jest.fn().mockResolvedValue(new (global as any).Response("{}"));
            (global as any).fetch = mockFetch;

            let customFetch: Function;
            mockCreateBrowserClient.mockImplementation((_url: string, _key: string, config: any) => {
                customFetch = config.global.fetch;
                return { from: jest.fn() };
            });

            createBrowserSupabaseClient();

            await customFetch!("https://api.test.com", { method: "POST" });

            expect(mockFetch).toHaveBeenCalledWith(
                "https://api.test.com",
                expect.objectContaining({
                    method: "POST",
                    headers: expect.any(Headers)
                })
            );
        });

        it("custom fetch preserves existing headers", async () => {
            (global as any).window = {};

            const mockFetch = jest.fn().mockResolvedValue(new (global as any).Response("{}"));
            (global as any).fetch = mockFetch;

            let customFetch: Function;
            mockCreateBrowserClient.mockImplementation((_url: string, _key: string, config: any) => {
                customFetch = config.global.fetch;
                return { from: jest.fn() };
            });

            createBrowserSupabaseClient();

            const existingHeaders = { "Content-Type": "application/json", "X-Custom": "value" };
            await customFetch!("https://api.test.com", { headers: existingHeaders });

            const callArgs = mockFetch.mock.calls[0];
            const headers = callArgs[1].headers;
            expect(headers.get("Content-Type")).toBe("application/json");
            expect(headers.get("X-Custom")).toBe("value");
        });
    });

    describe("getSupabaseClient", () => {
        it("creates client successfully with valid config", () => {
            const mockClient = { from: jest.fn() };
            mockCreateBrowserClient.mockReturnValue(mockClient);

            const result = getSupabaseClient();
            expect(result).toBe(mockClient);
            expect(mockCreateBrowserClient).toHaveBeenCalled();
        });

        it("handles error by logging and rethrowing", () => {
            // Use a different environment variable to trigger the error path
            const originalUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
            delete process.env.NEXT_PUBLIC_SUPABASE_URL;

            // Clear any cached instance to force re-creation
            consoleSpy.mockClear();

            expect(() => {
                // Call the function that creates the client
                createBrowserSupabaseClient();
            }).toThrow("Supabase URL and anon key must be provided");
            // Restore
            process.env.NEXT_PUBLIC_SUPABASE_URL = originalUrl;
        });
    });
});
