import { createClerkSupabaseClient, createClientFromRequest, createServiceRoleClient } from "@/utils/supabase/server";

// Mock dependencies
jest.mock("@supabase/ssr", () => ({
    createServerClient: jest.fn()
}));

jest.mock("@supabase/supabase-js", () => ({
    createClient: jest.fn()
}));

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("next/headers", () => ({
    cookies: jest.fn()
}));

const mockCreateServerClient = require("@supabase/ssr").createServerClient;
const mockCreateClient = require("@supabase/supabase-js").createClient;
const mockAuth = require("@clerk/nextjs/server").auth;

// Mock process.env
const originalEnv = process.env;

describe("Supabase Server Utils", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        process.env = {
            ...originalEnv,
            NEXT_PUBLIC_SUPABASE_URL: "https://test.supabase.co",
            NEXT_PUBLIC_SUPABASE_ANON_KEY: "test-anon-key",
            SUPABASE_SERVICE_ROLE_KEY: "test-service-role-key"
        };
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    describe("createClerkSupabaseClient", () => {
        const mockCookieStore = {
            getAll: jest.fn(),
            set: jest.fn()
        };

        beforeEach(() => {
            // Mock dynamic import of next/headers
            jest.doMock("next/headers", () => ({
                cookies: jest.fn(() => Promise.resolve(mockCookieStore))
            }));

            mockAuth.mockResolvedValue({
                getToken: jest.fn()
            });
        });

        it("creates server client with Clerk token", async () => {
            const mockToken = "test-jwt-token";
            const mockGetToken = jest.fn().mockResolvedValue(mockToken);
            mockAuth.mockResolvedValue({ getToken: mockGetToken });

            const mockClient = { from: jest.fn() };
            mockCreateServerClient.mockReturnValue(mockClient);

            mockCookieStore.getAll.mockReturnValue([{ name: "test-cookie", value: "test-value" }]);

            const result = await createClerkSupabaseClient();

            expect(mockGetToken).toHaveBeenCalledWith({ template: "supabase" });
            expect(mockCreateServerClient).toHaveBeenCalledWith(
                "https://test.supabase.co",
                "test-anon-key",
                expect.objectContaining({
                    global: {
                        headers: {
                            "Cache-Control": "no-store",
                            Authorization: `Bearer ${mockToken}`
                        }
                    },
                    cookies: expect.objectContaining({
                        getAll: expect.any(Function),
                        setAll: expect.any(Function)
                    })
                })
            );
            expect(result).toBe(mockClient);
        });

        it("creates server client without token when not available", async () => {
            const mockGetToken = jest.fn().mockResolvedValue(null);
            mockAuth.mockResolvedValue({ getToken: mockGetToken });

            const mockClient = { from: jest.fn() };
            mockCreateServerClient.mockReturnValue(mockClient);

            mockCookieStore.getAll.mockReturnValue([]);

            const result = await createClerkSupabaseClient();

            expect(mockCreateServerClient).toHaveBeenCalledWith(
                "https://test.supabase.co",
                "test-anon-key",
                expect.objectContaining({
                    global: {
                        headers: {
                            "Cache-Control": "no-store"
                        }
                    }
                })
            );
            expect(result).toBe(mockClient);
        });

        it("handles cookie operations correctly", async () => {
            const mockGetToken = jest.fn().mockResolvedValue("token");
            mockAuth.mockResolvedValue({ getToken: mockGetToken });

            mockCreateServerClient.mockImplementation((_url: any, _key: any, config: any) => {
                // Test the cookie functions
                const cookies = config.cookies;
                // Test getAll
                cookies.getAll();
                expect(mockCookieStore.getAll).toHaveBeenCalled();

                // Test setAll
                const cookiesToSet = [{ name: "test", value: "value", options: { httpOnly: true } }];
                cookies.setAll(cookiesToSet);
                expect(mockCookieStore.set).toHaveBeenCalledWith("test", "value", { httpOnly: true });

                return { from: jest.fn() };
            });

            await createClerkSupabaseClient();
        });

        it("handles cookie setting errors", async () => {
            const mockGetToken = jest.fn().mockResolvedValue("token");
            mockAuth.mockResolvedValue({ getToken: mockGetToken });

            mockCookieStore.set.mockImplementation(() => {
                throw new Error("Cookie error");
            });

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            mockCreateServerClient.mockImplementation((_url: any, _key: any, config: any) => {
                const cookies = config.cookies;

                expect(() => {
                    cookies.setAll([{ name: "test", value: "value" }]);
                }).toThrow("Cookie error");

                return { from: jest.fn() };
            });

            await createClerkSupabaseClient();

            consoleSpy.mockRestore();
        });
    });

    describe("createClientFromRequest", () => {
        it("is an alias for createClerkSupabaseClient", () => {
            expect(createClientFromRequest).toBe(createClerkSupabaseClient);
        });
    });

    describe("createServiceRoleClient", () => {
        it("creates service role client with correct configuration", () => {
            const mockClient = { from: jest.fn() };
            mockCreateClient.mockReturnValue(mockClient);

            const result = createServiceRoleClient();

            expect(mockCreateClient).toHaveBeenCalledWith("https://test.supabase.co", "test-service-role-key", {
                auth: {
                    autoRefreshToken: false,
                    persistSession: false,
                    detectSessionInUrl: false
                }
            });
            expect(result).toBe(mockClient);
        });

        it("throws error when SUPABASE_URL is missing", () => {
            delete process.env.NEXT_PUBLIC_SUPABASE_URL;

            expect(() => createServiceRoleClient()).toThrow(
                "Missing Supabase URL or Service Role Key for service role client."
            );
        });

        it("throws error when SERVICE_ROLE_KEY is missing", () => {
            delete process.env.SUPABASE_SERVICE_ROLE_KEY;

            expect(() => createServiceRoleClient()).toThrow(
                "Missing Supabase URL or Service Role Key for service role client."
            );
        });

        it("throws error when both environment variables are missing", () => {
            delete process.env.NEXT_PUBLIC_SUPABASE_URL;
            delete process.env.SUPABASE_SERVICE_ROLE_KEY;

            expect(() => createServiceRoleClient()).toThrow(
                "Missing Supabase URL or Service Role Key for service role client."
            );
        });
    });
});
