import { UserClaim<PERSON><PERSON>, setUserClaim, getUserClaim } from "@/utils/user-claims-client";
import { getSupabaseClient } from "@/utils/supabase/client";

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn()
}));

describe("user-claims-client", () => {
    let mockSupabaseClient: any;

    // Store the original console.error
    const originalConsoleError = console.error;

    beforeEach(() => {
        jest.clearAllMocks();
        // Temporarily suppress console.error for tests that expect errors
        console.error = jest.fn();

        mockSupabaseClient = {
            from: jest.fn(() => mockSupabaseClient),
            upsert: jest.fn(() => ({ error: null })),
            select: jest.fn(() => mockSupabaseClient),
            eq: jest.fn(() => mockSupabaseClient),
            maybeSingle: jest.fn(() => ({ data: null, error: null }))
        };
        (getSupabaseClient as jest.Mock).mockReturnValue(mockSupabaseClient);
    });

    afterEach(() => {
        // Restore the original console.error after each test
        console.error = originalConsoleError;
    });

    describe("setUserClaim", () => {
        it("should successfully upsert a user claim without errors", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.USER_EMAIL;
            const claimValue = "<EMAIL>";

            await setUserClaim(mockSupabaseClient, userId, claimKey, claimValue);

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSupabaseClient.upsert).toHaveBeenCalledWith(
                [{ user_id: userId, claim_key: claimKey, claim_value: claimValue }],
                { onConflict: "user_id,claim_key" }
            );
        });

        it("should throw an error if userId is not provided", async () => {
            const claimKey = UserClaimKey.USER_EMAIL;
            const claimValue = "<EMAIL>";

            await expect(setUserClaim(mockSupabaseClient, "", claimKey, claimValue)).rejects.toThrow(
                "No user ID provided"
            );
        });

        it("should successfully update an existing user claim", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.USER_EMAIL;
            const updatedClaimValue = "<EMAIL>";

            // Simulate the claim already existing (though upsert handles this internally)
            // The key is to ensure upsert is called with the new value

            await setUserClaim(mockSupabaseClient, userId, claimKey, updatedClaimValue);

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSupabaseClient.upsert).toHaveBeenCalledWith(
                [{ user_id: userId, claim_key: claimKey, claim_value: updatedClaimValue }],
                { onConflict: "user_id,claim_key" }
            );
        });

        it("should throw an error if the Supabase upsert operation fails", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.USER_EMAIL;
            const claimValue = "<EMAIL>";
            const mockError = new Error("Upsert failed");

            mockSupabaseClient.upsert.mockResolvedValueOnce({ error: mockError });

            await expect(setUserClaim(mockSupabaseClient, userId, claimKey, claimValue)).rejects.toThrow(
                "Upsert failed"
            );
        });
    });

    describe("getUserClaim", () => {
        it("should successfully retrieve an existing user claim", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.USER_EMAIL;
            const mockClaimValue = "<EMAIL>";

            mockSupabaseClient.maybeSingle.mockResolvedValueOnce({
                data: { claim_value: mockClaimValue },
                error: null
            });

            const result = await getUserClaim(mockSupabaseClient, userId, claimKey);

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSupabaseClient.select).toHaveBeenCalledWith("claim_value");
            expect(mockSupabaseClient.eq).toHaveBeenCalledWith("user_id", userId);
            expect(mockSupabaseClient.eq).toHaveBeenCalledWith("claim_key", claimKey);
            expect(result).toBe(mockClaimValue);
        });

        it("should return null if the requested claim does not exist for the user", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.ONBOARDING_STAGE;

            mockSupabaseClient.maybeSingle.mockResolvedValueOnce({
                data: null,
                error: null
            });

            const result = await getUserClaim(mockSupabaseClient, userId, claimKey);

            expect(result).toBeNull();
        });

        it("should throw an error if userId is not provided", async () => {
            const claimKey = UserClaimKey.USER_EMAIL;

            await expect(getUserClaim(mockSupabaseClient, "", claimKey)).rejects.toThrow("No user ID provided");
        });

        it("should throw an error if the Supabase select operation fails", async () => {
            const userId = "test-user-id";
            const claimKey = UserClaimKey.USER_EMAIL;
            const mockError = new Error("Select failed");

            mockSupabaseClient.maybeSingle.mockResolvedValueOnce({
                data: null,
                error: mockError
            });

            await expect(getUserClaim(mockSupabaseClient, userId, claimKey)).rejects.toThrow("Select failed");
        });
    });
});
