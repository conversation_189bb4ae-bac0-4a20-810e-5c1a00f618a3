import { renderHook, act } from "@testing-library/react";
import { ReactNode } from "react";

import { SignupPreferencesProvider, useSignupPreferences } from "@/contexts/signup-preferences-context";
import { useSignupPreferencesSave } from "@/hooks/use-signup-preferences-save";

// Mock dependencies
jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn(),
    useSignIn: jest.fn(),
    useSignUp: jest.fn()
}));

jest.mock("@/app/actions/signup-actions", () => ({
    saveSignupPreferences: jest.fn()
}));

jest.mock("@/utils/user-claims-client", () => ({
    getUserClaim: jest.fn(),
    setUserClaim: jest.fn(),
    UserClaimKey: {
        ACCEPTED_TERMS: "accepted_terms",
        SUBSCRIBED_TO_UPDATES: "subscribed_to_updates"
    }
}));

jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({}))
}));

jest.mock("@/hooks/use-signup-preferences-save");

const mockUseUser = require("@clerk/nextjs").useUser;
const mockUseSignIn = require("@clerk/nextjs").useSignIn;
const mockUseSignUp = require("@clerk/nextjs").useSignUp;
const mockSaveSignupPreferences = require("@/app/actions/signup-actions").saveSignupPreferences;
const mockGetUserClaim = require("@/utils/user-claims-client").getUserClaim;
const mockSetUserClaim = require("@/utils/user-claims-client").setUserClaim;
const mockUseSignupPreferencesSave = useSignupPreferencesSave as jest.MockedFunction<typeof useSignupPreferencesSave>;

// Mock sessionStorage
const mockSessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
};
Object.defineProperty(window, "sessionStorage", { value: mockSessionStorage });

// Test wrapper with context
const TestWrapper = ({ children }: { children: ReactNode }) => (
    <SignupPreferencesProvider>{children}</SignupPreferencesProvider>
);

describe("Signup Preferences Integration Flow", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Default mocks
        mockUseUser.mockReturnValue({ user: null });
        mockUseSignIn.mockReturnValue({
            isLoaded: true,
            signIn: { create: jest.fn() }
        });
        mockUseSignUp.mockReturnValue({
            isLoaded: true,
            signUp: { create: jest.fn() }
        });
        mockSaveSignupPreferences.mockResolvedValue({ success: true });
        mockGetUserClaim.mockResolvedValue(null);
        mockSetUserClaim.mockResolvedValue(undefined);
        mockUseSignupPreferencesSave.mockReturnValue({
            isSaving: false,
            saveResult: null,
            performSave: jest.fn()
        });
        mockSessionStorage.getItem.mockReturnValue(null);
    });

    describe("Context Persistence Flow", () => {
        it("should persist preferences in sessionStorage when user updates preferences", () => {
            const { result } = renderHook(() => useSignupPreferences(), {
                wrapper: TestWrapper
            });

            // Update preferences
            act(() => {
                result.current.setAcceptedTerms(true);
            });

            act(() => {
                result.current.setSubscribeNewsletter(false);
            });

            // Should save to sessionStorage
            expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
                "milgapo_signup_preferences",
                expect.stringContaining('"acceptedTerms":true')
            );
            expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
                "milgapo_signup_preferences",
                expect.stringContaining('"subscribeNewsletter":false')
            );
        });

        it("should load preferences from sessionStorage on hook mount", () => {
            const storedPreferences = {
                preferences: { acceptedTerms: true, subscribeNewsletter: false },
                isSet: true
            };
            mockSessionStorage.getItem.mockReturnValue(JSON.stringify(storedPreferences));

            const { result } = renderHook(() => useSignupPreferences(), {
                wrapper: TestWrapper
            });

            // Should reflect stored preferences
            expect(result.current.state.preferences.acceptedTerms).toBe(true);
            expect(result.current.state.preferences.subscribeNewsletter).toBe(false);
            expect(result.current.state.isSet).toBe(true);
        });
    });

    describe("Save Hook Integration", () => {
        it("should trigger save when user is available and preferences exist", async () => {
            // Mock user becoming available
            mockUseUser.mockReturnValue({ user: { id: "test-user-id" } });

            // Mock context with preferences
            const storedPreferences = {
                preferences: { acceptedTerms: true, subscribeNewsletter: false },
                isSet: true
            };
            mockSessionStorage.getItem.mockReturnValue(JSON.stringify(storedPreferences));

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Save hook should be called with user available
            expect(result.current).toBeDefined();
            expect(typeof result.current.performSave).toBe('function');
        });

        it("should handle save success", async () => {
            mockUseSignupPreferencesSave.mockReturnValue({
                isSaving: false,
                saveResult: { success: true, usedDefaults: false },
                performSave: jest.fn()
            });

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Should handle successful save without errors
            expect(result.current.saveResult?.success).toBe(true);
        });

        it("should handle save with defaults", async () => {
            mockUseSignupPreferencesSave.mockReturnValue({
                isSaving: false,
                saveResult: { success: true, usedDefaults: true },
                performSave: jest.fn()
            });

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Should handle default save without errors
            expect(result.current.saveResult?.success).toBe(true);
            expect(result.current.saveResult?.usedDefaults).toBe(true);
        });
    });

    describe("Error Scenarios", () => {
        it("should handle sessionStorage errors gracefully", () => {
            mockSessionStorage.setItem.mockImplementation(() => {
                throw new Error("Storage quota exceeded");
            });

            const { result } = renderHook(() => useSignupPreferences(), {
                wrapper: TestWrapper
            });

            // Should not throw when sessionStorage fails
            expect(() => {
                act(() => {
                    result.current.setAcceptedTerms(true);
                });
            }).not.toThrow();
        });

        it("should handle save failures gracefully", async () => {
            mockUseSignupPreferencesSave.mockReturnValue({
                isSaving: false,
                saveResult: { success: false, error: "Save failed" },
                performSave: jest.fn()
            });

            const { result } = renderHook(() => useSignupPreferencesSave(), {
                wrapper: TestWrapper
            });

            // Should handle save failure without throwing
            expect(result.current.saveResult?.success).toBe(false);
            expect(result.current.saveResult?.error).toBe("Save failed");
        });
    });

    describe("Context Cleanup", () => {
        it("should clear sessionStorage when preferences are cleared", () => {
            const { result } = renderHook(() => useSignupPreferences(), {
                wrapper: TestWrapper
            });

            // Set some preferences first
            act(() => {
                result.current.setAcceptedTerms(true);
            });

            // Clear preferences
            act(() => {
                result.current.clearPreferences();
            });

            // Should clear sessionStorage
            expect(mockSessionStorage.removeItem).toHaveBeenCalledWith("milgapo_signup_preferences");
        });
    });

    describe("Context State Management", () => {
        it("should maintain state consistency across hook calls", () => {
            const { result } = renderHook(() => useSignupPreferences(), {
                wrapper: TestWrapper
            });

            // Initial state
            expect(result.current.state.preferences.acceptedTerms).toBe(false);
            expect(result.current.state.preferences.subscribeNewsletter).toBe(true);
            expect(result.current.state.isSet).toBe(false);

            // Update state
            act(() => {
                result.current.setAcceptedTerms(true);
            });

            // State should be updated
            expect(result.current.state.preferences.acceptedTerms).toBe(true);
            expect(result.current.state.isSet).toBe(true);
        });
    });
});
