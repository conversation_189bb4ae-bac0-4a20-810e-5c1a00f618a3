import { checkScholarshipEligibility, evaluateCondition } from "@/app/services/scholarship-eligibility";
import type { Tables } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockSupabaseClient = {
    from: jest.fn()
};

const mockError = { message: "Database error", code: "500", details: "", hint: "" };

describe("evaluateCondition", () => {
    it("should return false if answer is undefined", () => {
        expect(evaluateCondition({ id: "1", type: "in", question_id: "q1" }, undefined)).toBe(false);
    });

    describe("'in' type condition", () => {
        const condition = {
            id: "1",
            question_id: "q1",
            type: "in",
            value: ["option1", "option2"]
        };

        it("should return true if answer is in the value array", () => {
            expect(evaluateCondition(condition, "option1")).toBe(true);
        });

        it("should return false if answer is not in the value array", () => {
            expect(evaluateCondition(condition, "option3")).toBe(false);
        });

        it("should handle JSON answer with id correctly", () => {
            const jsonCondition = {
                ...condition,
                value: { values: ["id1", "id2"] }
            };
            expect(evaluateCondition(jsonCondition, JSON.stringify({ id: "id1" }))).toBe(true);
            expect(evaluateCondition(jsonCondition, JSON.stringify({ id: "id3" }))).toBe(false);
        });

        it("should return false for unparsable JSON answer", () => {
            const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
            const jsonCondition = {
                ...condition,
                value: { values: ["id1"] }
            };
            expect(evaluateCondition(jsonCondition, "{ not: json }")).toBe(false);
            consoleErrorSpy.mockRestore();
        });
    });

    describe("'range' type condition", () => {
        const condition = {
            id: "2",
            question_id: "q2",
            type: "range",
            value: { min: 10, max: 20 }
        };

        it("should return true for a value within the range", () => {
            expect(evaluateCondition(condition, "15")).toBe(true);
        });

        it("should return true for a value at the edge of the range", () => {
            expect(evaluateCondition(condition, "10")).toBe(true);
            expect(evaluateCondition(condition, "20")).toBe(true);
        });

        it("should return false for a value outside the range", () => {
            expect(evaluateCondition(condition, "5")).toBe(false);
            expect(evaluateCondition(condition, "25")).toBe(false);
        });

        it("should handle open-ended ranges", () => {
            const minOnly = { ...condition, value: { min: 10 } };
            const maxOnly = { ...condition, value: { max: 20 } };
            expect(evaluateCondition(minOnly, "100")).toBe(true);
            expect(evaluateCondition(minOnly, "5")).toBe(false);
            expect(evaluateCondition(maxOnly, "0")).toBe(true);
            expect(evaluateCondition(maxOnly, "25")).toBe(false);
        });

        it("should return false if value is an array", () => {
            const arrayValueCondition = { ...condition, value: ["a", "b"] as any };
            expect(evaluateCondition(arrayValueCondition, "15")).toBe(false);
        });
    });

    describe("'date_range' type condition", () => {
        const today = new Date();
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + 10);
        const pastDate = new Date(today);
        pastDate.setDate(today.getDate() - 10);

        it("should evaluate 'greater_than' operator correctly", () => {
            const condition = {
                id: "3",
                question_id: "q3",
                type: "date_range",
                value: { operator: "greater_than", days_from_today: 5 }
            };
            expect(evaluateCondition(condition, pastDate.toISOString())).toBe(false);
            expect(evaluateCondition(condition, futureDate.toISOString())).toBe(true);
        });

        it("should evaluate 'less_than' operator correctly", () => {
            const condition = {
                id: "4",
                question_id: "q4",
                type: "date_range",
                value: { operator: "less_than", days_from_today: 5 }
            };
            expect(evaluateCondition(condition, pastDate.toISOString())).toBe(true);
            expect(evaluateCondition(condition, futureDate.toISOString())).toBe(false);
        });

        it("should evaluate date between start and end dates", () => {
            const condition = {
                id: "5",
                question_id: "q5",
                type: "date_range",
                value: {
                    start_date: pastDate.toISOString(),
                    end_date: futureDate.toISOString()
                }
            };
            const middleDate = new Date();
            expect(evaluateCondition(condition, middleDate.toISOString())).toBe(true);
        });

        it("should return false for an invalid date answer", () => {
            const condition = {
                id: "5",
                question_id: "q5",
                type: "date_range",
                value: {
                    start_date: pastDate.toISOString(),
                    end_date: futureDate.toISOString()
                }
            };
            expect(evaluateCondition(condition, "not a date")).toBe(false);
        });
    });

    it("should return false for an unknown condition type", () => {
        const condition = { id: "6", question_id: "q6", type: "unknown" };
        expect(evaluateCondition(condition, "any value")).toBe(false);
    });
});

describe("checkScholarshipEligibility", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockReturnValue(mockSupabaseClient);
    });

    const mockScholarships: Partial<Tables<"scholarships">>[] = [
        { id: "s1", title: "Test Scholarship 1", slug: "test-1", is_active: true }
    ];
    const mockAnswers: Partial<Tables<"answers">>[] = [{ user_id: "u1", question_id: "q1", answer: "a1" }];
    const mockConditions: Partial<Tables<"conditions">>[] = [
        { id: "c1", question_id: "q1", type: "in", value: ["a1"] }
    ];

    const mockDb = (data: Record<string, any[]>): ((table: string) => { select: any; eq: any }) => {
        return (table: string) => {
            const mockSelect = {
                eq: jest.fn().mockReturnThis(),
                then: (resolve: (value: { data: any; error: any }) => void) =>
                    resolve({ data: data[table] || [], error: null })
            };
            return {
                select: jest.fn(() => mockSelect),
                ...mockSelect
            };
        };
    };

    it("should return an error if fetching scholarships fails", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation((table: string) => {
            const selectResult = {
                eq: jest.fn().mockResolvedValue({ data: null, error: mockError }),
                then: (resolve: any) => resolve({ data: null, error: mockError })
            };

            if (table === "scholarships") {
                return {
                    select: () => selectResult
                };
            }
            return {
                select: () => ({
                    eq: jest.fn().mockResolvedValue({ data: [], error: null }),
                    then: (resolve: any) => resolve({ data: [], error: null })
                })
            };
        });

        const result = await checkScholarshipEligibility("u1");
        expect(result.error).toContain("Failed to fetch scholarships");
        expect(result.data).toBeNull();
    });

    it("should return an error if fetching user answers fails", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation((table: string) => {
            const selectResult = {
                eq: jest.fn().mockResolvedValue({ data: null, error: mockError }),
                then: (resolve: any) => resolve({ data: null, error: mockError })
            };

            if (table === "answers") {
                return { select: () => selectResult };
            }
            return {
                select: () => ({
                    eq: jest.fn().mockResolvedValue({ data: [], error: null }),
                    then: (resolve: any) => resolve({ data: [{ id: "s1" }], error: null })
                })
            };
        });

        const result = await checkScholarshipEligibility("u1");
        expect(result.error).toContain("Failed to fetch user answers");
        expect(result.data).toBeNull();
    });

    it("should return an error if fetching conditions fails", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation((table: string) => {
            if (table === "conditions") {
                return {
                    select: jest.fn().mockResolvedValue({ data: null, error: mockError })
                };
            }
            return {
                select: () => ({
                    eq: jest.fn().mockResolvedValue({ data: [], error: null }),
                    then: (resolve: any) => resolve({ data: [{ id: "s1" }], error: null })
                })
            };
        });

        const result = await checkScholarshipEligibility("u1");
        expect(result.error).toContain("Failed to fetch conditions");
        expect(result.data).toBeNull();
    });

    it("should return an empty array if no scholarships are found", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation(mockDb({}));
        const result = await checkScholarshipEligibility("u1");
        expect(result.data).toEqual([]);
        expect(result.error).toBeNull();
    });

    it("should mark a scholarship as eligible if it has no conditions", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation(mockDb({ scholarships: mockScholarships }));
        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(true);
    });

    it("should be ELIGIBLE when all direct conditions are met", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: mockAnswers,
                conditions: mockConditions,
                link_scholarship_to_condition: [{ scholarship_id: "s1", condition_id: "c1" }]
            })
        );
        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(true);
    });

    it("should be INELIGIBLE when a direct condition is not met", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: [{ ...mockAnswers[0], answer: "wrong_answer" }],
                conditions: mockConditions,
                link_scholarship_to_condition: [{ scholarship_id: "s1", condition_id: "c1" }]
            })
        );
        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(false);
    });

    it("should be INELIGIBLE and report missing answers", async () => {
        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: [], // No answers from user
                conditions: mockConditions,
                link_scholarship_to_condition: [{ scholarship_id: "s1", condition_id: "c1" }]
            })
        );
        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(false);
        expect(result.data?.[0].missingAnswers).toEqual(["q1"]);
    });

    it("should be ELIGIBLE if at least one condition group is fully met", async () => {
        const groupConditions = [
            { id: "c1", group_id: "g1", question_id: "q1", type: "in", value: ["a1"] },
            { id: "c2", group_id: "g1", question_id: "q2", type: "in", value: ["a2"] }
        ];
        const groupAnswers = [
            { user_id: "u1", question_id: "q1", answer: "a1" },
            { user_id: "u1", question_id: "q2", answer: "a2" }
        ];

        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: groupAnswers,
                conditions: groupConditions,
                link_scholarship_to_condition_groups: [{ scholarship_id: "s1", group_id: "g1" }]
            })
        );

        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(true);
    });

    it("should be INELIGIBLE if no condition groups are fully met", async () => {
        const groupConditions = [
            { id: "c1", group_id: "g1", question_id: "q1", type: "in", value: ["a1"] },
            { id: "c2", group_id: "g1", question_id: "q2", type: "in", value: ["a2"] }
        ];
        const groupAnswers = [{ user_id: "u1", question_id: "q1", answer: "a1" }]; // Missing q2 answer

        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: groupAnswers,
                conditions: groupConditions,
                link_scholarship_to_condition_groups: [{ scholarship_id: "s1", group_id: "g1" }]
            })
        );

        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(false);
    });

    it("should be ELIGIBLE when direct conditions AND a condition group are met", async () => {
        const directCondition = { id: "c-direct", question_id: "q-direct", type: "in", value: ["a-direct"] };
        const groupConditions = [
            { id: "c1", group_id: "g1", question_id: "q1", type: "in", value: ["a1"] },
            { id: "c2", group_id: "g1", question_id: "q2", type: "in", value: ["a2"] }
        ];
        const allAnswers = [
            { user_id: "u1", question_id: "q-direct", answer: "a-direct" },
            { user_id: "u1", question_id: "q1", answer: "a1" },
            { user_id: "u1", question_id: "q2", answer: "a2" }
        ];

        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: allAnswers,
                conditions: [directCondition, ...groupConditions],
                link_scholarship_to_condition: [{ scholarship_id: "s1", condition_id: "c-direct" }],
                link_scholarship_to_condition_groups: [{ scholarship_id: "s1", group_id: "g1" }]
            })
        );

        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(true);
    });

    it("should be INELIGIBLE if direct conditions are met but no condition group is", async () => {
        const directCondition = { id: "c-direct", question_id: "q-direct", type: "in", value: ["a-direct"] };
        const groupConditions = [
            { id: "c1", group_id: "g1", question_id: "q1", type: "in", value: ["a1"] },
            { id: "c2", group_id: "g1", question_id: "q2", type: "in", value: ["a2"] }
        ];
        // Missing answer for q2
        const partialAnswers = [
            { user_id: "u1", question_id: "q-direct", answer: "a-direct" },
            { user_id: "u1", question_id: "q1", answer: "a1" }
        ];

        (mockSupabaseClient.from as jest.Mock).mockImplementation(
            mockDb({
                scholarships: mockScholarships,
                answers: partialAnswers,
                conditions: [directCondition, ...groupConditions],
                link_scholarship_to_condition: [{ scholarship_id: "s1", condition_id: "c-direct" }],
                link_scholarship_to_condition_groups: [{ scholarship_id: "s1", group_id: "g1" }]
            })
        );

        const result = await checkScholarshipEligibility("u1");
        expect(result.data?.[0].isEligible).toBe(false);
    });
});
