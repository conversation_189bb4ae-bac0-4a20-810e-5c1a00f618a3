import { sanitizeFaqData, validateFaqData, TEXTS } from "@/lib/faq-constants";

describe("FAQ Constants", () => {
    describe("sanitizeFaqData", () => {
        it("sanitizes malicious content from question and answer", () => {
            const input = {
                question: '<script>alert("xss")</script>What is this?',
                answer: '<script>alert("xss")</script>This is a test answer.'
            };

            const result = sanitizeFaqData(input);

            expect(result.question).toBe("What is this?");
            expect(result.answer).toBe("This is a test answer.");
            expect(result.question).not.toContain("<script>");
            expect(result.answer).not.toContain("<script>");
            expect(result.question).not.toContain("alert");
            expect(result.answer).not.toContain("alert");
        });

        it("trims whitespace from question and answer", () => {
            const input = {
                question: "   What is this?   ",
                answer: "   This is a test answer.   "
            };

            const result = sanitizeFaqData(input);

            expect(result.question).toBe("What is this?");
            expect(result.answer).toBe("This is a test answer.");
        });

        it("enforces max length limits", () => {
            const longQuestion = "a".repeat(600);
            const longAnswer = "b".repeat(2500);

            const input = {
                question: longQuestion,
                answer: longAnswer
            };

            const result = sanitizeFaqData(input);

            expect(result.question.length).toBe(500); // Max for question
            expect(result.answer.length).toBe(2000); // Max for answer
        });

        it("handles empty strings", () => {
            const input = {
                question: "",
                answer: ""
            };

            const result = sanitizeFaqData(input);

            expect(result.question).toBe("");
            expect(result.answer).toBe("");
        });

        it("removes HTML tags but keeps content", () => {
            const input = {
                question: "<p>What is <strong>this</strong>?</p>",
                answer: "<div>This is a <em>test</em> answer.</div>"
            };

            const result = sanitizeFaqData(input);

            expect(result.question).toBe("What is this?");
            expect(result.answer).toBe("This is a test answer.");
        });
    });

    describe("validateFaqData", () => {
        it("returns valid for proper FAQ data", () => {
            const input = {
                question: "What is this?",
                answer: "This is a test answer."
            };

            const result = validateFaqData(input);

            expect(result.valid).toBe(true);
            expect(result.error).toBeUndefined();
        });

        it("returns invalid for empty question", () => {
            const input = {
                question: "",
                answer: "This is a test answer."
            };

            const result = validateFaqData(input);

            expect(result.valid).toBe(false);
            expect(result.error).toBe(TEXTS.QUESTION_REQUIRED);
        });

        it("returns invalid for empty answer", () => {
            const input = {
                question: "What is this?",
                answer: ""
            };

            const result = validateFaqData(input);

            expect(result.valid).toBe(false);
            expect(result.error).toBe(TEXTS.ANSWER_REQUIRED);
        });

        it("returns invalid for whitespace-only question", () => {
            const input = {
                question: "   ",
                answer: "This is a test answer."
            };

            const result = validateFaqData(input);

            expect(result.valid).toBe(false);
            expect(result.error).toBe(TEXTS.QUESTION_REQUIRED);
        });

        it("returns invalid for whitespace-only answer", () => {
            const input = {
                question: "What is this?",
                answer: "   "
            };

            const result = validateFaqData(input);

            expect(result.valid).toBe(false);
            expect(result.error).toBe(TEXTS.ANSWER_REQUIRED);
        });
    });

    describe("TEXTS constants", () => {
        it("includes all required error message constants", () => {
            expect(TEXTS.FAQ_DELETE_ERROR).toBeDefined();
            expect(TEXTS.FAQ_FETCH_ERROR).toBeDefined();
            expect(TEXTS.FAQ_NOT_FOUND).toBeDefined();
            expect(TEXTS.FAQ_UPDATE_ERROR).toBeDefined();
            expect(TEXTS.FAQ_CREATE_ERROR).toBeDefined();
            expect(TEXTS.QUESTION_REQUIRED).toBeDefined();
            expect(TEXTS.ANSWER_REQUIRED).toBeDefined();
        });

        it("includes all required UI text constants", () => {
            expect(TEXTS.PAGE_TITLE).toBeDefined();
            expect(TEXTS.NEW_FAQ).toBeDefined();
            expect(TEXTS.LOADING).toBeDefined();
            expect(TEXTS.QUESTION_LABEL).toBeDefined();
            expect(TEXTS.ANSWER_LABEL).toBeDefined();
            expect(TEXTS.CREATE_BUTTON_TEXT).toBeDefined();
            expect(TEXTS.UPDATE_BUTTON_TEXT).toBeDefined();
        });
    });
});
