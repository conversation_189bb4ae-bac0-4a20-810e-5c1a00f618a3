import { cn, shuffleArray } from "@/lib/utils";

describe("cn", () => {
    it("should merge class names correctly", () => {
        expect(cn("bg-red-500", "text-white")).toBe("bg-red-500 text-white");
        expect(cn("p-4", { "font-bold": true })).toBe("p-4 font-bold");
        expect(cn("p-4", "p-2")).toBe("p-2"); // twMerge handles conflicts
    });
});

describe("shuffleArray", () => {
    it("should return an array with the same length", () => {
        const originalArray = [1, 2, 3, 4, 5];
        const shuffled = shuffleArray(originalArray);
        expect(shuffled.length).toBe(originalArray.length);
    });

    it("should contain the same elements as the original array", () => {
        const originalArray = [1, 2, 3, 4, 5];
        const shuffled = shuffleArray(originalArray);
        expect(shuffled.sort()).toEqual(originalArray.sort());
    });

    it("should not modify the original array", () => {
        const originalArray = [1, 2, 3, 4, 5];
        const originalArrayCopy = [...originalArray];
        shuffleArray(originalArray);
        expect(originalArray).toEqual(originalArrayCopy);
    });

    it("should produce a different order for the elements (most of the time)", () => {
        const originalArray = Array.from({ length: 100 }, (_, i) => i);
        const shuffled = shuffleArray(originalArray);
        // This test might fail in the very rare case that shuffle returns the same array
        // but for a 100-element array, this is astronomically unlikely.
        expect(shuffled).not.toEqual(originalArray);
    });

    it("should handle an empty array", () => {
        const originalArray: number[] = [];
        const shuffled = shuffleArray(originalArray);
        expect(shuffled).toEqual([]);
    });

    it("should handle an array with one element", () => {
        const originalArray = [1];
        const shuffled = shuffleArray(originalArray);
        expect(shuffled).toEqual([1]);
    });
});
