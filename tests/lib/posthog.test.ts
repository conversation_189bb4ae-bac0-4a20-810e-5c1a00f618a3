import { PostHog } from "posthog-node";

const mockPostHogInstance = {
    capture: jest.fn(),
    identify: jest.fn(),
    shutdown: jest.fn()
};

const mockPostHogConstructor = jest.fn(() => mockPostHogInstance);

jest.mock("posthog-node", () => ({
    PostHog: mockPostHogConstructor
}));

describe("PostHogClient", () => {
    let PostHogClient: () => PostHog | null;

    beforeEach(() => {
        jest.clearAllMocks();
        mockPostHogConstructor.mockClear();

        if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) {
            process.env.NEXT_PUBLIC_POSTHOG_KEY = "test-api-key";
        }

        jest.resetModules();

        PostHogClient = require("@/lib/posthog").default;
    });

    it("should create a PostHog instance on first call", () => {
        const client = PostHogClient();

        expect(mockPostHogConstructor).toHaveBeenCalledTimes(1);
        expect(mockPostHogConstructor).toHaveBeenCalledWith(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
            host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
            flushAt: 1,
            flushInterval: 0
        });
        expect(client).toBeDefined();
        expect(client).toBe(mockPostHogInstance);
    });

    it("should return the same instance on subsequent calls (singleton pattern)", () => {
        const client1 = PostHogClient();
        const client2 = PostHogClient();
        const client3 = PostHogClient();

        expect(mockPostHogConstructor).toHaveBeenCalledTimes(1);

        expect(client1).toBe(client2);
        expect(client2).toBe(client3);
        expect(client1).toBe(client3);
        expect(client1).toBe(mockPostHogInstance);
    });

    it("should use environment variables for configuration", () => {
        const originalKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;
        const originalHost = process.env.NEXT_PUBLIC_POSTHOG_HOST;

        process.env.NEXT_PUBLIC_POSTHOG_KEY = "test-key-123";
        process.env.NEXT_PUBLIC_POSTHOG_HOST = "https://test.posthog.com";

        jest.resetModules();
        const PostHogClientWithNewEnv = require("@/lib/posthog").default;

        PostHogClientWithNewEnv();

        expect(mockPostHogConstructor).toHaveBeenCalledWith("test-key-123", {
            host: "https://test.posthog.com",
            flushAt: 1,
            flushInterval: 0
        });

        process.env.NEXT_PUBLIC_POSTHOG_KEY = originalKey;
        process.env.NEXT_PUBLIC_POSTHOG_HOST = originalHost;
    });

    it("should configure PostHog with correct options", () => {
        PostHogClient();

        expect(mockPostHogConstructor).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
                flushAt: 1,
                flushInterval: 0
            })
        );
    });

    it("should throw error when API key is missing", () => {
        const originalKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;
        delete process.env.NEXT_PUBLIC_POSTHOG_KEY;

        jest.resetModules();
        const PostHogClientWithoutKey = require("@/lib/posthog").default;

        expect(() => PostHogClientWithoutKey()).toThrow(
            "PostHog API key (NEXT_PUBLIC_POSTHOG_KEY) is not defined in environment variables"
        );

        process.env.NEXT_PUBLIC_POSTHOG_KEY = originalKey;
    });

    it("should throw error when API key is empty string", () => {
        const originalKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;
        process.env.NEXT_PUBLIC_POSTHOG_KEY = "";

        jest.resetModules();
        const PostHogClientWithEmptyKey = require("@/lib/posthog").default;

        expect(() => PostHogClientWithEmptyKey()).toThrow(
            "PostHog API key (NEXT_PUBLIC_POSTHOG_KEY) is not defined in environment variables"
        );

        process.env.NEXT_PUBLIC_POSTHOG_KEY = originalKey;
    });
});
