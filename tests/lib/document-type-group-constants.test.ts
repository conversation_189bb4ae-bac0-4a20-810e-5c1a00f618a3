import {
    TEXTS,
    DocumentTypeGroupFormValues,
    defaultValues,
    DocumentTypeGroupInsert,
    DocumentTypeGroupUpdate
} from "@/lib/document-type-group-constants";

describe("Document Type Group Constants", () => {
    describe("TEXTS", () => {
        it("should have all required error messages", () => {
            expect(TEXTS.FETCH_GROUPS_ERROR).toBe("שגיאה בטעינת קבוצות סוגי מסמכים");
            expect(TEXTS.FETCH_GROUP_ERROR).toBe("שגיאה בטעינת קבוצת סוגי מסמכים");
            expect(TEXTS.NAME_REQUIRED).toBe("שם קבוצת המסמכים הוא שדה חובה");
            expect(TEXTS.CREATE_ERROR).toBe("שגיאה ביצירת קבוצת המסמכים");
            expect(TEXTS.UPDATE_ERROR).toBe("שגיאה בעדכון קבוצת המסמכים");
        });

        it("should have all required success messages", () => {
            expect(TEXTS.CREATE_SUCCESS).toBe("קבוצת המסמכים נוצרה בהצלחה");
            expect(TEXTS.UPDATE_SUCCESS).toBe("קבוצת המסמכים עודכנה בהצלחה");
        });

        it("should have all required form labels", () => {
            expect(TEXTS.NAME_LABEL).toBe("שם");
            expect(TEXTS.NAME_PLACEHOLDER).toBe("הזן שם לקבוצת המסמכים");
            expect(TEXTS.DESCRIPTION_LABEL).toBe("תיאור");
            expect(TEXTS.DESCRIPTION_PLACEHOLDER).toBe("הזן תיאור לקבוצת המסמכים");
        });

        it("should have all required button texts", () => {
            expect(TEXTS.CREATE_BUTTON).toBe("צור קבוצת מסמכים");
            expect(TEXTS.UPDATE_BUTTON).toBe("עדכן קבוצת מסמכים");
            expect(TEXTS.CANCEL_BUTTON).toBe("ביטול");
        });

        it("should have all required loading state texts", () => {
            expect(TEXTS.LOADING_GROUP).toBe("טוען קבוצת מסמכים...");
            expect(TEXTS.GROUP_NOT_FOUND).toBe("קבוצת המסמכים לא נמצאה");
        });

        it("should have all texts as non-empty strings", () => {
            Object.values(TEXTS).forEach((text) => {
                expect(typeof text).toBe("string");
                expect(text.length).toBeGreaterThan(0);
            });
        });
    });

    describe("DocumentTypeGroupFormValues", () => {
        it("should have correct interface shape", () => {
            const validFormValues: DocumentTypeGroupFormValues = {
                name: "Test Group",
                description: "Test Description"
            };

            expect(validFormValues.name).toBe("Test Group");
            expect(validFormValues.description).toBe("Test Description");
        });

        it("should accept empty strings", () => {
            const validFormValues: DocumentTypeGroupFormValues = {
                name: "",
                description: ""
            };

            expect(validFormValues.name).toBe("");
            expect(validFormValues.description).toBe("");
        });
    });

    describe("defaultValues", () => {
        it("should have correct default values", () => {
            expect(defaultValues.name).toBe("");
            expect(defaultValues.description).toBe("");
        });

        it("should conform to DocumentTypeGroupFormValues interface", () => {
            const values: DocumentTypeGroupFormValues = defaultValues;
            expect(values).toBeDefined();
        });
    });

    describe("Type Exports", () => {
        it("should export DocumentTypeGroupInsert type", () => {
            const insertData: DocumentTypeGroupInsert = {
                name: "Test Group",
                description: "Test Description"
            };

            expect(insertData.name).toBe("Test Group");
            expect(insertData.description).toBe("Test Description");
        });

        it("should export DocumentTypeGroupUpdate type", () => {
            const updateData: DocumentTypeGroupUpdate = {
                name: "Updated Group",
                description: "Updated Description",
                updated_at: new Date().toISOString()
            };

            expect(updateData.name).toBe("Updated Group");
            expect(updateData.description).toBe("Updated Description");
            expect(updateData.updated_at).toBeDefined();
        });

        it("should allow partial updates", () => {
            const partialUpdate: DocumentTypeGroupUpdate = {
                name: "Only name updated"
            };

            expect(partialUpdate.name).toBe("Only name updated");
            expect(partialUpdate.description).toBeUndefined();
        });

        it("should allow null description in database types", () => {
            const insertData: DocumentTypeGroupInsert = {
                name: "Test Group",
                description: null
            };

            const updateData: DocumentTypeGroupUpdate = {
                description: null
            };

            expect(insertData.description).toBeNull();
            expect(updateData.description).toBeNull();
        });
    });

    describe("Constants Structure", () => {
        it("should export all required constants", () => {
            expect(TEXTS).toBeDefined();
            expect(defaultValues).toBeDefined();
            expect(typeof TEXTS).toBe("object");
            expect(typeof defaultValues).toBe("object");
        });

        it("should not have any undefined values in TEXTS", () => {
            Object.entries(TEXTS).forEach(([key, value]) => {
                expect(value).toBeDefined();
                expect(value).not.toBeNull();
                expect(value).not.toBe("");
            });
        });
    });
});
