import {
    defaultScholarshipGroupFormValues,
    ICON_MAP,
    iconOptions,
    TEXTS,
    type ScholarshipGroupFormValues
} from "@/lib/scholarship-group-constants";

describe("Scholarship Group Constants", () => {
    describe("TEXTS", () => {
        it("should contain all required text constants", () => {
            expect(TEXTS.errorMessage).toBeDefined();
            expect(TEXTS.editSuccessMessage).toBeDefined();
            expect(TEXTS.editErrorMessage).toBeDefined();
            expect(TEXTS.editLoadingMessage).toBeDefined();
            expect(TEXTS.editNotFoundMessage).toBeDefined();
            expect(TEXTS.updateButtonText).toBeDefined();
            expect(TEXTS.cancelButtonText).toBeDefined();
            expect(TEXTS.createSuccessMessage).toBeDefined();
            expect(TEXTS.createErrorMessage).toBeDefined();
            expect(TEXTS.createButtonText).toBeDefined();

            expect(TEXTS.groupNameLabel).toBeDefined();
            expect(TEXTS.groupNamePlaceholder).toBeDefined();
            expect(TEXTS.groupNameRequired).toBeDefined();
            expect(TEXTS.groupSlugLabel).toBeDefined();
            expect(TEXTS.groupSlugPlaceholder).toBeDefined();
            expect(TEXTS.groupSlugPattern).toBeDefined();
            expect(TEXTS.groupSlugRequired).toBeDefined();
            expect(TEXTS.descriptionLabel).toBeDefined();
            expect(TEXTS.descriptionPlaceholder).toBeDefined();
            expect(TEXTS.descriptionRequired).toBeDefined();
            expect(TEXTS.iconLabel).toBeDefined();
            expect(TEXTS.iconPlaceholder).toBeDefined();
            expect(TEXTS.iconRequired).toBeDefined();
            expect(TEXTS.imageLabel).toBeDefined();
            expect(TEXTS.imageRequired).toBeDefined();

            expect(TEXTS.slugExistsError).toBeDefined();
            expect(TEXTS.webpDescription).toBeDefined();
            expect(TEXTS.fileTypeError).toBeDefined();
            expect(TEXTS.dragDropText).toBeDefined();
            expect(TEXTS.selectedFileText).toBeDefined();
            expect(TEXTS.fileSizeError).toBeDefined();
            expect(TEXTS.fileFormatDesc).toBeDefined();
            expect(TEXTS.apiErrorGeneral).toBeDefined();
            expect(TEXTS.apiErrorUpload).toBeDefined();
        });

        it("should have Hebrew text content", () => {
            expect(TEXTS.groupNameLabel).toMatch(/[\u0590-\u05FF]/);
            expect(TEXTS.createSuccessMessage).toMatch(/[\u0590-\u05FF]/);
            expect(TEXTS.editErrorMessage).toMatch(/[\u0590-\u05FF]/);
        });
    });

    describe("iconOptions", () => {
        it("should be an array of icon options", () => {
            expect(Array.isArray(iconOptions)).toBe(true);
            expect(iconOptions.length).toBeGreaterThan(0);
        });

        it("should have valid icon option structure", () => {
            iconOptions.forEach((option) => {
                expect(option).toHaveProperty("id");
                expect(option).toHaveProperty("label");
                expect(typeof option.id).toBe("string");
                expect(typeof option.label).toBe("string");
                expect(option.id.length).toBeGreaterThan(0);
                expect(option.label.length).toBeGreaterThan(0);
            });
        });

        it("should have unique icon IDs", () => {
            const ids = iconOptions.map((option) => option.id);
            const uniqueIds = new Set(ids);
            expect(uniqueIds.size).toBe(ids.length);
        });

        it("should contain expected default icons", () => {
            const ids = iconOptions.map((option) => option.id);
            expect(ids).toContain("graduation-cap");
            expect(ids).toContain("award");
            expect(ids).toContain("trophy");
            expect(ids).toContain("medal");
        });

        it("should have Hebrew labels", () => {
            iconOptions.forEach((option) => {
                expect(option.label).toMatch(/[\u0590-\u05FF]/);
            });
        });
    });

    describe("ICON_MAP", () => {
        it("should map all icon IDs to LucideIcon components", () => {
            iconOptions.forEach((option) => {
                expect(ICON_MAP).toHaveProperty(option.id);
                expect(ICON_MAP[option.id]).toBeDefined();

                expect(typeof ICON_MAP[option.id] === "function" || typeof ICON_MAP[option.id] === "object").toBe(true);
            });
        });

        it("should contain commonly used icons", () => {
            expect(ICON_MAP).toHaveProperty("graduation-cap");
            expect(ICON_MAP).toHaveProperty("award");
            expect(ICON_MAP).toHaveProperty("trophy");
            expect(ICON_MAP).toHaveProperty("books");
            expect(ICON_MAP).toHaveProperty("school");
        });

        it("should have proper component structure", () => {
            Object.values(ICON_MAP).forEach((IconComponent) => {
                expect(typeof IconComponent === "function" || typeof IconComponent === "object").toBe(true);

                expect(IconComponent.name || IconComponent.displayName).toBeTruthy();
            });
        });
    });

    describe("defaultScholarshipGroupFormValues", () => {
        it("should have valid default form values", () => {
            expect(defaultScholarshipGroupFormValues).toHaveProperty("title");
            expect(defaultScholarshipGroupFormValues).toHaveProperty("slug");
            expect(defaultScholarshipGroupFormValues).toHaveProperty("description");
            expect(defaultScholarshipGroupFormValues).toHaveProperty("icon");
            expect(defaultScholarshipGroupFormValues).toHaveProperty("image_file");
        });

        it("should have correct default types", () => {
            expect(typeof defaultScholarshipGroupFormValues.title).toBe("string");
            expect(typeof defaultScholarshipGroupFormValues.slug).toBe("string");
            expect(typeof defaultScholarshipGroupFormValues.description).toBe("string");
            expect(typeof defaultScholarshipGroupFormValues.icon).toBe("object");
            expect(defaultScholarshipGroupFormValues.image_file).toBeNull();
        });

        it("should have empty strings for text fields", () => {
            expect(defaultScholarshipGroupFormValues.title).toBe("");
            expect(defaultScholarshipGroupFormValues.slug).toBe("");
            expect(defaultScholarshipGroupFormValues.description).toBe("");
        });

        it("should have valid default icon", () => {
            expect(defaultScholarshipGroupFormValues.icon).toHaveProperty("id");
            expect(defaultScholarshipGroupFormValues.icon).toHaveProperty("label");
            expect(typeof defaultScholarshipGroupFormValues.icon.id).toBe("string");
            expect(typeof defaultScholarshipGroupFormValues.icon.label).toBe("string");

            const defaultIconId = defaultScholarshipGroupFormValues.icon.id;
            const iconExists = iconOptions.some((option) => option.id === defaultIconId);
            expect(iconExists).toBe(true);
        });

        it("should match ScholarshipGroupFormValues type", () => {
            const values: ScholarshipGroupFormValues = defaultScholarshipGroupFormValues;
            expect(values).toBeDefined();
        });
    });

    describe("Type consistency", () => {
        it("should have consistent icon IDs between ICON_MAP and iconOptions", () => {
            const iconMapKeys = Object.keys(ICON_MAP);
            const optionIds = iconOptions.map((option) => option.id);

            optionIds.forEach((id) => {
                expect(iconMapKeys).toContain(id);
            });

            expect(optionIds.every((id) => iconMapKeys.includes(id))).toBe(true);
        });

        it("should have proper Option type structure for icons", () => {
            iconOptions.forEach((option) => {
                expect(option).toMatchObject({
                    id: expect.any(String),
                    label: expect.any(String)
                });

                const keys = Object.keys(option);
                expect(keys.sort()).toEqual(["id", "label"]);
            });
        });
    });

    describe("Constants completeness", () => {
        it("should export all required constants", () => {
            expect(TEXTS).toBeDefined();
            expect(iconOptions).toBeDefined();
            expect(ICON_MAP).toBeDefined();
            expect(defaultScholarshipGroupFormValues).toBeDefined();
        });

        it("should have non-empty constant values", () => {
            expect(Object.keys(TEXTS).length).toBeGreaterThan(0);
            expect(iconOptions.length).toBeGreaterThan(0);
            expect(Object.keys(ICON_MAP).length).toBeGreaterThan(0);
            expect(Object.keys(defaultScholarshipGroupFormValues).length).toBeGreaterThan(0);
        });
    });
});
