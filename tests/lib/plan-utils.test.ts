import { getPlanDisplayName, PLAN_DISPLAY_NAMES } from "@/lib/plan-utils";

describe("getPlanDisplayName", () => {
    it("should return the correct display name for 'milgapro'", () => {
        expect(getPlanDisplayName("milgapro")).toBe(PLAN_DISPLAY_NAMES.milgapro);
    });

    it("should return the correct display name for 'elite'", () => {
        expect(getPlanDisplayName("elite")).toBe(PLAN_DISPLAY_NAMES.elite);
    });

    it("should return the correct display name for 'vip'", () => {
        expect(getPlanDisplayName("vip")).toBe(PLAN_DISPLAY_NAMES.vip);
    });

    it("should return the correct display name for 'free'", () => {
        expect(getPlanDisplayName("free")).toBe(PLAN_DISPLAY_NAMES.free);
    });

    it("should return an empty string for an undefined plan type", () => {
        expect(getPlanDisplayName(undefined)).toBe("");
    });

    it("should return the plan type itself if it's not a recognized plan", () => {
        expect(getPlanDisplayName("unknown_plan")).toBe("unknown_plan");
    });

    it("should be case-sensitive and return the input if case does not match", () => {
        expect(getPlanDisplayName("MilgaPro")).toBe("MilgaPro");
    });
});
