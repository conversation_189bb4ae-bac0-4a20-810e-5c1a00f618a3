import { getUserOrgRole, isAdminRole, isAdminFromSessionClaims } from "@/lib/org-role";

describe("getUserOrgRole", () => {
    it('should return "admin" when sessionClaims indicate org:admin role', () => {
        const sessionClaims = { organizations: { org_abc: "org:admin" } };
        expect(getUserOrgRole(sessionClaims)).toBe("admin");
    });

    it('should return "employee" when sessionClaims indicate org:employee role', () => {
        const sessionClaims = { organizations: { org_xyz: "org:employee" } };
        expect(getUserOrgRole(sessionClaims)).toBe("employee");
    });

    it('should return "user" when sessionClaims indicate any other role (e.g., org:member, org:basic)', () => {
        const sessionClaims = { organizations: { org_123: "org:member" } };
        expect(getUserOrgRole(sessionClaims)).toBe("user");

        const sessionClaims2 = { organizations: { org_456: "org:basic" } };
        expect(getUserOrgRole(sessionClaims2)).toBe("user");
    });

    it('should return "user" when sessionClaims are empty or undefined', () => {
        expect(getUserOrgRole(undefined)).toBe("user");
        expect(getUserOrgRole({})).toBe("user");
    });

    it('should return "user" when sessionClaims do not contain organization data', () => {
        const sessionClaims = { someOtherClaim: "value" };
        expect(getUserOrgRole(sessionClaims)).toBe("user");
    });

    it('should return "user" when sessionClaims structure is unexpected but no admin/employee role is found', () => {
        const sessionClaims = { organizations: { org_test: 123 } }; // Invalid role type
        expect(getUserOrgRole(sessionClaims)).toBe("user");
    });
});

describe("isAdminRole", () => {
    it('should return true for "admin" role', () => {
        expect(isAdminRole("admin")).toBe(true);
    });

    it('should return true for "employee" role', () => {
        expect(isAdminRole("employee")).toBe(true);
    });

    it('should return false for "user" role', () => {
        expect(isAdminRole("user")).toBe(false);
    });

    it("should return false for any other unrecognized role string", () => {
        // @ts-ignore - testing an invalid type explicitly
        expect(isAdminRole("viewer")).toBe(false);
    });
});

describe("isAdminFromSessionClaims", () => {
    it("should return true for sessionClaims indicating org:admin", () => {
        const sessionClaims = { organizations: { org_abc: "org:admin" } };
        expect(isAdminFromSessionClaims(sessionClaims)).toBe(true);
    });

    it("should return true for sessionClaims indicating org:employee", () => {
        const sessionClaims = { organizations: { org_xyz: "org:employee" } };
        expect(isAdminFromSessionClaims(sessionClaims)).toBe(true);
    });

    it("should return false for sessionClaims indicating org:member or other user role", () => {
        const sessionClaims = { organizations: { org_123: "org:member" } };
        expect(isAdminFromSessionClaims(sessionClaims)).toBe(false);
    });

    it("should return false for undefined or empty sessionClaims", () => {
        expect(isAdminFromSessionClaims(undefined)).toBe(false);
        expect(isAdminFromSessionClaims({})).toBe(false);
    });
});
