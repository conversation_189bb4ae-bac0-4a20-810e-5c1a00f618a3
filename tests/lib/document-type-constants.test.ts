import {
    TEXTS,
    DocumentTypeFormValues,
    defaultValues,
    MIME_TYPE_OPTIONS,
    DocumentTypeInsert,
    DocumentTypeUpdate,
    DocumentTypeFormData
} from "@/lib/document-type-constants";

describe("Document Type Constants", () => {
    describe("TEXTS", () => {
        it("should have all required server action error messages", () => {
            expect(TEXTS.DOCUMENT_TYPE_UPDATE_ERROR).toBe("שגיאה בעדכון סוג המסמך. אנא נסה שנית.");
            expect(TEXTS.DOCUMENT_TYPE_NOT_FOUND).toBe("סוג המסמך לא נמצא.");
            expect(TEXTS.DOCUMENT_TYPE_DELETE_ERROR).toBe("שגיאה במחיקת סוג המסמך. אנא נסה שנית.");
            expect(TEXTS.DOCUMENT_TYPE_CREATE_ERROR).toBe("שגיאה ביצירת סוג המסמך. אנא נסה שנית.");
            expect(TEXTS.DOCUMENT_TYPE_LINK_ERROR).toBe("שגיאה בקישור סוג המסמך למלגה. אנא נסה שנית.");
            expect(TEXTS.FETCH_ERROR).toBe("שגיאה בטעינת סוגי מסמכים");
        });

        it("should have all required form validation messages", () => {
            expect(TEXTS.GROUP_REQUIRED).toBe("יש לבחור קבוצה");
            expect(TEXTS.NAME_REQUIRED).toBe("שם סוג המסמך הוא שדה חובה");
            expect(TEXTS.MIME_TYPE_REQUIRED).toBe("יש לבחור לפחות סוג קובץ אחד");
            expect(TEXTS.MAX_FILE_SIZE_REQUIRED).toBe("יש להזין גודל קובץ מקסימלי");
            expect(TEXTS.LINK_URL_INVALID).toBe("כתובת הקישור אינה תקינה");
        });

        it("should have all required success messages", () => {
            expect(TEXTS.CREATE_SUCCESS).toBe("סוג המסמך נוצר בהצלחה");
            expect(TEXTS.UPDATE_SUCCESS).toBe("סוג המסמך עודכן בהצלחה");
            expect(TEXTS.FILE_UPLOAD_SUCCESS).toBe("הקובץ הועלה בהצלחה");
        });

        it("should have all required form labels", () => {
            expect(TEXTS.GROUP_LABEL).toBe("קבוצת מסמכים");
            expect(TEXTS.GROUP_PLACEHOLDER).toBe("בחר קבוצה");
            expect(TEXTS.NAME_LABEL).toBe("שם סוג המסמך");
            expect(TEXTS.NAME_PLACEHOLDER).toBe("הזן את שם סוג המסמך");
            expect(TEXTS.DESCRIPTION_LABEL).toBe("תיאור");
            expect(TEXTS.DESCRIPTION_PLACEHOLDER).toBe("הזן תיאור לסוג המסמך");
            expect(TEXTS.LINK_URL_LABEL).toBe("קישור");
            expect(TEXTS.LINK_URL_PLACEHOLDER).toBe("הזן קישור לדוגמה או תבנית");
            expect(TEXTS.EXAMPLE_FILE_LABEL).toBe("קובץ לדוגמה");
            expect(TEXTS.ALLOWED_MIME_TYPES_LABEL).toBe("סוגי קבצים מותרים");
            expect(TEXTS.ALLOWED_MIME_TYPES_PLACEHOLDER).toBe("בחר סוגי קבצים מותרים");
            expect(TEXTS.MAX_FILE_SIZE_LABEL).toBe("גודל קובץ מקסימלי (MB)");
            expect(TEXTS.MAX_FILE_SIZE_PLACEHOLDER).toBe("הזן גודל קובץ מקסימלי במגה-בייט");
        });

        it("should have all required button texts", () => {
            expect(TEXTS.CREATE_BUTTON).toBe("צור סוג מסמך");
            expect(TEXTS.UPDATE_BUTTON).toBe("עדכן סוג מסמך");
            expect(TEXTS.CANCEL_BUTTON).toBe("ביטול");
        });

        it("should have all required loading state texts", () => {
            expect(TEXTS.LOADING_DOCUMENT_TYPE).toBe("טוען סוג מסמך...");
            expect(TEXTS.UPLOADING_FILE).toBe("מעלה קובץ...");
        });

        it("should have all required file upload texts", () => {
            expect(TEXTS.DRAG_DROP_TEXT).toBe("גרור קובץ לכאן או לחץ לבחירת קובץ");
            expect(TEXTS.SELECTED_FILE_TEXT).toBe("קובץ נבחר");
            expect(TEXTS.PDF_FILE_TYPE_DESCRIPTION).toBe("קובץ PDF בלבד");
            expect(TEXTS.FILE_TYPE_ERROR).toBe("סוג הקובץ אינו נתמך");
            expect(TEXTS.UPLOAD_ERROR).toBe("שגיאה בהעלאת הקובץ");
        });

        it("should have functional dynamic text generators", () => {
            expect(typeof TEXTS.FILE_FORMAT_DESCRIPTION).toBe("function");
            expect(TEXTS.FILE_FORMAT_DESCRIPTION(10)).toBe("גודל מקסימלי: 10MB");
            expect(TEXTS.FILE_FORMAT_DESCRIPTION(5)).toBe("גודל מקסימלי: 5MB");

            expect(typeof TEXTS.FILE_SIZE_ERROR).toBe("function");
            expect(TEXTS.FILE_SIZE_ERROR(10)).toBe("גודל הקובץ חורג מהמותר (10MB)");
            expect(TEXTS.FILE_SIZE_ERROR(25)).toBe("גודל הקובץ חורג מהמותר (25MB)");
        });

        it("should have all texts as non-empty strings or functions", () => {
            Object.entries(TEXTS).forEach(([key, value]) => {
                if (typeof value === "function") {
                    expect(typeof value).toBe("function");
                } else {
                    expect(typeof value).toBe("string");
                    expect(value.length).toBeGreaterThan(0);
                }
            });
        });
    });

    describe("DocumentTypeFormValues", () => {
        it("should have correct interface shape", () => {
            const validFormValues: DocumentTypeFormValues = {
                name: "Test Document Type",
                description: "Test Description",
                link_url: "https://example.com",
                example_file_path: "test.pdf",
                allowed_mime_types: ["application/pdf"],
                group_id: { id: "group-1", label: "Test Group" },
                max_file_size_mb: 10
            };

            expect(validFormValues.name).toBe("Test Document Type");
            expect(validFormValues.description).toBe("Test Description");
            expect(validFormValues.link_url).toBe("https://example.com");
            expect(validFormValues.example_file_path).toBe("test.pdf");
            expect(validFormValues.allowed_mime_types).toEqual(["application/pdf"]);
            expect(validFormValues.group_id).toEqual({ id: "group-1", label: "Test Group" });
            expect(validFormValues.max_file_size_mb).toBe(10);
        });

        it("should accept null values where appropriate", () => {
            const validFormValues: DocumentTypeFormValues = {
                name: "Test Document Type",
                description: "",
                link_url: "",
                example_file_path: null,
                allowed_mime_types: [],
                group_id: null,
                max_file_size_mb: 1
            };

            expect(validFormValues.example_file_path).toBeNull();
            expect(validFormValues.group_id).toBeNull();
        });
    });

    describe("defaultValues", () => {
        it("should have correct default values", () => {
            expect(defaultValues.group_id).toBeNull();
            expect(defaultValues.name).toBe("");
            expect(defaultValues.description).toBe("");
            expect(defaultValues.link_url).toBe("");
            expect(defaultValues.example_file_path).toBeNull();
            expect(defaultValues.allowed_mime_types).toEqual([]);
            expect(defaultValues.max_file_size_mb).toBe(10);
        });

        it("should conform to DocumentTypeFormValues interface", () => {
            const values: DocumentTypeFormValues = defaultValues;
            expect(values).toBeDefined();
        });
    });

    describe("MIME_TYPE_OPTIONS", () => {
        it("should have correct MIME type options", () => {
            expect(MIME_TYPE_OPTIONS).toHaveLength(4);
            expect(MIME_TYPE_OPTIONS).toEqual([
                { id: "application/pdf", label: "PDF" },
                { id: "image/jpeg", label: "תמונת JPEG" },
                { id: "image/png", label: "תמונת PNG" },
                { id: "image/webp", label: "תמונת WEBP" }
            ]);
        });

        it("should have correct structure for each option", () => {
            MIME_TYPE_OPTIONS.forEach((option) => {
                expect(option).toHaveProperty("id");
                expect(option).toHaveProperty("label");
                expect(typeof option.id).toBe("string");
                expect(typeof option.label).toBe("string");
                expect(option.id.length).toBeGreaterThan(0);
                expect(option.label.length).toBeGreaterThan(0);
            });
        });
    });

    describe("Type Exports", () => {
        it("should export DocumentTypeInsert type", () => {
            const insertData: DocumentTypeInsert = {
                name: "Test Document Type",
                description: "Test Description",
                example_file_path: "test.pdf",
                link_url: "https://example.com",
                allowed_mime_types: ["application/pdf"],
                group_id: "group-1",
                max_file_size_mb: 10
            };

            expect(insertData.name).toBe("Test Document Type");
            expect(insertData.description).toBe("Test Description");
            expect(insertData.group_id).toBe("group-1");
        });

        it("should export DocumentTypeUpdate type", () => {
            const updateData: DocumentTypeUpdate = {
                name: "Updated Document Type",
                description: "Updated Description",
                updated_at: new Date().toISOString()
            };

            expect(updateData.name).toBe("Updated Document Type");
            expect(updateData.description).toBe("Updated Description");
            expect(updateData.updated_at).toBeDefined();
        });

        it("should allow partial updates", () => {
            const partialUpdate: DocumentTypeUpdate = {
                name: "Only name updated"
            };

            expect(partialUpdate.name).toBe("Only name updated");
            expect(partialUpdate.description).toBeUndefined();
        });

        it("should allow null values in database types", () => {
            const insertData: DocumentTypeInsert = {
                name: "Test Document Type",
                description: null,
                example_file_path: null,
                link_url: null,
                allowed_mime_types: ["application/pdf"],
                group_id: "group-1"
            };

            const updateData: DocumentTypeUpdate = {
                description: null,
                example_file_path: null,
                link_url: null
            };

            expect(insertData.description).toBeNull();
            expect(insertData.example_file_path).toBeNull();
            expect(insertData.link_url).toBeNull();
            expect(updateData.description).toBeNull();
            expect(updateData.example_file_path).toBeNull();
            expect(updateData.link_url).toBeNull();
        });
    });

    describe("DocumentTypeFormData", () => {
        it("should handle different group_id types", () => {
            const formDataWithObject: DocumentTypeFormData = {
                name: "Test Document Type",
                description: "Test Description",
                example_file_path: "test.pdf",
                link_url: "https://example.com",
                allowed_mime_types: ["application/pdf"],
                group_id: { id: "group-1", label: "Test Group" },
                max_file_size_mb: 10
            };

            const formDataWithString: DocumentTypeFormData = {
                name: "Test Document Type",
                description: "Test Description",
                example_file_path: "test.pdf",
                link_url: "https://example.com",
                allowed_mime_types: ["application/pdf"],
                group_id: "group-1",
                max_file_size_mb: 10
            };

            expect(formDataWithObject.group_id).toEqual({ id: "group-1", label: "Test Group" });
            expect(formDataWithString.group_id).toBe("group-1");
        });

        it("should have optional max_file_size_mb", () => {
            const formDataWithoutSize: DocumentTypeFormData = {
                name: "Test Document Type",
                description: "Test Description",
                example_file_path: null,
                link_url: null,
                allowed_mime_types: ["application/pdf"],
                group_id: "group-1"
            };

            expect(formDataWithoutSize.max_file_size_mb).toBeUndefined();
        });
    });

    describe("Constants Structure", () => {
        it("should export all required constants", () => {
            expect(TEXTS).toBeDefined();
            expect(defaultValues).toBeDefined();
            expect(MIME_TYPE_OPTIONS).toBeDefined();
            expect(typeof TEXTS).toBe("object");
            expect(typeof defaultValues).toBe("object");
            expect(Array.isArray(MIME_TYPE_OPTIONS)).toBe(true);
        });

        it("should not have any undefined values in TEXTS", () => {
            Object.entries(TEXTS).forEach(([key, value]) => {
                expect(value).toBeDefined();
                expect(value).not.toBeNull();
                if (typeof value === "string") {
                    expect(value).not.toBe("");
                }
            });
        });
    });
});
