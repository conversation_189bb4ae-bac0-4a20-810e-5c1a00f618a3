import {
    DEFAULT_APPLICATION_OPTIONS,
    getApplyText,
    isPositiveAnswer,
    TEXTS,
    UserPlanVariant
} from "@/lib/eligible-scholarship-card-constants";
import { Option } from "@/components/forms/fields/dropdown-base";

describe("eligible-scholarship-card-constants", () => {
    describe("TEXTS", () => {
        it("contains all required text constants", () => {
            // Field labels
            expect(TEXTS.amountLabel).toBe("סכום המלגה:");
            expect(TEXTS.dateRangeLabel).toBe("תאריכי הרשמה:");
            expect(TEXTS.volunteerHoursLabel).toBe("שעות התנדבות");

            // Action buttons and links
            expect(TEXTS.externalLinkLabel).toBe("אתר המלגה");
            expect(TEXTS.completeDetailsButton).toBe("השלם פרטים");
            expect(TEXTS.contactInfoLabel).toBe("פרטי קשר");

            // Contact information labels
            expect(TEXTS.contactPersonLabel).toBe("איש קשר:");
            expect(TEXTS.contactEmailLabel).toBe("מייל ליצירת קשר:");
            expect(TEXTS.contactPhoneLabel).toBe("נייד ליצירת קשר:");

            // Application questions and status
            expect(TEXTS.applyQuestionSubmission).toBe("האם תרצה שנגיש אותך למלגה זו?");
            expect(TEXTS.applyQuestionGuidance).toBe("האם תרצה שנדריך אותך למלגה זו?");
            expect(TEXTS.applicationStatusPlaceholder).toBe("בחר סטטוס הגשה");
            expect(TEXTS.applicationStatusYes).toBe("כן, אני מעוניין");
            expect(TEXTS.applicationStatusNo).toBe("לא, תודה");

            // Scholarship type badges
            expect(TEXTS.scholarshipTypeGuidance).toBe("הדרכה");
            expect(TEXTS.scholarshipTypeSubmission).toBe("הגשה");

            // Error and status messages
            expect(TEXTS.missingDataLabel).toBe("חסרים נתונים או שאינך עומד בקריטריונים.");
            expect(TEXTS.missingFieldsLabel).toBe("חסרים {count} שדות נדרשים");
        });

        it("all text values are non-empty strings", () => {
            Object.values(TEXTS).forEach((text) => {
                expect(typeof text).toBe("string");
                expect(text.length).toBeGreaterThan(0);
            });
        });
    });

    describe("UserPlanVariant type", () => {
        it("supports free and premium variants", () => {
            const freePlan: UserPlanVariant = "free";
            const premiumPlan: UserPlanVariant = "premium";

            expect(freePlan).toBe("free");
            expect(premiumPlan).toBe("premium");
        });
    });

    describe("DEFAULT_APPLICATION_OPTIONS", () => {
        it("contains correct default options", () => {
            expect(DEFAULT_APPLICATION_OPTIONS).toHaveLength(2);
            expect(DEFAULT_APPLICATION_OPTIONS[0]).toEqual({
                id: "yes",
                label: TEXTS.applicationStatusYes
            });
            expect(DEFAULT_APPLICATION_OPTIONS[1]).toEqual({
                id: "no",
                label: TEXTS.applicationStatusNo
            });
        });

        it("follows Option interface structure", () => {
            DEFAULT_APPLICATION_OPTIONS.forEach((option) => {
                expect(option).toHaveProperty("id");
                expect(option).toHaveProperty("label");
                expect(typeof option.id).toBe("string");
                expect(typeof option.label).toBe("string");
                expect(option.id.length).toBeGreaterThan(0);
                expect(option.label.length).toBeGreaterThan(0);
            });
        });
    });

    describe("getApplyText", () => {
        it("returns guidance text for guidance type", () => {
            expect(getApplyText("guidance")).toBe(TEXTS.applyQuestionGuidance);
        });

        it("returns submission text for submission type", () => {
            expect(getApplyText("submission")).toBe(TEXTS.applyQuestionSubmission);
        });

        it("returns submission text for unknown type", () => {
            expect(getApplyText("unknown")).toBe(TEXTS.applyQuestionSubmission);
        });

        it("returns submission text for empty string", () => {
            expect(getApplyText("")).toBe(TEXTS.applyQuestionSubmission);
        });
    });

    describe("isPositiveAnswer", () => {
        const testOptions: Option[] = [
            { id: "option1", label: "First Option" },
            { id: "option2", label: "Second Option" },
            { id: "yes", label: "Yes" },
            { id: "no", label: "No" }
        ];

        it("returns true for explicit 'yes' option", () => {
            expect(isPositiveAnswer("yes", testOptions)).toBe(true);
        });

        it("returns false for explicit 'no' option", () => {
            expect(isPositiveAnswer("no", testOptions)).toBe(false);
        });

        it("returns true for first option when no explicit 'yes'", () => {
            const optionsWithoutYes: Option[] = [
                { id: "accept", label: "Accept" },
                { id: "decline", label: "Decline" }
            ];
            expect(isPositiveAnswer("accept", optionsWithoutYes)).toBe(true);
        });

        it("returns false for non-first option when no explicit 'yes'", () => {
            const optionsWithoutYes: Option[] = [
                { id: "accept", label: "Accept" },
                { id: "decline", label: "Decline" }
            ];
            expect(isPositiveAnswer("decline", optionsWithoutYes)).toBe(false);
        });

        it("returns false for unknown option", () => {
            expect(isPositiveAnswer("unknown", testOptions)).toBe(false);
        });

        it("returns false for empty options array", () => {
            expect(isPositiveAnswer("yes", [])).toBe(false);
        });

        it("handles single option array correctly", () => {
            const singleOption: Option[] = [{ id: "only", label: "Only Option" }];
            expect(isPositiveAnswer("only", singleOption)).toBe(true);
            expect(isPositiveAnswer("other", singleOption)).toBe(false);
        });

        it("prioritizes explicit 'yes' over first option", () => {
            const optionsWithYesNotFirst: Option[] = [
                { id: "maybe", label: "Maybe" },
                { id: "yes", label: "Yes" },
                { id: "no", label: "No" }
            ];
            expect(isPositiveAnswer("yes", optionsWithYesNotFirst)).toBe(true);
            expect(isPositiveAnswer("maybe", optionsWithYesNotFirst)).toBe(true); // First option
            expect(isPositiveAnswer("no", optionsWithYesNotFirst)).toBe(false);
        });
    });
});
