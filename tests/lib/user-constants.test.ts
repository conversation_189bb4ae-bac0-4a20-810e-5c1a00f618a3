import { TEXTS, UserFormData, defaultValues, EMAIL_REGEX } from "@/lib/user-constants";

describe("User Constants", () => {
    describe("TEXTS", () => {
        it("should have all required main page texts", () => {
            expect(TEXTS.pageTitle).toBe("ניהול משתמשים");
            expect(TEXTS.emailLabel).toBe("כתובת אימייל של המשתמש");
            expect(TEXTS.emailPlaceholder).toBe("הזן כתובת אימייל");
            expect(TEXTS.emailRequired).toBe("יש להזין כתובת אימייל");
            expect(TEXTS.emailError).toBe("יש להזין כתובת אימייל תקינה");
            expect(TEXTS.searchButton).toBe("חפש משתמש");
            expect(TEXTS.searching).toBe("מחפש...");
        });

        it("should have all required user detail page texts", () => {
            expect(TEXTS.userDetailsTitle).toBe("פרטי משתמש");
            expect(TEXTS.userIdLabel).toBe("מזהה משתמש");
            expect(TEXTS.userNotFound).toBe("משתמש לא נמצא");
            expect(TEXTS.loadingUserDetails).toBe("טוען פרטי משתמש...");
        });

        it("should have all required tab titles", () => {
            expect(TEXTS.questionsTab).toBe("שאלות");
            expect(TEXTS.documentsTab).toBe("מסמכים");
            expect(TEXTS.packageTab).toBe("חבילה");
            expect(TEXTS.notesTab).toBe("הערות");
        });

        it("should have all required tab content placeholders", () => {
            expect(TEXTS.questionsContent).toBe("תוכן שאלות יוצג כאן");
            expect(TEXTS.documentsContent).toBe("תוכן מסמכים יוצג כאן");
            expect(TEXTS.packageContent).toBe("תוכן חבילה יוצג כאן");
            expect(TEXTS.notesContent).toBe("תוכן הערות יוצג כאן");
        });

        it("should have all required error messages", () => {
            expect(TEXTS.userSearchError).toBe("שגיאה בחיפוש משתמש");
            expect(TEXTS.genericError).toBe("אירעה שגיאה לא צפויה");
        });

        it("should have all required copy messages", () => {
            expect(TEXTS.userIdCopySuccess).toBe("מזהה משתמש הועתק בהצלחה");
        });

        it("should have all texts as non-empty strings", () => {
            Object.values(TEXTS).forEach((text) => {
                expect(typeof text).toBe("string");
                expect(text.length).toBeGreaterThan(0);
            });
        });

        it("should have Hebrew text content", () => {
            // Test that texts contain Hebrew characters
            expect(TEXTS.pageTitle).toMatch(/[\u0590-\u05FF]/);
            expect(TEXTS.emailLabel).toMatch(/[\u0590-\u05FF]/);
            expect(TEXTS.userDetailsTitle).toMatch(/[\u0590-\u05FF]/);
        });
    });

    describe("UserFormData", () => {
        it("should have correct interface shape", () => {
            const validFormData: UserFormData = {
                email: "<EMAIL>"
            };

            expect(validFormData.email).toBe("<EMAIL>");
        });

        it("should accept empty email string", () => {
            const validFormData: UserFormData = {
                email: ""
            };

            expect(validFormData.email).toBe("");
        });
    });

    describe("defaultValues", () => {
        it("should have correct default values", () => {
            expect(defaultValues.email).toBe("");
        });

        it("should conform to UserFormData interface", () => {
            const values: UserFormData = defaultValues;
            expect(values).toBeDefined();
            expect(typeof values.email).toBe("string");
        });
    });

    describe("EMAIL_REGEX", () => {
        it("should validate correct email formats", () => {
            const validEmails = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ];

            validEmails.forEach((email) => {
                expect(EMAIL_REGEX.test(email)).toBe(true);
            });
        });

        it("should reject invalid email formats", () => {
            const invalidEmails = [
                "",
                "invalid",
                "@example.com",
                "user@",
                "user.name",
                "user <EMAIL>",
                "user@@domain.com"
            ];

            invalidEmails.forEach((email) => {
                expect(EMAIL_REGEX.test(email)).toBe(false);
            });
        });

        it("should be a RegExp object", () => {
            expect(EMAIL_REGEX).toBeInstanceOf(RegExp);
        });
    });

    describe("Constants Structure", () => {
        it("should export all required constants", () => {
            expect(TEXTS).toBeDefined();
            expect(defaultValues).toBeDefined();
            expect(EMAIL_REGEX).toBeDefined();
            expect(typeof TEXTS).toBe("object");
            expect(typeof defaultValues).toBe("object");
        });

        it("should not have any undefined values in TEXTS", () => {
            Object.entries(TEXTS).forEach(([key, value]) => {
                expect(value).toBeDefined();
                expect(value).not.toBeNull();
                expect(value).not.toBe("");
            });
        });

        it("should have consistent key naming conventions", () => {
            const textKeys = Object.keys(TEXTS);
            textKeys.forEach((key) => {
                // Should be camelCase
                expect(key).toMatch(/^[a-z][a-zA-Z]*$/);
            });
        });
    });
});
