import { getHebrewErrorMessage, TEXTS } from "@/lib/auth-constants";

describe("getHebrewErrorMessage", () => {
    describe("Sign-in errors", () => {
        it("should return Hebrew message for email not found errors", () => {
            expect(getHebrewErrorMessage("email not found", false)).toBe(TEXTS.emailNotFound);
            expect(getHebrewErrorMessage("Email doesn't exist", false)).toBe(TEXTS.emailNotFound);
            expect(getHebrewErrorMessage("identifier_not_found", false)).toBe(TEXTS.emailNotFound);
            expect(getHebrewErrorMessage("Couldn't find your account.", false)).toBe(TEXTS.emailNotFound);
        });

        it("should return Hebrew message for password errors", () => {
            expect(getHebrewErrorMessage("password incorrect", false)).toBe(TEXTS.invalidPassword);
            expect(getHebrewErrorMessage("credentials invalid", false)).toBe(TEXTS.invalidPassword);
            expect(getHebrewErrorMessage("credential_invalid", false)).toBe(TEXTS.invalidPassword);
        });

        it("should return Hebrew message for account locked errors", () => {
            expect(getHebrewErrorMessage("account locked", false)).toBe(TEXTS.accountLocked);
            expect(getHebrewErrorMessage("too many requests", false)).toBe(TEXTS.accountLocked);
            expect(getHebrewErrorMessage("user locked", false)).toBe(TEXTS.accountLocked);
        });

        it("should return Hebrew message for invalid code errors", () => {
            expect(getHebrewErrorMessage("incorrect code", false)).toBe(TEXTS.invalidCode);
            expect(getHebrewErrorMessage("invalid verification", false)).toBe(TEXTS.invalidCode);
            expect(getHebrewErrorMessage("code is wrong", false)).toBe(TEXTS.invalidCode);
        });

        it("should return Hebrew message for expired code errors", () => {
            expect(getHebrewErrorMessage("expired code", false)).toBe(TEXTS.expiredCode);
            expect(getHebrewErrorMessage("Code has expired", false)).toBe(TEXTS.expiredCode);
        });
    });

    describe("Sign-up errors", () => {
        it("should return Hebrew message for email exists errors", () => {
            expect(getHebrewErrorMessage("email address is taken", true)).toBe(TEXTS.emailExistsError);
            expect(getHebrewErrorMessage("That email address is taken", true)).toBe(TEXTS.emailExistsError);
            expect(getHebrewErrorMessage("identifier_exists", true)).toBe(TEXTS.emailExistsError);
        });

        it("should return Hebrew message for invalid email format errors", () => {
            expect(getHebrewErrorMessage("invalid email format", true)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("email format is wrong", true)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("form_email_invalid", true)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("email_address must be a valid email address.", true)).toBe(
                TEXTS.invalidEmailFormat
            );
        });

        it("should return Hebrew message for invalid email format errors in sign-in", () => {
            expect(getHebrewErrorMessage("invalid email format", false)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("email format is wrong", false)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("form_email_invalid", false)).toBe(TEXTS.invalidEmailFormat);
            expect(getHebrewErrorMessage("email_address must be a valid email address.", false)).toBe(
                TEXTS.invalidEmailFormat
            );
        });

        it("should return Hebrew message for required field errors", () => {
            expect(getHebrewErrorMessage("field is required", true)).toBe(TEXTS.requiredFieldMissing);
            expect(getHebrewErrorMessage("missing parameter", true)).toBe(TEXTS.requiredFieldMissing);
            expect(getHebrewErrorMessage("form_param_missing", true)).toBe(TEXTS.requiredFieldMissing);
        });

        it("should return Hebrew message for rate limit errors", () => {
            expect(getHebrewErrorMessage("rate limit exceeded", true)).toBe(TEXTS.tooManyRequests);
            expect(getHebrewErrorMessage("too many requests", true)).toBe(TEXTS.tooManyRequests);
            expect(getHebrewErrorMessage("too_many_requests", true)).toBe(TEXTS.tooManyRequests);
        });

        it("should return Hebrew message for validation errors", () => {
            expect(getHebrewErrorMessage("validation failed", true)).toBe(TEXTS.signUpValidationError);
            expect(getHebrewErrorMessage("form_validation_failed", true)).toBe(TEXTS.signUpValidationError);
        });
    });

    describe("Unknown errors", () => {
        it("should return null for unknown errors", () => {
            expect(getHebrewErrorMessage("unknown error", false)).toBeNull();
            expect(getHebrewErrorMessage("random message", true)).toBeNull();
            expect(getHebrewErrorMessage("", false)).toBeNull();
        });
    });

    describe("Case insensitive matching", () => {
        it("should handle different case variations", () => {
            expect(getHebrewErrorMessage("EMAIL NOT FOUND", false)).toBe(TEXTS.emailNotFound);
            expect(getHebrewErrorMessage("Email Address Is Taken", true)).toBe(TEXTS.emailExistsError);
            expect(getHebrewErrorMessage("PASSWORD INCORRECT", false)).toBe(TEXTS.invalidPassword);
        });
    });
});
