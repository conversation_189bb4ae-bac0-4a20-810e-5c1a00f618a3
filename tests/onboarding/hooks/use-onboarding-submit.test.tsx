import { useUser } from "@clerk/nextjs";
import { act, renderHook } from "@testing-library/react";
import { useRouter } from "next/navigation";

import { completeOnboarding } from "@/app/actions/onboarding-actions";
import { PRICING_PLANS } from "@/config/subscriptions";
import { CouponAppliedInfo } from "@/lib/subscription-constants";
import { calculateFinalPrice, incrementCouponUsage, updateUserSubscription } from "@/app/actions/subscriptions-actions";
import { getSupabaseClient } from "@/utils/supabase/client";
import { setUserClaim } from "@/utils/user-claims-client";

import { useOnboarding } from "@/app/onboarding/onboarding-context";
import { useOnboardingSubmit } from "@/app/onboarding/hooks/use-onboarding-submit";

// Mock dependencies
jest.mock("@/app/onboarding/onboarding-context");
jest.mock("@clerk/nextjs");
jest.mock("next/navigation");
jest.mock("@/app/actions/onboarding-actions", () => ({
    completeOnboarding: jest.fn()
}));
jest.mock("@/app/actions/subscriptions-actions");
jest.mock("@/utils/supabase/client");
jest.mock("@/utils/user-claims-client");
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(),
    clerkClient: jest.fn()
}));

global.fetch = jest.fn();

const mockDispatch = jest.fn();
const mockUseOnboarding = useOnboarding as jest.Mock;

const mockUseUser = useUser as jest.Mock;
const mockReload = jest.fn();

const mockRouterPush = jest.fn();
const mockUseRouter = useRouter as jest.Mock;

const mockCompleteOnboarding = completeOnboarding as jest.Mock;
const mockCalculateFinalPrice = calculateFinalPrice as jest.Mock;
const mockIncrementCouponUsage = incrementCouponUsage as jest.Mock;
const mockUpdateUserSubscription = updateUserSubscription as jest.Mock;

// Replace window.location with a stub to prevent jsdom navigation warnings
const originalLocation = window.location;
beforeAll(() => {
    // Suppress jsdom navigation warning error logs for cleaner output
    const originalConsoleError = console.error;
    jest.spyOn(console, "error").mockImplementation((...args: unknown[]) => {
        const first = args[0];
        const text = first && first.toString ? first.toString() : "";
        if (typeof text === "string" && text.includes("Not implemented: navigation")) {
            return;
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        originalConsoleError(...args);
    });

    delete (window as unknown as Record<string, unknown>).location;
    (window as unknown as Record<string, unknown>).location = {
        href: "",
        assign: jest.fn(),
        replace: jest.fn()
    };
});

afterAll(() => {
    delete (window as unknown as Record<string, unknown>).location;
    (window as unknown as Record<string, unknown>).location = originalLocation;

    (console.error as jest.Mock).mockRestore();
});

describe("useOnboardingSubmit", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (getSupabaseClient as jest.Mock).mockReturnValue({});
        (setUserClaim as jest.Mock).mockResolvedValue(true);

        mockUseOnboarding.mockReturnValue({ dispatch: mockDispatch });
        mockUseUser.mockReturnValue({
            user: {
                id: "user_123",
                reload: mockReload,
                firstName: "John",
                lastName: "Doe",
                emailAddresses: [{ emailAddress: "<EMAIL>" }],
                phoneNumbers: []
            }
        });
        mockUseRouter.mockReturnValue({ push: mockRouterPush });

        // Reset window.location.href between tests
        (window.location as unknown as { href: string }).href = "";
    });

    it("should dispatch SET_ERROR if no plan is selected", async () => {
        const { result } = renderHook(() => useOnboardingSubmit());
        await act(async () => {
            await result.current.handleFinalSubmit({ selectedPlanId: null, validatedCoupon: null });
        });

        expect(mockDispatch).toHaveBeenCalledWith({ type: "SET_ERROR", payload: "יש לבחור תוכנית תשלום כדי להמשיך" });
    });

    it("should dispatch SET_ERROR if the selected plan is not found", async () => {
        const { result } = renderHook(() => useOnboardingSubmit());
        await act(async () => {
            await result.current.handleFinalSubmit({ selectedPlanId: "invalid-plan-id", validatedCoupon: null });
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: "SET_ERROR",
            payload: "תוכנית לא נמצאה"
        });
    });

    // Free Plan Flow
    describe("Free Plan Flow (finalPrice is 0)", () => {
        const selectedPlanId = PRICING_PLANS.find((p) => p.price === 0)!.id;

        beforeEach(() => {
            mockCalculateFinalPrice.mockResolvedValue(0);
        });

        it("should handle successful subscription and onboarding completion", async () => {
            mockUpdateUserSubscription.mockResolvedValue({ success: true });
            mockCompleteOnboarding.mockResolvedValue({ message: { success: true } });

            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId, validatedCoupon: null });
            });

            expect(mockUpdateUserSubscription).toHaveBeenCalledWith("user_123", selectedPlanId);
            expect(mockIncrementCouponUsage).not.toHaveBeenCalled();
            expect(mockCompleteOnboarding).toHaveBeenCalled();
            expect(setUserClaim).toHaveBeenCalledWith(expect.any(Object), "user_123", "onboarding_stage", "completed");
            expect(mockReload).toHaveBeenCalled();
            expect(mockRouterPush).toHaveBeenCalledWith("/dashboard");
            expect(mockDispatch).not.toHaveBeenCalledWith(expect.objectContaining({ type: "SET_ERROR" }));
        });

        it("should call incrementCouponUsage if a coupon was applied", async () => {
            const coupon: CouponAppliedInfo = {
                couponCode: "FREE",
                couponType: "percentage",
                discountValue: 100,
                discountApplied: 10,
                finalAmount: 0
            };
            const plan = PRICING_PLANS.find((p) => p.price > 0)!; // A plan that has a price
            mockCalculateFinalPrice.mockResolvedValue(0); // but the final price is 0
            mockUpdateUserSubscription.mockResolvedValue({ success: true });
            mockCompleteOnboarding.mockResolvedValue({ message: { success: true } });

            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId: plan.id, validatedCoupon: coupon });
            });

            expect(mockIncrementCouponUsage).toHaveBeenCalledWith(coupon.couponCode);
        });

        it("should dispatch SET_ERROR if user is not identified", async () => {
            mockUseUser.mockReturnValue({ user: null });

            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId, validatedCoupon: null });
            });

            expect(mockDispatch).toHaveBeenCalledWith({ type: "SET_ERROR", payload: "משתמש לא מזוהה" });
        });

        it("should dispatch SET_ERROR if updateUserSubscription fails", async () => {
            mockUpdateUserSubscription.mockResolvedValue({ success: false, error: "DB Error" });

            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId, validatedCoupon: null });
            });
            expect(mockDispatch).toHaveBeenCalledWith({ type: "SET_ERROR", payload: "DB Error" });
        });

        it("should dispatch SET_ERROR if completeOnboarding returns an error message", async () => {
            mockUpdateUserSubscription.mockResolvedValue({ success: true });
            mockCompleteOnboarding.mockResolvedValue({ message: "Onboarding failed" });
            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId, validatedCoupon: null });
            });
            expect(mockDispatch).toHaveBeenCalledWith({ type: "SET_ERROR", payload: "Onboarding failed" });
        });
    });

    // Paid plan flow
    describe("Paid Plan Flow (finalPrice > 0)", () => {
        const selectedPlan = PRICING_PLANS.find((p) => p.price > 0)!;
        const finalPrice = selectedPlan.price;

        beforeEach(() => {
            mockCalculateFinalPrice.mockResolvedValue(finalPrice);
            mockCompleteOnboarding.mockResolvedValue({ message: { success: true } });
        });

        it("should redirect to checkout URL on successful API call", async () => {
            const signedUrl = "https://checkout.example.com/session_123";
            (fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true, signedUrl })
            });

            const { result } = renderHook(() => useOnboardingSubmit());
            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId: selectedPlan.id, validatedCoupon: null });
            });

            expect(fetch).toHaveBeenCalledWith("/api/generate-checkout-url", expect.any(Object));
            expect(setUserClaim).toHaveBeenCalledWith(expect.any(Object), "user_123", "onboarding_stage", "completed");
        });

        it("should dispatch SET_ERROR on API failure", async () => {
            (fetch as jest.Mock).mockResolvedValue({
                ok: false,
                json: () => Promise.resolve({ success: false, error: "API Error" })
            });

            const { result } = renderHook(() => useOnboardingSubmit());

            await act(async () => {
                await result.current.handleFinalSubmit({ selectedPlanId: selectedPlan.id, validatedCoupon: null });
            });

            expect(mockDispatch).toHaveBeenCalledWith({ type: "SET_ERROR", payload: "API Error" });
        });
    });

    it("should handle unexpected errors gracefully", async () => {
        const error = new Error("Unexpected!");
        mockCalculateFinalPrice.mockRejectedValue(error);
        const { result } = renderHook(() => useOnboardingSubmit());
        const selectedPlan = PRICING_PLANS.find((p) => p.price > 0)!;

        await act(async () => {
            await result.current.handleFinalSubmit({ selectedPlanId: selectedPlan.id, validatedCoupon: null });
        });

        expect(mockDispatch).toHaveBeenCalledWith({
            type: "SET_ERROR",
            payload: "אירעה שגיאה לא צפויה. אנא נסה שוב או פנה לתמיכה."
        });
    });
});
