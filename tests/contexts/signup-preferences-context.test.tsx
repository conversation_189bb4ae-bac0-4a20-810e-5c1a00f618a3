import { renderHook, act } from "@testing-library/react";
import { ReactNode } from "react";

import {
    SignupPreferencesProvider,
    useSignupPreferences,
    signupPreferencesReducer,
    initialState
} from "@/contexts/signup-preferences-context";

// Mock sessionStorage
const mockSessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};

Object.defineProperty(window, "sessionStorage", {
    value: mockSessionStorage
});

// Test wrapper
const wrapper = ({ children }: { children: ReactNode }) => (
    <SignupPreferencesProvider>{children}</SignupPreferencesProvider>
);

// Type for testing unknown actions
type UnknownAction = { type: "UNKNOWN_ACTION" };

describe("SignupPreferencesContext", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockSessionStorage.getItem.mockReturnValue(null);
    });

    describe("signupPreferencesReducer", () => {
        it("should return initial state for unknown actions", () => {
            expect(signupPreferencesReducer(initialState, { type: "UNKNOWN_ACTION" } as UnknownAction)).toEqual(
                initialState
            );
        });

        it("should handle SET_ACCEPTED_TERMS", () => {
            const state = signupPreferencesReducer(initialState, { type: "SET_ACCEPTED_TERMS", payload: true });
            expect(state.preferences.acceptedTerms).toBe(true);
            expect(state.isSet).toBe(true);
        });

        it("should handle SET_SUBSCRIBE_NEWSLETTER", () => {
            const state = signupPreferencesReducer(initialState, { type: "SET_SUBSCRIBE_NEWSLETTER", payload: false });
            expect(state.preferences.subscribeNewsletter).toBe(false);
            expect(state.isSet).toBe(true);
        });

        it("should handle SET_PREFERENCES", () => {
            const preferences = { acceptedTerms: true, subscribeNewsletter: false };
            const state = signupPreferencesReducer(initialState, { type: "SET_PREFERENCES", payload: preferences });
            expect(state.preferences).toEqual(preferences);
            expect(state.isSet).toBe(true);
        });

        it("should handle CLEAR_PREFERENCES", () => {
            const modifiedState = { ...initialState, isSet: true };
            const state = signupPreferencesReducer(modifiedState, { type: "CLEAR_PREFERENCES" });
            expect(state).toEqual(initialState);
        });
    });

    describe("useSignupPreferences hook", () => {
        it("should provide default values", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            expect(result.current.state.preferences.acceptedTerms).toBe(false);
            expect(result.current.state.preferences.subscribeNewsletter).toBe(true);
            expect(result.current.state.isSet).toBe(false);
        });

        it("should update accepted terms", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            act(() => {
                result.current.setAcceptedTerms(true);
            });

            expect(result.current.state.preferences.acceptedTerms).toBe(true);
            expect(result.current.state.isSet).toBe(true);
        });

        it("should update newsletter subscription", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            act(() => {
                result.current.setSubscribeNewsletter(false);
            });

            expect(result.current.state.preferences.subscribeNewsletter).toBe(false);
            expect(result.current.state.isSet).toBe(true);
        });

        it("should set preferences", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });
            const preferences = { acceptedTerms: true, subscribeNewsletter: false };

            act(() => {
                result.current.setPreferences(preferences);
            });

            expect(result.current.state.preferences).toEqual(preferences);
            expect(result.current.state.isSet).toBe(true);
        });

        it("should clear preferences", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            // First set some preferences
            act(() => {
                result.current.setAcceptedTerms(true);
            });

            expect(result.current.state.isSet).toBe(true);

            // Then clear them
            act(() => {
                result.current.clearPreferences();
            });

            expect(result.current.state).toEqual(initialState);
        });

        it("should return correct hasPreferences value", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            expect(result.current.hasPreferences()).toBe(false);

            act(() => {
                result.current.setAcceptedTerms(true);
            });

            expect(result.current.hasPreferences()).toBe(true);
        });
    });

    describe("sessionStorage integration", () => {
        it("should save to sessionStorage when preferences change", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            act(() => {
                result.current.setAcceptedTerms(true);
            });

            expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
                "milgapo_signup_preferences",
                expect.stringContaining('"acceptedTerms":true')
            );
        });

        it("should load from sessionStorage on initialization", () => {
            const storedState = {
                preferences: { acceptedTerms: true, subscribeNewsletter: false },
                isSet: true
            };
            mockSessionStorage.getItem.mockReturnValue(JSON.stringify(storedState));

            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            expect(result.current.state.preferences.acceptedTerms).toBe(true);
            expect(result.current.state.preferences.subscribeNewsletter).toBe(false);
            expect(result.current.state.isSet).toBe(true);
        });

        it("should clear sessionStorage when preferences are cleared", () => {
            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            act(() => {
                result.current.clearPreferences();
            });

            expect(mockSessionStorage.removeItem).toHaveBeenCalledWith("milgapo_signup_preferences");
        });

        it("should handle sessionStorage errors gracefully", () => {
            mockSessionStorage.setItem.mockImplementation(() => {
                throw new Error("Storage quota exceeded");
            });

            const { result } = renderHook(() => useSignupPreferences(), { wrapper });

            // Should not throw
            expect(() => {
                act(() => {
                    result.current.setAcceptedTerms(true);
                });
            }).not.toThrow();
        });
    });

    describe("error handling", () => {
        it("should throw error when used outside provider", () => {
            expect(() => {
                renderHook(() => useSignupPreferences());
            }).toThrow("useSignupPreferences must be used within a SignupPreferencesProvider");
        });
    });
});
