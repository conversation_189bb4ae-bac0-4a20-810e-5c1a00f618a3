import {
    createQuestion,
    updateQuestion,
    getQuestion,
    getQuestionFormData,
    deleteQuestion,
    QuestionFormData
} from "@/app/actions/question-actions";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockSupabase = {
    from: jest.fn(),
    rpc: jest.fn()
} as any;

const mockCreateClientFromRequest = createClientFromRequest as jest.MockedFunction<typeof createClientFromRequest>;

describe("question-actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockCreateClientFromRequest.mockResolvedValue(mockSupabase as any);
    });

    describe("createQuestion", () => {
        const mockQuestionData: QuestionFormData = {
            type: "single_select",
            group_id: "group-1",
            section: "data_entry",
            metadata: {
                label: "Test Question",
                required: true,
                options: [{ id: "opt1", label: "Option 1" }]
            },
            scholarship_ids: ["scholarship-1"],
            dependencies: [
                {
                    question_id: "dep-question-1",
                    condition_type: "in",
                    condition_value: ["value1"]
                }
            ]
        };

        it("should create question successfully without scholarships and dependencies", async () => {
            const questionDataWithoutExtras = {
                type: "short_text" as const,
                group_id: "group-1",
                section: "data_entry" as const,
                metadata: { label: "Test Question" }
            };

            const mockInsert = {
                insert: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: { id: "question-1" },
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockInsert);

            const result = await createQuestion(questionDataWithoutExtras);

            expect(result.success).toBe(true);
            expect(result.data).toEqual({ id: "question-1" });
            expect(mockSupabase.from).toHaveBeenCalledWith("questions");
            expect(mockInsert.insert).toHaveBeenCalledWith([questionDataWithoutExtras]);
        });

        it("should create question with scholarships successfully", async () => {
            const mockInsert = {
                insert: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: { id: "question-1" },
                    error: null
                })
            } as any;

            const mockScholarshipInsert = {
                insert: jest.fn().mockResolvedValue({ error: null })
            } as any;

            mockSupabase.from.mockReturnValueOnce(mockInsert).mockReturnValueOnce(mockScholarshipInsert);
            mockSupabase.rpc.mockResolvedValue({
                data: { success: true },
                error: null
            });

            const result = await createQuestion(mockQuestionData);

            expect(result.success).toBe(true);
            expect(mockScholarshipInsert.insert).toHaveBeenCalledWith([
                { scholarship_id: "scholarship-1", question_id: "question-1" }
            ]);
        });

        it("should create question with dependencies successfully", async () => {
            const mockInsert = {
                insert: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: { id: "question-1" },
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockInsert);
            mockSupabase.rpc.mockResolvedValue({
                data: { success: true },
                error: null
            });

            const result = await createQuestion(mockQuestionData);

            expect(result.success).toBe(true);
            expect(mockSupabase.rpc).toHaveBeenCalledWith("create_question_conditions", {
                p_question_id: "question-1",
                p_dependencies: [
                    {
                        question_id: "dep-question-1",
                        condition_type: "in",
                        condition_value: ["value1"]
                    }
                ]
            });
        });

        it("should handle database error during question creation", async () => {
            const mockInsert = {
                insert: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: null,
                    error: new Error("Database error")
                })
            };

            mockSupabase.from.mockReturnValue(mockInsert);

            const result = await createQuestion(mockQuestionData);

            expect(result.success).toBe(false);
            expect(result.error).toBe("Database error");
        });

        it("should handle error during dependency creation", async () => {
            const mockInsert = {
                insert: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: { id: "question-1" },
                    error: null
                })
            };

            mockSupabase.from.mockReturnValue(mockInsert);
            mockSupabase.rpc.mockRejectedValue(new Error("RPC error"));

            const result = await createQuestion(mockQuestionData);

            expect(result.success).toBe(false);
            expect(result.error).toBe("RPC error");
        });
    });

    describe("updateQuestion", () => {
        const mockQuestionData: QuestionFormData = {
            type: "single_select",
            group_id: "group-1",
            section: "data_entry",
            metadata: { label: "Updated Question" },
            scholarship_ids: ["scholarship-2"],
            dependencies: [
                {
                    question_id: "new-dep-question",
                    condition_type: "equals",
                    condition_value: "new-value"
                }
            ]
        };

        it("should update question successfully", async () => {
            const mockUpdate = {
                update: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockInsert = {
                insert: jest.fn().mockResolvedValue({ error: null })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockUpdate)
                .mockReturnValueOnce(mockDelete)
                .mockReturnValueOnce(mockInsert);

            mockSupabase.rpc
                .mockResolvedValueOnce({ data: { success: true }, error: null })
                .mockResolvedValueOnce({ data: { success: true }, error: null });

            const result = await updateQuestion("question-1", mockQuestionData);

            expect(result.success).toBe(true);
            expect(mockUpdate.update).toHaveBeenCalledWith({
                type: "single_select",
                group_id: "group-1",
                section: "data_entry",
                metadata: { label: "Updated Question" }
            });
        });

        it("should update scholarships when provided", async () => {
            const mockUpdate = {
                update: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockInsert = {
                insert: jest.fn().mockResolvedValue({ error: null })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockUpdate)
                .mockReturnValueOnce(mockDelete)
                .mockReturnValueOnce(mockInsert);

            mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });

            await updateQuestion("question-1", mockQuestionData);

            expect(mockDelete.eq).toHaveBeenCalledWith("question_id", "question-1");
            expect(mockInsert.insert).toHaveBeenCalledWith([
                { scholarship_id: "scholarship-2", question_id: "question-1" }
            ]);
        });

        it("should handle empty scholarship array", async () => {
            const mockUpdate = {
                update: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            mockSupabase.from.mockReturnValueOnce(mockUpdate).mockReturnValueOnce(mockDelete);

            mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });

            const dataWithEmptyScholarships = { ...mockQuestionData, scholarship_ids: [] };
            await updateQuestion("question-1", dataWithEmptyScholarships);

            expect(mockDelete.eq).toHaveBeenCalledWith("question_id", "question-1");

            expect(mockSupabase.from).toHaveBeenCalledTimes(2);
        });

        it("should handle database error during update", async () => {
            const mockUpdate = {
                update: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: new Error("Update failed") })
            };

            mockSupabase.from.mockReturnValue(mockUpdate);

            const result = await updateQuestion("question-1", mockQuestionData);

            expect(result.success).toBe(false);
            expect(result.error).toBe("Update failed");
        });
    });

    describe("getQuestion", () => {
        it("should fetch question with all related data successfully", async () => {
            const mockQuestionSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: {
                        id: "question-1",
                        type: "single_select",
                        metadata: { label: "Test Question" },
                        group_id: "group-1"
                    },
                    error: null
                })
            };

            const mockScholarshipSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({
                    data: [{ scholarship_id: "scholarship-1" }],
                    error: null
                })
            };

            const mockGroupSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: { id: "group-1", name: "Test Group" },
                    error: null
                })
            };

            const mockConditionLinksSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({
                    data: [{ condition_id: "condition-1" }],
                    error: null
                })
            };

            const mockConditionsSelect = {
                select: jest.fn().mockReturnThis(),
                in: jest.fn().mockResolvedValue({
                    data: [
                        {
                            id: "condition-1",
                            question_id: "dep-question-1",
                            type: "in",
                            value: ["option1"]
                        }
                    ],
                    error: null
                })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockQuestionSelect)
                .mockReturnValueOnce(mockScholarshipSelect)
                .mockReturnValueOnce(mockGroupSelect)
                .mockReturnValueOnce(mockConditionLinksSelect)
                .mockReturnValueOnce(mockConditionsSelect);

            const result = await getQuestion("question-1");

            expect(result.success).toBe(true);
            expect(result.data).toEqual({
                question: {
                    id: "question-1",
                    type: "single_select",
                    metadata: { label: "Test Question" },
                    group_id: "group-1"
                },
                scholarshipIds: ["scholarship-1"],
                dependencies: [
                    {
                        question_id: "dep-question-1",
                        condition_type: "in",
                        condition_value: ["option1"]
                    }
                ],
                groupInfo: { id: "group-1", name: "Test Group" }
            });
        });

        it("should handle question without group info", async () => {
            const mockQuestionSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: {
                        id: "question-1",
                        type: "single_select",
                        metadata: { label: "Test Question" },
                        group_id: null
                    },
                    error: null
                })
            };

            const mockScholarshipSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            };

            const mockConditionLinksSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockQuestionSelect)
                .mockReturnValueOnce(mockScholarshipSelect)
                .mockReturnValueOnce(mockConditionLinksSelect);

            const result = await getQuestion("question-1");

            expect(result.success).toBe(true);
            expect(result.data?.groupInfo).toBeUndefined();
            expect(result.data?.dependencies).toEqual([]);
        });

        it("should handle database error during question fetch", async () => {
            const mockQuestionSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({
                    data: null,
                    error: new Error("Question not found")
                })
            };

            mockSupabase.from.mockReturnValue(mockQuestionSelect);

            const result = await getQuestion("question-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Question not found");
        });
    });

    describe("getQuestionFormData", () => {
        it("should fetch scholarships and questions successfully", async () => {
            const mockScholarshipsSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: [{ id: "scholarship-1", title: "Test Scholarship" }],
                    error: null
                })
            };

            const mockQuestionsSelect = {
                select: jest.fn().mockReturnThis(),
                not: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: [
                        {
                            id: "question-1",
                            type: "single_select",
                            metadata: { label: "Test Question" },
                            groups_question: { id: "group-1", name: "Test Group" }
                        }
                    ],
                    error: null
                })
            };

            mockSupabase.from.mockReturnValueOnce(mockScholarshipsSelect).mockReturnValueOnce(mockQuestionsSelect);

            const result = await getQuestionFormData();

            expect(result.success).toBe(true);
            expect(result.data?.scholarships).toEqual([{ id: "scholarship-1", title: "Test Scholarship" }]);
            expect(result.data?.availableQuestions).toEqual([
                {
                    id: "question-1",
                    type: "single_select",
                    metadata: { label: "Test Question" },
                    groups_question: { id: "group-1", name: "Test Group" }
                }
            ]);
        });

        it("should handle invalid question type error with fallback", async () => {
            const mockScholarshipsSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: [{ id: "scholarship-1", title: "Test Scholarship" }],
                    error: null
                })
            };

            const mockQuestionsSelectWithError = {
                select: jest.fn().mockReturnThis(),
                not: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: null,
                    error: {
                        code: "22P02",
                        message: "invalid input value for enum question_type"
                    }
                })
            };

            const mockFallbackQuestionsSelect = {
                select: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: [
                        {
                            id: "question-1",
                            type: "single_select",
                            metadata: { label: "Valid Question" },
                            groups_question: [{ id: "group-1", name: "Test Group" }]
                        },
                        {
                            id: "question-2",
                            type: "short_text",
                            metadata: { label: "Text Question" },
                            groups_question: { id: "group-2", name: "Text Group" }
                        }
                    ],
                    error: null
                })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockScholarshipsSelect)
                .mockReturnValueOnce(mockQuestionsSelectWithError)
                .mockReturnValueOnce(mockFallbackQuestionsSelect);

            const result = await getQuestionFormData();

            expect(result.success).toBe(true);
            expect(result.data?.availableQuestions).toHaveLength(1);
            expect(result.data?.availableQuestions[0]).toEqual({
                id: "question-1",
                type: "single_select",
                metadata: { label: "Valid Question" },
                groups_question: { id: "group-1", name: "Test Group" }
            });
        });

        it("should handle error during data fetch", async () => {
            const mockScholarshipsSelect = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: null,
                    error: new Error("Scholarships fetch failed")
                })
            } as any;

            const mockQuestionsSelect = {
                select: jest.fn().mockReturnThis(),
                not: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            } as any;

            mockSupabase.from.mockReturnValueOnce(mockScholarshipsSelect).mockReturnValueOnce(mockQuestionsSelect);

            const result = await getQuestionFormData();

            expect(result.success).toBe(false);
            expect(result.error).toBe("Scholarships fetch failed");
        });
    });

    describe("deleteQuestion", () => {
        it("should delete question and all related data successfully", async () => {
            mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });

            const mockDependencyDelete1 = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            const mockDependencyDelete2 = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            const mockScholarshipDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            const mockQuestionDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            mockSupabase.from
                .mockReturnValueOnce(mockDependencyDelete1)
                .mockReturnValueOnce(mockDependencyDelete2)
                .mockReturnValueOnce(mockScholarshipDelete)
                .mockReturnValueOnce(mockQuestionDelete);

            const result = await deleteQuestion("question-1");

            expect(result.success).toBe(true);
            expect(mockSupabase.rpc).toHaveBeenCalledWith("delete_question_conditions", {
                p_question_id: "question-1"
            });
            expect(mockScholarshipDelete.eq).toHaveBeenCalledWith("question_id", "question-1");
            expect(mockQuestionDelete.eq).toHaveBeenCalledWith("id", "question-1");
        });

        it("should handle question_dependencies table cleanup", async () => {
            mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });

            const mockDependencyDelete1 = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockDependencyDelete2 = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockScholarshipDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            const mockQuestionDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            mockSupabase.from
                .mockReturnValueOnce(mockDependencyDelete1)
                .mockReturnValueOnce(mockDependencyDelete2)
                .mockReturnValueOnce(mockScholarshipDelete)
                .mockReturnValueOnce(mockQuestionDelete);

            const result = await deleteQuestion("question-1");

            expect(result.success).toBe(true);
        });

        it("should handle missing question_dependencies table gracefully", async () => {
            mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });

            const mockDependencyDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            const mockScholarshipDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            const mockQuestionDelete = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            } as any;

            mockSupabase.from
                .mockReturnValueOnce(mockDependencyDelete)
                .mockReturnValueOnce(mockDependencyDelete)
                .mockReturnValueOnce(mockScholarshipDelete)
                .mockReturnValueOnce(mockQuestionDelete);

            const result = await deleteQuestion("question-1");

            expect(result.success).toBe(true);
        });

        it("should handle database error during deletion", async () => {
            mockSupabase.rpc.mockRejectedValue(new Error("RPC deletion failed"));

            const result = await deleteQuestion("question-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe("RPC deletion failed");
        });
    });
});
