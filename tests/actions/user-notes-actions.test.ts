import { createUserNote, deleteUserNote, getUserNotes } from "@/app/actions/user-notes-actions";
import { sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@clerk/nextjs/server", () => ({ auth: jest.fn() }));
jest.mock("next/cache", () => ({ revalidatePath: jest.fn() }));
jest.mock("@/utils/sanitization", () => ({ sanitizeText: jest.fn() }));
jest.mock("@/utils/supabase/server", () => ({ createClientFromRequest: jest.fn() }));

const mockAuth = require("@clerk/nextjs/server").auth;
const mockRevalidatePath = require("next/cache").revalidatePath;
const mockSanitizeText = require("@/utils/sanitization").sanitizeText;
const mockCreateClientFromRequest = require("@/utils/supabase/server").createClientFromRequest;

const mockSupabaseClient = {
    from: jest.fn(),
    insert: jest.fn(),
    delete: jest.fn(),
    select: jest.fn(),
    eq: jest.fn(),
    order: jest.fn(),
    limit: jest.fn(),
    single: jest.fn()
};

describe("User Notes Actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient);
        mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.insert.mockReturnValue({ error: null });
        mockSupabaseClient.delete.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.order.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.limit.mockReturnValue(mockSupabaseClient);
        mockSupabaseClient.single.mockResolvedValue({ data: null, error: null });
    });

    describe("createUserNote", () => {
        it("should successfully create a user note", async () => {
            const userId = "test-user-id";
            const reportedUserId = "reported-user-id";
            const note = "Test note content";
            const sanitizedReportedUserId = "reported-user-id";
            const sanitizedNote = "Test note content";

            mockAuth.mockResolvedValue({ userId });
            mockSanitizeText.mockReturnValueOnce(sanitizedReportedUserId).mockReturnValueOnce(sanitizedNote);
            mockSupabaseClient.insert.mockResolvedValue({ error: null });

            const result = await createUserNote(reportedUserId, note);

            expect(mockAuth).toHaveBeenCalled();
            expect(mockSanitizeText).toHaveBeenCalledWith(reportedUserId, 100);
            expect(mockSanitizeText).toHaveBeenCalledWith(note, 1000);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_notes");
            expect(mockSupabaseClient.insert).toHaveBeenCalledWith({
                user_id: userId,
                reported_user_id: sanitizedReportedUserId,
                note: sanitizedNote
            });
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/users");
            expect(result).toEqual({ success: true });
        });

        it("should return error when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });

            const result = await createUserNote("reported-user-id", "Test note");

            expect(result).toEqual({ success: false, error: "אין הרשאה לבצע פעולה זו" });
            expect(mockCreateClientFromRequest).not.toHaveBeenCalled();
        });

        it("should return error when reported user ID is invalid", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValueOnce("").mockReturnValueOnce("Test note");

            const result = await createUserNote("", "Test note");

            expect(result).toEqual({ success: false, error: "מזהה משתמש נדרש" });
        });

        it("should return error when note is empty", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValueOnce("reported-user-id").mockReturnValueOnce("");

            const result = await createUserNote("reported-user-id", "");

            expect(result).toEqual({ success: false, error: "יש להזין הערה" });
        });

        it("should handle database errors", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValueOnce("reported-user-id").mockReturnValueOnce("Test note");
            mockSupabaseClient.insert.mockResolvedValue({ error: new Error("Database error") });

            const result = await createUserNote("reported-user-id", "Test note");

            expect(result).toEqual({ success: false, error: "שגיאה ביצירת הערה" });
        });
    });

    describe("deleteUserNote", () => {
        it("should successfully delete a user note", async () => {
            const userId = "test-user-id";
            const noteId = "note-id";
            const sanitizedNoteId = "note-id";

            mockAuth.mockResolvedValue({ userId });
            mockSanitizeText.mockReturnValue(sanitizedNoteId);
            mockSupabaseClient.eq.mockResolvedValue({ error: null });

            const result = await deleteUserNote(noteId);

            expect(mockAuth).toHaveBeenCalled();
            expect(mockSanitizeText).toHaveBeenCalledWith(noteId, 100);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_notes");
            expect(mockSupabaseClient.delete).toHaveBeenCalled();
            expect(mockSupabaseClient.eq).toHaveBeenCalledWith("id", sanitizedNoteId);
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/users");
            expect(result).toEqual({ success: true });
        });

        it("should return error when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });

            const result = await deleteUserNote("note-id");

            expect(result).toEqual({ success: false, error: "אין הרשאה לבצע פעולה זו" });
        });

        it("should return error when note ID is invalid", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValue("");

            const result = await deleteUserNote("");

            expect(result).toEqual({ success: false, error: "הערה לא נמצאה" });
        });

        it("should handle database errors", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValue("note-id");
            mockSupabaseClient.eq.mockResolvedValue({ error: new Error("Database error") });

            const result = await deleteUserNote("note-id");

            expect(result).toEqual({ success: false, error: "שגיאה במחיקת הערה" });
        });
    });

    describe("getUserNotes", () => {
        it("should successfully get user notes with reporter emails", async () => {
            const userId = "test-user-id";
            const reportedUserId = "reported-user-id";
            const sanitizedReportedUserId = "reported-user-id";
            const mockNotes = [
                {
                    id: "note-1",
                    user_id: "reporter-1",
                    reported_user_id: reportedUserId,
                    note: "Note 1",
                    created_at: "2023-01-01T00:00:00Z",
                    updated_at: "2023-01-01T00:00:00Z"
                }
            ];

            mockAuth.mockResolvedValue({ userId });
            mockSanitizeText.mockReturnValue(sanitizedReportedUserId);

            mockSupabaseClient.order.mockImplementation((field, options) => {
                if (field === "created_at") {
                    return Promise.resolve({ data: mockNotes, error: null });
                }
                return mockSupabaseClient;
            });

            mockSupabaseClient.single.mockResolvedValue({
                data: { claim_value: "<EMAIL>" },
                error: null
            });

            const result = await getUserNotes(reportedUserId);

            expect(mockAuth).toHaveBeenCalled();
            expect(mockSanitizeText).toHaveBeenCalledWith(reportedUserId, 100);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_notes");
            expect(mockSupabaseClient.select).toHaveBeenCalledWith("*");
            expect(mockSupabaseClient.eq).toHaveBeenCalledWith("reported_user_id", sanitizedReportedUserId);
            expect(result.success).toBe(true);
            expect(result.notes).toHaveLength(1);
            expect(result.notes?.[0]).toEqual({
                ...mockNotes[0],
                reporter_email: "<EMAIL>"
            });
        });

        it("should fallback to user_id when reporter email is not found", async () => {
            const userId = "test-user-id";
            const reportedUserId = "reported-user-id";
            const mockNotes = [
                {
                    id: "note-1",
                    user_id: "reporter-1",
                    reported_user_id: reportedUserId,
                    note: "Note 1",
                    created_at: "2023-01-01T00:00:00Z",
                    updated_at: "2023-01-01T00:00:00Z"
                }
            ];

            mockAuth.mockResolvedValue({ userId });
            mockSanitizeText.mockReturnValue(reportedUserId);

            mockSupabaseClient.order.mockImplementation((field, options) => {
                if (field === "created_at") {
                    return Promise.resolve({ data: mockNotes, error: null });
                }
                return mockSupabaseClient;
            });

            mockSupabaseClient.single.mockResolvedValue({ data: null, error: null });

            const result = await getUserNotes(reportedUserId);

            expect(result.success).toBe(true);
            expect(result.notes?.[0]).toEqual({
                ...mockNotes[0],
                reporter_email: "reporter-1"
            });
        });

        it("should return error when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });

            const result = await getUserNotes("reported-user-id");

            expect(result).toEqual({ success: false, error: "אין הרשאה לבצע פעולה זו" });
        });

        it("should return error when reported user ID is invalid", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValue("");

            const result = await getUserNotes("");

            expect(result).toEqual({ success: false, error: "מזהה משתמש נדרש" });
        });

        it("should handle database errors", async () => {
            mockAuth.mockResolvedValue({ userId: "test-user-id" });
            mockSanitizeText.mockReturnValue("reported-user-id");
            mockSupabaseClient.order.mockResolvedValue({ data: null, error: new Error("Database error") });

            const result = await getUserNotes("reported-user-id");

            expect(result).toEqual({ success: false, error: "שגיאה בטעינת הערות" });
        });
    });
});
