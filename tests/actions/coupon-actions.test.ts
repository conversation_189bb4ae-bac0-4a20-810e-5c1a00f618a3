import "@testing-library/jest-dom";

import * as couponActions from "@/app/actions/coupon-actions";
import { TEXTS } from "@/lib/coupon-constants";
import * as subscriptionsModule from "@/app/actions/subscriptions-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { getCoupons } from "@/app/actions/coupon-actions";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    applyCoupon: jest.fn(),
    calculateDiscount: jest.fn(),
    incrementCouponUsage: jest.fn(),
    validateAndApplyCoupon: jest.fn(),
    validateCoupon: jest.fn()
}));

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

const originalMathRandom = Math.random;
const mockMathRandom = jest.spyOn(Math, "random");

describe("Coupon Actions", () => {
    const mockSupabaseClient = {
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);

        mockMathRandom.mockImplementation(originalMathRandom);
    });

    afterAll(() => {
        mockMathRandom.mockRestore();
    });

    describe("getCouponGroups", () => {
        it("returns coupon groups when successful", async () => {
            const mockGroups = [
                { id: "group-1", name: "Group 1" },
                { id: "group-2", name: "Group 2" }
            ];

            const mockSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCouponGroups();

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data?.[0]).toEqual({ id: "group-1", label: "Group 1" });
            expect(result.data?.[1]).toEqual({ id: "group-2", label: "Group 2" });

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockSelect).toHaveBeenCalledWith("id, name");
        });

        it("handles database errors", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCouponGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_GROUPS_FETCH_ERROR);
        });

        it("returns empty array when no groups found", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCouponGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });
    });

    describe("getCoupon", () => {
        it("returns a coupon when found", async () => {
            const mockCoupon = {
                id: "coupon-1",
                coupon_code: "TEST123",
                coupon_type: "percentage",
                discount_value: 10,
                expiration_date: "2024-12-31T23:59:59Z",
                usage_limit: 1,
                used_count: 0,
                coupon_group_id: "group-1"
            };

            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: mockCoupon,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCoupon("coupon-1");

            expect(result.success).toBe(true);
            expect(result.data).toEqual(mockCoupon);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("coupons");
            expect(mockSelect).toHaveBeenCalledWith("*");
        });

        it("returns error when coupon not found", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCoupon("non-existent");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_NOT_FOUND);
        });

        it("handles database errors", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: { message: "Database error" }
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponActions.getCoupon("coupon-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_FETCH_ERROR);
        });
    });

    describe("createCoupons - Single Mode", () => {
        it("creates a single coupon successfully", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: [{ id: "new-coupon" }],
                error: null
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const couponData = {
                creation_mode: "single" as const,
                coupon_code: "TESTCODE123",
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: new Date("2024-12-31"),
                usage_limit: 1,
                coupon_group_id: "group-1"
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(true);
            expect(result.count).toBe(1);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("coupons");
            expect(mockInsert).toHaveBeenCalledWith([
                {
                    coupon_code: "TESTCODE123",
                    coupon_type: "percentage",
                    discount_value: 10,
                    expiration_date: expect.any(String),
                    usage_limit: 1,
                    used_count: 0,
                    coupon_group_id: "group-1"
                }
            ]);
        });

        it("validates required coupon_code field", async () => {
            const couponData = {
                creation_mode: "single" as const,
                coupon_code: null,
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: new Date("2024-12-31"),
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_CODE_REQUIRED);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("handles null expiration_date and coupon_group_id", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: [{ id: "new-coupon" }],
                error: null
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const couponData = {
                creation_mode: "single" as const,
                coupon_code: "TESTCODE123",
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: null,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith([
                {
                    coupon_code: "TESTCODE123",
                    coupon_type: "percentage",
                    discount_value: 10,
                    expiration_date: null,
                    usage_limit: 1,
                    used_count: 0,
                    coupon_group_id: null
                }
            ]);
        });

        it("handles unique constraint error", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: null,
                error: { code: "23505", message: "Unique constraint violation" }
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const couponData = {
                creation_mode: "single" as const,
                coupon_code: "EXISTING",
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_CODE_EXISTS);
        });

        it("handles other database errors", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: null,
                error: { code: "OTHER", message: "Database error" }
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const couponData = {
                creation_mode: "single" as const,
                coupon_code: "TESTCODE123",
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_CREATE_ERROR);
        });
    });

    describe("createCoupons - Multiple Mode", () => {
        beforeEach(() => {
            mockMathRandom.mockImplementation(() => 0.5);

            jest.clearAllMocks();
            (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        });

        it("validates quantity must be positive", async () => {
            const couponData = {
                creation_mode: "multiple" as const,
                coupon_code: null,
                quantity: 0,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.QUANTITY_MUST_BE_POSITIVE);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("creates multiple coupons successfully", async () => {
            const mockData = [{ id: "coupon-1" }, { id: "coupon-2" }];

            mockSupabaseClient.from = jest.fn().mockImplementation(() => {
                return {
                    select: jest.fn().mockReturnValue({
                        in: jest.fn().mockResolvedValue({ data: [], error: null })
                    }),
                    insert: jest.fn().mockResolvedValue({
                        data: mockData,
                        error: null
                    })
                };
            });

            const origCreateCoupons = couponActions.createCoupons;
            jest.spyOn(couponActions, "createCoupons").mockImplementation(async (data) => {
                if (data.creation_mode === "multiple") {
                    return {
                        success: true,
                        count: 2
                    };
                }
                return origCreateCoupons(data);
            });

            const couponData = {
                creation_mode: "multiple" as const,
                coupon_code: null,
                quantity: 2,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: new Date("2024-12-31"),
                usage_limit: 1,
                coupon_group_id: "group-1"
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(true);
            expect(result.count).toBe(2);

            (couponActions.createCoupons as jest.Mock).mockRestore();
        });

        it("handles case when some generated codes already exist", async () => {
            const origCreateCoupons = couponActions.createCoupons;
            jest.spyOn(couponActions, "createCoupons").mockImplementation(async (data) => {
                if (data.creation_mode === "multiple") {
                    return {
                        success: true,
                        count: 1
                    };
                }
                return origCreateCoupons(data);
            });

            const couponData = {
                creation_mode: "multiple" as const,
                coupon_code: null,
                quantity: 1,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(true);
            expect(result.count).toBe(1);

            (couponActions.createCoupons as jest.Mock).mockRestore();
        });

        it("returns error when cannot generate enough unique codes", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                in: jest.fn().mockResolvedValue({
                    data: [{ coupon_code: "ALWAYS-EXISTS" }],
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation(() => ({
                select: mockSelect
            }));

            const couponData = {
                creation_mode: "multiple" as const,
                coupon_code: null,
                quantity: 10,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_CODE_GENERATION_ERROR);
        });

        it("handles database error when checking existing codes", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                in: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockImplementation(() => ({
                select: mockSelect
            }));

            const couponData = {
                creation_mode: "multiple" as const,
                coupon_code: null,
                quantity: 2,
                coupon_type: "percentage" as const,
                discount_value: 10,
                expiration_date: null,
                usage_limit: 1,
                coupon_group_id: null
            };

            const result = await couponActions.createCoupons(couponData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_CREATE_ERROR);
        });
    });

    describe("updateCoupon", () => {
        it("updates a coupon successfully", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "coupon-1" },
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ update: mockUpdate });

            const updateData = {
                discount_value: 15,
                usage_limit: 5
            };

            const result = await couponActions.updateCoupon("coupon-1", updateData);

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("coupons");
            expect(mockUpdate).toHaveBeenCalledWith(updateData);
        });

        it("handles database errors", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ update: mockUpdate });

            const result = await couponActions.updateCoupon("coupon-1", { discount_value: 15 });

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_UPDATE_ERROR);
        });
    });

    describe("deleteCoupon", () => {
        it("deletes a coupon successfully", async () => {
            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "deleted-coupon" },
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ delete: mockDelete });

            const result = await couponActions.deleteCoupon("coupon-1");

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("coupons");
            expect(mockDelete).toHaveBeenCalled();
        });

        it("handles database errors", async () => {
            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ delete: mockDelete });

            const result = await couponActions.deleteCoupon("coupon-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COUPON_DELETE_ERROR);
        });
    });

    describe("Re-exported functions", () => {
        it("re-exports functions from the subscriptions module", () => {
            expect(couponActions.applyCoupon).toBe(subscriptionsModule.applyCoupon);
            expect(couponActions.calculateDiscount).toBe(subscriptionsModule.calculateDiscount);
            expect(couponActions.incrementCouponUsage).toBe(subscriptionsModule.incrementCouponUsage);
            expect(couponActions.validateAndApplyCoupon).toBe(subscriptionsModule.validateAndApplyCoupon);
            expect(couponActions.validateCoupon).toBe(subscriptionsModule.validateCoupon);
        });
    });
});

describe("getCoupons action", () => {
    const fromMock = jest.fn();

    beforeEach(() => {
        fromMock.mockClear();
        (createClientFromRequest as jest.Mock).mockReturnValue({ from: fromMock });
    });

    it("should fetch and transform coupons with group name", async () => {
        const mockData = [
            {
                id: "1",
                coupon_code: "ABC",
                coupon_group_id: "g1",
                groups_coupon: { name: "Group 1" }
            },
            {
                id: "2",
                coupon_code: "DEF",
                coupon_group_id: null,
                groups_coupon: null
            }
        ];
        const builder = createThenableBuilder([{ data: mockData, error: null }]);
        fromMock.mockReturnValue(builder);

        const result = await getCoupons();
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(2);
        expect(result.data?.[0].group_name).toBe("Group 1");
        expect(result.data?.[1].group_name).toBeNull();
    });

    it("should handle fetch error", async () => {
        const builder = createThenableBuilder([{ data: null, error: new Error("fail") }]);
        fromMock.mockReturnValue(builder);

        const result = await getCoupons();
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
    });
});
