import {
    createDocumentTypeGroup,
    getDocumentTypeGroup,
    getDocumentTypeGroups,
    updateDocumentTypeGroup
} from "@/app/actions/document-type-group-actions";
import { TEXTS } from "@/lib/document-type-group-constants";
import { sanitizeText } from "@/utils/sanitization";

// Mock dependencies
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("@/utils/sanitization", () => ({
    sanitizeText: jest.fn()
}));

// Create a mock that properly handles chaining
const createMockSupabase = () => {
    const mockChain = {
        insert: jest.fn().mockResolvedValue({ error: null }),
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ error: null }),
        order: jest.fn().mockResolvedValue({ data: [], error: null }),
        single: jest.fn().mockResolvedValue({ data: null, error: null }),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis()
    };

    // Make all methods return the chain object for proper chaining
    mockChain.from.mockReturnValue(mockChain);
    mockChain.select.mockReturnValue(mockChain);
    mockChain.update.mockReturnValue(mockChain);

    return mockChain;
};

const { createClientFromRequest } = require("@/utils/supabase/server");
const mockSanitizeText = sanitizeText as jest.MockedFunction<typeof sanitizeText>;

describe("Document Type Group Actions", () => {
    let mockSupabase: ReturnType<typeof createMockSupabase>;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});

        mockSupabase = createMockSupabase();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabase);

        mockSanitizeText.mockImplementation((input: string, maxLength?: number) => {
            if (!input) return "";
            return input.slice(0, maxLength || 1000);
        });
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    describe("createDocumentTypeGroup", () => {
        const validFormData = {
            name: "Test Group",
            description: "Test Description"
        };

        it("should create a group successfully with sanitized data", async () => {
            const result = await createDocumentTypeGroup(validFormData);

            expect(mockSanitizeText).toHaveBeenCalledWith("Test Group", 100);
            expect(mockSanitizeText).toHaveBeenCalledWith("Test Description", 1000);
            expect(mockSupabase.from).toHaveBeenCalledWith("groups_document_type");
            expect(mockSupabase.insert).toHaveBeenCalledWith({
                name: "Test Group",
                description: "Test Description"
            });
            expect(result.success).toBe(true);
        });

        it("should handle empty description correctly", async () => {
            const result = await createDocumentTypeGroup({
                name: "Test Group",
                description: ""
            });

            expect(mockSupabase.insert).toHaveBeenCalledWith({
                name: "Test Group",
                description: null
            });
            expect(result.success).toBe(true);
        });

        it("should return error when name is empty after sanitization", async () => {
            mockSanitizeText.mockImplementation((input: string) => (input === "Test Group" ? "" : input));

            const result = await createDocumentTypeGroup({
                name: "Test Group",
                description: "Test Description"
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.NAME_REQUIRED);
            expect(mockSupabase.insert).not.toHaveBeenCalled();
        });

        it("should sanitize malicious input", async () => {
            mockSanitizeText.mockImplementation((input: string) => {
                if (input.includes("<script>")) return "Clean Name";
                return input;
            });

            const result = await createDocumentTypeGroup({
                name: "<script>alert('xss')</script>Malicious",
                description: "Normal description"
            });

            expect(mockSanitizeText).toHaveBeenCalledWith("<script>alert('xss')</script>Malicious", 100);
            expect(mockSupabase.insert).toHaveBeenCalledWith({
                name: "Clean Name",
                description: "Normal description"
            });
            expect(result.success).toBe(true);
        });

        it("should handle database error", async () => {
            mockSupabase.insert.mockResolvedValue({ error: { message: "DB Error" } });

            const result = await createDocumentTypeGroup(validFormData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CREATE_ERROR);
        });

        it("should handle exceptions", async () => {
            mockSupabase.insert.mockRejectedValue(new Error("Network error"));

            const result = await createDocumentTypeGroup(validFormData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CREATE_ERROR);
        });
    });

    describe("updateDocumentTypeGroup", () => {
        const groupId = "test-group-id";
        const validFormData = {
            name: "Updated Group",
            description: "Updated Description"
        };

        it("should update a group successfully with sanitized data", async () => {
            const result = await updateDocumentTypeGroup(groupId, validFormData);

            expect(mockSanitizeText).toHaveBeenCalledWith("Updated Group", 100);
            expect(mockSanitizeText).toHaveBeenCalledWith("Updated Description", 1000);
            expect(mockSupabase.from).toHaveBeenCalledWith("groups_document_type");
            expect(mockSupabase.update).toHaveBeenCalledWith({
                name: "Updated Group",
                description: "Updated Description"
            });
            expect(mockSupabase.eq).toHaveBeenCalledWith("id", groupId);
            expect(result.success).toBe(true);
        });

        it("should handle empty description correctly", async () => {
            const result = await updateDocumentTypeGroup(groupId, {
                name: "Updated Group",
                description: ""
            });

            expect(mockSupabase.update).toHaveBeenCalledWith({
                name: "Updated Group",
                description: null
            });
            expect(result.success).toBe(true);
        });

        it("should return error when name is empty after sanitization", async () => {
            mockSanitizeText.mockImplementation((input: string) => (input === "Updated Group" ? "" : input));

            const result = await updateDocumentTypeGroup(groupId, validFormData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.NAME_REQUIRED);
            expect(mockSupabase.update).not.toHaveBeenCalled();
        });

        it("should sanitize malicious input", async () => {
            mockSanitizeText.mockImplementation((input: string) => {
                if (input.includes("<script>")) return "Clean Updated Name";
                return input;
            });

            const result = await updateDocumentTypeGroup(groupId, {
                name: "<script>alert('xss')</script>Malicious Update",
                description: "Normal description"
            });

            expect(mockSanitizeText).toHaveBeenCalledWith("<script>alert('xss')</script>Malicious Update", 100);
            expect(mockSupabase.update).toHaveBeenCalledWith({
                name: "Clean Updated Name",
                description: "Normal description"
            });
            expect(result.success).toBe(true);
        });

        it("should handle database error", async () => {
            mockSupabase.eq.mockResolvedValue({ error: { message: "DB Error" } });

            const result = await updateDocumentTypeGroup(groupId, validFormData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.UPDATE_ERROR);
        });

        it("should handle exceptions", async () => {
            mockSupabase.eq.mockRejectedValue(new Error("Network error"));

            const result = await updateDocumentTypeGroup(groupId, validFormData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.UPDATE_ERROR);
        });
    });

    describe("getDocumentTypeGroups", () => {
        it("should return groups with count on success", async () => {
            const mockGroups = [
                { id: "1", name: "Group 1", document_types_count: [{ count: 3 }] },
                { id: "2", name: "Group 2", document_types_count: [{ count: 1 }] }
            ];
            mockSupabase.order.mockResolvedValue({ data: mockGroups, error: null });

            const result = await getDocumentTypeGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([
                { id: "1", name: "Group 1", document_types_count: 3 },
                { id: "2", name: "Group 2", document_types_count: 1 }
            ]);
            expect(mockSupabase.from).toHaveBeenCalledWith("groups_document_type");
            expect(mockSupabase.select).toHaveBeenCalledWith("*, document_types_count:document_types(count)");
            expect(mockSupabase.order).toHaveBeenCalledWith("name", { ascending: true });
        });

        it("should handle groups with null count", async () => {
            const mockGroups = [{ id: "1", name: "Group 1", document_types_count: null }];
            mockSupabase.order.mockResolvedValue({ data: mockGroups, error: null });

            const result = await getDocumentTypeGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([{ id: "1", name: "Group 1", document_types_count: 0 }]);
        });

        it("should handle database error", async () => {
            mockSupabase.order.mockResolvedValue({
                data: null,
                error: { message: "DB Error" }
            });

            const result = await getDocumentTypeGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.FETCH_GROUPS_ERROR);
        });
    });

    describe("getDocumentTypeGroup", () => {
        it("should return a single group on success", async () => {
            const mockGroup = { id: "1", name: "Test Group", description: "Test Description" };

            // Create a mock for the eq().single() chain
            const mockEqResult = {
                single: jest.fn().mockResolvedValue({ data: mockGroup, error: null })
            };
            mockSupabase.eq.mockReturnValue(mockEqResult);

            const result = await getDocumentTypeGroup("1");

            expect(result.success).toBe(true);
            expect(result.data).toEqual(mockGroup);
            expect(mockSupabase.from).toHaveBeenCalledWith("groups_document_type");
            expect(mockSupabase.select).toHaveBeenCalledWith("*");
            expect(mockSupabase.eq).toHaveBeenCalledWith("id", "1");
            expect(mockEqResult.single).toHaveBeenCalled();
        });

        it("should handle database error", async () => {
            // Create a mock for the eq().single() chain with error
            const mockEqResult = {
                single: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "DB Error" }
                })
            };
            mockSupabase.eq.mockReturnValue(mockEqResult);

            const result = await getDocumentTypeGroup("1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.FETCH_GROUP_ERROR);
        });
    });
});
