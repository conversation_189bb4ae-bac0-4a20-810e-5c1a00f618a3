import { uploadUserDocument } from "@/app/actions/user-document-actions";
import { isAdminFromSessionClaims } from "@/lib/org-role";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "@/types/database.types";

// Helper to create a typed mock Supabase client
function createMockSupabaseClient(): jest.Mocked<SupabaseClient<Database>> {
    return {
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
            data: {
                id: "doc",
                name: "Doc",
                description: "",
                allowed_mime_types: ["application/pdf"],
                max_file_size_mb: 10
            },
            error: null
        }),
        storage: {
            from: jest.fn().mockReturnThis(),
            upload: jest.fn().mockResolvedValue({ error: null }),
            createSignedUrl: jest.fn().mockResolvedValue({ data: { signedUrl: "url" }, error: null })
        }
    } as unknown as jest.Mocked<SupabaseClient<Database>>;
}

jest.mock("@/lib/org-role");
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn().mockResolvedValue(createMockSupabaseClient()),
    createServiceRoleClient: jest.fn().mockReturnValue({
        storage: {
            from: jest.fn().mockReturnThis(),
            upload: jest.fn().mockResolvedValue({ error: null }),
            createSignedUrl: jest.fn().mockResolvedValue({ data: { signedUrl: "url" }, error: null })
        }
    })
}));

const mockAuth = jest.fn();
jest.mock("@clerk/nextjs/server", () => ({
    auth: () => mockAuth()
}));

describe("uploadUserDocument admin authorization", () => {
    const file = new File(["dummy content"], "test.pdf", { type: "application/pdf" });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("allows admin to use overrideUserId", async () => {
        mockAuth.mockResolvedValue({ userId: "admin-id", sessionClaims: { organizations: { org1: "org:admin" } } });
        (isAdminFromSessionClaims as jest.Mock).mockReturnValue(true);
        const result = await uploadUserDocument("doc", file, "other-user-id");
        expect(result.success).toBe(true);
    });

    it("rejects non-admin using overrideUserId", async () => {
        mockAuth.mockResolvedValue({ userId: "user-id", sessionClaims: { organizations: { org1: "org:user" } } });
        (isAdminFromSessionClaims as jest.Mock).mockReturnValue(false);
        const result = await uploadUserDocument("doc", file, "other-user-id");
        expect(result.success).toBe(false);
        expect(result.error).toBe("נדרשות הרשאות מנהל לביצוע פעולה זו.");
    });

    it("allows user to upload for self (no overrideUserId)", async () => {
        mockAuth.mockResolvedValue({ userId: "user-id", sessionClaims: { organizations: { org1: "org:user" } } });
        (isAdminFromSessionClaims as jest.Mock).mockReturnValue(false);
        const result = await uploadUserDocument("doc", file);
        expect(result.success).toBe(true);
    });

    it("allows admin to upload for self (no overrideUserId)", async () => {
        mockAuth.mockResolvedValue({ userId: "admin-id", sessionClaims: { organizations: { org1: "org:admin" } } });
        (isAdminFromSessionClaims as jest.Mock).mockReturnValue(true);
        const result = await uploadUserDocument("doc", file);
        expect(result.success).toBe(true);
    });
});
