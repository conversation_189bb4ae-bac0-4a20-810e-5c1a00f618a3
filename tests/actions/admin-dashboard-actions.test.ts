import { getSubscriptionStats, getUserStats } from "@/app/actions/admin-dashboard-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { clerkClient } from "@clerk/nextjs/server";
import { PRICING_PLANS } from "@/config/subscriptions";
import { createThenableBuilder, SupabaseResponse } from "@/tests/mocks/supabase-mock";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));
jest.mock("@clerk/nextjs/server", () => ({
    clerkClient: jest.fn()
}));

const mockCreateClientFromRequest = createClientFromRequest as jest.Mock;
const mockClerkClient = clerkClient as jest.Mock;

describe("Dashboard Actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    describe("getUserStats", () => {
        it("should return the total user count from Clerk", async () => {
            const mockUsersGetCount = jest.fn().mockResolvedValue(123);
            mockClerkClient.mockReturnValue({
                users: {
                    getCount: mockUsersGetCount
                }
            });

            const result = await getUserStats();

            expect(result.data?.total).toBe(123);
            expect(mockClerkClient).toHaveBeenCalled();
            expect(mockUsersGetCount).toHaveBeenCalled();
        });

        it("should throw an error if Clerk client fails", async () => {
            const error = new Error("Clerk API error");
            mockClerkClient.mockRejectedValue(error);
            const result = await getUserStats();
            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch user stats");
        });
    });

    describe("getSubscriptionStats", () => {
        it("should calculate subscription stats correctly", async () => {
            const planA = PRICING_PLANS[0].id; // basic
            const planB = PRICING_PLANS[1].id; // pro

            const mockActiveSubs = [{ plan_id: planA }, { plan_id: planA }, { plan_id: planB }];
            const mockNewSubs = [{ plan_id: planB }];

            const queue: SupabaseResponse[] = [
                { data: mockActiveSubs, error: null },
                { data: mockNewSubs, error: null }
            ];
            const builder = createThenableBuilder(queue);
            mockCreateClientFromRequest.mockReturnValue({ from: () => builder });

            const result = await getSubscriptionStats();

            expect(result.data).toHaveLength(PRICING_PLANS.length);
            const planAStats = result.data?.find((p) => p.planId === planA);
            const planBStats = result.data?.find((p) => p.planId === planB);

            expect(planAStats?.count).toBe(2);
            expect(planAStats?.percentage).toBe(67); // Math.round((2/3) * 100)
            expect(planAStats?.newLast24h).toBe(0);

            expect(planBStats?.count).toBe(1);
            expect(planBStats?.percentage).toBe(33); // Math.round((1/3) * 100)
            expect(planBStats?.newLast24h).toBe(1);

            expect(builder.eq).toHaveBeenCalledWith("is_active", true);
            expect(builder.gte).toHaveBeenCalledWith("created_at", expect.any(String));
        });

        it("should throw an error if fetching active subscriptions fails", async () => {
            const error = new Error("DB error");
            const queue: SupabaseResponse[] = [{ data: null, error }];
            const builder = createThenableBuilder(queue);
            mockCreateClientFromRequest.mockReturnValue({ from: () => builder });

            const result = await getSubscriptionStats();
            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch active subscriptions");
        });

        it("should throw an error if fetching new subscriptions fails", async () => {
            const error = new Error("DB error");
            const queue: SupabaseResponse[] = [
                { data: [{ plan_id: "pro" }], error: null }, // Active subs succeed
                { data: null, error } // New subs fail
            ];
            const builder = createThenableBuilder(queue);
            mockCreateClientFromRequest.mockReturnValue({ from: () => builder });

            const result = await getSubscriptionStats();
            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch new subscriptions");
        });
    });
});
