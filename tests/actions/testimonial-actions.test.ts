import {
    createTestimonial,
    deleteTestimonial,
    getAllTestimonials,
    getTestimonial,
    updateTestimonial
} from "@/app/actions/testimonial-actions";
import { TESTIMONIAL_TEXTS } from "@/lib/testimonial-constants";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("Testimonial Actions", () => {
    const mockTestimonial = {
        id: "test-id",
        name: "Test Testimonial",
        text: "This is a test testimonial",
        type: "personal",
        created_at: "2023-01-01T00:00:00.000Z",
        updated_at: "2023-01-01T00:00:00.000Z"
    };

    const eq = jest.fn();
    const single = jest.fn();
    const insert = jest.fn();
    const update = jest.fn(() => ({ eq }));
    const del = jest.fn(() => ({ eq }));
    const order = jest.fn();
    const select = jest.fn(() => ({ eq, order, single }));

    const from = jest.fn(() => ({
        select,
        insert,
        update,
        delete: del
    }));

    const mockSupabase = {
        from
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabase);
    });

    describe("getTestimonial", () => {
        it("returns testimonial when found", async () => {
            eq.mockReturnValueOnce({ single });
            single.mockResolvedValueOnce({ data: mockTestimonial, error: null });

            const result = await getTestimonial("test-id");

            expect(from).toHaveBeenCalledWith("testimonials");
            expect(select).toHaveBeenCalledWith("*");
            expect(eq).toHaveBeenCalledWith("id", "test-id");
            expect(single).toHaveBeenCalled();
            expect(result).toEqual({
                success: true,
                data: mockTestimonial
            });
        });

        it("returns error when testimonial is not found", async () => {
            eq.mockReturnValueOnce({ single });
            single.mockResolvedValueOnce({ data: null, error: null });

            const result = await getTestimonial("non-existent-id");

            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.TESTIMONIAL_NOT_FOUND
            });
        });

        it("handles database error", async () => {
            const dbError = new Error("Database error");
            eq.mockReturnValueOnce({ single });
            single.mockResolvedValueOnce({ data: null, error: dbError });

            const result = await getTestimonial("test-id");

            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.TESTIMONIAL_FETCH_ERROR
            });
        });
    });

    describe("createTestimonial", () => {
        const validTestimonial = {
            name: "New Testimonial",
            text: "This is a new testimonial",
            type: { id: "personal" as const, label: "אישי" }
        };

        it("creates a testimonial successfully", async () => {
            insert.mockResolvedValueOnce({ data: { id: "new-id" }, error: null });

            const result = await createTestimonial(validTestimonial);

            expect(from).toHaveBeenCalledWith("testimonials");
            expect(insert).toHaveBeenCalledWith({
                name: validTestimonial.name,
                text: validTestimonial.text,
                type: validTestimonial.type.id
            });
            expect(result).toEqual({ success: true });
        });

        it("validates required fields", async () => {
            const invalidTestimonial = {
                name: "",
                text: "Valid text",
                type: { id: "personal" as const, label: "אישי" }
            };

            const result = await createTestimonial(invalidTestimonial);

            expect(insert).not.toHaveBeenCalled();
            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.nameRequired
            });
        });

        it("handles database error on insert", async () => {
            const dbError = new Error("Insert failed");
            insert.mockResolvedValueOnce({ data: null, error: dbError });

            const result = await createTestimonial(validTestimonial);

            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.TESTIMONIAL_CREATE_ERROR
            });
        });
    });

    describe("updateTestimonial", () => {
        const updateData = {
            name: "Updated Testimonial",
            text: "This is an updated testimonial",
            type: { id: "institution" as const, label: "מוסדי" }
        };

        it("updates a testimonial successfully", async () => {
            eq.mockResolvedValueOnce({ error: null });

            const result = await updateTestimonial("test-id", updateData);

            expect(from).toHaveBeenCalledWith("testimonials");
            expect(update).toHaveBeenCalledWith({
                name: updateData.name,
                text: updateData.text,
                type: updateData.type.id
            });
            expect(eq).toHaveBeenCalledWith("id", "test-id");
            expect(result).toEqual({ success: true });
        });

        it("validates required fields", async () => {
            const invalidUpdate = {
                name: "Valid name",
                text: "",
                type: { id: "personal" as const, label: "אישי" }
            };

            const result = await updateTestimonial("test-id", invalidUpdate);

            expect(update).not.toHaveBeenCalled();
            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.textRequired
            });
        });

        it("handles database error on update", async () => {
            eq.mockResolvedValueOnce({ error: new Error("Update failed") });

            const result = await updateTestimonial("test-id", updateData);

            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.TESTIMONIAL_UPDATE_ERROR
            });
        });
    });

    describe("deleteTestimonial", () => {
        it("deletes a testimonial successfully", async () => {
            eq.mockResolvedValueOnce({ error: null });

            const result = await deleteTestimonial("test-id");

            expect(from).toHaveBeenCalledWith("testimonials");
            expect(del).toHaveBeenCalled();
            expect(eq).toHaveBeenCalledWith("id", "test-id");
            expect(result).toEqual({ success: true });
        });

        it("handles database error on delete", async () => {
            eq.mockResolvedValueOnce({ error: new Error("Delete failed") });

            const result = await deleteTestimonial("test-id");

            expect(result).toEqual({
                success: false,
                error: TESTIMONIAL_TEXTS.TESTIMONIAL_DELETE_ERROR
            });
        });
    });

    describe("getAllTestimonials", () => {
        it("returns all testimonials successfully", async () => {
            const mockTestimonials = [
                mockTestimonial,
                {
                    ...mockTestimonial,
                    id: "test-id-2",
                    name: "Second Testimonial"
                }
            ];

            order.mockResolvedValueOnce({ data: mockTestimonials, error: null });

            const result = await getAllTestimonials();

            expect(from).toHaveBeenCalledWith("testimonials");
            expect(select).toHaveBeenCalledWith("*");
            expect(order).toHaveBeenCalledWith("created_at", { ascending: false });
            expect(result).toEqual({
                success: true,
                data: mockTestimonials
            });
        });

        it("returns empty array when no testimonials found", async () => {
            order.mockResolvedValueOnce({ data: [], error: null });

            const result = await getAllTestimonials();

            expect(result).toEqual({
                success: true,
                data: []
            });
        });

        it("should return an error if fetching testimonials fails", async () => {
            const error = new Error("Fetch error");
            order.mockResolvedValue({ data: null, error });

            const result = await getAllTestimonials();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TESTIMONIAL_TEXTS.TESTIMONIAL_FETCH_ERROR);
        });
    });
});
