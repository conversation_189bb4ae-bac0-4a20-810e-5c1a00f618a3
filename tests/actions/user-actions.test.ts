import {
    searchUserByEmail,
    getUserDetailsByUserId,
    getUserSubscriptionByUserId,
    updateUserPlanByAdmin
} from "@/app/actions/user-actions";
import { sanitizeEmail, sanitizeText } from "@/utils/sanitization";
import { createClientFromRequest } from "@/utils/supabase/server";
import { TEXTS } from "@/lib/user-constants";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "@/types/database.types";

// Mock dependencies
jest.mock("@/utils/sanitization");
jest.mock("@/utils/supabase/server");
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({ userId: "test-user-id" }))
}));

const mockSanitizeEmail = sanitizeEmail as jest.MockedFunction<typeof sanitizeEmail>;
const mockSanitizeText = sanitizeText as jest.MockedFunction<typeof sanitizeText>;
const mockCreateClientFromRequest = createClientFromRequest as jest.MockedFunction<typeof createClientFromRequest>;

interface EqMock extends jest.Mock {
    secondEq?: jest.Mock;
}

describe("User Actions", () => {
    const mockSupabaseClient = {
        from: jest.fn(),
        update: jest.fn(),
        insert: jest.fn()
    };

    const mockSelect = jest.fn();
    const mockEq = jest.fn();
    const mockSingle = jest.fn();
    const mockLimit = jest.fn();
    const mockOrder = jest.fn();
    const mockOr = jest.fn();

    describe("searchUserByEmail", () => {
        beforeEach(() => {
            jest.clearAllMocks();

            // Setup mock chain - need the full chain including order, limit, single
            const mockEq2 = jest.fn();

            mockSupabaseClient.from.mockReturnValue({
                select: mockSelect
            });
            mockSelect.mockReturnValue({
                eq: mockEq
            });
            mockEq.mockReturnValue({
                eq: mockEq2
            });
            mockEq2.mockReturnValue({
                order: mockOrder
            });
            mockOrder.mockReturnValue({
                limit: mockLimit
            });
            mockLimit.mockReturnValue({
                single: mockSingle
            });

            // Store references to both eq functions for testing
            (mockEq as EqMock).secondEq = mockEq2;

            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as unknown as SupabaseClient<Database>);
        });

        it("should successfully find user by email", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";
            const mockUserData = {
                id: "user-claim-id",
                user_id: "user-123",
                claim_value: sanitizedEmail
            };

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: mockUserData,
                error: null
            });

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(mockCreateClientFromRequest).toHaveBeenCalled();
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSelect).toHaveBeenCalledWith("id, user_id, claim_value");
            expect(mockEq).toHaveBeenCalledWith("claim_key", "user_email");
            expect((mockEq as EqMock).secondEq).toHaveBeenCalledWith("claim_value", `"${sanitizedEmail}"`);
            expect(mockSingle).toHaveBeenCalled();

            expect(result).toEqual({
                success: true,
                user: {
                    id: mockUserData.id,
                    email: mockUserData.claim_value, // Email comes from JSONB claim_value
                    user_id: mockUserData.user_id
                }
            });
        });

        it("should return error when email sanitization fails", async () => {
            const testEmail = "invalid-email";

            mockSanitizeEmail.mockReturnValue("");

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(mockCreateClientFromRequest).not.toHaveBeenCalled();

            expect(result).toEqual({
                success: false,
                error: TEXTS.emailRequired
            });
        });

        it("should return error when email sanitization returns null", async () => {
            const testEmail = "";

            mockSanitizeEmail.mockReturnValue("");

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(mockCreateClientFromRequest).not.toHaveBeenCalled();

            expect(result).toEqual({
                success: false,
                error: TEXTS.emailRequired
            });
        });

        it("should return error when user is not found", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: null,
                error: null
            });

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(mockCreateClientFromRequest).toHaveBeenCalled();

            expect(result).toEqual({
                success: false,
                error: TEXTS.userNotFound
            });
        });

        it("should return error when Supabase query fails", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";
            const mockError = new Error("Database connection failed");

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: null,
                error: mockError
            });

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(mockCreateClientFromRequest).toHaveBeenCalled();
            expect(consoleSpy).toHaveBeenCalledWith("Error searching user:", mockError);

            expect(result).toEqual({
                success: false,
                error: TEXTS.userNotFound
            });

            consoleSpy.mockRestore();
        });

        it("should handle unexpected errors gracefully", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";
            const unexpectedError = new Error("Unexpected error");

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockCreateClientFromRequest.mockRejectedValue(unexpectedError);

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            const result = await searchUserByEmail(testEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(testEmail);
            expect(consoleSpy).toHaveBeenCalledWith("Error in searchUserByEmail:", unexpectedError);

            expect(result).toEqual({
                success: false,
                error: TEXTS.userSearchError
            });

            consoleSpy.mockRestore();
        });

        it("should handle malicious input through sanitization", async () => {
            const maliciousEmail = "<script>alert('xss')</script>@example.com";
            const sanitizedEmail = "<EMAIL>";
            const mockUserData = {
                id: "user-claim-id",
                user_id: "user-123",
                claim_value: sanitizedEmail
            };

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: mockUserData,
                error: null
            });

            const result = await searchUserByEmail(maliciousEmail);

            expect(mockSanitizeEmail).toHaveBeenCalledWith(maliciousEmail);
            expect(result.success).toBe(true);
            expect(result.user?.email).toBe(sanitizedEmail);
        });

        it("should query the correct database structure", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: {
                    id: "user-claim-id",
                    user_id: "user-123",
                    claim_value: sanitizedEmail
                },
                error: null
            });

            await searchUserByEmail(testEmail);

            // Verify the correct table and fields are queried
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSelect).toHaveBeenCalledWith("id, user_id, claim_value");
            expect(mockEq).toHaveBeenCalledWith("claim_key", "user_email");
        });

        it("should return user data in correct format", async () => {
            const testEmail = "<EMAIL>";
            const sanitizedEmail = "<EMAIL>";
            const mockUserData = {
                id: "claim-uuid-123",
                user_id: "user-id-456",
                claim_value: sanitizedEmail
            };

            mockSanitizeEmail.mockReturnValue(sanitizedEmail);
            mockSingle.mockResolvedValue({
                data: mockUserData,
                error: null
            });

            const result = await searchUserByEmail(testEmail);

            expect(result).toEqual({
                success: true,
                user: {
                    id: "claim-uuid-123",
                    email: sanitizedEmail, // Email comes from JSONB claim_value
                    user_id: "user-id-456"
                }
            });
        });
    });

    describe("getUserDetailsByUserId", () => {
        beforeEach(() => {
            jest.clearAllMocks();

            // Setup mock chain - need the full chain including order, limit, single
            const mockEq2 = jest.fn();

            mockSupabaseClient.from.mockReturnValue({
                select: mockSelect
            });
            mockSelect.mockReturnValue({
                eq: mockEq
            });
            mockEq.mockReturnValue({
                eq: mockEq2
            });
            mockEq2.mockReturnValue({
                order: mockOrder
            });
            mockOrder.mockReturnValue({
                limit: mockLimit
            });
            mockLimit.mockReturnValue({
                single: mockSingle
            });

            // Store references to both eq functions for testing
            (mockEq as EqMock).secondEq = mockEq2;

            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as unknown as SupabaseClient<Database>);
        });

        it("should successfully fetch user details by user ID", async () => {
            const testUserId = "user-123";
            const sanitizedUserId = "user-123";
            const userEmail = "<EMAIL>";
            const mockUserData = {
                id: "claim-id",
                user_id: testUserId,
                claim_value: userEmail
            };

            mockSanitizeText.mockReturnValue(sanitizedUserId);
            mockSingle.mockResolvedValue({
                data: mockUserData,
                error: null
            });

            const result = await getUserDetailsByUserId(testUserId);

            expect(mockSanitizeText).toHaveBeenCalledWith(testUserId, 100);
            expect(mockCreateClientFromRequest).toHaveBeenCalled();
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_claims");
            expect(mockSelect).toHaveBeenCalledWith("id, user_id, claim_value");
            expect(mockEq).toHaveBeenCalledWith("claim_key", "user_email");
            expect((mockEq as EqMock).secondEq).toHaveBeenCalledWith("user_id", sanitizedUserId);

            expect(result).toEqual({
                success: true,
                user: {
                    id: mockUserData.id,
                    email: userEmail,
                    user_id: mockUserData.user_id
                }
            });
        });

        it("should return error when user ID sanitization fails", async () => {
            const testUserId = "";

            mockSanitizeText.mockReturnValue("");

            const result = await getUserDetailsByUserId(testUserId);

            expect(mockSanitizeText).toHaveBeenCalledWith(testUserId, 100);
            expect(mockCreateClientFromRequest).not.toHaveBeenCalled();

            expect(result).toEqual({
                success: false,
                error: TEXTS.userNotFound
            });
        });

        it("should return error when user is not found by ID", async () => {
            const testUserId = "nonexistent-user";
            const sanitizedUserId = "nonexistent-user";

            mockSanitizeText.mockReturnValue(sanitizedUserId);
            mockSingle.mockResolvedValue({
                data: null,
                error: null
            });

            const result = await getUserDetailsByUserId(testUserId);

            expect(mockSanitizeText).toHaveBeenCalledWith(testUserId, 100);
            expect(mockCreateClientFromRequest).toHaveBeenCalled();

            expect(result).toEqual({
                success: false,
                error: TEXTS.userNotFound
            });
        });

        it("should handle database errors gracefully", async () => {
            const testUserId = "user-123";
            const sanitizedUserId = "user-123";
            const mockError = new Error("Database connection failed");

            mockSanitizeText.mockReturnValue(sanitizedUserId);
            mockSingle.mockResolvedValue({
                data: null,
                error: mockError
            });

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            const result = await getUserDetailsByUserId(testUserId);

            expect(consoleSpy).toHaveBeenCalledWith("Error fetching user details:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.userNotFound
            });

            consoleSpy.mockRestore();
        });

        it("should handle unexpected errors gracefully", async () => {
            const testUserId = "user-123";
            const sanitizedUserId = "user-123";
            const unexpectedError = new Error("Unexpected error");

            mockSanitizeText.mockReturnValue(sanitizedUserId);
            mockCreateClientFromRequest.mockRejectedValue(unexpectedError);

            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            const result = await getUserDetailsByUserId(testUserId);

            expect(consoleSpy).toHaveBeenCalledWith("Error in getUserDetailsByUserId:", unexpectedError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.userSearchError
            });

            consoleSpy.mockRestore();
        });
    });

    describe("getUserSubscriptionByUserId", () => {
        beforeEach(() => {
            jest.clearAllMocks();

            // Setup mock chain for subscription queries
            mockSupabaseClient.from.mockReturnValue({
                select: mockSelect
            });
            mockSelect.mockReturnValue({
                eq: mockEq
            });
            mockEq.mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    or: mockOr
                })
            });
            mockOr.mockReturnValue({
                order: mockOrder
            });
            mockOrder.mockReturnValue({
                limit: mockLimit
            });

            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as unknown as SupabaseClient<Database>);
        });

        it("returns user subscription successfully", async () => {
            const mockSubscription = {
                id: "sub-123",
                user_id: "user-123",
                plan_id: "milgapro",
                is_active: true,
                start_date: "2023-01-01",
                expiration_date: "2024-01-01",
                created_at: "2023-01-01",
                updated_at: "2023-01-01",
                coupon_id: null,
                plan_price: 100,
                paid_amount: 100,
                payment_details: null,
                transaction_id: null,
                order_id: null
            };

            mockLimit.mockResolvedValue({
                data: [mockSubscription],
                error: null
            });

            const result = await getUserSubscriptionByUserId("user-123");

            expect(result.success).toBe(true);
            expect(result.subscription).toMatchObject({
                ...mockSubscription,
                planType: expect.any(String)
            });
        });

        it("returns null when no subscription found", async () => {
            mockLimit.mockResolvedValue({
                data: [],
                error: null
            });

            const result = await getUserSubscriptionByUserId("user-123");

            expect(result.success).toBe(true);
            expect(result.subscription).toBeNull();
        });

        it("handles database error", async () => {
            mockLimit.mockResolvedValue({
                data: null,
                error: new Error("DB Error")
            });

            const result = await getUserSubscriptionByUserId("user-123");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch user subscription");
        });

        it("validates required userId parameter", async () => {
            const result = await getUserSubscriptionByUserId("");

            expect(result.success).toBe(false);
            expect(result.error).toBe("User ID is required");
        });
    });

    describe("updateUserPlanByAdmin", () => {
        const mockUpdateEq = jest.fn();
        const mockInsert = jest.fn();

        beforeEach(() => {
            jest.clearAllMocks();

            // Setup mock chain for update operations
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "user_subscriptions") {
                    return {
                        update: jest.fn().mockReturnValue({
                            eq: mockUpdateEq
                        }),
                        insert: mockInsert
                    };
                }
                return {};
            });

            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as unknown as SupabaseClient<Database>);
        });

        it("updates user plan successfully", async () => {
            mockUpdateEq.mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockInsert.mockResolvedValue({
                data: [{ id: "new-sub-123" }],
                error: null
            });

            const result = await updateUserPlanByAdmin("admin-123", "user-123", "milgapro");

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith(
                expect.objectContaining({
                    user_id: "user-123",
                    plan_id: "milgapro",
                    is_active: true,
                    plan_price: 0,
                    paid_amount: 0
                })
            );
        });

        it("validates required parameters", async () => {
            const result = await updateUserPlanByAdmin("", "user-123", "milgapro");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Missing required parameters");
        });

        it("validates plan exists", async () => {
            const result = await updateUserPlanByAdmin("admin-123", "user-123", "invalid-plan");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Plan not found: invalid-plan");
        });

        it("handles database error during insert", async () => {
            mockUpdateEq.mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockInsert.mockResolvedValue({
                data: null,
                error: new Error("Insert failed")
            });

            const result = await updateUserPlanByAdmin("admin-123", "user-123", "milgapro");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to update user subscription");
        });
    });
});
