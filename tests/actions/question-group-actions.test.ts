import { revalidatePath } from "next/cache";

import {
    createQuestionGroup,
    deleteQuestionGroup,
    getQuestionGroup,
    getQuestionGroups,
    updateQuestionGroup
} from "@/app/actions/question-group-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";
import { TEXTS } from "@/lib/question-group-constants";

jest.mock("next/cache");
jest.mock("@/utils/supabase/server");

const mockRevalidatePath = revalidatePath as jest.MockedFunction<typeof revalidatePath>;
const fromMock = jest.fn();

(createClientFromRequest as jest.Mock).mockReturnValue({
    from: fromMock
});

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("Question Group Actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("createQuestionGroup", () => {
        it("creates question group successfully", async () => {
            const insertBuilder = createThenableBuilder([{ error: null }]);
            fromMock.mockReturnValue(insertBuilder);

            const result = await createQuestionGroup({ name: "שאלות בסיס" });

            expect(result).toEqual({ success: true });
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(insertBuilder.insert).toHaveBeenCalledWith({ name: "שאלות בסיס" });
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/questions");
        });

        it("handles validation error for empty name", async () => {
            const result = await createQuestionGroup({ name: "" });

            expect(result).toEqual({
                success: false,
                error: TEXTS.NAME_REQUIRED
            });
            expect(fromMock).not.toHaveBeenCalled();
        });

        it("truncates name to 100 characters when exceeding max length", async () => {
            const longName = "א".repeat(101);
            const truncatedName = longName.slice(0, 100);
            const insertBuilder = createThenableBuilder([{ error: null }]);
            fromMock.mockReturnValue(insertBuilder);

            const result = await createQuestionGroup({ name: longName });

            expect(result).toEqual({ success: true });
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(insertBuilder.insert).toHaveBeenCalledWith({ name: truncatedName });
        });

        it("handles database error", async () => {
            const insertBuilder = createThenableBuilder([{ error: new Error("Database error") }]);
            fromMock.mockReturnValue(insertBuilder);

            const result = await createQuestionGroup({ name: "שאלות בסיס" });

            expect(result).toEqual({
                success: false,
                error: TEXTS.GROUP_CREATE_ERROR
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });
    });

    describe("updateQuestionGroup", () => {
        it("updates question group successfully", async () => {
            const updateBuilder = createThenableBuilder([{ error: null }]);
            fromMock.mockReturnValue(updateBuilder);

            const result = await updateQuestionGroup("test-id", { name: "שם מעודכן" });

            expect(result).toEqual({ success: true });
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(updateBuilder.update).toHaveBeenCalledWith({ name: "שם מעודכן" });
            expect(updateBuilder.eq).toHaveBeenCalledWith("id", "test-id");
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/questions");
        });

        it("handles validation error for empty name", async () => {
            const result = await updateQuestionGroup("test-id", { name: "" });

            expect(result).toEqual({
                success: false,
                error: TEXTS.NAME_REQUIRED
            });
            expect(fromMock).not.toHaveBeenCalled();
        });

        it("truncates name to 100 characters when exceeding max length", async () => {
            const longName = "א".repeat(101);
            const truncatedName = longName.slice(0, 100);
            const updateBuilder = createThenableBuilder([{ error: null }]);
            fromMock.mockReturnValue(updateBuilder);

            const result = await updateQuestionGroup("test-id", { name: longName });

            expect(result).toEqual({ success: true });
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(updateBuilder.update).toHaveBeenCalledWith({ name: truncatedName });
            expect(updateBuilder.eq).toHaveBeenCalledWith("id", "test-id");
        });

        it("handles database error", async () => {
            const updateBuilder = createThenableBuilder([{ error: new Error("Database error") }]);
            fromMock.mockReturnValue(updateBuilder);

            const result = await updateQuestionGroup("test-id", { name: "שם מעודכן" });

            expect(result).toEqual({
                success: false,
                error: TEXTS.GROUP_UPDATE_ERROR
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });
    });

    describe("getQuestionGroup", () => {
        it("fetches question group successfully", async () => {
            const mockData = {
                id: "test-id",
                name: "שאלות בסיס",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            };

            const selectBuilder = createThenableBuilder([{ data: mockData, error: null }]);
            fromMock.mockReturnValue(selectBuilder);

            const result = await getQuestionGroup("test-id");

            expect(result).toEqual({ success: true, data: mockData });
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(selectBuilder.select).toHaveBeenCalledWith("*");
            expect(selectBuilder.eq).toHaveBeenCalledWith("id", "test-id");
            expect(selectBuilder.single).toHaveBeenCalled();
        });

        it("handles not found error", async () => {
            const selectBuilder = createThenableBuilder([{ data: null, error: null }]);
            fromMock.mockReturnValue(selectBuilder);

            const result = await getQuestionGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: TEXTS.GROUP_NOT_FOUND
            });
        });

        it("handles database error", async () => {
            const selectBuilder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);
            fromMock.mockReturnValue(selectBuilder);

            const result = await getQuestionGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: TEXTS.GROUP_FETCH_ERROR
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });
    });

    describe("deleteQuestionGroup", () => {
        it("deletes question group successfully when not in use", async () => {
            const countBuilder = createThenableBuilder([{ count: 0, error: null }]);
            const deleteBuilder = createThenableBuilder([{ error: null }]);
            fromMock.mockReturnValueOnce(countBuilder).mockReturnValueOnce(deleteBuilder);

            const result = await deleteQuestionGroup("test-id");

            expect(result).toEqual({ success: true });
            expect(fromMock).toHaveBeenCalledWith("questions");
            expect(countBuilder.select).toHaveBeenCalledWith("id", { count: "exact", head: true });
            expect(countBuilder.eq).toHaveBeenCalledWith("group_id", "test-id");
            expect(fromMock).toHaveBeenCalledWith("groups_question");
            expect(deleteBuilder.delete).toHaveBeenCalled();
            expect(deleteBuilder.eq).toHaveBeenCalledWith("id", "test-id");
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/questions");
        });

        it("prevents deletion when group is in use", async () => {
            const countBuilder = createThenableBuilder([{ count: 5, error: null }]);
            fromMock.mockReturnValue(countBuilder);

            const result = await deleteQuestionGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: `${TEXTS.DELETION_IN_USE_PREFIX}5${TEXTS.DELETION_IN_USE_SUFFIX}`
            });
        });

        it("handles count query error", async () => {
            const countBuilder = createThenableBuilder([{ count: null, error: new Error("Database error") }]);
            fromMock.mockReturnValue(countBuilder);

            const result = await deleteQuestionGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: "Database error"
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });

        it("handles deletion error", async () => {
            const countBuilder = createThenableBuilder([{ count: 0, error: null }]);
            const deleteBuilder = createThenableBuilder([{ error: new Error("Database error") }]);
            fromMock.mockReturnValueOnce(countBuilder).mockReturnValueOnce(deleteBuilder);

            const result = await deleteQuestionGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: "Database error"
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });
    });

    describe("getQuestionGroups", () => {
        it("fetches question groups with counts successfully", async () => {
            const mockGroupsData = [
                {
                    id: "group1",
                    name: "שאלות בסיס",
                    created_at: "2023-01-01T00:00:00Z",
                    updated_at: "2023-01-01T00:00:00Z"
                },
                {
                    id: "group2",
                    name: "שאלות מתקדמות",
                    created_at: "2023-01-02T00:00:00Z",
                    updated_at: "2023-01-02T00:00:00Z"
                }
            ];

            const mockQuestionsData = [{ group_id: "group1" }, { group_id: "group1" }, { group_id: "group2" }];

            const groupsBuilder = createThenableBuilder([{ data: mockGroupsData, error: null }]);
            const questionsBuilder = createThenableBuilder([{ data: mockQuestionsData, error: null }]);
            fromMock.mockReturnValueOnce(groupsBuilder).mockReturnValueOnce(questionsBuilder);

            const result = await getQuestionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([
                { ...mockGroupsData[0], questions_count: 2 },
                { ...mockGroupsData[1], questions_count: 1 }
            ]);
        });

        it("handles empty groups", async () => {
            const groupsBuilder = createThenableBuilder([{ data: [], error: null }]);
            const questionsBuilder = createThenableBuilder([{ data: [], error: null }]);
            fromMock.mockReturnValueOnce(groupsBuilder).mockReturnValueOnce(questionsBuilder);

            const result = await getQuestionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });

        it("handles groups query error", async () => {
            const groupsBuilder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);
            fromMock.mockReturnValue(groupsBuilder);

            const result = await getQuestionGroups();

            expect(result).toEqual({
                success: false,
                error: "Database error"
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });

        it("handles questions count query error", async () => {
            const mockGroupsData = [
                {
                    id: "group1",
                    name: "שאלות בסיס",
                    created_at: "2023-01-01T00:00:00Z",
                    updated_at: "2023-01-01T00:00:00Z"
                }
            ];

            const groupsBuilder = createThenableBuilder([{ data: mockGroupsData, error: null }]);
            const questionsBuilder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);
            fromMock.mockReturnValueOnce(groupsBuilder).mockReturnValueOnce(questionsBuilder);

            const result = await getQuestionGroups();

            expect(result).toEqual({
                success: false,
                error: "Database error"
            });
            expect(consoleErrorSpy).toHaveBeenCalled();
        });
    });
});
