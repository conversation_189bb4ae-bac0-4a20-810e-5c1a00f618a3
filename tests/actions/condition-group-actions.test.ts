import "@testing-library/jest-dom";

import * as conditionGroupActions from "@/app/actions/condition-group-actions";
import { TEXTS } from "@/lib/condition-group-constants";
import { createClientFromRequest } from "@/utils/supabase/server";
import { type Tables } from "@/types/database.types";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

describe("Condition Group Actions", () => {
    const mockSupabaseClient = {
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        mockConsoleError.mockClear();
    });

    afterAll(() => {
        mockConsoleError.mockRestore();
    });

    describe("getConditionGroups", () => {
        it("returns condition groups with condition counts when successful", async () => {
            const mockGroups = [
                { id: "group-1", name: "Test Group 1", created_at: "2023-01-01T00:00:00Z" },
                { id: "group-2", name: "Test Group 2", created_at: "2023-01-02T00:00:00Z" }
            ];

            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            const mockConditions = [{ group_id: "group-1" }, { group_id: "group-1" }, { group_id: "group-2" }];

            const mockConditionsSelect = jest.fn().mockResolvedValue({
                data: mockConditions,
                error: null
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { select: mockGroupsSelect };
                } else if (table === "conditions") {
                    return { select: mockConditionsSelect };
                }
                return {};
            });

            const result = await conditionGroupActions.getConditionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data?.[0].conditions_count).toBe(2);
            expect(result.data?.[1].conditions_count).toBe(1);

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("conditions");
            expect(mockGroupsSelect).toHaveBeenCalledWith("*");
        });

        it("handles groups query error", async () => {
            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { select: mockGroupsSelect };
                }
                return {};
            });

            const result = await conditionGroupActions.getConditionGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUPS_FETCH_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("handles conditions query error", async () => {
            const mockGroups = [{ id: "group-1", name: "Test Group 1" }];

            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            const mockConditionsSelect = jest.fn().mockResolvedValue({
                data: null,
                error: { message: "Database error" }
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { select: mockGroupsSelect };
                } else if (table === "conditions") {
                    return { select: mockConditionsSelect };
                }
                return {};
            });

            const result = await conditionGroupActions.getConditionGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUPS_FETCH_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("returns empty array when no groups found", async () => {
            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            const mockConditionsSelect = jest.fn().mockResolvedValue({
                data: [],
                error: null
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { select: mockGroupsSelect };
                } else if (table === "conditions") {
                    return { select: mockConditionsSelect };
                }
                return {};
            });

            const result = await conditionGroupActions.getConditionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });
    });

    describe("getConditionGroup", () => {
        it("returns a condition group when found", async () => {
            const mockGroup = {
                id: "group-1",
                name: "Test Group",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            };

            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: mockGroup,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await conditionGroupActions.getConditionGroup("group-1");

            expect(result.success).toBe(true);
            expect(result.data).toEqual(mockGroup);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockSelect).toHaveBeenCalledWith("*");
        });

        it("returns error when condition group not found", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await conditionGroupActions.getConditionGroup("non-existent");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_NOT_FOUND);
        });

        it("handles database errors", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: { message: "Database error" }
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await conditionGroupActions.getConditionGroup("group-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_FETCH_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("getPersonalDetailsQuestions", () => {
        it("fetches and formats questions correctly", async () => {
            const mockQuestionsData = [
                {
                    id: "question-1",
                    type: "single_select",
                    metadata: { label: "Test Question 1" },
                    groups_question: [{ id: "group-1", name: "Personal Details" }]
                },
                {
                    id: "question-2",
                    type: "number_input",
                    metadata: { placeholder: "Enter number" },
                    groups_question: null
                }
            ];

            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    order: jest.fn().mockResolvedValue({
                        data: mockQuestionsData,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await conditionGroupActions.getPersonalDetailsQuestions();

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data?.[0]).toEqual({
                id: "question-1",
                type: "single_select",
                metadata: { label: "Test Question 1" },
                groups_question: { id: "group-1", name: "Personal Details" }
            });
            expect(result.data?.[1]).toEqual({
                id: "question-2",
                type: "number_input",
                metadata: { placeholder: "Enter number" },
                groups_question: undefined
            });
        });

        it("handles database errors", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    order: jest.fn().mockResolvedValue({
                        data: null,
                        error: { message: "Database error" }
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await conditionGroupActions.getPersonalDetailsQuestions();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.QUESTIONS_FETCH_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("getConditionGroupWithDependencies", () => {
        it("fetches condition group with dependencies", async () => {
            const mockGroup = {
                id: "group-1",
                name: "Test Group",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            };

            const mockConditions = [
                {
                    id: "condition-1",
                    group_id: "group-1",
                    question_id: "question-1",
                    type: "in",
                    value: ["option1", "option2"]
                }
            ];

            const mockQuestions = [
                {
                    id: "question-1",
                    type: "single_select",
                    metadata: { label: "Test Question 1" }
                }
            ];

            const mockGroupSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: mockGroup,
                        error: null
                    })
                })
            });

            const mockConditionsSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: mockConditions,
                    error: null
                })
            });

            const mockQuestionsSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    order: jest.fn().mockResolvedValue({
                        data: mockQuestions,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { select: mockGroupSelect };
                } else if (table === "conditions") {
                    return { select: mockConditionsSelect };
                } else if (table === "questions") {
                    return { select: mockQuestionsSelect };
                }
                return {};
            });

            const result = await conditionGroupActions.getConditionGroupWithDependencies("group-1");

            expect(result.success).toBe(true);
            expect(result.data?.group).toEqual(mockGroup);
            expect(result.data?.dependencies).toHaveLength(1);
            expect(result.data?.dependencies[0]).toEqual({
                id: "condition-1",
                question_id: { id: "question-1", label: "Test Question 1" },
                condition_type: "in",
                condition_value: ["option1", "option2"]
            });
        });

        it("handles group not found", async () => {
            const mockGroupSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockGroupSelect });

            const result = await conditionGroupActions.getConditionGroupWithDependencies("non-existent");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_NOT_FOUND);
        });
    });

    describe("createConditionGroup", () => {
        it("creates a condition group successfully", async () => {
            const mockInsert = jest.fn().mockReturnValue({
                select: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: { id: "new-group", name: "New Test Group" },
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "New Test Group",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockInsert).toHaveBeenCalledWith({
                name: "New Test Group"
            });
        });

        it("validates required name field", async () => {
            const groupData = {
                name: "",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates whitespace-only name field", async () => {
            const groupData = {
                name: "   \t\n   ",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates null name field", async () => {
            const groupData = {
                name: null as any,
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates undefined name field", async () => {
            const groupData = {
                name: undefined as any,
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("sanitizes HTML tags from name", async () => {
            const mockInsert = jest.fn().mockReturnValue({
                select: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: { id: "new-group", name: "Test Group" },
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "<script>alert('xss')</script>Test Group<b>Bold</b>",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith({
                name: "Test GroupBold"
            });
        });

        it("handles very long names by truncating", async () => {
            const mockInsert = jest.fn().mockReturnValue({
                select: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: { id: "new-group", name: "Very long name" },
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const longName = "A".repeat(150);
            const groupData = {
                name: longName,
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith({
                name: "A".repeat(100)
            });
        });

        it("trims whitespace from valid names", async () => {
            const mockInsert = jest.fn().mockReturnValue({
                select: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: { id: "new-group", name: "Test Group" },
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "  Test Group  ",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith({
                name: "Test Group"
            });
        });

        it("handles database errors", async () => {
            const mockInsert = jest.fn().mockReturnValue({
                select: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: { message: "Database error" }
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "New Test Group",
                dependencies: []
            };

            const result = await conditionGroupActions.createConditionGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_CREATE_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("updateConditionGroup", () => {
        it("updates a condition group successfully", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "group-1" },
                    error: null
                })
            });

            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { update: mockUpdate };
                } else if (table === "conditions") {
                    return { delete: mockDelete };
                }
                return {};
            });

            const groupData = {
                name: "Updated Group",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockUpdate).toHaveBeenCalledWith({
                name: "Updated Group"
            });
        });

        it("validates required name field", async () => {
            const groupData = {
                name: "",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates whitespace-only name field", async () => {
            const groupData = {
                name: "   \t\n   ",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates null name field", async () => {
            const groupData = {
                name: null as any,
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("validates undefined name field", async () => {
            const groupData = {
                name: undefined as any,
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("sanitizes HTML tags from name", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "group-1" },
                    error: null
                })
            });

            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { update: mockUpdate };
                } else if (table === "conditions") {
                    return { delete: mockDelete };
                }
                return {};
            });

            const groupData = {
                name: "<script>alert('xss')</script>Updated Group<b>Bold</b>",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(true);
            expect(mockUpdate).toHaveBeenCalledWith({
                name: "Updated GroupBold"
            });
        });

        it("handles very long names by truncating", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "group-1" },
                    error: null
                })
            });

            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { update: mockUpdate };
                } else if (table === "conditions") {
                    return { delete: mockDelete };
                }
                return {};
            });

            const longName = "B".repeat(150);
            const groupData = {
                name: longName,
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(true);
            expect(mockUpdate).toHaveBeenCalledWith({
                name: "B".repeat(100)
            });
        });

        it("trims whitespace from valid names", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "group-1" },
                    error: null
                })
            });

            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_condition") {
                    return { update: mockUpdate };
                } else if (table === "conditions") {
                    return { delete: mockDelete };
                }
                return {};
            });

            const groupData = {
                name: "  Updated Group  ",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(true);
            expect(mockUpdate).toHaveBeenCalledWith({
                name: "Updated Group"
            });
        });

        it("handles database errors", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ update: mockUpdate });

            const groupData = {
                name: "Updated Group",
                dependencies: []
            };

            const result = await conditionGroupActions.updateConditionGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_UPDATE_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("deleteConditionGroup", () => {
        it("deletes a condition group successfully", async () => {
            const mockConditionsDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            const mockGroupDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "deleted-group" },
                    error: null
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "conditions") {
                    return { delete: mockConditionsDelete };
                } else if (table === "groups_condition") {
                    return { delete: mockGroupDelete };
                }
                return {};
            });

            const result = await conditionGroupActions.deleteConditionGroup("group-1");

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("conditions");
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockConditionsDelete).toHaveBeenCalled();
            expect(mockGroupDelete).toHaveBeenCalled();
        });

        it("handles database errors", async () => {
            const mockConditionsDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ delete: mockConditionsDelete });

            const result = await conditionGroupActions.deleteConditionGroup("group-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.CONDITION_GROUP_DELETE_ERROR);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });
});
