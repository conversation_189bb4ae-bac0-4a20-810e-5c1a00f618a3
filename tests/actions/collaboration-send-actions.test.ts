import { sendCollaborationData } from "@/app/actions/collaboration-send-actions";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

interface SupabaseResponse {
    data?: any;
    count?: number | null;
    error?: Error | null;
}

interface MockBuilder {
    select: jest.Mock;
    insert: jest.Mock;
    update: jest.Mock;
    upsert: jest.Mock;
    order: jest.Mock;
    eq: jest.Mock;
    gte: jest.Mock;
    lte: jest.Mock;
    ilike: jest.<PERSON>ck;
    in: jest.Mock;
    not: jest.Mock;
    neq: jest.Mock;
    range: jest.Mock;
    delete: jest.Mock;
    maybeSingle: jest.Mock;
    single: jest.Mock;
    limit: jest.Mock;
    then: (resolve: (v: SupabaseResponse) => void) => void;
}

function createThenableBuilder(queue: SupabaseResponse[]) {
    const builder: MockBuilder = {
        select: jest.fn(() => builder),
        insert: jest.fn(() => builder),
        update: jest.fn(() => builder),
        upsert: jest.fn(() => builder),
        order: jest.fn(() => builder),
        eq: jest.fn(() => builder),
        gte: jest.fn(() => builder),
        lte: jest.fn(() => builder),
        ilike: jest.fn(() => builder),
        in: jest.fn(() => builder),
        not: jest.fn(() => builder),
        neq: jest.fn(() => builder),
        range: jest.fn(() => builder),
        delete: jest.fn(() => builder),
        maybeSingle: jest.fn(() => builder),
        single: jest.fn(() => builder),
        limit: jest.fn(() => builder),
        then: (resolve: (v: SupabaseResponse) => void) => {
            const next = queue.shift() ?? { data: [], count: 0, error: null };
            resolve(next);
        }
    };
    return builder;
}

const fromMock = jest.fn();
const authMock = {
    getSession: jest.fn()
};

(createClientFromRequest as jest.Mock).mockResolvedValue({
    from: fromMock,
    auth: authMock
});

let consoleErrorSpy: jest.SpyInstance;
let fetchSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

beforeEach(() => {
    jest.clearAllMocks();
    if (!global.fetch) {
        global.fetch = jest.fn();
    }
    fetchSpy = jest.spyOn(global, "fetch").mockImplementation(jest.fn());
});

afterEach(() => {
    if (fetchSpy) {
        fetchSpy.mockRestore();
    }
});

describe("sendCollaborationData", () => {
    const userId = "user-123";

    it("should return success with empty array when no answers found", async () => {
        const answersBuilder = createThenableBuilder([{ data: [], error: null }]);
        fromMock.mockReturnValue(answersBuilder);

        const result = await sendCollaborationData(userId);

        expect(fromMock).toHaveBeenCalledWith("answers");
        expect(answersBuilder.select).toHaveBeenCalledWith("*");
        expect(answersBuilder.eq).toHaveBeenCalledWith("user_id", userId);
        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toEqual([]);
    });

    it("should return success with empty array when no question-collaboration links found", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "test", created_at: "2024-01-01" }];
        const answersBuilder = createThenableBuilder([{ data: answers, error: null }]);
        const linksBuilder = createThenableBuilder([{ data: [], error: null }]);

        fromMock.mockReturnValueOnce(answersBuilder).mockReturnValueOnce(linksBuilder);

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toEqual([]);
    });

    it("should successfully send collaboration data with basic setup", async () => {
        const answers = [
            { id: "a1", user_id: userId, question_id: "q1", answer: "test answer", created_at: "2024-01-01" }
        ];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];
        const collaborationConditionLinks: any[] = [];
        const conditions: any[] = [];
        const linkedQuestions = [{ question_id: "q1" }];
        const questions = [
            {
                id: "q1",
                type: "short_text",
                metadata: { label: "Test Question" },
                section: "personal_details",
                group_id: "g1",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: {
                session: {
                    user: {
                        id: userId,
                        email: "<EMAIL>",
                        phone: "+1234567890"
                    }
                }
            }
        });

        const answersBuilder = createThenableBuilder([{ data: answers, error: null }]);
        const linksBuilder = createThenableBuilder([{ data: questionCollaborationLinks, error: null }]);
        const collaborationsBuilder = createThenableBuilder([{ data: collaborations, error: null }]);
        const conditionLinksBuilder = createThenableBuilder([{ data: collaborationConditionLinks, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: conditions, error: null }]);
        const linkedQuestionsBuilder = createThenableBuilder([{ data: linkedQuestions, error: null }]);
        const questionsBuilder = createThenableBuilder([{ data: questions, error: null }]);
        const historyBuilder = createThenableBuilder([{ error: null }]);

        fromMock
            .mockReturnValueOnce(answersBuilder)
            .mockReturnValueOnce(linksBuilder)
            .mockReturnValueOnce(collaborationsBuilder)
            .mockReturnValueOnce(conditionLinksBuilder)
            .mockReturnValueOnce(conditionsBuilder)
            .mockReturnValueOnce(questionsBuilder)
            .mockReturnValueOnce(linkedQuestionsBuilder)
            .mockReturnValueOnce(historyBuilder);

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toHaveLength(1);
        expect(result.collaborationsSent[0]).toEqual({
            collaborationId: "collab-1",
            statusCode: 200
        });

        expect(fetchSpy).toHaveBeenCalledWith("https://api.example.com/webhook", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: expect.stringContaining("test answer")
        });

        expect(historyBuilder.insert).toHaveBeenCalledWith({
            user_id: userId,
            collaboration_id: "collab-1",
            request_payload: expect.any(Object),
            response_status_code: 200,
            response_body: { success: true }
        });
    });

    it("should handle bearer token authentication", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "test", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: "bearer_token",
                auth_value: { token: "secret-token-123" },
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "short_text",
                            metadata: { label: "Test" },
                            section: "personal_details",
                            group_id: "g1",
                            created_at: "2024-01-01",
                            updated_at: "2024-01-01"
                        }
                    ],
                    error: null
                }
            ]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder, index) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(fetchSpy).toHaveBeenCalledWith("https://api.example.com/webhook", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer secret-token-123"
            },
            body: expect.any(String)
        });
    });

    it("should handle multi_select question type", async () => {
        const answers = [
            { id: "a1", user_id: userId, question_id: "q1", answer: '["option1", "option2"]', created_at: "2024-01-01" }
        ];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];
        const questions = [
            {
                id: "q1",
                type: "multi_select",
                metadata: { label: "Multi Select Question" },
                section: "personal_details",
                group_id: "g1",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: questions, error: null }]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);

        const fetchCall = fetchSpy.mock.calls[0];
        const payload = JSON.parse(fetchCall[1].body);
        expect(payload.answers["Multi Select Question"].value).toEqual(["option1", "option2"]);
    });

    it("should handle number_input question type", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "42.5", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];
        const questions = [
            {
                id: "q1",
                type: "number_input",
                metadata: { label: "Number Question" },
                section: "personal_details",
                group_id: "g1",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: questions, error: null }]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);

        const fetchCall = fetchSpy.mock.calls[0];
        const payload = JSON.parse(fetchCall[1].body);
        expect(payload.answers["Number Question"].value).toBe(42.5);
    });

    it("should handle API endpoint failure", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "test", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockRejectedValue(new Error("Network error"));

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "short_text",
                            metadata: { label: "Test" },
                            section: "personal_details",
                            group_id: "g1",
                            created_at: "2024-01-01",
                            updated_at: "2024-01-01"
                        }
                    ],
                    error: null
                }
            ]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toHaveLength(1);
        expect(result.collaborationsSent[0].statusCode).toBe(503);
    });

    it("should handle range condition checking", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "25", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];
        const collaborationConditionLinks = [{ collaboration_id: "collab-1", condition_id: "cond-1" }];
        const conditions = [
            {
                id: "cond-1",
                question_id: "q1",
                type: "range",
                value: { min: 18, max: 65 },
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: collaborationConditionLinks, error: null }]),
            createThenableBuilder([{ data: conditions, error: null }]),
            createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "number_input",
                            metadata: { label: "Age" },
                            section: "personal_details",
                            group_id: "g1",
                            created_at: "2024-01-01",
                            updated_at: "2024-01-01"
                        }
                    ],
                    error: null
                }
            ]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toHaveLength(1);
        expect(fetchSpy).toHaveBeenCalled();
    });

    it("should skip collaboration when conditions are not met", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "70", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];
        const collaborationConditionLinks = [{ collaboration_id: "collab-1", condition_id: "cond-1" }];
        const conditions = [
            {
                id: "cond-1",
                question_id: "q1",
                type: "range",
                value: { min: 18, max: 65 },
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        authMock.getSession.mockResolvedValue({
            data: { session: { user: { id: userId, email: "<EMAIL>" } } }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: collaborationConditionLinks, error: null }]),
            createThenableBuilder([{ data: conditions, error: null }]),
            createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "number_input",
                            metadata: { label: "Age" },
                            section: "personal_details",
                            group_id: "g1",
                            created_at: "2024-01-01",
                            updated_at: "2024-01-01"
                        }
                    ],
                    error: null
                }
            ])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toEqual([]);
        expect(fetchSpy).not.toHaveBeenCalled();
    });

    it("should return error when fetching answers fails", async () => {
        const answersBuilder = createThenableBuilder([{ data: null, error: new Error("Answers fetch failed") }]);
        fromMock.mockReturnValue(answersBuilder);

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Error fetching answers: Answers fetch failed");
        expect(result.collaborationsSent).toEqual([]);
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return error when fetching question-collaboration links fails", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "test", created_at: "2024-01-01" }];
        const answersBuilder = createThenableBuilder([{ data: answers, error: null }]);
        const linksBuilder = createThenableBuilder([{ data: null, error: new Error("Links fetch failed") }]);

        fromMock.mockReturnValueOnce(answersBuilder).mockReturnValueOnce(linksBuilder);

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Error fetching question-collaboration links: Links fetch failed");
        expect(result.collaborationsSent).toEqual([]);
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should handle fallback to user_claims table for user data", async () => {
        const answers = [{ id: "a1", user_id: userId, question_id: "q1", answer: "test", created_at: "2024-01-01" }];
        const questionCollaborationLinks = [{ collaboration_id: "collab-1", question_id: "q1" }];
        const collaborations = [
            {
                id: "collab-1",
                api_endpoint: "https://api.example.com/webhook",
                auth_type: null,
                auth_value: null,
                name: "Test Collaboration",
                description: "Test Description",
                created_at: "2024-01-01",
                updated_at: "2024-01-01"
            }
        ];

        fetchSpy.mockResolvedValue({
            status: 200,
            json: jest.fn().mockResolvedValue({ success: true })
        });

        authMock.getSession.mockResolvedValue({
            data: {
                session: {
                    user: {
                        id: "different-user-id",
                        email: "<EMAIL>",
                        phone: "+1111111111"
                    }
                }
            }
        });

        const builders = [
            createThenableBuilder([{ data: answers, error: null }]),
            createThenableBuilder([{ data: questionCollaborationLinks, error: null }]),
            createThenableBuilder([{ data: collaborations, error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([{ data: [], error: null }]),
            createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "short_text",
                            metadata: { label: "Test" },
                            section: "personal_details",
                            group_id: "g1",
                            created_at: "2024-01-01",
                            updated_at: "2024-01-01"
                        }
                    ],
                    error: null
                }
            ]),
            createThenableBuilder([{ data: [{ question_id: "q1" }], error: null }]),
            createThenableBuilder([
                {
                    data: [
                        { claim_key: "email", claim_value: "<EMAIL>" },
                        { claim_key: "phone", claim_value: "+1234567890" }
                    ],
                    error: null
                }
            ]),
            createThenableBuilder([{ error: null }])
        ];

        builders.forEach((builder) => {
            fromMock.mockReturnValueOnce(builder);
        });

        const result = await sendCollaborationData(userId);

        expect(result.success).toBe(true);
        expect(result.collaborationsSent).toHaveLength(1);

        const fetchCall = fetchSpy.mock.calls[0];
        const payload = JSON.parse(fetchCall[1].body);
        expect(payload.user.phone).toBe("+1234567890");
    });
});
