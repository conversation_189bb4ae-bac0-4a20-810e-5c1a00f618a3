import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { type Tables } from "@/types/database.types";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));
jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn(),
    createServiceRoleClient: jest.fn()
}));

const mockPlans = [
    {
        id: "milgapo-basic",
        title: "חבילת Basic",
        description: "גישה בסיסית",
        price: 0,
        planType: "free",
        duration_days: null,
        isFeatured: false,
        features: [],
        colors: {}
    },
    {
        id: "milgapo-elite",
        title: "Elite Student",
        description: "חבילה יוקרתית",
        price: 350,
        planType: "elite",
        duration_days: 365,
        isFeatured: true,
        features: [],
        colors: {}
    }
];

const mockFeatures = {
    personal_dashboard: {
        id: "personal_dashboard",
        name: "התאמת מלגות אישית"
    },
    auto_submit: {
        id: "auto_submit",
        name: "מנגנון הגשות אוטומטיות"
    }
};

jest.mock("@/config/subscriptions", () => ({
    FEATURES: mockFeatures,
    PRICING_PLANS: mockPlans,
    __esModule: true,
    default: {
        plans: mockPlans,
        features: mockFeatures
    }
}));

jest.mock("@/lib/subscription-constants", () => ({
    TEXTS: {
        COUPON_NOT_FOUND: "קופון לא נמצא",
        COUPON_EXPIRED: "קופון פג תוקף",
        COUPON_USAGE_LIMIT_REACHED: "קופון הגיע למגבלת השימוש",
        COUPON_UPDATE_ERROR: "שגיאה בעדכון קופון",
        PAYMENT_GATEWAY_CONFIG_ERROR: "שגיאת הגדרות תשלום",
        PAYMENT_MISSING_PARAMS: "חסרים פרמטרים לתשלום",
        PAYMENT_VERIFIED_SUCCESS: "תשלום אומת בהצלחה",
        PAYMENT_VERIFICATION_ERROR: "שגיאה באימות תשלום"
    }
}));

import * as subscriptionsActions from "@/app/actions/subscriptions-actions";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { createClientFromRequest, createServiceRoleClient } from "@/utils/supabase/server";

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockRevalidatePath = revalidatePath as jest.MockedFunction<typeof revalidatePath>;
const mockCreateClientFromRequest = createClientFromRequest as jest.MockedFunction<typeof createClientFromRequest>;
const mockCreateServiceRoleClient = createServiceRoleClient as jest.MockedFunction<typeof createServiceRoleClient>;

type MockSupabaseClient = {
    from: jest.MockedFunction<any>;
    select: jest.MockedFunction<any>;
    eq: jest.MockedFunction<any>;
    or: jest.MockedFunction<any>;
    order: jest.MockedFunction<any>;
    limit: jest.MockedFunction<any>;
};

// Silence console error/warn during these tests
global.console.error = jest.fn();
global.console.warn = jest.fn();

describe("Subscriptions Actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("getPlans", () => {
        it("returns all pricing plans", async () => {
            const plans = await subscriptionsActions.getPlans();

            expect(plans).toHaveLength(2);
            expect(plans[0]).toMatchObject({
                id: "milgapo-basic",
                title: "חבילת Basic",
                planType: "free"
            });
            expect(plans[1]).toMatchObject({
                id: "milgapo-elite",
                title: "Elite Student",
                planType: "elite"
            });
        });
    });

    describe("getFeatures", () => {
        it("returns all features", async () => {
            const features = await subscriptionsActions.getFeatures();

            expect(features).toHaveProperty("personal_dashboard");
            expect(features).toHaveProperty("auto_submit");
            expect(features.personal_dashboard).toMatchObject({
                id: "personal_dashboard",
                name: "התאמת מלגות אישית"
            });
        });
    });

    describe("getCurrentUserSubscription", () => {
        const mockSupabaseClient: MockSupabaseClient = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn()
        };

        beforeEach(() => {
            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as any);

            mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.or.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.order.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.limit.mockReturnValue(mockSupabaseClient);
        });

        it("returns null when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null } as any);

            const result = await subscriptionsActions.getCurrentUserSubscription();

            expect(result).toBeNull();
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("returns null when no subscription is found", async () => {
            mockAuth.mockResolvedValue({ userId: "user123" } as any);
            mockSupabaseClient.limit.mockResolvedValue({ data: [], error: null });

            const result = await subscriptionsActions.getCurrentUserSubscription();

            expect(result).toBeNull();
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("user_subscriptions");
        });

        it("returns subscription with mapped planType", async () => {
            mockAuth.mockResolvedValue({ userId: "user123" } as any);

            const mockSubscription: Tables<"user_subscriptions"> = {
                id: "sub123",
                user_id: "user123",
                plan_id: "milgapo-elite",
                is_active: true,
                start_date: "2023-01-01",
                expiration_date: "2024-01-01",
                created_at: "2023-01-01",
                updated_at: "2023-01-01",
                coupon_id: null,
                plan_price: 350,
                paid_amount: 350,
                payment_details: null,
                transaction_id: null,
                order_id: null
            };

            mockSupabaseClient.limit.mockResolvedValue({
                data: [mockSubscription],
                error: null
            });

            const result = await subscriptionsActions.getCurrentUserSubscription();

            expect(result).toMatchObject({
                ...mockSubscription,
                planType: "elite"
            });
        });

        it("defaults to free planType when plan not found in config", async () => {
            mockAuth.mockResolvedValue({ userId: "user123" } as any);

            const mockSubscription: Tables<"user_subscriptions"> = {
                id: "sub123",
                user_id: "user123",
                plan_id: "unknown-plan",
                is_active: true,
                start_date: "2023-01-01",
                expiration_date: "2024-01-01",
                created_at: "2023-01-01",
                updated_at: "2023-01-01",
                coupon_id: null,
                plan_price: 0,
                paid_amount: 0,
                payment_details: null,
                transaction_id: null,
                order_id: null
            };

            mockSupabaseClient.limit.mockResolvedValue({
                data: [mockSubscription],
                error: null
            });

            const result = await subscriptionsActions.getCurrentUserSubscription();

            expect(result).toMatchObject({
                ...mockSubscription,
                planType: "free"
            });
        });

        it("returns null on database error", async () => {
            mockAuth.mockResolvedValue({ userId: "user123" } as any);
            mockSupabaseClient.limit.mockResolvedValue({
                data: null,
                error: new Error("DB Error")
            });

            const result = await subscriptionsActions.getCurrentUserSubscription();

            expect(result).toBeNull();
        });
    });

    describe("calculateFinalPrice", () => {
        it("returns the correct final price with a fixed amount coupon", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(100, {
                couponType: "fixed_amount",
                discountValue: 20
            });
            expect(price).toBe(80);
        });

        it("returns the correct final price with a percentage coupon", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(100, {
                couponType: "percentage",
                discountValue: 15
            });
            expect(price).toBe(85);
        });

        it("handles a discount greater than the price", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(100, {
                couponType: "fixed_amount",
                discountValue: 120
            });
            expect(price).toBe(0);
        });

        it("returns the original price if the coupon is null", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(100, null);
            expect(price).toBe(100);
        });

        it("handles a zero price", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(0, {
                couponType: "percentage",
                discountValue: 10
            });
            expect(price).toBe(0);
        });

        it("rounds the final price to two decimal places", async () => {
            const price = await subscriptionsActions.calculateFinalPrice(99.99, {
                couponType: "percentage",
                discountValue: 10
            });
            expect(price).toBe(89.99); // 99.99 * 0.9 = 89.991 -> 89.99 rounded
        });
    });

    describe("extractCouponFromOrderId", () => {
        it("returns null if no coupon in orderId", async () => {
            const coupon = await subscriptionsActions.extractCouponFromOrderId("plan-id_user-id");
            expect(coupon).toBeNull();
        });

        it("extracts coupon code from orderId", async () => {
            const coupon = await subscriptionsActions.extractCouponFromOrderId("plan-id_user-id_COUPON_TEST2024");
            expect(coupon).toBe("TEST2024");
        });
    });

    describe("validateCoupon", () => {
        const mockSupabaseClient: MockSupabaseClient & { single: jest.MockedFunction<any> } = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn(),
            single: jest.fn()
        };

        beforeEach(() => {
            mockAuth.mockResolvedValue({ userId: "user-123" } as any);
            mockCreateServiceRoleClient.mockReturnValue(mockSupabaseClient as any);

            mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
        });

        it("returns invalid if coupon not found", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: { message: "Not found" } });
            const result = await subscriptionsActions.validateCoupon("NOT_FOUND");
            expect(result.isValid).toBe(false);
            if (!result.isValid) {
                expect(result.error).toBe("קופון לא נמצא");
            }
        });

        it("returns invalid if coupon is expired", async () => {
            const expiredCoupon = {
                coupon_code: "EXPIRED",
                expiration_date: "2020-01-01T00:00:00Z"
            };
            mockSupabaseClient.single.mockResolvedValue({ data: expiredCoupon, error: null });
            const result = await subscriptionsActions.validateCoupon("EXPIRED");
            expect(result.isValid).toBe(false);
            if (!result.isValid) {
                expect(result.error).toBe("קופון פג תוקף");
            }
        });

        it("returns invalid if usage limit reached", async () => {
            const usedCoupon = {
                coupon_code: "USED",
                expiration_date: null,
                usage_limit: 10,
                used_count: 10
            };
            mockSupabaseClient.single.mockResolvedValue({ data: usedCoupon, error: null });
            const result = await subscriptionsActions.validateCoupon("USED");
            expect(result.isValid).toBe(false);
            if (!result.isValid) {
                expect(result.error).toBe("קופון הגיע למגבלת השימוש");
            }
        });

        it("returns valid for a good coupon", async () => {
            const validCoupon = {
                coupon_code: "VALID",
                expiration_date: null,
                usage_limit: 10,
                used_count: 5,
                coupon_type: "percentage",
                discount_value: 15
            };
            mockSupabaseClient.single.mockResolvedValue({ data: validCoupon, error: null });
            const result = await subscriptionsActions.validateCoupon("VALID");
            expect(result.isValid).toBe(true);
            if (result.isValid) {
                expect(result.coupon).toEqual(validCoupon);
            }
        });
    });

    describe("incrementCouponUsage", () => {
        const mockSupabaseClient: MockSupabaseClient & { rpc: jest.MockedFunction<any> } = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn(),
            rpc: jest.fn()
        };

        beforeEach(() => {
            mockCreateClientFromRequest.mockResolvedValue(mockSupabaseClient as any);
        });

        it("returns success when increment is successful", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({ data: [{ incremented: true }], error: null });
            const result = await subscriptionsActions.incrementCouponUsage("VALID");
            expect(result.success).toBe(true);
        });

        it("returns failure on RPC error", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: { message: "DB error" } });
            const result = await subscriptionsActions.incrementCouponUsage("ANY");
            expect(result.success).toBe(false);
            expect(result.error).toBe("שגיאה בעדכון קופון");
        });

        it("returns failure if coupon not found by RPC", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: null });
            const result = await subscriptionsActions.incrementCouponUsage("NOT_FOUND");
            expect(result.success).toBe(false);
            expect(result.error).toBe("קופון לא נמצא");
        });

        it("returns failure if coupon usage limit is reached via RPC", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({ data: [{ incremented: false }], error: null });
            const result = await subscriptionsActions.incrementCouponUsage("LIMIT_REACHED");
            expect(result.success).toBe(false);
            expect(result.error).toBe("קופון הגיע למגבלת השימוש");
        });
    });

    describe("validateAndApplyCoupon", () => {
        const mockSupabaseClient: MockSupabaseClient & { single: jest.MockedFunction<any> } = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn(),
            single: jest.fn()
        };

        beforeEach(() => {
            mockAuth.mockResolvedValue({ userId: "user-123" } as any);
            mockCreateServiceRoleClient.mockReturnValue(mockSupabaseClient as any);
            mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
        });

        it("returns error if coupon is invalid", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: { message: "Not found" } });
            const result = await subscriptionsActions.validateAndApplyCoupon("INVALID", 100);
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toBe("קופון לא נמצא");
            }
            expect(result.finalAmount).toBe(100);
        });

        it("applies percentage coupon correctly", async () => {
            const validCoupon = {
                coupon_code: "PERCENT",
                coupon_type: "percentage",
                discount_value: 20
            } as Tables<"coupons">;
            mockSupabaseClient.single.mockResolvedValue({ data: validCoupon, error: null });
            const result = await subscriptionsActions.validateAndApplyCoupon("PERCENT", 100);
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.discountApplied).toBe(20);
                expect(result.finalAmount).toBe(80);
            }
        });
    });

    describe("applyCoupon", () => {
        const dummySupabase = {
            from: () => ({
                select: () => ({
                    eq: () => ({
                        single: () => Promise.resolve({ data: null, error: null })
                    })
                })
            }),
            // @ts-ignore – mock rpc for updateUserSubscription path
            rpc: jest.fn().mockResolvedValue({ data: [{ success: true }], error: null })
        } as unknown;

        beforeEach(() => {
            mockCreateClientFromRequest.mockResolvedValue(dummySupabase as any);
        });

        it("returns error if validation fails", async () => {
            jest.spyOn(subscriptionsActions, "validateAndApplyCoupon").mockResolvedValue({
                success: false,
                error: "Invalid coupon",
                finalAmount: 100
            } as any);

            const result = await subscriptionsActions.applyCoupon("INVALID", 100);
            expect(result.success).toBe(false);
        });

        it("returns error if increment fails", async () => {
            jest.spyOn(subscriptionsActions, "validateAndApplyCoupon").mockResolvedValue({
                success: true
            } as any);
            jest.spyOn(subscriptionsActions, "incrementCouponUsage").mockResolvedValue({
                success: false,
                error: "Increment failed"
            } as any);

            const result = await subscriptionsActions.applyCoupon("TEST10", 100);
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toBeDefined();
            }
        });
    });

    describe("updateUserSubscription", () => {
        const mockSupabaseClient: MockSupabaseClient & {
            rpc: jest.MockedFunction<any>;
            single: jest.MockedFunction<any>;
        } = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn(),
            rpc: jest.fn(),
            single: jest.fn()
        };

        beforeEach(() => {
            mockCreateServiceRoleClient.mockReturnValue(mockSupabaseClient as any);
            mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
        });

        it("returns success if transaction is already processed", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: { id: "sub123" }, error: null });
            const result = await subscriptionsActions.updateUserSubscription("user123", "milgapo-elite", {
                transactionId: "txn_existing"
            } as any);
            expect(result.success).toBe(true);
            expect(mockSupabaseClient.rpc).not.toHaveBeenCalled();
        });

        it("returns error if plan is not found", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: true });
            const result = await subscriptionsActions.updateUserSubscription("user123", "non-existent-plan");
            expect(result.success).toBe(false);
            expect(result.error).toContain("Plan not found");
        });

        it("calls RPC with correct data and returns success", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: true });
            mockSupabaseClient.rpc.mockResolvedValue({ data: [{ success: true }], error: null });

            const result = await subscriptionsActions.updateUserSubscription("user123", "milgapo-elite");

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.rpc).toHaveBeenCalledWith("update_user_subscription_with_coupon", {
                p_user_id: "user123",
                p_plan_id: "milgapo-elite",
                p_subscription_data: expect.any(Object),
                p_coupon_code: undefined
            });
            expect(revalidatePath).toHaveBeenCalledWith("/dashboard");
            expect(revalidatePath).toHaveBeenCalledWith("/account");
        });

        it("returns error on RPC failure", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: true });
            mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: { message: "RPC Error" } });

            const result = await subscriptionsActions.updateUserSubscription("user123", "milgapo-elite");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to update subscription");
        });

        it("returns error when RPC returns success:false", async () => {
            mockSupabaseClient.single.mockResolvedValue({ data: null, error: true });
            mockSupabaseClient.rpc.mockResolvedValue({
                data: [{ success: false, error_message: "Custom RPC error" }],
                error: null
            });

            const result = await subscriptionsActions.updateUserSubscription("user123", "milgapo-elite");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Custom RPC error");
        });
    });

    // ---------------------------------------------------------------------
    // Tests for verifyPaymentAndUpdateSubscription (basic coverage)
    // ---------------------------------------------------------------------
    describe("verifyPaymentAndUpdateSubscription", () => {
        const originalEnv = { ...process.env };
        const REQUIRED_ENV = {
            PAYMENT_GATEWAY_MERCHANT_ID: "123456",
            PAYMENT_GATEWAY_API_KEY: "apikey",
            PAYMENT_GATEWAY_PASSP: "passp",
            PAYMENT_GATEWAY_VERIFY_URL: "https://mock-gateway/verify"
        };

        const dummySupabase: any = {
            from: () => ({
                select: () => ({
                    eq: () => ({
                        single: () => Promise.resolve({ data: null, error: null })
                    })
                })
            }),
            // @ts-ignore – mock rpc for incrementCouponUsage
            rpc: jest.fn().mockResolvedValue({ data: [{ incremented: true }], error: null })
        };

        beforeAll(() => {
            // Ensure Response exists (provided by whatwg-fetch polyfill from jest.setup.js)
            if (typeof Response === "undefined") {
                global.Response = class {} as any;
            }
        });

        beforeEach(() => {
            // Set required env vars for success path
            Object.assign(process.env, REQUIRED_ENV);
            jest.resetAllMocks();
            mockCreateClientFromRequest.mockResolvedValue(dummySupabase);

            // Re-mock console.error and warn after resetAllMocks
            jest.spyOn(console, "error").mockImplementation(() => {});
            jest.spyOn(console, "warn").mockImplementation(() => {});
        });

        afterEach(() => {
            process.env = { ...originalEnv };
            jest.restoreAllMocks();
        });

        it("returns error when payment gateway env vars are missing", async () => {
            delete process.env.PAYMENT_GATEWAY_MERCHANT_ID;
            const result = await subscriptionsActions.verifyPaymentAndUpdateSubscription({} as any);
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });

        it("verifies payment successfully and updates subscription", async () => {
            // @ts-ignore – suppress type checks for mocked fetch
            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve("CCode=0")
            }) as any;

            // Mock auth to return a user
            mockAuth.mockResolvedValue({ userId: "user123" } as any);

            // Mock updateUserSubscription to succeed
            jest.spyOn(subscriptionsActions, "updateUserSubscription").mockResolvedValue({ success: true });

            const paymentParams = {
                Order: "milgapo-basic_12345",
                Id: "txn_1",
                CCode: 0,
                Amount: 49.9
            } as Parameters<typeof subscriptionsActions.verifyPaymentAndUpdateSubscription>[0];

            const result = await subscriptionsActions.verifyPaymentAndUpdateSubscription(paymentParams);

            expect(global.fetch).toHaveBeenCalled();
            expect(typeof result.success).toBe("boolean");
            // We primarily care that the function processed verification flow and returned an object with orderId / transactionId
            expect(result.orderId).toContain("milgapo-basic");
            expect(result.transactionId).toBe("txn_1");
        });
    });

    describe("applyFreePlan", () => {
        let mockUpdateUserSubscription: jest.MockedFunction<any>;
        const mockSupabaseClient: MockSupabaseClient & { rpc: jest.MockedFunction<any> } = {
            from: jest.fn(),
            select: jest.fn(),
            eq: jest.fn(),
            or: jest.fn(),
            order: jest.fn(),
            limit: jest.fn(),
            rpc: jest.fn()
        };

        beforeEach(() => {
            mockCreateServiceRoleClient.mockReturnValue(mockSupabaseClient as any);

            mockSupabaseClient.from.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.select.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.eq.mockReturnValue(mockSupabaseClient);
            mockSupabaseClient.rpc.mockResolvedValue({
                data: [{ success: true, error_message: null, subscription_id: "sub123" }],
                error: null
            });
            mockAuth.mockResolvedValue({ userId: "user123" } as any);
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it("should call updateUserSubscription with correct parameters", async () => {
            const result = await subscriptionsActions.applyFreePlan("user123", "milgapo-basic", "FREEBIE");
            expect(result.success).toBe(true);
            expect(revalidatePath).toHaveBeenCalledWith("/dashboard");
        });

        it("should return error if user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null } as any);
            const result = await subscriptionsActions.applyFreePlan("user123", "milgapo-basic");
            expect(result.success).toBe(false);
            expect(result.error).toBe("User not authenticated");
        });

        it("handles errors during subscription update", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: null,
                error: { message: "Update failed" }
            });
            const result = await subscriptionsActions.applyFreePlan("user123", "milgapo-basic");
            expect(result.success).toBe(false);
            expect(result.error).not.toBeNull();
        });

        it("handles unexpected errors", async () => {
            mockAuth.mockRejectedValue(new Error("Unexpected auth error"));
            const result = await subscriptionsActions.applyFreePlan("user123", "milgapo-basic");
            expect(result.success).toBe(false);
            expect(result.error).toBe("Unexpected auth error");
        });
    });
});
