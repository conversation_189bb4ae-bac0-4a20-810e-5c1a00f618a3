import {
    getScholarshipDocumentTypes,
    updateScholarshipDocumentTypes,
    getScholarshipConditionGroupsForScholarship,
    updateScholarshipConditionGroups,
    getAllQuestionsForConditionSelector,
    getScholarshipDefinedConditions,
    updateScholarshipDefinedConditions
} from "@/app/actions/scholarship-form-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";
import type { ConditionDependency } from "@/components/forms/fields/condition-selector";

// Mock Supabase client
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn(),
    createServiceRoleClient: jest.fn()
}));

const fromMock = jest.fn();
const mockSupabase = {
    from: fromMock
};

beforeEach(() => {
    jest.clearAllMocks();
    (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabase);
});

const scholarshipId = "sch-1";

describe("scholarship-form-actions", () => {
    describe("getScholarshipDocumentTypes", () => {
        it("returns document type ids on success", async () => {
            const builder = createThenableBuilder([
                {
                    data: [{ document_type_id: "doc-1" }, { document_type_id: "doc-2" }],
                    error: null
                }
            ]);
            fromMock.mockReturnValueOnce(builder);
            const result = await getScholarshipDocumentTypes(scholarshipId);
            expect(result.success).toBe(true);
            expect(result.data).toEqual(["doc-1", "doc-2"]);
            expect(fromMock).toHaveBeenCalledWith("link_scholarship_to_document_type");
        });

        it("returns error when Supabase fails", async () => {
            const builder = createThenableBuilder([
                {
                    data: null,
                    error: new Error("db fail")
                }
            ]);
            fromMock.mockReturnValueOnce(builder);
            const result = await getScholarshipDocumentTypes(scholarshipId);
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });

    describe("updateScholarshipDocumentTypes", () => {
        it("updates after deleting existing links (success path)", async () => {
            const deleteBuilder = createThenableBuilder([{ data: null, error: null }]);
            const insertBuilder = createThenableBuilder([{ error: null }]);
            fromMock
                .mockReturnValueOnce(deleteBuilder) // delete existing links
                .mockReturnValueOnce(insertBuilder); // insert new links

            const result = await updateScholarshipDocumentTypes(scholarshipId, ["doc-1", "doc-2"]);

            expect(result.success).toBe(true);
            expect(insertBuilder.insert).toHaveBeenCalledWith([
                { scholarship_id: scholarshipId, document_type_id: "doc-1" },
                { scholarship_id: scholarshipId, document_type_id: "doc-2" }
            ]);
        });

        it("returns error when delete fails", async () => {
            const deleteBuilder = createThenableBuilder([{ data: null, error: new Error("delete fail") }]);
            fromMock.mockReturnValueOnce(deleteBuilder);
            const result = await updateScholarshipDocumentTypes(scholarshipId, ["doc-1"]);
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });

    describe("getScholarshipConditionGroupsForScholarship", () => {
        it("returns group ids on success", async () => {
            const builder = createThenableBuilder([{ data: [{ group_id: "group-1" }], error: null }]);
            fromMock.mockReturnValueOnce(builder);
            const result = await getScholarshipConditionGroupsForScholarship(scholarshipId);
            expect(result.success).toBe(true);
            expect(result.data).toEqual(["group-1"]);
        });
    });

    describe("updateScholarshipConditionGroups", () => {
        it("updates groups successfully", async () => {
            const deleteBuilder = createThenableBuilder([{ data: null, error: null }]);
            const insertBuilder = createThenableBuilder([{ error: null }]);
            fromMock
                .mockReturnValueOnce(deleteBuilder) // delete
                .mockReturnValueOnce(insertBuilder); // insert
            const result = await updateScholarshipConditionGroups(scholarshipId, ["group-1"]);
            expect(result.success).toBe(true);
            expect(insertBuilder.insert).toHaveBeenCalledWith([{ scholarship_id: scholarshipId, group_id: "group-1" }]);
        });
    });

    describe("getAllQuestionsForConditionSelector", () => {
        it("transforms DB data into ConditionQuestion format", async () => {
            const builder = createThenableBuilder([
                {
                    data: [
                        {
                            id: "q1",
                            type: "single_select",
                            metadata: { label: "Label", placeholder: "Ph" },
                            groups_question: { id: "g1", name: "Group1" }
                        },
                        {
                            id: "q2",
                            type: "number_input",
                            metadata: null,
                            groups_question: null
                        }
                    ],
                    error: null
                }
            ]);
            fromMock.mockReturnValueOnce(builder);
            const result = await getAllQuestionsForConditionSelector();
            expect(result.success).toBe(true);
            expect(result.data?.[0]).toEqual(
                expect.objectContaining({ id: "q1", metadata: expect.objectContaining({ label: "Label" }) })
            );
            expect(result.data?.[0].groups_question).toEqual({ id: "g1", name: "Group1" });
            expect(result.data?.[1].groups_question).toBeUndefined();
        });
    });

    describe("getScholarshipDefinedConditions", () => {
        it("maps conditions correctly", async () => {
            // Builder for link_scholarship_to_condition
            const linksBuilder = createThenableBuilder([{ data: [{ condition_id: "cond-1" }], error: null }]);
            // Builder for conditions
            const conditionsBuilder = createThenableBuilder([
                {
                    data: [
                        {
                            id: "cond-1",
                            question_id: "q1",
                            type: "range",
                            value: { min: 10, max: 20 }
                        }
                    ],
                    error: null
                }
            ]);
            // Builder for questions
            const questionsBuilder = createThenableBuilder([
                {
                    data: [{ id: "q1", type: "number_input" }],
                    error: null
                }
            ]);
            fromMock
                .mockReturnValueOnce(linksBuilder)
                .mockReturnValueOnce(conditionsBuilder)
                .mockReturnValueOnce(questionsBuilder);

            const result = await getScholarshipDefinedConditions(scholarshipId);
            expect(result.success).toBe(true);
            expect(result.data).toEqual([
                {
                    question_id: "q1",
                    condition_type: "range",
                    condition_value: { min: 10, max: 20 }
                }
            ]);
        });

        it("returns empty array when no links", async () => {
            const linksBuilder = createThenableBuilder([{ data: [], error: null }]);
            fromMock.mockReturnValueOnce(linksBuilder);
            const result = await getScholarshipDefinedConditions(scholarshipId);
            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });
    });

    describe("updateScholarshipDefinedConditions", () => {
        it("inserts new conditions when none exist", async () => {
            const existingLinksBuilder = createThenableBuilder([{ data: [], error: null }]);
            const insertConditionsBuilder = createThenableBuilder([{ data: [{ id: "new-cond" }], error: null }]);
            const insertLinksBuilder = createThenableBuilder([{ error: null }]);

            fromMock
                .mockReturnValueOnce(existingLinksBuilder) // fetch existing links
                .mockReturnValueOnce(insertConditionsBuilder) // insert conditions
                .mockReturnValueOnce(insertLinksBuilder); // insert links

            const conditions: ConditionDependency[] = [
                {
                    question_id: { id: "q1", label: "q1" },
                    condition_type: "in",
                    condition_value: [{ id: "opt", label: "Opt" }]
                }
            ];

            const result = await updateScholarshipDefinedConditions(scholarshipId, conditions);
            expect(result.success).toBe(true);
            expect(insertConditionsBuilder.insert).toHaveBeenCalled();
            expect(insertLinksBuilder.insert).toHaveBeenCalled();
        });
    });
});
