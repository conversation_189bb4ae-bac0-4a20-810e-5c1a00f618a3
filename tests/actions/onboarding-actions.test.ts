import { completeOnboarding } from "@/app/actions/onboarding-actions";
import { auth, clerkClient } from "@clerk/nextjs/server";

// Mock the Clerk dependencies
jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(),
    clerkClient: jest.fn()
}));

const mockAuth = auth as unknown as jest.Mock;
const mockClerkClient = clerkClient as unknown as jest.Mock;

describe("Onboarding Actions", () => {
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();
        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe("completeOnboarding", () => {
        it("should return error message when user is not logged in", async () => {
            // Arrange
            mockAuth.mockResolvedValue({ userId: null });

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(result).toEqual({ message: "No Logged In User" });
            expect(mockClerkClient).not.toHaveBeenCalled();
        });

        it("should successfully complete onboarding for authenticated user", async () => {
            // Arrange
            const mockUserId = "user_123";
            const mockPublicMetadata = { onboardingComplete: true };
            const mockUpdateResponse = {
                publicMetadata: mockPublicMetadata
            };

            mockAuth.mockResolvedValue({ userId: mockUserId });

            const mockClient = {
                users: {
                    updateUser: jest.fn().mockResolvedValue(mockUpdateResponse)
                }
            };
            mockClerkClient.mockResolvedValue(mockClient);

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(mockAuth).toHaveBeenCalledTimes(1);
            expect(mockClerkClient).toHaveBeenCalledTimes(1);
            expect(mockClient.users.updateUser).toHaveBeenCalledWith(mockUserId, {
                publicMetadata: {
                    onboardingComplete: true
                }
            });
            expect(result).toEqual({ message: mockPublicMetadata });
        });

        it("should handle Clerk client creation error", async () => {
            // Arrange
            const mockUserId = "user_123";
            const mockError = new Error("Failed to create Clerk client");

            mockAuth.mockResolvedValue({ userId: mockUserId });
            mockClerkClient.mockRejectedValue(mockError);

            // Act & Assert
            // Since clerkClient() is called outside the try-catch block,
            // the error will be thrown and not caught
            await expect(completeOnboarding()).rejects.toThrow("Failed to create Clerk client");
            expect(mockAuth).toHaveBeenCalledTimes(1);
            expect(mockClerkClient).toHaveBeenCalledTimes(1);
        });

        it("should handle user update error", async () => {
            // Arrange
            const mockUserId = "user_123";
            const mockError = new Error("Failed to update user");

            mockAuth.mockResolvedValue({ userId: mockUserId });

            const mockClient = {
                users: {
                    updateUser: jest.fn().mockRejectedValue(mockError)
                }
            };
            mockClerkClient.mockResolvedValue(mockClient);

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(mockAuth).toHaveBeenCalledTimes(1);
            expect(mockClerkClient).toHaveBeenCalledTimes(1);
            expect(mockClient.users.updateUser).toHaveBeenCalledWith(mockUserId, {
                publicMetadata: {
                    onboardingComplete: true
                }
            });
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                "Error updating user metadata in completeOnboarding:",
                mockError
            );
            expect(result).toEqual({
                message: "There was an error updating the user metadata. Failed to update user"
            });
        });

        it("should handle non-Error exceptions", async () => {
            // Arrange
            const mockUserId = "user_123";
            const mockError = "String error message";

            mockAuth.mockResolvedValue({ userId: mockUserId });

            const mockClient = {
                users: {
                    updateUser: jest.fn().mockRejectedValue(mockError)
                }
            };
            mockClerkClient.mockResolvedValue(mockClient);

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                "Error updating user metadata in completeOnboarding:",
                mockError
            );
            expect(result).toEqual({
                message: "There was an error updating the user metadata. String error message"
            });
        });

        it("should handle auth function error", async () => {
            // Arrange
            const mockError = new Error("Auth failed");
            mockAuth.mockRejectedValue(mockError);

            // Act & Assert
            await expect(completeOnboarding()).rejects.toThrow("Auth failed");
            expect(mockClerkClient).not.toHaveBeenCalled();
        });

        it("should handle empty userId string", async () => {
            // Arrange
            mockAuth.mockResolvedValue({ userId: "" });

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(result).toEqual({ message: "No Logged In User" });
            expect(mockClerkClient).not.toHaveBeenCalled();
        });

        it("should handle undefined userId", async () => {
            // Arrange
            mockAuth.mockResolvedValue({ userId: undefined });

            // Act
            const result = await completeOnboarding();

            // Assert
            expect(result).toEqual({ message: "No Logged In User" });
            expect(mockClerkClient).not.toHaveBeenCalled();
        });
    });
});
