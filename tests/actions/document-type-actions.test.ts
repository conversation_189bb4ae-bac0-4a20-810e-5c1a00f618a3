import {
    getDocumentTypes,
    linkDocumentTypeToScholarship,
    unlinkDocumentTypeFromScholarship
} from "@/app/actions/document-type-actions";
import { TEXTS } from "@/lib/document-type-constants";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@clerk/nextjs/server", () => ({ auth: jest.fn() }));

jest.mock("@/lib/org-role", () => ({
    isAdminFromSessionClaims: jest.fn()
}));

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockAuth = require("@clerk/nextjs/server").auth;
const mockIsAdminFromSessionClaims = require("@/lib/org-role").isAdminFromSessionClaims;

const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    order: jest.fn(),
    insert: jest.fn(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn(),
    storage: {
        from: jest.fn().mockReturnThis(),
        getPublicUrl: jest.fn()
    }
};

(createClientFromRequest as jest.Mock).mockReturnValue(mockSupabase);

describe("Document Type Actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        (console.error as jest.Mock).mockRestore();
    });

    describe("getDocumentTypes", () => {
        it("should fetch and process document types successfully", async () => {
            const mockData = [
                { id: "1", example_file_path: "path/1.pdf" },
                { id: "2", example_file_path: null }
            ];
            (mockSupabase.order as jest.Mock).mockResolvedValue({ data: mockData, error: null });
            (mockSupabase.storage.getPublicUrl as jest.Mock).mockReturnValue({
                data: { publicUrl: "http://example.com/1.pdf" }
            });

            const result = await getDocumentTypes();

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data?.[0].example_file_url).toBe("http://example.com/1.pdf");
            expect(result.data?.[1].example_file_url).toBeUndefined();
            expect(mockSupabase.from).toHaveBeenCalledWith("document_types");
            expect(mockSupabase.select).toHaveBeenCalledWith("*, group:groups_document_type(name)");
            expect(mockSupabase.storage.from).toHaveBeenCalledWith("document_examples");
        });

        it("should return an error if Supabase fetch fails", async () => {
            (mockSupabase.order as jest.Mock).mockResolvedValue({
                data: null,
                error: { message: "Database connection failed" }
            });

            const result = await getDocumentTypes();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.FETCH_ERROR);
        });

        it("should handle unexpected errors gracefully", async () => {
            const unexpectedError = new Error("Network error");
            (mockSupabase.order as jest.Mock).mockRejectedValue(unexpectedError);

            const result = await getDocumentTypes();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.FETCH_ERROR);
        });
    });

    describe("linkDocumentTypeToScholarship", () => {
        const documentTypeId = "doc-123";
        const scholarshipId = "scholarship-456";

        it("should successfully link document type to scholarship for authenticated admin user", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:admin" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(true);
            (mockSupabase.insert as jest.Mock).mockResolvedValue({ error: null });

            const result = await linkDocumentTypeToScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(true);
            expect(mockAuth).toHaveBeenCalled();
            expect(mockIsAdminFromSessionClaims).toHaveBeenCalledWith({ organizations: { "org-1": "org:admin" } });
            expect(mockSupabase.from).toHaveBeenCalledWith("link_scholarship_to_document_type");
            expect(mockSupabase.insert).toHaveBeenCalledWith({
                document_type_id: documentTypeId,
                scholarship_id: scholarshipId
            });
        });

        it("should return auth error when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });

            const result = await linkDocumentTypeToScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.AUTH_REQUIRED);
            expect(mockSupabase.insert).not.toHaveBeenCalled();
        });

        it("should return admin error when user is not an admin", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:user" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(false);

            const result = await linkDocumentTypeToScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.ADMIN_REQUIRED);
            expect(mockSupabase.insert).not.toHaveBeenCalled();
        });

        it("should handle database errors gracefully", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:admin" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(true);
            (mockSupabase.insert as jest.Mock).mockResolvedValue({
                error: { message: "Database error" }
            });

            const result = await linkDocumentTypeToScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.DOCUMENT_TYPE_LINK_ERROR);
        });
    });

    describe("unlinkDocumentTypeFromScholarship", () => {
        const documentTypeId = "doc-123";
        const scholarshipId = "scholarship-456";

        it("should successfully unlink document type from scholarship for authenticated admin user", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:admin" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(true);
            (mockSupabase.eq as jest.Mock).mockReturnValueOnce(mockSupabase).mockReturnValueOnce({ error: null });

            const result = await unlinkDocumentTypeFromScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(true);
            expect(mockAuth).toHaveBeenCalled();
            expect(mockIsAdminFromSessionClaims).toHaveBeenCalledWith({ organizations: { "org-1": "org:admin" } });
            expect(mockSupabase.from).toHaveBeenCalledWith("link_scholarship_to_document_type");
            expect(mockSupabase.delete).toHaveBeenCalled();
            expect(mockSupabase.eq).toHaveBeenCalledWith("document_type_id", documentTypeId);
            expect(mockSupabase.eq).toHaveBeenCalledWith("scholarship_id", scholarshipId);
        });

        it("should return auth error when user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });

            const result = await unlinkDocumentTypeFromScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.AUTH_REQUIRED);
            expect(mockSupabase.delete).not.toHaveBeenCalled();
        });

        it("should return admin error when user is not an admin", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:user" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(false);

            const result = await unlinkDocumentTypeFromScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.ADMIN_REQUIRED);
            expect(mockSupabase.delete).not.toHaveBeenCalled();
        });

        it("should handle database errors gracefully", async () => {
            mockAuth.mockResolvedValue({
                userId: "user-123",
                sessionClaims: { organizations: { "org-1": "org:admin" } }
            });
            mockIsAdminFromSessionClaims.mockReturnValue(true);
            (mockSupabase.eq as jest.Mock)
                .mockReturnValueOnce(mockSupabase)
                .mockReturnValueOnce({ error: { message: "Database error" } });

            const result = await unlinkDocumentTypeFromScholarship(documentTypeId, scholarshipId);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.DOCUMENT_TYPE_UNLINK_ERROR);
        });
    });
});
