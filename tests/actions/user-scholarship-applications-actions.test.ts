import { type Tables, type TablesInsert } from "@/types/database.types";
import {
    getUserScholarshipApplications,
    updateUserScholarshipApplication
} from "@/app/actions/user-scholarship-applications-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";
import { auth } from "@clerk/nextjs/server";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

const TEXTS = {
    authError: "נדרשת התחברות"
};

const mockAuth = auth as jest.MockedFunction<typeof auth>;

const mockSupabase = {
    from: jest.fn()
};

(createClientFromRequest as jest.Mock).mockReturnValue(mockSupabase);

describe("User Scholarship Applications Actions", () => {
    const testUserId = "user-1";

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("getUserScholarshipApplications", () => {
        it("should return auth error if user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null } as any);

            const result = await getUserScholarshipApplications(["1"]);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.authError);
        });

        it("should fetch applications successfully for an authenticated user", async () => {
            mockAuth.mockResolvedValue({ userId: testUserId } as any);

            const mockApplications: Partial<Tables<"user_scholarship_applications">>[] = [
                { scholarship_id: "1", should_apply: true }
            ];
            const builder = createThenableBuilder([{ data: mockApplications, error: null }]);
            mockSupabase.from.mockReturnValue(builder);

            const result = await getUserScholarshipApplications(["1"]);

            expect(result.success).toBe(true);
            expect(result.applications).toEqual(mockApplications);
            expect(mockSupabase.from).toHaveBeenCalledWith("user_scholarship_applications");
            expect(builder.select).toHaveBeenCalledWith("scholarship_id, should_apply");
            expect(builder.in).toHaveBeenCalledWith("scholarship_id", ["1"]);
            expect(builder.eq).toHaveBeenCalledWith("user_id", testUserId);
        });

        it("should return an error if Supabase fetch fails", async () => {
            mockAuth.mockResolvedValue({ userId: testUserId } as any);

            const builder = createThenableBuilder([{ data: null, error: new Error("DB Error") }]);
            mockSupabase.from.mockReturnValue(builder);

            const result = await getUserScholarshipApplications(["1"]);

            expect(result.success).toBe(false);
            expect(result.error).toBe("DB Error");
        });
    });

    describe("updateUserScholarshipApplication", () => {
        it("should return auth error if user is not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null } as any);

            const result = await updateUserScholarshipApplication("1", true);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.authError);
        });

        it("should upsert application successfully for an authenticated user", async () => {
            mockAuth.mockResolvedValue({ userId: testUserId } as any);

            const builder = createThenableBuilder([{ data: null, error: null }]);
            mockSupabase.from.mockReturnValue(builder);

            const result = await updateUserScholarshipApplication("1", true);

            expect(result.success).toBe(true);
            expect(mockSupabase.from).toHaveBeenCalledWith("user_scholarship_applications");
            expect(builder.upsert).toHaveBeenCalledWith(
                {
                    user_id: testUserId,
                    scholarship_id: "1",
                    should_apply: true
                },
                { onConflict: "user_id,scholarship_id" }
            );
        });

        it("should return an error if Supabase upsert fails", async () => {
            mockAuth.mockResolvedValue({ userId: testUserId } as any);

            const builder = createThenableBuilder([{ data: null, error: new Error("DB Error") }]);
            mockSupabase.from.mockReturnValue(builder);

            const result = await updateUserScholarshipApplication("1", true);

            expect(result.success).toBe(false);
            expect(result.error).toBe("DB Error");
        });
    });
});
