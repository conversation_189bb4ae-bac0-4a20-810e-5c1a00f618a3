import { saveSignupPreferences } from "@/app/actions/signup-actions";
import { TEXTS } from "@/lib/auth-constants";
import { setUserClaim, UserClaimKey } from "@/utils/user-claims-client";

jest.mock("@clerk/nextjs/server", () => ({ auth: jest.fn() }));
jest.mock("@/utils/user-claims-client", () => ({
    setUserClaim: jest.fn(),
    UserClaimKey: {
        ACCEPTED_TERMS: "accepted_terms",
        SUBSCRIBED_TO_UPDATES: "subscribed_to_updates"
    }
}));
jest.mock("@/utils/supabase/server", () => ({
    createClerkSupabaseClient: jest.fn()
}));

const mockAuth = require("@clerk/nextjs/server").auth;
const mockSetUserClaim = require("@/utils/user-claims-client").setUserClaim;
const mockCreateClerkSupabaseClient = require("@/utils/supabase/server").createClerkSupabaseClient;

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("saveSignupPreferences", () => {
    const validData = {
        acceptedTerms: true,
        subscribeNewsletter: false
    };

    const mockSupabaseClient = {
        from: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        select: jest.fn(),
        eq: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockCreateClerkSupabaseClient.mockResolvedValue(mockSupabaseClient);
        mockSetUserClaim.mockResolvedValue(undefined);
    });

    it("should successfully save signup preferences", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const result = await saveSignupPreferences(validData);

        expect(mockAuth).toHaveBeenCalled();
        expect(mockCreateClerkSupabaseClient).toHaveBeenCalled();
        expect(mockSetUserClaim).toHaveBeenCalledWith(mockSupabaseClient, userId, UserClaimKey.ACCEPTED_TERMS, true);
        expect(mockSetUserClaim).toHaveBeenCalledWith(
            mockSupabaseClient,
            userId,
            UserClaimKey.SUBSCRIBED_TO_UPDATES,
            false
        );
        expect(result).toEqual({ success: true });
    });

    it("should return error when user is not authenticated", async () => {
        mockAuth.mockResolvedValue({ userId: null });

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.unauthorizedError
        });
        expect(mockCreateClerkSupabaseClient).not.toHaveBeenCalled();
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should return error when acceptedTerms is not boolean", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const invalidData = {
            acceptedTerms: "true" as any,
            subscribeNewsletter: false
        };

        const result = await saveSignupPreferences(invalidData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.invalidDataError
        });
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should return error when subscribeNewsletter is not boolean", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const invalidData = {
            acceptedTerms: true,
            subscribeNewsletter: "false" as any
        };

        const result = await saveSignupPreferences(invalidData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.invalidDataError
        });
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should return error when both fields are not boolean", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const invalidData = {
            acceptedTerms: 1 as any,
            subscribeNewsletter: 0 as any
        };

        const result = await saveSignupPreferences(invalidData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.invalidDataError
        });
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should handle database errors when setting user claims", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });
        mockSetUserClaim.mockRejectedValue(new Error("Database error"));

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.savePreferencesError
        });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error saving signup preferences:", expect.any(Error));
    });

    it("should handle Promise.all rejection when one claim fails", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });
        mockSetUserClaim.mockResolvedValueOnce(undefined).mockRejectedValueOnce(new Error("Second claim failed"));

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.savePreferencesError
        });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error saving signup preferences:", expect.any(Error));
    });

    it("should handle auth errors", async () => {
        mockAuth.mockRejectedValue(new Error("Auth error"));

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.savePreferencesError
        });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error saving signup preferences:", expect.any(Error));
    });

    it("should handle acceptedTerms as false", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const dataWithFalseTerms = {
            acceptedTerms: false,
            subscribeNewsletter: true
        };

        const result = await saveSignupPreferences(dataWithFalseTerms);

        expect(mockSetUserClaim).toHaveBeenCalledWith(mockSupabaseClient, userId, UserClaimKey.ACCEPTED_TERMS, false);
        expect(mockSetUserClaim).toHaveBeenCalledWith(
            mockSupabaseClient,
            userId,
            UserClaimKey.SUBSCRIBED_TO_UPDATES,
            true
        );
        expect(result).toEqual({ success: true });
    });

    it("should handle subscribeNewsletter as true", async () => {
        const userId = "test-user-id";
        mockAuth.mockResolvedValue({ userId });

        const dataWithTrueNewsletter = {
            acceptedTerms: true,
            subscribeNewsletter: true
        };

        const result = await saveSignupPreferences(dataWithTrueNewsletter);

        expect(mockSetUserClaim).toHaveBeenCalledWith(mockSupabaseClient, userId, UserClaimKey.ACCEPTED_TERMS, true);
        expect(mockSetUserClaim).toHaveBeenCalledWith(
            mockSupabaseClient,
            userId,
            UserClaimKey.SUBSCRIBED_TO_UPDATES,
            true
        );
        expect(result).toEqual({ success: true });
    });

    it("should handle undefined userId from auth", async () => {
        mockAuth.mockResolvedValue({ userId: undefined });

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.unauthorizedError
        });
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });

    it("should handle empty string userId from auth", async () => {
        mockAuth.mockResolvedValue({ userId: "" });

        const result = await saveSignupPreferences(validData);

        expect(result).toEqual({
            success: false,
            error: TEXTS.unauthorizedError
        });
        expect(mockSetUserClaim).not.toHaveBeenCalled();
    });
});
