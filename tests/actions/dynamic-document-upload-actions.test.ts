import {
    uploadDocument,
    getRequiredDocuments,
    checkUserDocumentStatus
} from "@/app/actions/dynamic-document-upload-actions";
import { TEXTS } from "@/lib/dynamic-document-upload-constants";

jest.mock("@clerk/nextjs/server", () => ({ auth: jest.fn() }));
jest.mock("next/cache", () => ({ revalidatePath: jest.fn() }));
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn(),
    createServiceRoleClient: jest.fn()
}));

const mockAuth = require("@clerk/nextjs/server").auth;
const mockCreateClientFromRequest = require("@/utils/supabase/server").createClientFromRequest;
const mockCreateServiceRoleClient = require("@/utils/supabase/server").createServiceRoleClient;

const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
    order: jest.fn()
};
const mockServiceRole = {
    storage: {
        from: jest.fn().mockReturnThis(),
        upload: jest.fn(),
        createSignedUrl: jest.fn(),
        list: jest.fn()
    }
};

describe("dynamic-document-upload-actions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockCreateClientFromRequest.mockResolvedValue(mockSupabase);
        mockCreateServiceRoleClient.mockReturnValue(mockServiceRole);
    });

    describe("uploadDocument", () => {
        it("returns error if not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });
            const result = await uploadDocument("docid", new File([""], "file.pdf", { type: "application/pdf" }));
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.userNotAuthenticatedError);
        });

        it("returns error if file is missing or empty", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            let result = await uploadDocument("docid", null as any);
            expect(result.success).toBe(false);
            result = await uploadDocument("docid", new File([""], "file.pdf", { type: "application/pdf" }));
            expect(result.success).toBe(false);
        });

        it("returns error if documentTypeId is invalid after sanitization", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            const result = await uploadDocument("", new File(["abc"], "file.pdf", { type: "application/pdf" }));
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.invalidDocumentTypeIdError);
        });

        it("returns error if document type not found", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({ data: null, error: true });
            mockSupabase.select.mockReturnThis();
            mockSupabase.eq.mockReturnThis();
            mockSupabase.single.mockResolvedValue({ data: null, error: true });
            const file = new File(["abc"], "file.pdf", { type: "application/pdf" });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.documentTypeNotFoundError);
        });

        it("returns error if file type not allowed", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({ data: { allowed_mime_types: ["application/pdf"] }, error: null });
            const file = new File(["abc"], "file.png", { type: "image/png" });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toContain("image/png");
        });

        it("returns error if file is too large", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({
                data: { allowed_mime_types: ["application/pdf"], max_file_size_mb: 1 },
                error: null
            });
            const file = new File(["abc".repeat(1024 * 1024)], "file.pdf", { type: "application/pdf" });
            Object.defineProperty(file, "size", { value: 2 * 1024 * 1024 });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toContain("1");
        });

        it("returns error if file has no extension", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({
                data: { allowed_mime_types: ["application/pdf"], max_file_size_mb: 2 },
                error: null
            });
            const file = new File(["abc"], "file", { type: "application/pdf" });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.fileMissingExtensionError);
        });

        it("returns error if upload fails", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({
                data: { allowed_mime_types: ["application/pdf"], max_file_size_mb: 2 },
                error: null
            });
            const file = new File(["abc"], "file.pdf", { type: "application/pdf" });
            mockServiceRole.storage.upload.mockResolvedValue({ error: true });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.fileUploadStorageError);
        });

        it("returns success and documentUrl if upload and signedUrl succeed", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({
                data: { allowed_mime_types: ["application/pdf"], max_file_size_mb: 2 },
                error: null
            });
            const file = new File(["abc"], "file.pdf", { type: "application/pdf" });
            mockServiceRole.storage.upload.mockResolvedValue({ error: null });
            mockServiceRole.storage.createSignedUrl.mockResolvedValue({
                data: { signedUrl: "https://signed.url" },
                error: null
            });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(true);
            expect(result.documentUrl).toBe("https://signed.url");
        });

        it("returns success even if signedUrl fails", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockResolvedValue({
                data: { allowed_mime_types: ["application/pdf"], max_file_size_mb: 2 },
                error: null
            });
            const file = new File(["abc"], "file.pdf", { type: "application/pdf" });
            mockServiceRole.storage.upload.mockResolvedValue({ error: null });
            mockServiceRole.storage.createSignedUrl.mockResolvedValue({ data: null, error: true });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(true);
            expect(result.documentUrl).toBeUndefined();
        });

        it("returns general error on exception", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.single.mockImplementation(() => {
                throw new Error("fail");
            });
            const file = new File(["abc"], "file.pdf", { type: "application/pdf" });
            const result = await uploadDocument("docid", file);
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.generalUploadError);
        });
    });

    describe("getRequiredDocuments", () => {
        it("returns error if not authenticated", async () => {
            mockAuth.mockResolvedValue({ userId: null });
            const result = await getRequiredDocuments();
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.userNotAuthenticatedError);
        });
        it("returns error if supabase fails", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.order.mockReturnValue({ data: null, error: true });
            mockSupabase.select.mockReturnThis();
            const result = await getRequiredDocuments();
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.errorLoadingDocumentTypesError);
        });
        it("returns documents if successful", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.order.mockReturnValue({ data: [{ id: 1 }], error: null });
            mockSupabase.select.mockReturnThis();
            const result = await getRequiredDocuments();
            expect(result.success).toBe(true);
            expect(result.documents).toEqual([{ id: 1 }]);
        });
        it("returns general error on exception", async () => {
            mockAuth.mockResolvedValue({ userId: "user1" });
            mockSupabase.order.mockImplementation(() => {
                throw new Error("fail");
            });
            const result = await getRequiredDocuments();
            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.generalLoadingDocumentsError);
        });
    });

    describe("checkUserDocumentStatus", () => {
        it("returns isUploaded false if list error", async () => {
            mockServiceRole.storage.list.mockResolvedValue({ data: null, error: { message: "fail" } });
            const result = await checkUserDocumentStatus("user1", "docid");
            expect(result.isUploaded).toBe(false);
        });
        it("returns isUploaded false if no file found", async () => {
            mockServiceRole.storage.list.mockResolvedValue({ data: [], error: null });
            const result = await checkUserDocumentStatus("user1", "docid");
            expect(result.isUploaded).toBe(false);
        });
        it("returns isUploaded true and url if file found and signedUrl succeeds", async () => {
            mockServiceRole.storage.list.mockResolvedValue({ data: [{ name: "docid.pdf" }], error: null });
            mockServiceRole.storage.createSignedUrl.mockResolvedValue({
                data: { signedUrl: "https://signed.url" },
                error: null
            });
            const result = await checkUserDocumentStatus("user1", "docid");
            expect(result.isUploaded).toBe(true);
            expect(result.uploadedFileName).toBe("docid.pdf");
            expect(result.uploadedFileUrl).toBe("https://signed.url");
        });
        it("returns isUploaded true even if signedUrl fails", async () => {
            mockServiceRole.storage.list.mockResolvedValue({ data: [{ name: "docid.pdf" }], error: null });
            mockServiceRole.storage.createSignedUrl.mockResolvedValue({ data: null, error: true });
            const result = await checkUserDocumentStatus("user1", "docid");
            expect(result.isUploaded).toBe(true);
            expect(result.uploadedFileName).toBe("docid.pdf");
            expect(result.uploadedFileUrl).toBeUndefined();
        });
        it("returns isUploaded false on exception", async () => {
            mockServiceRole.storage.list.mockImplementation(() => {
                throw new Error("fail");
            });
            const result = await checkUserDocumentStatus("user1", "docid");
            expect(result.isUploaded).toBe(false);
        });
    });
});
