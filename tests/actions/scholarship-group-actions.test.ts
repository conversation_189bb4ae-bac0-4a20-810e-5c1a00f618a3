import {
    createScholarshipGroup,
    deleteScholarshipGroup,
    getScholarshipGroup,
    updateScholarshipGroup,
    uploadScholarshipGroupImage,
    getScholarshipGroupsWithCounts
} from "@/app/actions/scholarship-group-actions";
import { TEXTS } from "@/lib/scholarship-group-constants";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server");

const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

const mockSupabase: any = {
    from: jest.fn(),
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    eq: jest.fn(),
    neq: jest.fn(),
    single: jest.fn(),
    limit: jest.fn(),
    storage: {
        from: jest.fn(),
        upload: jest.fn(),
        getPublicUrl: jest.fn()
    },
    rpc: jest.fn()
};

mockSupabase.from.mockReturnValue(mockSupabase);
mockSupabase.select.mockReturnValue(mockSupabase);
mockSupabase.insert.mockReturnValue(mockSupabase);
mockSupabase.update.mockReturnValue(mockSupabase);
mockSupabase.delete.mockReturnValue(mockSupabase);
mockSupabase.eq.mockReturnValue(mockSupabase);
mockSupabase.neq.mockReturnValue(mockSupabase);
mockSupabase.limit.mockReturnValue(mockSupabase);
mockSupabase.storage.from.mockReturnValue(mockSupabase.storage);

const mockCreateClientFromRequest = createClientFromRequest as jest.MockedFunction<typeof createClientFromRequest>;

beforeEach(() => {
    jest.clearAllMocks();
    mockConsoleError.mockClear();
    mockCreateClientFromRequest.mockResolvedValue(mockSupabase as any);
});

afterAll(() => {
    mockConsoleError.mockRestore();
});

describe("Scholarship Group Actions", () => {
    describe("getScholarshipGroup", () => {
        it("should fetch scholarship group successfully", async () => {
            const mockData = {
                id: "test-id",
                title: "Test Group",
                slug: "test-group",
                description: "Test description",
                icon: "graduation-cap",
                image_url: null,
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            };

            mockSupabase.single.mockResolvedValue({ data: mockData, error: null });

            const result = await getScholarshipGroup("test-id");

            expect(result).toEqual({ success: true, data: mockData });
        });

        it("should handle not found error", async () => {
            mockSupabase.single.mockResolvedValue({ data: null, error: null });

            const result = await getScholarshipGroup("non-existent-id");

            expect(result).toEqual({ success: false, error: TEXTS.editNotFoundMessage });
        });

        it("should handle database error", async () => {
            mockSupabase.single.mockResolvedValue({
                data: null,
                error: { message: "Database connection failed" }
            });

            const result = await getScholarshipGroup("test-id");

            expect(result).toEqual({ success: false, error: TEXTS.apiErrorGeneral });
        });
    });

    describe("createScholarshipGroup", () => {
        const validData = {
            title: "New Group",
            slug: "new-group",
            description: "New group description that is long enough",
            icon: "graduation-cap",
            image_url: null
        };

        it("should create scholarship group successfully", async () => {
            const mockCreatedData = {
                ...validData,
                id: "new-id",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            };

            mockSupabase.single.mockResolvedValueOnce({ data: null, error: null });

            mockSupabase.single.mockResolvedValueOnce({ data: mockCreatedData, error: null });

            const result = await createScholarshipGroup(validData);

            expect(mockSupabase.from).toHaveBeenCalledWith("groups_scholarship");
            expect(mockSupabase.eq).toHaveBeenCalledWith("slug", "new-group");
            expect(mockSupabase.insert).toHaveBeenCalledWith({
                title: "New Group",
                slug: "new-group",
                description: "New group description that is long enough",
                icon: "graduation-cap",
                image_url: null
            });
            expect(result).toEqual({ success: true, data: mockCreatedData });
        });

        it("should validate title length", async () => {
            const invalidData = { ...validData, title: "X" };

            const result = await createScholarshipGroup(invalidData);

            expect(result).toEqual({
                success: false,
                error: "Title is required and must be at least 2 characters"
            });
        });

        it("should validate slug pattern", async () => {
            const invalidData = { ...validData, slug: "invalid slug!" };

            const result = await createScholarshipGroup(invalidData);

            expect(result).toEqual({
                success: false,
                error: "Slug is required and must contain only English letters and hyphens"
            });
        });

        it("should validate description length", async () => {
            const invalidData = { ...validData, description: "Short" };

            const result = await createScholarshipGroup(invalidData);

            expect(result).toEqual({
                success: false,
                error: "Description is required and must be at least 10 characters"
            });
        });

        it("should handle slug already exists", async () => {
            mockSupabase.single.mockResolvedValue({ data: { id: "existing-id" }, error: null });

            const result = await createScholarshipGroup(validData);

            expect(result).toEqual({ success: false, error: TEXTS.slugExistsError });
        });

        it("should sanitize input data", async () => {
            const unsafeData = {
                title: "Group <script>alert('xss')</script>",
                slug: "TEST-GROUP",
                description: "Description with <img src=x onerror=alert('xss')> tags",
                icon: "graduation-cap",
                image_url: null
            };

            mockSupabase.single.mockResolvedValueOnce({ data: null, error: null });

            mockSupabase.single.mockResolvedValueOnce({
                data: { ...unsafeData, id: "new-id" },
                error: null
            });

            await createScholarshipGroup(unsafeData);

            expect(mockSupabase.insert).toHaveBeenCalledWith({
                title: "Group scriptalert('xss')/script",
                slug: "test-group",
                description: "Description with img src=x onerror=alert('xss') tags",
                icon: "graduation-cap",
                image_url: null
            });
        });

        it("should handle database error during creation", async () => {
            mockSupabase.single.mockResolvedValueOnce({ data: null, error: null });

            mockSupabase.single.mockResolvedValueOnce({
                data: null,
                error: { message: "Database constraint violation" }
            });

            const result = await createScholarshipGroup(validData);

            expect(result).toEqual({ success: false, error: TEXTS.createErrorMessage });
        });
    });

    describe("updateScholarshipGroup", () => {
        const validData = {
            title: "Updated Group",
            slug: "updated-group",
            description: "Updated description that is long enough",
            icon: "award"
        };

        it("should update scholarship group successfully", async () => {
            const partialData = { title: "New Title Only" };

            mockSupabase.eq.mockResolvedValue({ error: null });

            const result = await updateScholarshipGroup("test-id", partialData);

            expect(result).toEqual({ success: true });
        });

        it("should handle partial updates", async () => {
            const partialData = { title: "New Title Only" };

            mockSupabase.eq.mockResolvedValue({ error: null });

            const result = await updateScholarshipGroup("test-id", partialData);

            expect(mockSupabase.update).toHaveBeenCalledWith({
                title: "New Title Only"
            });
            expect(result).toEqual({ success: true });
        });

        it("should validate updated slug uniqueness", async () => {
            mockSupabase.single.mockResolvedValue({ data: { id: "other-id" }, error: null });

            const result = await updateScholarshipGroup("test-id", validData);

            expect(result.success).toBe(false);
            expect(result.error).toBeTruthy();
        });

        it("should handle database error during update", async () => {
            mockSupabase.single.mockResolvedValueOnce({ data: null, error: null });

            mockSupabase.eq.mockResolvedValue({
                error: { message: "Database constraint violation" }
            });

            const result = await updateScholarshipGroup("test-id", validData);

            expect(result).toEqual({ success: false, error: TEXTS.editErrorMessage });
        });
    });

    describe("deleteScholarshipGroup", () => {
        it("should delete scholarship group successfully", async () => {
            const associationCheckMock = {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue({ data: [], error: null })
            };

            const deleteMock = {
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null })
            };

            mockSupabase.from
                .mockImplementationOnce(() => associationCheckMock)
                .mockImplementationOnce(() => deleteMock);

            const result = await deleteScholarshipGroup("test-id");

            expect(mockSupabase.from).toHaveBeenCalledWith("link_scholarship_to_scholarship_groups");
            expect(mockSupabase.from).toHaveBeenCalledWith("groups_scholarship");
            expect(result).toEqual({ success: true });
        });

        it("should prevent deletion when associations exist", async () => {
            mockSupabase.limit.mockResolvedValue({
                data: [{ scholarship_id: "scholarship-1" }],
                error: null
            });

            const result = await deleteScholarshipGroup("test-id");

            expect(result.success).toBe(false);
            expect(result.error).toBeTruthy();
            expect(mockSupabase.delete).not.toHaveBeenCalled();
        });

        it("should handle database error during association check", async () => {
            mockSupabase.limit.mockResolvedValue({
                data: null,
                error: { message: "Database connection failed" }
            });

            const result = await deleteScholarshipGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: TEXTS.deleteErrorMessage
            });
        });

        it("should handle database error during deletion", async () => {
            mockSupabase.limit.mockResolvedValue({ data: [], error: null });

            mockSupabase.eq.mockResolvedValue({
                error: { message: "Database constraint violation" }
            });

            const result = await deleteScholarshipGroup("test-id");

            expect(result).toEqual({
                success: false,
                error: TEXTS.deleteErrorMessage
            });
        });
    });

    describe("uploadScholarshipGroupImage", () => {
        const mockFile = new File(["test"], "test.webp", { type: "image/webp" });

        it("should upload image successfully", async () => {
            mockSupabase.storage.upload.mockResolvedValue({ error: null });
            mockSupabase.storage.getPublicUrl.mockReturnValue({
                data: { publicUrl: "http://example.com/test-id.webp" }
            });

            mockSupabase.eq.mockResolvedValue({ error: null });

            const result = await uploadScholarshipGroupImage("test-id", mockFile);

            expect(mockSupabase.storage.from).toHaveBeenCalledWith("groups_scholarship");
            expect(mockSupabase.storage.upload).toHaveBeenCalledWith("test-id.webp", mockFile, {
                cacheControl: "3600",
                upsert: true
            });
            expect(mockSupabase.storage.getPublicUrl).toHaveBeenCalledWith("test-id.webp");
            expect(mockSupabase.update).toHaveBeenCalledWith({
                image_url: "http://example.com/test-id.webp"
            });
            expect(result).toEqual({
                success: true,
                imageUrl: "http://example.com/test-id.webp"
            });
        });

        it("should validate file type", async () => {
            const invalidFile = new File(["test"], "test.jpg", { type: "image/jpeg" });

            const result = await uploadScholarshipGroupImage("test-id", invalidFile);

            expect(result).toEqual({
                success: false,
                error: TEXTS.webpOnlyError
            });
        });

        it("should validate file size", async () => {
            const largeFile = new File([new ArrayBuffer(6 * 1024 * 1024)], "test.webp", {
                type: "image/webp"
            });

            const result = await uploadScholarshipGroupImage("test-id", largeFile);

            expect(result).toEqual({
                success: false,
                error: TEXTS.fileSizeExceededError
            });
        });

        it("should handle storage upload error", async () => {
            mockSupabase.storage.upload.mockResolvedValue({
                error: { message: "Storage error" }
            });

            const result = await uploadScholarshipGroupImage("test-id", mockFile);

            expect(result).toEqual({
                success: false,
                error: TEXTS.apiErrorUpload
            });
        });
    });

    describe("getScholarshipGroupsWithCounts", () => {
        it("should fetch scholarship groups with counts successfully", async () => {
            const mockGroupsData = [
                {
                    id: "group-1",
                    title: "Group 1",
                    slug: "group-1",
                    description: "Desc 1",
                    icon: "icon1",
                    image_url: null,
                    created_at: "",
                    updated_at: ""
                }
            ];
            const mockCountsData = [{ scholarship_group_id: "group-1" }, { scholarship_group_id: "group-1" }];

            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({ data: mockGroupsData, error: null })
            });

            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockResolvedValue({ data: mockCountsData, error: null })
            });

            const result = await getScholarshipGroupsWithCounts();

            expect(mockSupabase.from).toHaveBeenCalledWith("groups_scholarship");
            expect(mockSupabase.from).toHaveBeenCalledWith("link_scholarship_to_scholarship_groups");
            expect(result.success).toBe(true);
            expect(result.data).toEqual([{ ...mockGroupsData[0], scholarships_count: 2 }]);
        });

        it("should handle groups fetch error", async () => {
            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({ data: null, error: { message: "Groups fetch error" } })
            });

            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockResolvedValue({ data: [], error: null })
            });

            const result = await getScholarshipGroupsWithCounts();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.apiErrorGeneral);
        });

        it("should handle counts fetch error", async () => {
            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockReturnThis(),
                order: jest.fn().mockResolvedValue({ data: [], error: null })
            });

            mockSupabase.from.mockReturnValueOnce({
                select: jest.fn().mockResolvedValue({ data: null, error: { message: "Counts fetch error" } })
            });

            const result = await getScholarshipGroupsWithCounts();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.apiErrorGeneral);
        });
    });
});
