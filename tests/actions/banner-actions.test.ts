import { getActiveBanners, deleteBanner, getBanner, createBanner, updateBanner } from "@/app/actions/banner-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { createThenableBuilder } from "@/tests/mocks/supabase-mock";
import { type BannerFormValues, bannerColors, bannerIdToColorMap } from "@/lib/banner-constants";
import { type Tables } from "@/types/database.types";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const fromMock = jest.fn();
const authMock = {
    getUser: jest.fn()
};

(createClientFromRequest as jest.Mock).mockReturnValue({
    from: fromMock,
    auth: authMock
});

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("getActiveBanners action", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch banners for a Guest user", async () => {
        authMock.getUser.mockResolvedValue({ data: { user: null }, error: null });

        const bannerDataBuilder = createThenableBuilder([
            {
                data: [{ id: "banner-guest", audience: "Guest" }],
                error: null
            }
        ]);
        fromMock.mockReturnValue(bannerDataBuilder);

        const result = await getActiveBanners();

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(bannerDataBuilder.eq).toHaveBeenCalledWith("enabled", true);
        expect(bannerDataBuilder.eq).toHaveBeenCalledWith("audience", "Guest");
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(1);
        expect(result.data?.[0].audience).toBe("Guest");
    });

    it("should fetch banners for a signed-in User (no subscription)", async () => {
        authMock.getUser.mockResolvedValue({ data: { user: { id: "user-123" } }, error: null });

        const subscriptionBuilder = createThenableBuilder([{ data: null, error: null }]);
        const bannerDataBuilder = createThenableBuilder([
            {
                data: [{ id: "banner-user", audience: "User" }],
                error: null
            }
        ]);

        fromMock.mockImplementationOnce(() => subscriptionBuilder).mockImplementationOnce(() => bannerDataBuilder);

        const result = await getActiveBanners();

        expect(fromMock).toHaveBeenCalledWith("user_subscriptions");
        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(bannerDataBuilder.eq).toHaveBeenCalledWith("audience", "User");
        expect(result.success).toBe(true);
        expect(result.data?.[0].audience).toBe("User");
    });

    it("should fetch banners for a Subscriber", async () => {
        authMock.getUser.mockResolvedValue({ data: { user: { id: "user-123" } }, error: null });

        const subscriptionBuilder = createThenableBuilder([
            {
                data: { user_id: "user-123", plan_id: "milgapo-pro", is_active: true },
                error: null
            }
        ]);
        const bannerDataBuilder = createThenableBuilder([
            {
                data: [{ id: "banner-subscriber", audience: "Subscriber" }],
                error: null
            }
        ]);

        fromMock.mockImplementationOnce(() => subscriptionBuilder).mockImplementationOnce(() => bannerDataBuilder);

        const result = await getActiveBanners();

        expect(fromMock).toHaveBeenCalledWith("user_subscriptions");
        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(bannerDataBuilder.eq).toHaveBeenCalledWith("audience", "Subscriber");
        expect(result.success).toBe(true);
        expect(result.data?.[0].audience).toBe("Subscriber");
    });

    it("should return an error if fetching fails", async () => {
        authMock.getUser.mockResolvedValue({ data: { user: null }, error: null });

        const bannerDataBuilder = createThenableBuilder([
            {
                data: null,
                error: new Error("DB Error")
            }
        ]);
        fromMock.mockReturnValue(bannerDataBuilder);

        const result = await getActiveBanners();

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
    });
});

describe("getBanner", () => {
    const bannerData: Tables<"banners"> = {
        id: "1",
        title: "Test Banner",
        text: "Test Text",
        audience: "Guest",
        background_color: "#BAE1FF",
        text_color: "#000000",
        icon: "Info",
        cta_text: null,
        cta_link: null,
        days_to_live: 0,
        seconds_before_show: 0,
        enable_dismiss: true,
        enabled: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };

    it("should fetch a banner successfully", async () => {
        const selectBuilder = createThenableBuilder([
            {
                data: bannerData,
                error: null
            }
        ]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getBanner("1");

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(selectBuilder.select).toHaveBeenCalledWith("*");
        expect(selectBuilder.eq).toHaveBeenCalledWith("id", "1");
        expect(selectBuilder.single).toHaveBeenCalled();
        expect(result.success).toBe(true);
        expect(result.data).toEqual(bannerData);
    });

    it("should return a not found error if banner does not exist", async () => {
        const selectBuilder = createThenableBuilder([{ data: null, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getBanner("1");

        expect(result.success).toBe(false);
        expect(result.error).toBe("הבאנר לא נמצא");
    });

    it("should return an error if fetching fails", async () => {
        const selectBuilder = createThenableBuilder([
            {
                data: null,
                error: new Error("DB Error")
            }
        ]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getBanner("1");

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});

describe("createBanner", () => {
    const bannerFormData: BannerFormValues = {
        title: "New Banner",
        text: "New Text",
        audience: { id: "Guest", label: "Guest" },
        color_scheme: { id: "info", label: "Info" },
        icon: { id: "Info", label: "Info" },
        cta_text: null,
        cta_link: null,
        days_to_live: 1,
        seconds_before_show: 1,
        enable_dismiss: true,
        enabled: true
    };

    it("should create a banner successfully", async () => {
        const insertBuilder = createThenableBuilder([{ error: null }]);
        fromMock.mockReturnValue(insertBuilder);

        const result = await createBanner(bannerFormData);

        const expectedData = {
            title: bannerFormData.title,
            text: bannerFormData.text,
            audience: bannerFormData.audience?.id,
            background_color: bannerIdToColorMap.info.background,
            text_color: bannerIdToColorMap.info.text,
            icon: bannerFormData.icon?.id,
            cta_text: bannerFormData.cta_text,
            cta_link: bannerFormData.cta_link,
            days_to_live: bannerFormData.days_to_live,
            seconds_before_show: bannerFormData.seconds_before_show,
            enable_dismiss: bannerFormData.enable_dismiss,
            enabled: bannerFormData.enabled
        };

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(insertBuilder.insert).toHaveBeenCalledWith(expectedData);
        expect(result.success).toBe(true);
    });

    it("should return an error if creation fails", async () => {
        const insertBuilder = createThenableBuilder([{ error: new Error("DB Error") }]);
        fromMock.mockReturnValue(insertBuilder);

        const result = await createBanner(bannerFormData);

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});

describe("updateBanner", () => {
    const bannerFormData: BannerFormValues = {
        title: "Updated Banner",
        text: "Updated Text",
        audience: { id: "User", label: "User" },
        color_scheme: { id: "success", label: "Success" },
        icon: { id: "AlertCircle", label: "Alert" },
        cta_text: "Click Me",
        cta_link: "https://example.com",
        days_to_live: 2,
        seconds_before_show: 2,
        enable_dismiss: false,
        enabled: false
    };

    it("should update a banner successfully", async () => {
        const updateBuilder = createThenableBuilder([{ error: null }]);
        fromMock.mockReturnValue(updateBuilder);

        const result = await updateBanner("1", bannerFormData);

        const expectedData = {
            title: bannerFormData.title,
            text: bannerFormData.text,
            audience: bannerFormData.audience?.id,
            background_color: bannerIdToColorMap.success.background,
            text_color: bannerIdToColorMap.success.text,
            icon: bannerFormData.icon?.id,
            cta_text: bannerFormData.cta_text,
            cta_link: bannerFormData.cta_link,
            days_to_live: bannerFormData.days_to_live,
            seconds_before_show: bannerFormData.seconds_before_show,
            enable_dismiss: bannerFormData.enable_dismiss,
            enabled: bannerFormData.enabled
        };

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(updateBuilder.update).toHaveBeenCalledWith(expectedData);
        expect(updateBuilder.eq).toHaveBeenCalledWith("id", "1");
        expect(result.success).toBe(true);
    });

    it("should return an error if update fails", async () => {
        const updateBuilder = createThenableBuilder([{ error: new Error("DB Error") }]);
        fromMock.mockReturnValue(updateBuilder);

        const result = await updateBanner("1", bannerFormData);

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return an error for invalid color scheme", async () => {
        const invalidBannerData = { ...bannerFormData, color_scheme: { id: "invalid", label: "Invalid" } };
        const result = await updateBanner("1", invalidBannerData);
        expect(result.success).toBe(false);
        expect(result.error).toBe("סכמת צבעים לא חוקית");
    });
});

describe("deleteBanner", () => {
    it("should delete a banner successfully", async () => {
        const deleteBuilder = createThenableBuilder([{ error: null }]);
        fromMock.mockReturnValue(deleteBuilder);

        const result = await deleteBanner("123");

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(deleteBuilder.delete).toHaveBeenCalled();
        expect(deleteBuilder.eq).toHaveBeenCalledWith("id", "123");
        expect(result.success).toBe(true);
    });

    it("should return an error when deletion fails", async () => {
        const deleteBuilder = createThenableBuilder([
            {
                error: new Error("DB Error")
            }
        ]);
        fromMock.mockReturnValue(deleteBuilder);

        const result = await deleteBanner("123");

        expect(fromMock).toHaveBeenCalledWith("banners");
        expect(deleteBuilder.delete).toHaveBeenCalled();
        expect(deleteBuilder.eq).toHaveBeenCalledWith("id", "123");
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
    });
});
