import {
    getScholarshipConditionGroups,
    deleteScholarshipConditionGroup
} from "@/app/actions/scholarship-condition-group-actions";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

describe("Scholarship Condition Group Actions", () => {
    const mockSupabaseClient = {
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        mockConsoleError.mockClear();
    });

    afterAll(() => {
        mockConsoleError.mockRestore();
    });

    describe("getScholarshipConditionGroups", () => {
        it("returns scholarship condition groups with correct counts when successful", async () => {
            const mockGroups = [
                { id: "g1", name: "Group 1" },
                { id: "g2", name: "Group 2" }
            ];
            const mockConditions = [{ group_id: "g1" }, { group_id: "g1" }, { group_id: "g2" }];

            mockSupabaseClient.from.mockImplementation((tableName: string) => {
                if (tableName === "groups_condition") {
                    return {
                        select: jest.fn().mockReturnThis(),
                        order: jest.fn().mockResolvedValue({ data: mockGroups, error: null })
                    };
                } else if (tableName === "conditions") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockConditions, error: null })
                    };
                }
                return {};
            });

            const result = await getScholarshipConditionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([
                { id: "g1", name: "Group 1", conditions_count: 2 },
                { id: "g2", name: "Group 2", conditions_count: 1 }
            ]);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("conditions");
        });

        it("returns empty array when no groups are found", async () => {
            mockSupabaseClient.from.mockImplementation((tableName: string) => {
                if (tableName === "groups_condition") {
                    return {
                        select: jest.fn().mockReturnThis(),
                        order: jest.fn().mockResolvedValue({ data: [], error: null })
                    };
                } else if (tableName === "conditions") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: [], error: null })
                    };
                }
                return {};
            });

            const result = await getScholarshipConditionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });

        it("returns empty array when no conditions are found", async () => {
            const mockGroups = [{ id: "g1", name: "Group 1" }];
            mockSupabaseClient.from.mockImplementation((tableName: string) => {
                if (tableName === "groups_condition") {
                    return {
                        select: jest.fn().mockReturnThis(),
                        order: jest.fn().mockResolvedValue({ data: mockGroups, error: null })
                    };
                } else if (tableName === "conditions") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: [], error: null })
                    };
                }
                return {};
            });

            const result = await getScholarshipConditionGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([{ id: "g1", name: "Group 1", conditions_count: 0 }]);
        });

        it("handles groups fetch error", async () => {
            const mockError = { message: "Groups fetch error" };
            mockSupabaseClient.from.mockImplementation((tableName: string) => {
                if (tableName === "groups_condition") {
                    return {
                        select: jest.fn().mockReturnThis(),
                        order: jest.fn().mockResolvedValue({ data: null, error: mockError })
                    };
                } else if (tableName === "conditions") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: [], error: null })
                    };
                }
                return {};
            });

            const result = await getScholarshipConditionGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch scholarship condition groups");
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("handles conditions fetch error", async () => {
            const mockError = { message: "Conditions fetch error" };
            const mockGroups = [{ id: "g1", name: "Group 1" }];
            mockSupabaseClient.from.mockImplementation((tableName: string) => {
                if (tableName === "groups_condition") {
                    return {
                        select: jest.fn().mockReturnThis(),
                        order: jest.fn().mockResolvedValue({ data: mockGroups, error: null })
                    };
                } else if (tableName === "conditions") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: null, error: mockError })
                    };
                }
                return {};
            });

            const result = await getScholarshipConditionGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to fetch scholarship condition groups");
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("handles unknown error types", async () => {
            mockSupabaseClient.from.mockImplementation(() => {
                throw new Error("Unknown error");
            });

            const result = await getScholarshipConditionGroups();
            expect(result.success).toBe(false);
            expect(result.error).toBe("Unknown error");
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("deleteScholarshipConditionGroup", () => {
        it("deletes a scholarship condition group successfully", async () => {
            const mockDelete = jest.fn().mockReturnThis();
            const mockEq = jest.fn().mockResolvedValue({ error: null });

            mockSupabaseClient.from.mockReturnValue({
                delete: mockDelete,
                eq: mockEq
            });

            const result = await deleteScholarshipConditionGroup("some-id");

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_condition");
            expect(mockDelete).toHaveBeenCalled();
            expect(mockEq).toHaveBeenCalledWith("id", "some-id");
        });

        it("handles deletion error", async () => {
            const mockError = { message: "Deletion error" };
            const mockDelete = jest.fn().mockReturnThis();
            const mockEq = jest.fn().mockResolvedValue({ error: mockError });

            mockSupabaseClient.from.mockReturnValue({
                delete: mockDelete,
                eq: mockEq
            });

            const result = await deleteScholarshipConditionGroup("some-id");

            expect(result.success).toBe(false);
            expect(result.error).toBe("Failed to delete scholarship condition group");
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("handles unknown error types", async () => {
            mockSupabaseClient.from.mockImplementation(() => {
                throw new Error("Unknown deletion error");
            });

            const result = await deleteScholarshipConditionGroup("some-id");
            expect(result.success).toBe(false);
            expect(result.error).toBe("Unknown deletion error");
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });
});
