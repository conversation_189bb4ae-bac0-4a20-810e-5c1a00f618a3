import "@testing-library/jest-dom";

import * as collaborationActions from "@/app/actions/collaboration-actions";
import { TEXTS } from "@/lib/collaboration-constants";
import { createClientFromRequest } from "@/utils/supabase/server";

// Mock dependencies
jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

// Mock console.error to suppress error logs in tests
const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

describe("Collaboration Actions", () => {
    const mockSupabaseClient = {
        rpc: jest.fn(),
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        mockConsoleError.mockClear();
    });

    afterAll(() => {
        mockConsoleError.mockRestore();
    });

    describe("Data Transformation Tests", () => {
        describe("Question ID Object to String Transformation", () => {
            it("transforms question_ids from objects to strings in createCollaboration", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    question_ids: [
                        { id: "question-1", label: "Question 1" },
                        { id: "question-2", label: "Question 2" },
                        "question-3" // Already a string
                    ]
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_question_ids: ["question-1", "question-2", "question-3"]
                    })
                );
            });

            it("transforms question_ids from objects to strings in updateCollaboration", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Updated Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    question_ids: [
                        { id: "question-1", label: "Updated Question 1" },
                        { id: "question-2", label: "Updated Question 2" }
                    ]
                };

                await collaborationActions.updateCollaboration("collab-1", formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "update_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_question_ids: ["question-1", "question-2"]
                    })
                );
            });

            it("handles empty question_ids array", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    question_ids: []
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_question_ids: []
                    })
                );
            });
        });

        describe("Dependencies Question ID Transformation", () => {
            it("transforms dependencies question_id from objects to strings", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    dependencies: [
                        {
                            question_id: { id: "question-1", label: "Age Question" },
                            condition_type: "in",
                            condition_value: ["18-25", "26-35"]
                        },
                        {
                            question_id: { id: "question-2", label: "Location Question" },
                            condition_type: "in",
                            condition_value: ["Tel Aviv", "Jerusalem"],
                            id: "existing-condition-id"
                        }
                    ]
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_dependencies: [
                            {
                                question_id: "question-1",
                                condition_type: "in",
                                condition_value: ["18-25", "26-35"]
                            },
                            {
                                question_id: "question-2",
                                condition_type: "in",
                                condition_value: ["Tel Aviv", "Jerusalem"],
                                id: "existing-condition-id"
                            }
                        ]
                    })
                );
            });

            it("handles dependencies with string question_id (already transformed)", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    dependencies: [
                        {
                            question_id: "question-1", // Already a string
                            condition_type: "in",
                            condition_value: ["value1", "value2"]
                        }
                    ]
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_dependencies: [
                            {
                                question_id: "question-1",
                                condition_type: "in",
                                condition_value: ["value1", "value2"]
                            }
                        ]
                    })
                );
            });
        });

        describe("Auth Value Transformation", () => {
            it("transforms auth_value for bearer_token type", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "bearer_token",
                    auth_value: "my-secret-token"
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_auth_value: "my-secret-token"
                    })
                );
            });

            it("sets auth_value to null for none type", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const formData = {
                    name: "Test Collaboration",
                    api_endpoint: "https://test.com",
                    auth_type: "none",
                    auth_value: "should-be-ignored"
                };

                await collaborationActions.createCollaboration(formData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                    "create_collaboration_with_dependencies",
                    expect.objectContaining({
                        p_auth_value: ""
                    })
                );
            });
        });

        describe("Complete Data Transformation Integration", () => {
            it("transforms all data types correctly in a complex scenario", async () => {
                mockSupabaseClient.rpc.mockResolvedValue({
                    data: { success: true, collaboration_id: "test-id" },
                    error: null
                });

                const complexFormData = {
                    name: "Complex Collaboration",
                    description: "Test description",
                    api_endpoint: "https://complex.test.com/webhook",
                    auth_type: "bearer_token",
                    auth_value: "complex-token",
                    dependencies: [
                        {
                            question_id: { id: "age-question", label: "What is your age?" },
                            condition_type: "in",
                            condition_value: [
                                { id: "18-25", label: "18-25 years" },
                                { id: "26-35", label: "26-35 years" }
                            ]
                        },
                        {
                            question_id: { id: "location-question", label: "Where do you live?" },
                            condition_type: "in",
                            condition_value: ["tel-aviv", "jerusalem"],
                            id: "existing-condition"
                        }
                    ],
                    question_ids: [
                        { id: "personal-info", label: "Personal Information" },
                        { id: "contact-info", label: "Contact Information" },
                        "already-string-id"
                    ]
                };

                await collaborationActions.createCollaboration(complexFormData);

                expect(mockSupabaseClient.rpc).toHaveBeenCalledWith("create_collaboration_with_dependencies", {
                    p_name: "Complex Collaboration",
                    p_description: "Test description",
                    p_api_endpoint: "https://complex.test.com/webhook",
                    p_auth_type: "bearer_token",
                    p_auth_value: "complex-token",
                    p_dependencies: [
                        {
                            question_id: "age-question",
                            condition_type: "in",
                            condition_value: [
                                { id: "18-25", label: "18-25 years" },
                                { id: "26-35", label: "26-35 years" }
                            ]
                        },
                        {
                            question_id: "location-question",
                            condition_type: "in",
                            condition_value: ["tel-aviv", "jerusalem"],
                            id: "existing-condition"
                        }
                    ],
                    p_question_ids: ["personal-info", "contact-info", "already-string-id"]
                });
            });
        });
    });

    describe("RPC Function Integration", () => {
        it("calls create_collaboration_with_dependencies RPC with correct parameters", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: { success: true, collaboration_id: "new-id", condition_ids: ["cond-1"] },
                error: null
            });

            const result = await collaborationActions.createCollaboration({
                name: "RPC Test",
                api_endpoint: "https://rpc.test.com",
                auth_type: "none"
            });

            expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                "create_collaboration_with_dependencies",
                expect.objectContaining({
                    p_name: "RPC Test",
                    p_api_endpoint: "https://rpc.test.com",
                    p_auth_type: "none"
                })
            );

            expect(result).toEqual({
                success: true,
                id: "new-id"
            });
        });

        it("calls update_collaboration_with_dependencies RPC with correct parameters", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: { success: true, collaboration_id: "updated-id" },
                error: null
            });

            const result = await collaborationActions.updateCollaboration("collab-1", {
                name: "Updated RPC Test",
                api_endpoint: "https://updated.test.com",
                auth_type: "bearer_token",
                auth_value: "token"
            });

            expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                "update_collaboration_with_dependencies",
                expect.objectContaining({
                    p_collaboration_id: "collab-1",
                    p_name: "Updated RPC Test",
                    p_api_endpoint: "https://updated.test.com",
                    p_auth_type: "bearer_token",
                    p_auth_value: "token"
                })
            );

            expect(result).toEqual({
                success: true
            });
        });

        it("handles RPC function errors correctly", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: {
                    success: false,
                    error: "invalid input syntax for type uuid",
                    error_code: "22P02"
                },
                error: null
            });

            const result = await collaborationActions.createCollaboration({
                name: "Error Test",
                api_endpoint: "https://error.test.com",
                auth_type: "none"
            });

            expect(result).toEqual({
                success: false,
                error: "invalid input syntax for type uuid"
            });
        });

        it("handles Supabase client errors", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: null,
                error: { message: "Database connection failed" }
            });

            const result = await collaborationActions.createCollaboration({
                name: "Connection Error Test",
                api_endpoint: "https://error.test.com",
                auth_type: "none"
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.COLLABORATION_CREATE_ERROR);
        });
    });

    describe("Validation Edge Cases", () => {
        it("handles invalid question_id objects gracefully", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: { success: true, collaboration_id: "test-id" },
                error: null
            });

            const formData = {
                name: "Edge Case Test",
                api_endpoint: "https://test.com",
                auth_type: "none",
                question_ids: [
                    { id: "", label: "Empty ID" }, // Edge case: empty ID
                    { label: "Missing ID" }, // Edge case: missing ID
                    null, // Edge case: null value
                    undefined, // Edge case: undefined value
                    { id: "valid-id", label: "Valid" }
                ]
            };

            await collaborationActions.createCollaboration(formData);

            // Should handle edge cases gracefully and extract valid IDs
            expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                "create_collaboration_with_dependencies",
                expect.objectContaining({
                    p_question_ids: expect.any(Array)
                })
            );
        });

        it("handles mixed dependency formats", async () => {
            mockSupabaseClient.rpc.mockResolvedValue({
                data: { success: true, collaboration_id: "test-id" },
                error: null
            });

            const formData = {
                name: "Mixed Format Test",
                api_endpoint: "https://test.com",
                auth_type: "none",
                dependencies: [
                    {
                        question_id: { id: "q1", label: "Question 1" },
                        condition_type: "in",
                        condition_value: ["val1"]
                    },
                    {
                        question_id: "q2", // Already string format
                        condition_type: "in",
                        condition_value: ["val2"]
                    }
                ]
            };

            await collaborationActions.createCollaboration(formData);

            expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
                "create_collaboration_with_dependencies",
                expect.objectContaining({
                    p_dependencies: [
                        expect.objectContaining({ question_id: "q1" }),
                        expect.objectContaining({ question_id: "q2" })
                    ]
                })
            );
        });
    });
});
