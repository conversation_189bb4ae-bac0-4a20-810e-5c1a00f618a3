import {
    getAllQuestionsForCollaboration,
    getCollaborationConditions,
    getCollaborationQuestions
} from "@/app/actions/collaboration-form-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import type { Database } from "@/types/database.types";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

interface SupabaseResponse {
    data?: any;
    count?: number | null;
    error?: Error | null;
}

interface MockBuilder {
    select: jest.Mock;
    insert: jest.Mock;
    update: jest.Mock;
    upsert: jest.Mock;
    order: jest.Mock;
    eq: jest.Mock;
    gte: jest.Mock;
    lte: jest.Mock;
    ilike: jest.Mock;
    in: jest.Mock;
    not: jest.Mock;
    range: jest.Mock;
    delete: jest.Mock;
    maybeSingle: jest.Mock;
    single: jest.<PERSON>ck;
    limit: jest.<PERSON>ck;
    then: (resolve: (v: SupabaseResponse) => void) => void;
}

function createThenableBuilder(queue: SupabaseResponse[]) {
    const builder: MockBuilder = {
        select: jest.fn(() => builder),
        insert: jest.fn(() => builder),
        update: jest.fn(() => builder),
        upsert: jest.fn(() => builder),
        order: jest.fn(() => builder),
        eq: jest.fn(() => builder),
        gte: jest.fn(() => builder),
        lte: jest.fn(() => builder),
        ilike: jest.fn(() => builder),
        in: jest.fn(() => builder),
        not: jest.fn(() => builder),
        range: jest.fn(() => builder),
        delete: jest.fn(() => builder),
        maybeSingle: jest.fn(() => builder),
        single: jest.fn(() => builder),
        limit: jest.fn(() => builder),
        then: (resolve: (v: SupabaseResponse) => void) => {
            const next = queue.shift() ?? { data: [], count: 0, error: null };
            resolve(next);
        }
    };
    return builder;
}

const fromMock = jest.fn();
(createClientFromRequest as jest.Mock).mockResolvedValue({
    from: fromMock
});

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

describe("getAllQuestionsForCollaboration", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch and format questions successfully", async () => {
        const questionData = [
            {
                id: "q1",
                type: "single_select" as Database["public"]["Enums"]["question_type"],
                metadata: {
                    label: "Test Question",
                    placeholder: "Select option",
                    options: ["Option 1", "Option 2"]
                },
                groups_question: { id: "g1", name: "Group 1" }
            },
            {
                id: "q2",
                type: "multi_select" as Database["public"]["Enums"]["question_type"],
                metadata: {
                    label: "Another Question"
                },
                groups_question: [{ id: "g2", name: "Group 2" }]
            }
        ];

        const selectBuilder = createThenableBuilder([{ data: questionData, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getAllQuestionsForCollaboration();

        expect(fromMock).toHaveBeenCalledWith("questions");
        expect(selectBuilder.select).toHaveBeenCalledWith(`
                id,
                type,
                metadata,
                groups_question ( id, name )
            `);
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(2);
        expect(result.data?.[0]).toEqual({
            id: "q1",
            type: "single_select",
            metadata: {
                label: "Test Question",
                placeholder: "Select option",
                options: ["Option 1", "Option 2"]
            },
            groups_question: { id: "g1", name: "Group 1" }
        });
        expect(result.data?.[1].groups_question).toEqual({ id: "g2", name: "Group 2" });
    });

    it("should handle questions with no group", async () => {
        const questionData = [
            {
                id: "q1",
                type: "short_text" as Database["public"]["Enums"]["question_type"],
                metadata: { label: "No Group Question" },
                groups_question: null
            }
        ];

        const selectBuilder = createThenableBuilder([{ data: questionData, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getAllQuestionsForCollaboration();

        expect(result.success).toBe(true);
        expect(result.data?.[0].groups_question).toBeUndefined();
    });

    it("should handle questions with empty group array", async () => {
        const questionData = [
            {
                id: "q1",
                type: "short_text" as Database["public"]["Enums"]["question_type"],
                metadata: { label: "Empty Group Question" },
                groups_question: []
            }
        ];

        const selectBuilder = createThenableBuilder([{ data: questionData, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getAllQuestionsForCollaboration();

        expect(result.success).toBe(true);
        expect(result.data?.[0].groups_question).toBeUndefined();
    });

    it("should handle null metadata", async () => {
        const questionData = [
            {
                id: "q1",
                type: "short_text" as Database["public"]["Enums"]["question_type"],
                metadata: null,
                groups_question: null
            }
        ];

        const selectBuilder = createThenableBuilder([{ data: questionData, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getAllQuestionsForCollaboration();

        expect(result.success).toBe(true);
        expect(result.data?.[0].metadata).toEqual({
            label: undefined,
            placeholder: undefined,
            options: undefined
        });
    });

    it("should return error when database query fails", async () => {
        const selectBuilder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getAllQuestionsForCollaboration();

        expect(result.success).toBe(false);
        expect(result.error).toBe("Database error");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});

describe("getCollaborationConditions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch and format conditions successfully", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }, { condition_id: "cond-2" }];
        const conditions = [
            {
                id: "cond-1",
                question_id: "q1",
                type: "in",
                value: ["option1", "option2"]
            },
            {
                id: "cond-2",
                question_id: "q2",
                type: "range",
                value: { min: 10, max: 50 }
            }
        ];
        const questions = [
            { id: "q1", type: "single_select" as Database["public"]["Enums"]["question_type"] },
            { id: "q2", type: "number_input" as Database["public"]["Enums"]["question_type"] }
        ];

        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: conditions, error: null }]);
        const questionsBuilder = createThenableBuilder([{ data: questions, error: null }]);

        fromMock
            .mockReturnValueOnce(linksBuilder)
            .mockReturnValueOnce(conditionsBuilder)
            .mockReturnValueOnce(questionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(fromMock).toHaveBeenCalledWith("link_collaboration_to_condition");
        expect(linksBuilder.select).toHaveBeenCalledWith("condition_id");
        expect(linksBuilder.eq).toHaveBeenCalledWith("collaboration_id", collaborationId);
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(2);
        expect(result.data?.[0].condition_type).toBe("in");
        expect(result.data?.[1].condition_type).toBe("range");
    });

    it("should return empty array when no links found", async () => {
        const collaborationId = "collab-1";
        const linksBuilder = createThenableBuilder([{ data: [], error: null }]);
        fromMock.mockReturnValue(linksBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data).toEqual([]);
    });

    it("should return empty array when no conditions found", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }];
        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: [], error: null }]);

        fromMock.mockReturnValueOnce(linksBuilder).mockReturnValueOnce(conditionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data).toEqual([]);
    });

    it("should handle date_picker question type", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }];
        const conditions = [
            {
                id: "cond-1",
                question_id: "q1",
                type: "date_range",
                value: { operator: "greater_than", days_from_today: 30 }
            }
        ];
        const questions = [{ id: "q1", type: "date_picker" as Database["public"]["Enums"]["question_type"] }];

        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: conditions, error: null }]);
        const questionsBuilder = createThenableBuilder([{ data: questions, error: null }]);

        fromMock
            .mockReturnValueOnce(linksBuilder)
            .mockReturnValueOnce(conditionsBuilder)
            .mockReturnValueOnce(questionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data?.[0].condition_type).toBe("date_range");
        expect(result.data?.[0].condition_value).toEqual({ operator: "greater_than", days_from_today: 30 });
    });

    it("should handle invalid condition values", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }];
        const conditions = [
            {
                id: "cond-1",
                question_id: "q1",
                type: "range",
                value: "invalid"
            }
        ];
        const questions = [{ id: "q1", type: "number_input" as Database["public"]["Enums"]["question_type"] }];

        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: conditions, error: null }]);
        const questionsBuilder = createThenableBuilder([{ data: questions, error: null }]);

        fromMock
            .mockReturnValueOnce(linksBuilder)
            .mockReturnValueOnce(conditionsBuilder)
            .mockReturnValueOnce(questionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data?.[0].condition_value).toEqual({ min: undefined, max: undefined });
    });

    it("should return error when fetching links fails", async () => {
        const collaborationId = "collab-1";
        const linksBuilder = createThenableBuilder([{ data: null, error: new Error("Link fetch error") }]);
        fromMock.mockReturnValue(linksBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Link fetch error");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return error when fetching conditions fails", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }];
        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: null, error: new Error("Conditions fetch error") }]);

        fromMock.mockReturnValueOnce(linksBuilder).mockReturnValueOnce(conditionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Conditions fetch error");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it("should return error when fetching questions fails", async () => {
        const collaborationId = "collab-1";
        const links = [{ condition_id: "cond-1" }];
        const conditions = [{ id: "cond-1", question_id: "q1", type: "in", value: [] }];
        const linksBuilder = createThenableBuilder([{ data: links, error: null }]);
        const conditionsBuilder = createThenableBuilder([{ data: conditions, error: null }]);
        const questionsBuilder = createThenableBuilder([{ data: null, error: new Error("Questions fetch error") }]);

        fromMock
            .mockReturnValueOnce(linksBuilder)
            .mockReturnValueOnce(conditionsBuilder)
            .mockReturnValueOnce(questionsBuilder);

        const result = await getCollaborationConditions(collaborationId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Questions fetch error");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});

describe("getCollaborationQuestions", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch collaboration questions successfully", async () => {
        const collaborationId = "collab-1";
        const questionLinks = [{ question_id: "q1" }, { question_id: "q2" }, { question_id: "q3" }];

        const selectBuilder = createThenableBuilder([{ data: questionLinks, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getCollaborationQuestions(collaborationId);

        expect(fromMock).toHaveBeenCalledWith("link_question_to_collaboration");
        expect(selectBuilder.select).toHaveBeenCalledWith("question_id");
        expect(selectBuilder.eq).toHaveBeenCalledWith("collaboration_id", collaborationId);
        expect(result.success).toBe(true);
        expect(result.data).toEqual(["q1", "q2", "q3"]);
    });

    it("should return empty array when no questions found", async () => {
        const collaborationId = "collab-1";
        const selectBuilder = createThenableBuilder([{ data: [], error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getCollaborationQuestions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data).toEqual([]);
    });

    it("should handle null data response", async () => {
        const collaborationId = "collab-1";
        const selectBuilder = createThenableBuilder([{ data: null, error: null }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getCollaborationQuestions(collaborationId);

        expect(result.success).toBe(true);
        expect(result.data).toEqual([]);
    });

    it("should return error when database query fails", async () => {
        const collaborationId = "collab-1";
        const selectBuilder = createThenableBuilder([{ data: null, error: new Error("Query failed") }]);
        fromMock.mockReturnValue(selectBuilder);

        const result = await getCollaborationQuestions(collaborationId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("Query failed");
        expect(consoleErrorSpy).toHaveBeenCalled();
    });
});
