import { createClientFromRequest } from "@/utils/supabase/server";
import { deleteScholarship, getScholarshipsPage, getAdminScholarships } from "@/app/actions/scholarship-actions";
import { createThenableBuilder, SupabaseResponse } from "@/tests/mocks/supabase-mock";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

let consoleErrorSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
});

const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockResolvedValue({ data: [], error: null })
};

mockSupabase.select.mockImplementation((selectString, options) => {
    if (options?.count === "exact") {
        return {
            ...mockSupabase,
            ...Promise.resolve({ count: 0, error: null })
        };
    }
    return mockSupabase;
});

describe("scholarship-actions", () => {
    let from: jest.Mock;

    beforeEach(() => {
        from = jest.fn();
        (createClientFromRequest as jest.Mock).mockReturnValue({ from });
        jest.clearAllMocks();
    });

    describe("getScholarshipsPage", () => {
        it("should fetch scholarships and count successfully", async () => {
            const queue: SupabaseResponse[] = [
                { count: 1, error: null },
                { data: [{ id: "1", name: "Scholarship 1" }], error: null }
            ];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);

            const result = await getScholarshipsPage(1);

            expect(from).toHaveBeenCalledWith("scholarships");
            expect(builder.select).toHaveBeenCalledWith(expect.any(String), {
                count: "exact",
                head: true
            });
            expect(builder.select).toHaveBeenCalledWith(expect.any(String));
            expect(builder.range).toHaveBeenCalledWith(0, 4);
            expect(result.data).toEqual([{ id: "1", name: "Scholarship 1" }]);
            expect(result.count).toBe(1);
        });

        it("should apply groupSlug filter when provided", async () => {
            const queue: SupabaseResponse[] = [
                { count: 1, error: null },
                { data: [{ id: "1" }], error: null }
            ];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);

            await getScholarshipsPage(1, "tech-group");

            expect(builder.eq).toHaveBeenCalledWith("is_public", true);
            expect(builder.eq).toHaveBeenCalledWith(
                "link_scholarship_to_scholarship_groups.groups_scholarship.slug",
                "tech-group"
            );
        });

        it("should handle count query error", async () => {
            const queue: SupabaseResponse[] = [{ count: null, error: new Error("Count failed") }];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);

            const result = await getScholarshipsPage(1);

            expect(result.error).toBe("שגיאה בטעינת המלגות.");
        });

        it("should handle data query error", async () => {
            const queue: SupabaseResponse[] = [
                { count: 1, error: null },
                { data: null, error: new Error("Data failed") }
            ];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);
            const result = await getScholarshipsPage(1);
            expect(result.error).toBe("שגיאה בטעינת המלגות.");
        });
    });

    describe("deleteScholarship", () => {
        it("should delete a scholarship and its links successfully", async () => {
            const queue: SupabaseResponse[] = [{ error: null }, { error: null }, { error: null }];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);

            const result = await deleteScholarship("scholarship-id");

            expect(result.success).toBe(true);
            expect(from).toHaveBeenCalledTimes(3);
            expect(builder.delete).toHaveBeenCalledTimes(3);
            expect(builder.eq).toHaveBeenCalledWith("scholarship_id", "scholarship-id");
            expect(builder.eq).toHaveBeenCalledWith("id", "scholarship-id");
        });

        it("should return error if any deletion fails", async () => {
            const queue: SupabaseResponse[] = [{ error: new Error("Deletion failed") }];
            const builder = createThenableBuilder(queue);
            from.mockReturnValue(builder);

            const result = await deleteScholarship("scholarship-id");

            expect(result.success).toBe(false);
            expect(result.error).toBe("שגיאה במחיקת המלגה. אנא נסה שנית.");
        });
    });
});

describe("getAdminScholarships action", () => {
    const fromMock = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockReturnValue({ from: fromMock });
    });

    it("should fetch scholarships with default parameters", async () => {
        const mockScholarships = [{ id: "1", title: "Scholarship 1" }];
        const mockCount = 1;

        const countBuilder = createThenableBuilder([{ count: mockCount, error: null }]);
        const dataBuilder = createThenableBuilder([{ data: mockScholarships, error: null }]);

        fromMock.mockImplementation((table: string) => {
            if (table === "scholarships") {
                // The first call to from("scholarships") is for the count
                if (countBuilder.select.mock.calls.length === 0) {
                    return countBuilder;
                }
                // The second is for the data
                return dataBuilder;
            }
            return createThenableBuilder([]);
        });

        const result = await getAdminScholarships({});

        expect(result.data).toEqual(mockScholarships);
        expect(result.count).toBe(mockCount);
        expect(fromMock).toHaveBeenCalledWith("scholarships");
        expect(countBuilder.select).toHaveBeenCalledWith("id", { count: "exact" });
        expect(dataBuilder.range).toHaveBeenCalledWith(0, 9);
    });

    it("should handle various filters", async () => {
        const queryBuilder = createThenableBuilder([
            { count: 0, data: [] },
            { data: [], count: 0 }
        ]);
        fromMock.mockReturnValue(queryBuilder);

        // Title filter
        await getAdminScholarships({ filters: { title: "test" } });
        expect(queryBuilder.ilike).toHaveBeenCalledWith("title", "%test%");

        // Amount filter
        await getAdminScholarships({ filters: { amount: 1000 } });
        expect(queryBuilder.eq).toHaveBeenCalledWith("amount", 1000);

        // Public filter
        await getAdminScholarships({ filters: { is_public: ["true"] } });
        expect(queryBuilder.eq).toHaveBeenCalledWith("is_public", true);

        // Date filter
        const startDate = new Date("2023-01-01");
        await getAdminScholarships({ filters: { start_date: { startDate } } });
        expect(queryBuilder.gte).toHaveBeenCalledWith("start_date", startDate.toISOString());
    });

    it("should handle group filter", async () => {
        const groupIds = ["group-1"];
        const scholarshipIdsInGroup = [{ scholarship_id: "scholarship-1" }];

        const groupCountBuilder = createThenableBuilder([
            { data: scholarshipIdsInGroup, error: null }, // for group count
            { data: scholarshipIdsInGroup, error: null } // for group data
        ]);
        const scholarshipCountBuilder = createThenableBuilder([{ count: 1, error: null }]);
        const scholarshipDataBuilder = createThenableBuilder([{ data: [{ id: "scholarship-1" }], error: null }]);

        let groupCallCount = 0;
        let scholarshipCallCount = 0;
        fromMock.mockImplementation((table: string) => {
            if (table === "link_scholarship_to_scholarship_groups") {
                groupCallCount++;
                return groupCountBuilder;
            }
            if (table === "scholarships") {
                scholarshipCallCount++;
                if (scholarshipCallCount === 1) {
                    return scholarshipCountBuilder;
                }
                return scholarshipDataBuilder;
            }
            return createThenableBuilder([]);
        });

        await getAdminScholarships({ filters: { group: groupIds } });

        expect(groupCountBuilder.select).toHaveBeenCalledWith("scholarship_id", { count: "exact" });
        expect(groupCountBuilder.in).toHaveBeenCalledWith("scholarship_group_id", groupIds);

        expect(scholarshipCountBuilder.in).toHaveBeenCalledWith("id", ["scholarship-1"]);
        expect(scholarshipDataBuilder.in).toHaveBeenCalledWith("id", ["scholarship-1"]);
    });

    it("should return an error if fetching fails", async () => {
        const errorMessage = "Database error";
        const queryBuilder = createThenableBuilder([{ error: new Error(errorMessage) }]);
        fromMock.mockReturnValue(queryBuilder);

        const result = await getAdminScholarships({});

        expect(result.error).toContain(errorMessage);
        expect(result.data).toEqual([]);
        expect(result.count).toBe(0);
    });
});
