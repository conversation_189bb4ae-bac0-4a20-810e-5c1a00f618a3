import { type Tables } from "@/types/database.types";
import {
    getContacts,
    getContactById,
    deleteContact,
    createContact,
    updateContact
} from "@/app/actions/contact-actions";
import { createClientFromRequest } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { TEXTS } from "@/lib/contact-constants";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

const single = jest.fn();
const eqForSelect = jest.fn(() => ({ single }));
const eqForUpdate = jest.fn();
const eqForDelete = jest.fn();
const insert = jest.fn();
const update = jest.fn(() => ({ eq: eqForUpdate }));
const del = jest.fn(() => ({ eq: eqForDelete }));
const order = jest.fn();
const select = jest.fn(() => ({ eq: eqForSelect, order, single }));

const from = jest.fn(() => ({
    select,
    insert,
    update,
    delete: del
}));

const mockSupabase = {
    from
};

const mockCreateClientFromRequest = createClientFromRequest as jest.Mock;
const mockRevalidatePath = revalidatePath as jest.Mock;

describe("Contact Actions", () => {
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();
        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        mockCreateClientFromRequest.mockResolvedValue(mockSupabase);
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe("getContacts", () => {
        it("should fetch contacts successfully", async () => {
            const mockContacts: Tables<"contact">[] = [
                { id: "1", email: "<EMAIL>", created_at: "2023-01-01", updated_at: "2023-01-01" },
                { id: "2", email: "<EMAIL>", created_at: "2023-01-02", updated_at: "2023-01-02" }
            ];

            order.mockResolvedValue({ data: mockContacts, error: null });

            const result = await getContacts();

            expect(from).toHaveBeenCalledWith("contact");
            expect(select).toHaveBeenCalledWith("*");
            expect(order).toHaveBeenCalledWith("created_at", { ascending: false });
            expect(result).toEqual({ success: true, data: mockContacts });
        });

        it("should handle database error", async () => {
            const mockError = new Error("Database connection failed");
            order.mockResolvedValue({ data: null, error: mockError });

            const result = await getContacts();

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching contacts:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Database connection failed"
            });
        });

        it("should handle non-Error exceptions", async () => {
            const mockError = "String error";
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await getContacts();

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching contacts:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Unknown error occurred"
            });
        });

        it("should handle createClientFromRequest failure", async () => {
            const mockError = new Error("Failed to create client");
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await getContacts();

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching contacts:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Failed to create client"
            });
        });
    });

    describe("getContactById", () => {
        it("should fetch contact by ID successfully", async () => {
            const mockContact: Tables<"contact"> = {
                id: "1",
                email: "<EMAIL>",
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            };
            single.mockResolvedValue({ data: mockContact, error: null });

            const result = await getContactById("1");

            expect(from).toHaveBeenCalledWith("contact");
            expect(select).toHaveBeenCalledWith("*");
            expect(eqForSelect).toHaveBeenCalledWith("id", "1");
            expect(single).toHaveBeenCalled();
            expect(result).toEqual({ success: true, data: mockContact });
        });

        it("should handle contact not found", async () => {
            const mockError = new Error("Contact not found");
            single.mockResolvedValue({ data: null, error: mockError });

            const result = await getContactById("nonexistent");

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching contact by ID:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Contact not found"
            });
        });

        it("should handle database error", async () => {
            const mockError = new Error("Database error");
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await getContactById("1");

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching contact by ID:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Database error"
            });
        });
    });

    describe("deleteContact", () => {
        it("should delete contact successfully", async () => {
            eqForDelete.mockResolvedValue({ error: null });

            const result = await deleteContact("1");

            expect(from).toHaveBeenCalledWith("contact");
            expect(del).toHaveBeenCalled();
            expect(eqForDelete).toHaveBeenCalledWith("id", "1");
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/contact");
            expect(result).toEqual({ success: true });
        });

        it("should handle delete error", async () => {
            const mockError = new Error("Delete failed");
            eqForDelete.mockResolvedValue({ error: mockError });

            const result = await deleteContact("1");

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error deleting contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Delete failed"
            });
            expect(mockRevalidatePath).not.toHaveBeenCalled();
        });

        it("should handle database connection error", async () => {
            const mockError = new Error("Connection failed");
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await deleteContact("1");

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error deleting contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: "Connection failed"
            });
        });
    });

    describe("createContact", () => {
        it("should create contact successfully", async () => {
            insert.mockResolvedValue({ error: null });

            const result = await createContact({ email: "<EMAIL>" });

            expect(from).toHaveBeenCalledWith("contact");
            expect(insert).toHaveBeenCalledWith([{ email: "<EMAIL>" }]);
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/contact");
            expect(result).toEqual({ success: true });
        });

        it("should handle create error", async () => {
            const mockError = new Error("Email already exists");
            insert.mockResolvedValue({ error: mockError });

            const result = await createContact({ email: "<EMAIL>" });

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error creating contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.createError
            });
            expect(mockRevalidatePath).not.toHaveBeenCalled();
        });

        it("should handle database connection error", async () => {
            const mockError = new Error("Connection failed");
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await createContact({ email: "<EMAIL>" });

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error creating contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.createError
            });
        });
    });

    describe("updateContact", () => {
        it("should update contact successfully", async () => {
            eqForUpdate.mockResolvedValue({ error: null });

            const result = await updateContact("1", { email: "<EMAIL>" });

            expect(from).toHaveBeenCalledWith("contact");
            expect(update).toHaveBeenCalledWith({ email: "<EMAIL>" });
            expect(eqForUpdate).toHaveBeenCalledWith("id", "1");
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/contact");
            expect(mockRevalidatePath).toHaveBeenCalledWith("/admin/contact/1");
            expect(result).toEqual({ success: true });
        });

        it("should handle update error", async () => {
            const mockError = new Error("Contact not found");
            eqForUpdate.mockResolvedValue({ error: mockError });

            const result = await updateContact("nonexistent", { email: "<EMAIL>" });

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error updating contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.updateError
            });
            expect(mockRevalidatePath).not.toHaveBeenCalled();
        });

        it("should handle database connection error", async () => {
            const mockError = new Error("Connection failed");
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await updateContact("1", { email: "<EMAIL>" });

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error updating contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.updateError
            });
        });

        it("should handle non-Error exceptions", async () => {
            const mockError = "String error";
            mockCreateClientFromRequest.mockRejectedValue(mockError);

            const result = await updateContact("1", { email: "<EMAIL>" });

            expect(consoleErrorSpy).toHaveBeenCalledWith("Error updating contact:", mockError);
            expect(result).toEqual({
                success: false,
                error: TEXTS.updateError
            });
        });
    });
});
