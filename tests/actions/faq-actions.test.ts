import { createClientFromRequest } from "@/utils/supabase/server";
import { createFaq, deleteFaq, getAllFaqs, getFaq, reorderFaqs, updateFaq } from "@/app/actions/faq-actions";
import { type Tables } from "@/types/database.types";
import { TEXTS } from "@/lib/faq-constants";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockSupabase = {
    from: jest.fn(),
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    eq: jest.fn(),
    order: jest.fn(),
    limit: jest.fn(),
    single: jest.fn()
};

describe("FAQ Actions", () => {
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabase);

        mockSupabase.from.mockReturnValue(mockSupabase);
        mockSupabase.select.mockReturnValue(mockSupabase);
        mockSupabase.update.mockReturnValue(mockSupabase);
        mockSupabase.delete.mockReturnValue(mockSupabase);
        mockSupabase.order.mockReturnValue(mockSupabase);
        mockSupabase.eq.mockReturnValue(mockSupabase);
        mockSupabase.limit.mockReturnValue(mockSupabase);

        consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        consoleErrorSpy.mockRestore();
    });

    describe("createFaq", () => {
        it("should create a FAQ successfully", async () => {
            mockSupabase.limit.mockResolvedValueOnce({ data: [{ order_index: 5 }], error: null });
            mockSupabase.insert.mockResolvedValueOnce({ data: null, error: null });
            const result = await createFaq({ question: "Test Question", answer: "Test Answer" });
            expect(result).toEqual({ success: true });
        });

        it("should handle empty question validation", async () => {
            const result = await createFaq({ question: "", answer: "Test Answer" });
            expect(result).toEqual({ success: false, error: TEXTS.QUESTION_REQUIRED });
        });

        it("should handle empty answer validation", async () => {
            const result = await createFaq({ question: "Test Question", answer: "" });
            expect(result).toEqual({ success: false, error: TEXTS.ANSWER_REQUIRED });
        });

        it("should handle database error", async () => {
            mockSupabase.limit.mockResolvedValueOnce({ data: [], error: null });
            mockSupabase.insert.mockResolvedValueOnce({ data: null, error: new Error("DB error") });
            const result = await createFaq({ question: "Test Question", answer: "Test Answer" });
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_CREATE_ERROR });
        });
    });

    describe("updateFaq", () => {
        it("should update a FAQ successfully", async () => {
            mockSupabase.eq.mockResolvedValueOnce({ error: null });
            const result = await updateFaq("1", { question: "Updated Question", answer: "Updated Answer" });
            expect(result).toEqual({ success: true });
        });

        it("should handle validation errors", async () => {
            const result = await updateFaq("1", { question: "", answer: "" });
            expect(result).toEqual({ success: false, error: TEXTS.QUESTION_REQUIRED });
        });

        it("should handle database error", async () => {
            mockSupabase.eq.mockResolvedValueOnce({ error: new Error("DB error") });
            const result = await updateFaq("1", { question: "Updated", answer: "Updated" });
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_UPDATE_ERROR });
        });
    });

    describe("getFaq", () => {
        it("should get a FAQ by ID successfully", async () => {
            const mockFaq = {
                id: "1",
                question: "Q",
                answer: "A",
                order_index: 1,
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            };
            mockSupabase.single.mockResolvedValueOnce({ data: mockFaq, error: null });
            const result = await getFaq("1");
            expect(result).toEqual({ success: true, data: mockFaq });
        });

        it("should handle not found", async () => {
            mockSupabase.single.mockResolvedValueOnce({ data: null, error: null });
            const result = await getFaq("1");
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_NOT_FOUND });
        });

        it("should handle database error", async () => {
            mockSupabase.single.mockResolvedValueOnce({ data: null, error: new Error("Database error") });
            const result = await getFaq("1");
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_FETCH_ERROR });
        });
    });

    describe("getAllFaqs", () => {
        it("should get all FAQs successfully", async () => {
            const mockFaqs = [
                {
                    id: "1",
                    question: "Q1",
                    answer: "A1",
                    order_index: 1,
                    created_at: "2023-01-01",
                    updated_at: "2023-01-01"
                }
            ];
            mockSupabase.order.mockResolvedValueOnce({ data: mockFaqs, error: null });
            const result = await getAllFaqs();
            expect(result).toEqual({ success: true, data: mockFaqs });
        });

        it("should handle empty results", async () => {
            mockSupabase.order.mockResolvedValueOnce({ data: [], error: null });
            const result = await getAllFaqs();
            expect(result).toEqual({ success: true, data: [] });
        });

        it("should handle database error", async () => {
            mockSupabase.order.mockResolvedValueOnce({ data: null, error: new Error("Database error") });
            const result = await getAllFaqs();
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_FETCH_ERROR });
        });
    });

    describe("deleteFaq", () => {
        it("should delete a FAQ successfully", async () => {
            mockSupabase.eq.mockResolvedValueOnce({ error: null });
            const result = await deleteFaq("1");
            expect(result).toEqual({ success: true });
        });

        it("should handle database error", async () => {
            mockSupabase.eq.mockResolvedValueOnce({ error: new Error("Database error") });
            const result = await deleteFaq("1");
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_DELETE_ERROR });
        });
    });

    describe("reorderFaqs", () => {
        const mockFaqs = [
            {
                id: "1",
                question: "Q1",
                answer: "A1",
                order_index: 2,
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            },
            {
                id: "2",
                question: "Q2",
                answer: "A2",
                order_index: 1,
                created_at: "2023-01-01",
                updated_at: "2023-01-01"
            }
        ];

        it("should reorder FAQs successfully", async () => {
            mockSupabase.eq.mockResolvedValue({ error: null });
            const result = await reorderFaqs(mockFaqs);
            expect(result).toEqual({ success: true });
            expect(mockSupabase.update).toHaveBeenCalledTimes(4);
        });

        it("should handle database error in first pass", async () => {
            mockSupabase.eq.mockResolvedValueOnce({ error: new Error("DB error") });
            const result = await reorderFaqs(mockFaqs);
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_REORDER_ERROR });
        });

        it("should handle database error in second pass", async () => {
            mockSupabase.eq
                .mockResolvedValueOnce({ error: null })
                .mockResolvedValueOnce({ error: new Error("DB error") });
            const result = await reorderFaqs(mockFaqs);
            expect(result).toEqual({ success: false, error: TEXTS.FAQ_REORDER_ERROR });
        });
    });
});
