import "@testing-library/jest-dom";

import * as couponGroupActions from "@/app/actions/coupon-group-actions";
import { TEXTS } from "@/lib/coupon-group-constants";
import { createClientFromRequest } from "@/utils/supabase/server";
import { type Tables } from "@/types/database.types";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("next/cache", () => ({
    revalidatePath: jest.fn()
}));

const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

describe("Coupon Group Actions", () => {
    const mockSupabaseClient = {
        from: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        mockConsoleError.mockClear();
    });

    afterAll(() => {
        mockConsoleError.mockRestore();
    });

    describe("getCouponGroups", () => {
        it("returns coupon groups with coupon counts when successful", async () => {
            const mockGroups = [
                { id: "group-1", name: "Test Group 1", description: "Description 1" },
                { id: "group-2", name: "Test Group 2", description: null }
            ];

            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            const mockCoupons = [
                { coupon_group_id: "group-1" },
                { coupon_group_id: "group-1" },
                { coupon_group_id: "group-2" }
            ];

            const mockCouponsSelect = jest.fn().mockResolvedValue({
                data: mockCoupons,
                error: null
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_coupon") {
                    return { select: mockGroupsSelect };
                } else if (table === "coupons") {
                    return { select: mockCouponsSelect };
                }
                return {};
            });

            const result = await couponGroupActions.getCouponGroups();

            expect(result.success).toBe(true);
            expect(result.data).toHaveLength(2);
            expect(result.data?.[0].coupons_count).toBe(2);
            expect(result.data?.[1].coupons_count).toBe(1);

            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("coupons");
            expect(mockGroupsSelect).toHaveBeenCalledWith("*");
        });

        it("handles groups query error", async () => {
            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_coupon") {
                    return { select: mockGroupsSelect };
                }
                return {};
            });

            const result = await couponGroupActions.getCouponGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupsFetchError);
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("handles coupons query error", async () => {
            const mockGroups = [{ id: "group-1", name: "Test Group 1", description: "Description 1" }];

            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            const mockCouponsSelect = jest.fn().mockResolvedValue({
                data: null,
                error: { message: "Database error" }
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_coupon") {
                    return { select: mockGroupsSelect };
                } else if (table === "coupons") {
                    return { select: mockCouponsSelect };
                }
                return {};
            });

            const result = await couponGroupActions.getCouponGroups();

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupsFetchError);
            expect(mockConsoleError).toHaveBeenCalled();
        });

        it("returns empty array when no groups found", async () => {
            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: [],
                    error: null
                })
            });

            const mockCouponsSelect = jest.fn().mockResolvedValue({
                data: [],
                error: null
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_coupon") {
                    return { select: mockGroupsSelect };
                } else if (table === "coupons") {
                    return { select: mockCouponsSelect };
                }
                return {};
            });

            const result = await couponGroupActions.getCouponGroups();

            expect(result.success).toBe(true);
            expect(result.data).toEqual([]);
        });

        it("handles null counts and sets default count to 0", async () => {
            const mockGroups = [{ id: "group-1", name: "Test Group 1", description: "Description 1" }];

            const mockGroupsSelect = jest.fn().mockReturnValue({
                order: jest.fn().mockResolvedValue({
                    data: mockGroups,
                    error: null
                })
            });

            const mockCouponsSelect = jest.fn().mockResolvedValue({
                data: null,
                error: null
            });

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_coupon") {
                    return { select: mockGroupsSelect };
                } else if (table === "coupons") {
                    return { select: mockCouponsSelect };
                }
                return {};
            });

            const result = await couponGroupActions.getCouponGroups();

            expect(result.success).toBe(true);
            expect(result.data?.[0].coupons_count).toBe(0);
        });
    });

    describe("getCouponGroup", () => {
        it("returns a coupon group when found", async () => {
            const mockGroup = {
                id: "group-1",
                name: "Test Group",
                description: "Test Description",
                created_at: "2023-01-01T00:00:00Z"
            };

            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: mockGroup,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponGroupActions.getCouponGroup("group-1");

            expect(result.success).toBe(true);
            expect(result.data).toEqual(mockGroup);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockSelect).toHaveBeenCalledWith("*");
        });

        it("returns error when coupon group not found", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: null
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponGroupActions.getCouponGroup("non-existent");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupNotFound);
        });

        it("handles database errors", async () => {
            const mockSelect = jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                    single: jest.fn().mockResolvedValue({
                        data: null,
                        error: { message: "Database error" }
                    })
                })
            });

            mockSupabaseClient.from.mockReturnValue({ select: mockSelect });

            const result = await couponGroupActions.getCouponGroup("group-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupFetchError);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("createCouponGroup", () => {
        it("creates a coupon group successfully", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: [{ id: "new-group" }],
                error: null
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "New Test Group",
                description: "New Description"
            };

            const result = await couponGroupActions.createCouponGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockInsert).toHaveBeenCalledWith({
                name: "New Test Group",
                description: "New Description"
            });
        });

        it("validates required name field", async () => {
            const groupData = {
                name: "",
                description: "Description"
            };

            const result = await couponGroupActions.createCouponGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("handles null description", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: [{ id: "new-group" }],
                error: null
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "New Test Group",
                description: ""
            };

            const result = await couponGroupActions.createCouponGroup(groupData);

            expect(result.success).toBe(true);
            expect(mockInsert).toHaveBeenCalledWith({
                name: "New Test Group",
                description: null
            });
        });

        it("handles database errors", async () => {
            const mockInsert = jest.fn().mockResolvedValue({
                data: null,
                error: { message: "Database error" }
            });

            mockSupabaseClient.from.mockReturnValue({ insert: mockInsert });

            const groupData = {
                name: "New Test Group",
                description: "New Description"
            };

            const result = await couponGroupActions.createCouponGroup(groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupCreateError);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("updateCouponGroup", () => {
        it("updates a coupon group successfully", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "group-1" },
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ update: mockUpdate });

            const groupData = {
                name: "Updated Group",
                description: "Updated Description"
            };

            const result = await couponGroupActions.updateCouponGroup("group-1", groupData);

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockUpdate).toHaveBeenCalledWith({
                name: "Updated Group",
                description: "Updated Description"
            });
        });

        it("validates required name field", async () => {
            const groupData = {
                name: "",
                description: "Description"
            };

            const result = await couponGroupActions.updateCouponGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.nameRequired);
            expect(mockSupabaseClient.from).not.toHaveBeenCalled();
        });

        it("handles database errors", async () => {
            const mockUpdate = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ update: mockUpdate });

            const groupData = {
                name: "Updated Group",
                description: "Updated Description"
            };

            const result = await couponGroupActions.updateCouponGroup("group-1", groupData);

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupUpdateError);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });

    describe("deleteCouponGroup", () => {
        it("deletes a coupon group successfully", async () => {
            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: { id: "deleted-group" },
                    error: null
                })
            });

            mockSupabaseClient.from.mockReturnValue({ delete: mockDelete });

            const result = await couponGroupActions.deleteCouponGroup("group-1");

            expect(result.success).toBe(true);
            expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_coupon");
            expect(mockDelete).toHaveBeenCalled();
        });

        it("handles database errors", async () => {
            const mockDelete = jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({
                    data: null,
                    error: { message: "Database error" }
                })
            });

            mockSupabaseClient.from.mockReturnValue({ delete: mockDelete });

            const result = await couponGroupActions.deleteCouponGroup("group-1");

            expect(result.success).toBe(false);
            expect(result.error).toBe(TEXTS.couponGroupDeleteError);
            expect(mockConsoleError).toHaveBeenCalled();
        });
    });
});
