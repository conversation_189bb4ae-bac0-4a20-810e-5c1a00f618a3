import {
    createScholarship,
    updateScholarship,
    getScholarship,
    uploadScholarshipImage,
    linkScholarshipTestimonials,
    linkScholarshipGroups
} from "@/app/actions/scholarship-crud-actions";
import { Tables, TablesInsert, TablesUpdate, Database } from "@/types/database.types";
import { createServiceRoleClient } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createServiceRoleClient: jest.fn(),
    createClientFromRequest: jest.fn()
}));

interface SupabaseResponse {
    data?: any;
    count?: number | null;
    error?: Error | null;
}

interface MockBuilder {
    select: jest.Mock;
    insert: jest.Mock;
    update: jest.Mock;
    upsert: jest.Mock;
    order: jest.Mock;
    eq: jest.Mock;
    gte: jest.Mock;
    lte: jest.Mock;
    ilike: jest.Mock;
    in: jest.Mock;
    not: jest.Mock;
    range: jest.Mock;
    delete: jest.Mock;
    maybeSingle: jest.Mock;
    single: jest.Mock;
    limit: jest.Mock;
    then: (resolve: (v: SupabaseResponse) => void) => void;
}

function createThenableBuilder(queue: SupabaseResponse[]) {
    const builder: MockBuilder = {
        select: jest.fn(() => builder),
        insert: jest.fn(() => builder),
        update: jest.fn(() => builder),
        upsert: jest.fn(() => builder),
        order: jest.fn(() => builder),
        eq: jest.fn(() => builder),
        gte: jest.fn(() => builder),
        lte: jest.fn(() => builder),
        ilike: jest.fn(() => builder),
        in: jest.fn(() => builder),
        not: jest.fn(() => builder),
        range: jest.fn(() => builder),
        delete: jest.fn(() => builder),
        maybeSingle: jest.fn(() => builder),
        single: jest.fn(() => builder),
        limit: jest.fn(() => builder),
        then: (resolve: (v: SupabaseResponse) => void) => {
            const next = queue.shift() ?? { data: [], count: 0, error: null };
            resolve(next);
        }
    };
    return builder;
}

const fromMock = jest.fn();
const storageMock = {
    from: jest.fn().mockReturnThis(),
    upload: jest.fn(),
    getPublicUrl: jest.fn(() => ({ data: { publicUrl: "https://example.com/test.webp" } })),
    remove: jest.fn()
};
const mockSupabase = {
    from: fromMock,
    storage: storageMock
};

let consoleErrorSpy: jest.SpyInstance;
let consoleLogSpy: jest.SpyInstance;

beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation(() => {});
});

afterAll(() => {
    consoleErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
});

beforeEach(() => {
    jest.clearAllMocks();
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
});

describe("createScholarship", () => {
    const validScholarshipData: Omit<TablesInsert<"scholarships">, "id" | "created_at" | "updated_at"> = {
        title: "Test Scholarship",
        slug: "test-scholarship",
        scholarship_type: "guidance",
        is_active: true,
        description: "Test description",
        short_description: "Short test description",
        target_audience: "Students",
        benefits: ["Free tuition", "Monthly stipend"],
        requirements: ["High GPA", "Community service"],
        start_date: "2024-01-01",
        end_date: "2024-12-31",
        min_amount: 1000,
        max_amount: 5000,
        volunteer_hours: 40,
        is_public: true
    };

    it("should create scholarship successfully", async () => {
        const expectedScholarship: Tables<"scholarships"> = {
            id: "scholarship-123",
            title: validScholarshipData.title,
            slug: validScholarshipData.slug,
            scholarship_type: validScholarshipData.scholarship_type!,
            is_active: validScholarshipData.is_active!,
            description: validScholarshipData.description,
            short_description: validScholarshipData.short_description,
            target_audience: validScholarshipData.target_audience!,
            benefits: validScholarshipData.benefits!,
            requirements: validScholarshipData.requirements!,
            start_date: validScholarshipData.start_date!,
            end_date: validScholarshipData.end_date!,
            min_amount: validScholarshipData.min_amount!,
            max_amount: validScholarshipData.max_amount!,
            volunteer_hours: validScholarshipData.volunteer_hours!,
            is_public: validScholarshipData.is_public!,
            image_url: null,
            contact_email: null,
            contact_person: null,
            contact_phone: null,
            internal_notes: null,
            response_date: null,
            url: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z"
        };

        const checkBuilder = createThenableBuilder([{ data: [], error: null }]);
        const insertBuilder = createThenableBuilder([{ data: expectedScholarship, error: null }]);

        fromMock.mockReturnValueOnce(checkBuilder).mockReturnValueOnce(insertBuilder);

        const result = await createScholarship(validScholarshipData);

        expect(consoleLogSpy).toHaveBeenCalledWith("Creating scholarship with data:", validScholarshipData);
        expect(result.success).toBe(true);
        expect(result.data).toEqual(expectedScholarship);
        expect(checkBuilder.select).toHaveBeenCalledWith("id");
        expect(checkBuilder.eq).toHaveBeenCalledWith("slug", validScholarshipData.slug);
        expect(insertBuilder.insert).toHaveBeenCalledWith([validScholarshipData]);
    });

    it("should return error when slug already exists", async () => {
        const existingScholarship: Pick<Tables<"scholarships">, "id"> = { id: "existing-123" };
        const checkBuilder = createThenableBuilder([{ data: [existingScholarship], error: null }]);

        fromMock.mockReturnValueOnce(checkBuilder);

        const result = await createScholarship(validScholarshipData);

        expect(consoleLogSpy).toHaveBeenCalledWith("Slug already exists:", validScholarshipData.slug);
        expect(result.success).toBe(false);
        expect(result.error).toBe("קידומת זו כבר קיימת במערכת. אנא בחר קידומת אחרת.");
    });

    it("should handle slug check database error", async () => {
        const checkBuilder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);

        fromMock.mockReturnValueOnce(checkBuilder);

        const result = await createScholarship(validScholarshipData);

        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה ביצירת המלגה. אנא נסה שנית.");
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error checking existing slug:", expect.any(Error));
    });

    it("should handle scholarship creation database error", async () => {
        const checkBuilder = createThenableBuilder([{ data: [], error: null }]);
        const insertBuilder = createThenableBuilder([{ data: null, error: new Error("Insert failed") }]);

        fromMock.mockReturnValueOnce(checkBuilder).mockReturnValueOnce(insertBuilder);

        const result = await createScholarship(validScholarshipData);

        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה ביצירת המלגה. אנא נסה שנית.");
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error creating scholarship:", expect.any(Object));
    });
});

describe("updateScholarship", () => {
    const scholarshipId = "scholarship-123";
    const updateData: TablesUpdate<"scholarships"> = {
        title: "Updated Scholarship",
        slug: "updated-scholarship"
    };

    it("should update scholarship successfully", async () => {
        const updatedScholarship: Pick<Tables<"scholarships">, "id"> = { id: scholarshipId };
        const checkBuilder = createThenableBuilder([{ data: [], error: null }]);
        const updateBuilder = createThenableBuilder([{ data: updatedScholarship, error: null }]);

        fromMock.mockReturnValueOnce(checkBuilder).mockReturnValueOnce(updateBuilder);

        const result = await updateScholarship(scholarshipId, updateData);

        expect(consoleLogSpy).toHaveBeenCalledWith("Updating scholarship with data:", {
            id: scholarshipId,
            data: updateData
        });
        expect(consoleLogSpy).toHaveBeenCalledWith("Scholarship updated successfully");
        expect(result.success).toBe(true);
        expect(updateBuilder.update).toHaveBeenCalledWith(updateData);
        expect(updateBuilder.eq).toHaveBeenCalledWith("id", scholarshipId);
    });

    it("should update scholarship without slug check when slug not provided", async () => {
        const updateDataWithoutSlug: TablesUpdate<"scholarships"> = {
            title: "Updated Title Only"
        };
        const updatedScholarship: Pick<Tables<"scholarships">, "id"> = { id: scholarshipId };
        const updateBuilder = createThenableBuilder([{ data: updatedScholarship, error: null }]);

        fromMock.mockReturnValueOnce(updateBuilder);

        const result = await updateScholarship(scholarshipId, updateDataWithoutSlug);

        expect(consoleLogSpy).toHaveBeenCalledWith("Updating scholarship with data:", {
            id: scholarshipId,
            data: updateDataWithoutSlug
        });
        expect(result.success).toBe(true);
        expect(fromMock).toHaveBeenCalledTimes(1); // Only update call, no slug check
    });

    it("should return error when slug already exists", async () => {
        const existingScholarship: Pick<Tables<"scholarships">, "id"> = { id: "other-scholarship" };
        const checkBuilder = createThenableBuilder([{ data: [existingScholarship], error: null }]);

        fromMock.mockReturnValueOnce(checkBuilder);

        const result = await updateScholarship(scholarshipId, updateData);

        expect(consoleLogSpy).toHaveBeenCalledWith("Slug already exists:", updateData.slug);
        expect(result.success).toBe(false);
        expect(result.error).toBe("קידומת זו כבר קיימת במערכת. אנא בחר קידומת אחרת.");
    });

    it("should handle scholarship not found", async () => {
        const checkBuilder = createThenableBuilder([{ data: [], error: null }]);
        const updateBuilder = createThenableBuilder([{ data: null, error: null }]);

        fromMock.mockReturnValueOnce(checkBuilder).mockReturnValueOnce(updateBuilder);

        const result = await updateScholarship(scholarshipId, updateData);

        expect(result.success).toBe(false);
        expect(result.error).toBe("המלגה לא נמצאה.");
        expect(consoleErrorSpy).toHaveBeenCalledWith("No scholarship found with ID:", scholarshipId);
    });
});

describe("getScholarship", () => {
    const scholarshipId = "scholarship-123";

    it("should fetch scholarship successfully", async () => {
        const scholarshipWithLinks: Tables<"scholarships"> & {
            link_scholarship_to_testimonial: { testimonial_id: string }[];
            link_scholarship_to_scholarship_groups: { scholarship_group_id: string }[];
        } = {
            id: scholarshipId,
            title: "Test Scholarship",
            slug: "test-scholarship",
            description: "Test description",
            short_description: "Short description",
            scholarship_type: "guidance",
            is_active: true,
            is_public: true,
            target_audience: "Students",
            benefits: ["Benefit 1"],
            requirements: ["Requirement 1"],
            start_date: "2024-01-01",
            end_date: "2024-12-31",
            min_amount: 1000,
            max_amount: 5000,
            volunteer_hours: 40,
            image_url: null,
            contact_email: null,
            contact_person: null,
            contact_phone: null,
            internal_notes: null,
            response_date: null,
            url: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            link_scholarship_to_testimonial: [{ testimonial_id: "t1" }],
            link_scholarship_to_scholarship_groups: [{ scholarship_group_id: "g1" }]
        };

        const builder = createThenableBuilder([{ data: scholarshipWithLinks, error: null }]);
        fromMock.mockReturnValueOnce(builder);

        const result = await getScholarship(scholarshipId);

        expect(consoleLogSpy).toHaveBeenCalledWith("Fetching scholarship:", scholarshipId);
        expect(consoleLogSpy).toHaveBeenCalledWith("Scholarship fetched successfully");
        expect(result.success).toBe(true);
        expect(result.data).toEqual(scholarshipWithLinks);
        expect(builder.select).toHaveBeenCalledWith(
            `
                *,
                link_scholarship_to_testimonial(testimonial_id),
                link_scholarship_to_scholarship_groups(scholarship_group_id)
            `
        );
        expect(builder.eq).toHaveBeenCalledWith("id", scholarshipId);
        expect(builder.maybeSingle).toHaveBeenCalled();
    });

    it("should return error when scholarship not found", async () => {
        const builder = createThenableBuilder([{ data: null, error: null }]);
        fromMock.mockReturnValueOnce(builder);

        const result = await getScholarship(scholarshipId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("המלגה לא נמצאה.");
    });

    it("should return error when database query fails", async () => {
        const builder = createThenableBuilder([{ data: null, error: new Error("Database error") }]);
        fromMock.mockReturnValueOnce(builder);

        const result = await getScholarship(scholarshipId);

        expect(result.success).toBe(false);
        expect(result.error).toBe("המלגה לא נמצאה.");
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error fetching scholarship:", expect.any(Error));
    });
});

describe("uploadScholarshipImage", () => {
    const scholarshipId = "scholarship-123";

    it("should upload image successfully", async () => {
        const file = new File(["test"], "test.webp", { type: "image/webp" });
        const uploadData = { path: "scholarship-123.webp" };
        const publicUrl = "https://example.com/test.webp";

        storageMock.upload.mockResolvedValue({ data: uploadData, error: null });
        storageMock.getPublicUrl.mockReturnValue({ data: { publicUrl } });

        const updateBuilder = createThenableBuilder([{ error: null }]);
        fromMock.mockReturnValueOnce(updateBuilder);

        const result = await uploadScholarshipImage(scholarshipId, file);

        expect(consoleLogSpy).toHaveBeenCalledWith("Starting upload for scholarship:", scholarshipId);
        expect(consoleLogSpy).toHaveBeenCalledWith("File details:", { name: "test.webp", size: 4, type: "image/webp" });
        expect(consoleLogSpy).toHaveBeenCalledWith("Uploading to path:", "scholarship-123.webp");
        expect(consoleLogSpy).toHaveBeenCalledWith("Upload successful:", uploadData);
        expect(consoleLogSpy).toHaveBeenCalledWith("Public URL generated:", publicUrl);
        expect(consoleLogSpy).toHaveBeenCalledWith("Image upload and database update completed successfully");
        expect(result.success).toBe(true);
        expect(result.imageUrl).toBe(publicUrl);
        expect(storageMock.upload).toHaveBeenCalledWith("scholarship-123.webp", file, { upsert: true });
        expect(updateBuilder.update).toHaveBeenCalledWith({ image_url: publicUrl });
        expect(updateBuilder.eq).toHaveBeenCalledWith("id", scholarshipId);
    });

    it("should return error for unsupported file type", async () => {
        const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

        const result = await uploadScholarshipImage(scholarshipId, file);

        expect(consoleLogSpy).toHaveBeenCalledWith("Starting upload for scholarship:", scholarshipId);
        expect(consoleLogSpy).toHaveBeenCalledWith("File details:", { name: "test.jpg", size: 4, type: "image/jpeg" });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Unsupported file type:", "image/jpeg");
        expect(result.success).toBe(false);
        expect(result.error).toBe("רק קבצי WebP נתמכים");
    });

    it("should return error when upload fails", async () => {
        const file = new File(["test"], "test.webp", { type: "image/webp" });
        const uploadError = new Error("Upload failed");

        storageMock.upload.mockResolvedValue({ data: null, error: uploadError });

        const result = await uploadScholarshipImage(scholarshipId, file);

        expect(consoleLogSpy).toHaveBeenCalledWith("Starting upload for scholarship:", scholarshipId);
        expect(consoleLogSpy).toHaveBeenCalledWith("Uploading to path:", "scholarship-123.webp");
        expect(consoleErrorSpy).toHaveBeenCalledWith("Upload error details:", expect.any(Object));
        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה בהעלאת התמונה: Upload failed");
    });

    it("should rollback upload when database update fails", async () => {
        const file = new File(["test"], "test.webp", { type: "image/webp" });
        const uploadData = { path: "scholarship-123.webp" };
        const publicUrl = "https://example.com/test.webp";

        storageMock.upload.mockResolvedValue({ data: uploadData, error: null });
        storageMock.getPublicUrl.mockReturnValue({ data: { publicUrl } });
        storageMock.remove.mockResolvedValue({ error: null });

        const updateBuilder = createThenableBuilder([{ error: new Error("Database update failed") }]);
        fromMock.mockReturnValueOnce(updateBuilder);

        const result = await uploadScholarshipImage(scholarshipId, file);

        expect(consoleLogSpy).toHaveBeenCalledWith("Upload successful:", uploadData);
        expect(consoleLogSpy).toHaveBeenCalledWith("Public URL generated:", publicUrl);
        expect(consoleErrorSpy).toHaveBeenCalledWith("Database update error:", expect.any(Error));
        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה בעדכון התמונה");
        expect(storageMock.remove).toHaveBeenCalledWith(["scholarship-123.webp"]);
    });
});

describe("linkScholarshipTestimonials", () => {
    const scholarshipId = "scholarship-123";
    const testimonialIds = ["t1", "t2", "t3"];

    it("should link testimonials successfully", async () => {
        const deleteBuilder = createThenableBuilder([{ error: null }]);
        const insertBuilder = createThenableBuilder([{ error: null }]);

        fromMock.mockReturnValueOnce(deleteBuilder).mockReturnValueOnce(insertBuilder);

        const result = await linkScholarshipTestimonials(scholarshipId, testimonialIds);

        expect(consoleLogSpy).toHaveBeenCalledWith("Linking testimonials:", { scholarshipId, testimonialIds });
        expect(consoleLogSpy).toHaveBeenCalledWith("Testimonial links updated successfully");
        expect(result.success).toBe(true);
        expect(deleteBuilder.delete).toHaveBeenCalled();
        expect(deleteBuilder.eq).toHaveBeenCalledWith("scholarship_id", scholarshipId);
        expect(insertBuilder.insert).toHaveBeenCalledWith([
            { scholarship_id: scholarshipId, testimonial_id: "t1" },
            { scholarship_id: scholarshipId, testimonial_id: "t2" },
            { scholarship_id: scholarshipId, testimonial_id: "t3" }
        ]);
    });

    it("should handle empty testimonial IDs", async () => {
        const deleteBuilder = createThenableBuilder([{ error: null }]);
        fromMock.mockReturnValueOnce(deleteBuilder);

        const result = await linkScholarshipTestimonials(scholarshipId, []);

        expect(consoleLogSpy).toHaveBeenCalledWith("Linking testimonials:", { scholarshipId, testimonialIds: [] });
        expect(consoleLogSpy).toHaveBeenCalledWith("Testimonial links updated successfully");
        expect(result.success).toBe(true);
        expect(deleteBuilder.delete).toHaveBeenCalled();
        expect(fromMock).toHaveBeenCalledTimes(1); // Only delete call, no insert
    });

    it("should return error when delete fails", async () => {
        const deleteBuilder = createThenableBuilder([{ error: new Error("Delete failed") }]);
        fromMock.mockReturnValueOnce(deleteBuilder);

        const result = await linkScholarshipTestimonials(scholarshipId, testimonialIds);

        expect(consoleLogSpy).toHaveBeenCalledWith("Linking testimonials:", { scholarshipId, testimonialIds });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error deleting existing testimonial links:", expect.any(Error));
        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה בעדכון חוות דעת");
    });
});

describe("linkScholarshipGroups", () => {
    const scholarshipId = "scholarship-123";
    const groupIds = ["g1", "g2"];

    it("should link groups successfully", async () => {
        const deleteBuilder = createThenableBuilder([{ error: null }]);
        const insertBuilder = createThenableBuilder([{ error: null }]);

        fromMock.mockReturnValueOnce(deleteBuilder).mockReturnValueOnce(insertBuilder);

        const result = await linkScholarshipGroups(scholarshipId, groupIds);

        expect(consoleLogSpy).toHaveBeenCalledWith("Linking scholarship groups:", { scholarshipId, groupIds });
        expect(consoleLogSpy).toHaveBeenCalledWith("Scholarship group links updated successfully");
        expect(result.success).toBe(true);
        expect(deleteBuilder.delete).toHaveBeenCalled();
        expect(deleteBuilder.eq).toHaveBeenCalledWith("scholarship_id", scholarshipId);
        expect(insertBuilder.insert).toHaveBeenCalledWith([
            { scholarship_id: scholarshipId, scholarship_group_id: "g1" },
            { scholarship_id: scholarshipId, scholarship_group_id: "g2" }
        ]);
    });

    it("should return error when insert fails", async () => {
        const deleteBuilder = createThenableBuilder([{ error: null }]);
        const insertBuilder = createThenableBuilder([{ error: new Error("Insert failed") }]);

        fromMock.mockReturnValueOnce(deleteBuilder).mockReturnValueOnce(insertBuilder);

        const result = await linkScholarshipGroups(scholarshipId, groupIds);

        expect(consoleLogSpy).toHaveBeenCalledWith("Linking scholarship groups:", { scholarshipId, groupIds });
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error inserting group links:", expect.any(Error));
        expect(result.success).toBe(false);
        expect(result.error).toBe("שגיאה בעדכון קבוצות מלגה");
    });
});
