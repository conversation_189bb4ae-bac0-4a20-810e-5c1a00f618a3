export interface SupabaseMock {
    from: jest.<PERSON><PERSON>;
    select: jest.<PERSON><PERSON>;
    eq: jest.<PERSON><PERSON>;
    gte: jest.<PERSON><PERSON>;
    ilike: jest.<PERSON>ck;
    in: jest.<PERSON>ck;
    not: jest.<PERSON>ck;
}

export interface SupabaseResponse {
    data?: any;
    count?: number | null;
    error?: Error | null;
}

export interface MockBuilder {
    select: jest.<PERSON>ck;
    insert: jest.<PERSON>ck;
    update: jest.<PERSON>ck;
    upsert: jest.<PERSON>ck;
    order: jest.<PERSON><PERSON>;
    eq: jest.<PERSON>ck;
    gte: jest.<PERSON>ck;
    lte: jest.<PERSON>ck;
    ilike: jest.<PERSON>ck;
    in: jest.<PERSON>ck;
    not: jest.Mock;
    range: jest.Mock;
    delete: jest.Mock;
    maybeSingle: jest.Mock;
    single: jest.Mock;
    limit: jest.Mock;
    then: (resolve: (v: SupabaseResponse) => void) => void;
}

/**
 * Mocks a Supabase query builder with chainable methods (e.g., select, eq, order)
 * and simulates async `.then()` behavior for testing purposes.
 *
 * Usage:
 * - Pass an array of predefined responses (`queue`), which will be returned one by one
 *   each time `.then()` is called (simulating Supabase query results).
 * - Supports typical query chaining as in real Supabase client.
 */
export function createThenableBuilder(queue: SupabaseResponse[]) {
    const builder: MockBuilder = {
        select: jest.fn(() => builder),
        insert: jest.fn(() => builder),
        update: jest.fn(() => builder),
        upsert: jest.fn(() => builder),
        order: jest.fn(() => builder),
        eq: jest.fn(() => builder),
        gte: jest.fn(() => builder),
        lte: jest.fn(() => builder),
        ilike: jest.fn(() => builder),
        in: jest.fn(() => builder),
        not: jest.fn(() => builder),
        range: jest.fn(() => builder),
        delete: jest.fn(() => builder),
        maybeSingle: jest.fn(() => builder),
        single: jest.fn(() => builder),
        limit: jest.fn(() => builder),
        then: (resolve: (v: SupabaseResponse) => void) => {
            const next = queue.shift() ?? { data: [], count: 0, error: null };
            resolve(next);
        }
    };
    return builder;
}
