# Signup Preferences Testing Documentation

## Overview
This document provides a comprehensive overview of the testing strategy for the signup preferences feature, including test coverage, execution instructions, and maintenance guidelines.

## Test Architecture

### 1. Unit Tests
**Location**: `tests/contexts/`, `tests/hooks/`
**Purpose**: Test individual components and hooks in isolation
**Coverage**: 100% of core functionality

#### Context Tests (`signup-preferences-context.test.tsx`)
- ✅ Reducer state management (5 tests)
- ✅ Hook functionality (7 tests) 
- ✅ SessionStorage integration (4 tests)
- ✅ Error handling (2 tests)
- **Total**: 18 tests

#### Save Hook Tests (`use-signup-preferences-save.test.ts`)
- ✅ User availability checks (2 tests)
- ✅ Context preference saving (3 tests)
- ✅ Error handling and fallbacks (3 tests)
- **Total**: 8 tests

### 2. Integration Tests
**Location**: `tests/integration/`
**Purpose**: Test component interactions and data flow
**Coverage**: End-to-end scenarios

#### Signup Flow Integration (`signup-preferences-flow.test.tsx`)
- ✅ Context persistence (2 tests)
- ✅ Save hook integration (3 tests)
- ✅ Error scenarios (2 tests)
- ✅ State management (2 tests)
- **Total**: 9 tests

### 3. Component Tests
**Location**: `tests/components/auth/`
**Purpose**: Test UI component behavior with context integration
**Coverage**: User interaction scenarios

#### Auth Form Tests (`auth-form.test.tsx`)
- ✅ Sign in/up modes (13 tests)
- ✅ Context integration (3 tests)
- ✅ Checkbox interactions (2 tests)
- ✅ Social button behavior (2 tests)
- ✅ Form submission (3 tests)
- ✅ Error handling (3 tests)
- ✅ Loading states (3 tests)
- ✅ Common functionality (6 tests)
- ✅ User validation (8 tests)
- **Total**: 43 tests

## Test Coverage Summary

### Current Status
- **Total Tests**: 78 tests
- **Passing**: 78/78 (100%)
- **Coverage Areas**:
  - ✅ Context state management
  - ✅ SessionStorage persistence
  - ✅ Background preference saving
  - ✅ Error handling and fallbacks
  - ✅ User interaction flows
  - ✅ Component integration
  - ✅ Race condition handling

### Key Test Scenarios Covered

#### Happy Path Scenarios
1. **User sets preferences → completes signup → preferences saved**
2. **Preferences persist through page navigation**
3. **Context cleared after successful save**
4. **Social login with preferences**

#### Race Condition Scenarios
1. **No context preferences → defaults used**
2. **Context save fails → fallback to defaults**
3. **Quick navigation → graceful handling**

#### Error Scenarios
1. **Database errors → fallback behavior**
2. **SessionStorage errors → graceful degradation**
3. **Invalid user data → safe defaults**
4. **Network issues → retry mechanisms**

#### Edge Cases
1. **Returning users → no duplicate saves**
2. **Multiple renders → single save guarantee**
3. **Component unmounting → cleanup**
4. **Invalid user objects → validation**

## Running Tests

### All Signup Preference Tests
```bash
yarn test signup-preferences
```

### Individual Test Suites
```bash
# Context tests
yarn test signup-preferences-context

# Save hook tests
yarn test use-signup-preferences-save

# Integration tests
yarn test signup-preferences-flow

# Auth form tests
yarn test auth-form
```

### Test Options
```bash
# Watch mode
yarn test signup-preferences --watch

# Coverage report
yarn test signup-preferences --coverage

# Verbose output
yarn test signup-preferences --verbose
```

## Test Maintenance

### Adding New Tests
1. **Identify the test category** (unit/integration/component)
2. **Follow existing patterns** in the relevant test file
3. **Use appropriate mocking strategy**:
   - Mock external dependencies (Clerk, Supabase)
   - Don't mock the code under test
   - Use consistent mock setup

### Mock Strategy
```typescript
// External dependencies - always mock
jest.mock("@clerk/nextjs")
jest.mock("@/app/actions/signup-actions")
jest.mock("@/utils/user-claims-client")

// Internal code - don't mock (test the real implementation)
// ❌ Don't do this:
// jest.mock("@/contexts/signup-preferences-context")

// ✅ Do this instead:
import { useSignupPreferences } from "@/contexts/signup-preferences-context"
```

### Test Data Patterns
```typescript
// Consistent test user
const mockUser = { id: "test-user-id" };

// Consistent preferences
const mockPreferences = {
  acceptedTerms: true,
  subscribeNewsletter: false
};

// SessionStorage mock
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
};
```

### Common Test Utilities
```typescript
// Test wrapper for context
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <SignupPreferencesProvider>{children}</SignupPreferencesProvider>
);

// Async testing pattern
await waitFor(() => {
  expect(mockFunction).toHaveBeenCalledWith(expectedValue);
});
```

## Debugging Tests

### Common Issues
1. **Async timing issues**: Use `waitFor()` for async operations
2. **Mock not working**: Check mock setup in `beforeEach`
3. **Context errors**: Ensure `TestWrapper` is used
4. **SessionStorage issues**: Verify mock implementation

### Debug Commands
```bash
# Run single test with debug info
yarn test signup-preferences --testNamePattern="specific test name" --verbose

# Run with console output
yarn test signup-preferences --silent=false

# Generate coverage report
yarn test signup-preferences --coverage --coverageReporters=html
```

### Test Environment
- **Node.js**: Tests run in Node environment with jsdom
- **React Testing Library**: For component testing
- **Jest**: Test runner and assertion library
- **Mocks**: External dependencies mocked consistently

## Performance Considerations

### Test Execution Time
- **Unit tests**: < 1 second each
- **Integration tests**: < 2 seconds each
- **Component tests**: < 3 seconds each
- **Full suite**: < 10 seconds

### Optimization Tips
1. **Parallel execution**: Jest runs tests in parallel by default
2. **Mock efficiency**: Keep mocks lightweight
3. **Test isolation**: Each test cleans up after itself
4. **Selective running**: Use pattern matching for specific tests

## Quality Gates

### Pre-commit Requirements
- ✅ All tests must pass
- ✅ No console errors or warnings
- ✅ TypeScript compilation successful
- ✅ Linting passes

### CI/CD Integration
- ✅ Tests run on every PR
- ✅ Coverage reports generated
- ✅ Test results visible in PR status
- ✅ Failing tests block deployment

## Future Enhancements

### Planned Additions
1. **E2E Tests**: Browser automation tests (see `tests/e2e/signup-preferences-e2e.md`)
2. **Performance Tests**: Load testing for concurrent users
3. **Accessibility Tests**: Screen reader and keyboard navigation
4. **Visual Regression**: Screenshot comparison tests

### Test Metrics to Track
- **Test execution time trends**
- **Flaky test identification**
- **Coverage percentage over time**
- **Test maintenance effort**

## Troubleshooting

### Test Failures
1. **Check mock setup**: Ensure all mocks are properly configured
2. **Verify test isolation**: Each test should be independent
3. **Review async handling**: Use proper async/await patterns
4. **Check TypeScript**: Ensure types are correct

### Common Error Messages
- `"useClerk can only be used within ClerkProvider"`: Add proper mocking
- `"Cannot read property of undefined"`: Check mock return values
- `"Test timeout"`: Add proper async handling with `waitFor()`
- `"Context not found"`: Wrap component in `TestWrapper`

## Contact and Support

For questions about the testing strategy or specific test failures:
1. **Review this documentation first**
2. **Check existing test patterns**
3. **Consult the team for complex scenarios**
4. **Update documentation when adding new patterns**

---

**Last Updated**: Current implementation
**Test Coverage**: 78 tests covering all core functionality
**Status**: ✅ All tests passing, ready for production
