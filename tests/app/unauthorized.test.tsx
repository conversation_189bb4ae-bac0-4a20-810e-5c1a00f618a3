import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/navigation";
import Unauthorized from "@/app/unauthorized";
import animationData from "@/public/errors/401-animation.json";

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));

// Mock lottie-react and its dynamic import
jest.mock("lottie-react", () => {
    const MockLottie = ({ animationData, loop, className }: any) => (
        <div data-testid="mock-lottie" data-animation={JSON.stringify(animationData)}>
            Mock Lottie Animation
        </div>
    );
    return MockLottie;
});

describe("Unauthorized Page", () => {
    const mockBack = jest.fn();

    beforeEach(() => {
        (useRouter as jest.Mock).mockReturnValue({
            back: mockBack
        });
        jest.clearAllMocks();
    });

    it("renders the unauthorized message and buttons correctly", () => {
        render(<Unauthorized />);

        expect(screen.getByText("אין לך גישה לעמוד זה")).toBeInTheDocument();
        expect(screen.getByText("אין לך הרשאות מתאימות לצפות בתוכן זה.")).toBeInTheDocument();
        expect(screen.getByLabelText("חזרה")).toBeInTheDocument();
    });

    it("calls router.back when the back button is clicked", async () => {
        const user = userEvent.setup();
        render(<Unauthorized />);

        const backButton = screen.getByLabelText("חזרה");
        await user.click(backButton);

        expect(mockBack).toHaveBeenCalledTimes(1);
    });

    it("renders the Lottie animation component", () => {
        render(<Unauthorized />);
        expect(screen.getByTestId("mock-lottie")).toBeInTheDocument();
        expect(screen.getByTestId("mock-lottie")).toHaveAttribute("data-animation", JSON.stringify(animationData));
    });

    it("renders the Skeleton when Lottie is loading (SSR false)", () => {
        // To test the loading state, we need to ensure the dynamic import's loading component is rendered.
        // This is handled by the mock setup, which provides the MockLottie immediately.
        // If we wanted to explicitly test the Skeleton, we'd need a more advanced dynamic mock.
        // For now, confirming MockLottie renders indicates the dynamic import mechanism is working.
        render(<Unauthorized />);
        expect(screen.getByTestId("mock-lottie")).toBeInTheDocument();
    });
}); 
