import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import "@testing-library/jest-dom";

import HomeLayout, { metadata } from "../../../app/(home)/layout";

jest.mock("@/components/layout/navbar", () => ({
    Navbar: () => <div data-testid="navbar">Navbar</div>
}));

jest.mock("@/components/layout/site-footer", () => ({
    SiteFooter: () => <div data-testid="site-footer">SiteFooter</div>
}));

jest.mock("@/components/ui/banner", () => ({
    Banner: () => <div data-testid="banner">Banner</div>
}));

jest.mock("@vercel/analytics/react", () => ({
    Analytics: () => <div data-testid="analytics">Analytics</div>
}));

jest.mock("@vercel/speed-insights/next", () => ({
    SpeedInsights: () => <div data-testid="speed-insights">SpeedInsights</div>
}));

describe("HomeLayout", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders all layout components", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        expect(screen.getByTestId("banner")).toBeInTheDocument();
        expect(screen.getByTestId("navbar")).toBeInTheDocument();
        expect(screen.getByTestId("site-footer")).toBeInTheDocument();
        expect(screen.getByTestId("analytics")).toBeInTheDocument();
        expect(screen.getByTestId("speed-insights")).toBeInTheDocument();
        expect(screen.getByTestId("home-children")).toBeInTheDocument();
    });

    it("renders children in main element with correct padding", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const mainElement = screen.getByRole("main");
        expect(mainElement).toBeInTheDocument();
        expect(mainElement).toHaveClass("flex-1", "pt-16");
        expect(mainElement).toContainElement(screen.getByTestId("home-children"));
    });

    it("has correct RTL direction on root container", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const rootContainer = screen.getByTestId("home-children").closest('div[dir="rtl"]');
        expect(rootContainer).toBeInTheDocument();
        expect(rootContainer).toHaveAttribute("dir", "rtl");
    });

    it("applies correct CSS classes to root container", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const rootContainer = screen.getByTestId("home-children").closest('div[dir="rtl"]');
        expect(rootContainer).toHaveClass("min-h-screen", "bg-background", "font-sans", "antialiased");
    });

    it("applies correct flex structure", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const innerContainer = screen.getByTestId("home-children").closest(".flex.min-h-screen.flex-col");
        expect(innerContainer).toBeInTheDocument();
        expect(innerContainer).toHaveClass("flex", "min-h-screen", "flex-col");
    });

    it("renders navbar with relative positioning", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const navbarContainer = screen.getByTestId("navbar").closest(".relative");
        expect(navbarContainer).toBeInTheDocument();
        expect(navbarContainer).toHaveClass("relative");
    });

    it("renders site footer with correct margin", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        const footerContainer = screen.getByTestId("site-footer").closest("span");
        expect(footerContainer).toBeInTheDocument();
        expect(footerContainer).toHaveClass("mt-12");
    });

    it("has correct component hierarchy", () => {
        render(
            <HomeLayout>
                <div data-testid="home-children">Test content</div>
            </HomeLayout>
        );

        // Check that banner and navbar are in the header section
        const headerSection = screen.getByTestId("banner").closest(".flex.flex-col");
        expect(headerSection).toBeInTheDocument();
        expect(headerSection).toContainElement(screen.getByTestId("banner"));
        expect(headerSection).toContainElement(screen.getByTestId("navbar"));

        // Check that main content is separate
        const mainElement = screen.getByRole("main");
        expect(mainElement).not.toContainElement(screen.getByTestId("banner"));
        expect(mainElement).not.toContainElement(screen.getByTestId("navbar"));
    });

    it("renders multiple children correctly", () => {
        render(
            <HomeLayout>
                <div data-testid="child-1">Child 1</div>
                <div data-testid="child-2">Child 2</div>
                <div data-testid="child-3">Child 3</div>
            </HomeLayout>
        );

        expect(screen.getByTestId("child-1")).toBeInTheDocument();
        expect(screen.getByTestId("child-2")).toBeInTheDocument();
        expect(screen.getByTestId("child-3")).toBeInTheDocument();
    });
});

describe("HomeLayout metadata", () => {
    it("exports correct metadata", () => {
        expect(metadata).toEqual({
            dir: "rtl"
        });
    });
});
