import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import "@testing-library/jest-dom";

import ScholarshipNotFound from "../../../../app/(home)/scholarships/not-found";

jest.mock("next/link", () => ({
    __esModule: true,
    default: ({ href, children, ...props }: { href: string; children: React.ReactNode }) => (
        <a href={href} {...props} data-testid="link">
            {children}
        </a>
    )
}));

jest.mock("@/components/ui/button", () => ({
    Button: ({ asChild, children, ...props }: { asChild?: boolean; children: React.ReactNode }) => (
        <button {...props} data-testid="button">
            {children}
        </button>
    )
}));

describe("ScholarshipNotFound", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the not found page correctly", () => {
        render(<ScholarshipNotFound />);

        expect(screen.getByText("לא נמצאה מלגה")).toBeInTheDocument();
        expect(
            screen.getByText("מצטערים, לא מצאנו את המלגה שחיפשת. ייתכן שהקישור שגוי או שהמלגה כבר לא זמינה.")
        ).toBeInTheDocument();
        expect(screen.getByTestId("button")).toBeInTheDocument();
        expect(screen.getByTestId("link")).toBeInTheDocument();
    });

    it("displays correct heading with proper styling", () => {
        render(<ScholarshipNotFound />);

        const heading = screen.getByRole("heading", { level: 1 });
        expect(heading).toBeInTheDocument();
        expect(heading).toHaveTextContent("לא נמצאה מלגה");
        expect(heading).toHaveClass("text-4xl", "font-extrabold", "tracking-tighter", "sm:text-5xl");
    });

    it("displays correct description text", () => {
        render(<ScholarshipNotFound />);

        const description = screen.getByText(
            "מצטערים, לא מצאנו את המלגה שחיפשת. ייתכן שהקישור שגוי או שהמלגה כבר לא זמינה."
        );
        expect(description).toBeInTheDocument();
        expect(description).toHaveClass("mx-auto", "max-w-[600px]", "text-muted-foreground", "md:text-xl");
    });

    it("renders link with correct href to scholarships page", () => {
        render(<ScholarshipNotFound />);

        const link = screen.getByTestId("link");
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute("href", "/scholarships");
        expect(link).toHaveTextContent("חזרה לרשימת המלגות");
    });

    it("renders button with correct text", () => {
        render(<ScholarshipNotFound />);

        const button = screen.getByTestId("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent("חזרה לרשימת המלגות");
    });

    it("has correct container structure and styling", () => {
        render(<ScholarshipNotFound />);

        const container = screen.getByRole("heading").closest("div.flex.min-h-\\[calc\\(100vh-80px\\)\\]");
        expect(container).toBeInTheDocument();
        expect(container).toHaveClass(
            "flex",
            "min-h-[calc(100vh-80px)]",
            "flex-col",
            "items-center",
            "justify-center",
            "space-y-6",
            "text-center"
        );
    });

    it("has correct content spacing structure", () => {
        render(<ScholarshipNotFound />);

        const contentContainer = screen.getByRole("heading").closest(".space-y-2");
        expect(contentContainer).toBeInTheDocument();
        expect(contentContainer).toHaveClass("space-y-2");
    });

    it("renders all elements in correct hierarchy", () => {
        render(<ScholarshipNotFound />);

        const mainContainer = screen
            .getByRole("heading")
            .closest("div.flex.min-h-\\[calc\\(100vh-80px\\)\\]") as HTMLElement;
        const contentContainer = screen.getByRole("heading").closest(".space-y-2") as HTMLElement;
        const heading = screen.getByRole("heading");
        const description = screen.getByText(
            "מצטערים, לא מצאנו את המלגה שחיפשת. ייתכן שהקישור שגוי או שהמלגה כבר לא זמינה."
        );
        const button = screen.getByTestId("button");

        // Check hierarchy
        expect(mainContainer).toContainElement(contentContainer);
        expect(mainContainer).toContainElement(button);
        expect(contentContainer).toContainElement(heading);
        expect(contentContainer).toContainElement(description);
    });

    it("has accessible heading structure", () => {
        render(<ScholarshipNotFound />);

        const heading = screen.getByRole("heading", { level: 1 });
        expect(heading).toBeInTheDocument();
        expect(heading).toHaveTextContent("לא נמצאה מלגה");
    });

    it("has descriptive alt text for screen readers", () => {
        render(<ScholarshipNotFound />);

        const description = screen.getByText(
            "מצטערים, לא מצאנו את המלגה שחיפשת. ייתכן שהקישור שגוי או שהמלגה כבר לא זמינה."
        );
        expect(description).toBeInTheDocument();

        // The paragraph provides context for screen readers
        expect(description.tagName).toBe("P");
    });
});
