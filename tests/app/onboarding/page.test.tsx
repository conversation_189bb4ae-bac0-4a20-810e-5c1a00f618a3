import { render, screen, waitFor } from "@testing-library/react";
import { currentUser } from "@clerk/nextjs/server";

import OnboardingPage from "@/app/onboarding/page";
import { createClientFromRequest } from "@/utils/supabase/server";
import { getUserClaim } from "@/utils/user-claims-client";
import { fetchQuestionsData } from "@/app/actions/dynamic-questions-form-actions";
import OnboardingClient from "@/app/onboarding/onboarding-client";

jest.mock("@clerk/nextjs/server", () => ({
    currentUser: jest.fn()
}));

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("@/utils/user-claims-client", () => ({
    getUserClaim: jest.fn(),
    UserClaimKey: {
        ONBOARDING_STAGE: "onboarding_stage"
    }
}));

jest.mock("@/app/actions/dynamic-questions-form-actions", () => ({
    fetchQuestionsData: jest.fn()
}));

jest.mock("@/app/onboarding/onboarding-client", () => {
    return jest.fn(({ prefetchedQuestions, initialStage }) => (
        <div>
            <h1>Onboarding Client</h1>
            <p>Stage: {initialStage}</p>
            <p>Questions: {JSON.stringify(prefetchedQuestions)}</p>
        </div>
    ));
});

const currentUserMock = currentUser as unknown as jest.Mock;
const createClientFromRequestMock = createClientFromRequest as unknown as jest.Mock;
const getUserClaimMock = getUserClaim as unknown as jest.Mock;
const fetchQuestionsDataMock = fetchQuestionsData as unknown as jest.Mock;
const OnboardingClientMock = OnboardingClient as unknown as jest.Mock;

describe("OnboardingPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    const renderPage = async () => {
        const Page = await OnboardingPage();
        return render(Page);
    };

    it("should display 'Not logged in' when user is not found", async () => {
        currentUserMock.mockResolvedValue(null);
        await renderPage();
        expect(screen.getByText("Not logged in")).toBeInTheDocument();
    });

    it("should display failure message when question fetching fails", async () => {
        currentUserMock.mockResolvedValue({ id: "user_123" });
        fetchQuestionsDataMock.mockResolvedValue({ data: null });
        getUserClaimMock.mockResolvedValue("personal_details");

        await renderPage();
        expect(screen.getByText("Failed to load onboarding data.")).toBeInTheDocument();
    });

    it("should fetch data and render OnboardingClient with correct props", async () => {
        const mockUser = { id: "user_123" };
        const mockQuestions = [{ id: 1, text: "Question 1" }];
        const mockStage = "personal_details";

        currentUserMock.mockResolvedValue(mockUser);
        fetchQuestionsDataMock.mockResolvedValue({ data: mockQuestions });
        getUserClaimMock.mockResolvedValue(mockStage);
        createClientFromRequestMock.mockReturnValue({}); // Mock Supabase client

        await renderPage();

        await waitFor(() => {
            expect(fetchQuestionsDataMock).toHaveBeenCalledWith(
                ["personal_details", "specific_scholarship", "data_entry"],
                mockUser.id
            );
            expect(getUserClaimMock).toHaveBeenCalledWith(expect.anything(), mockUser.id, "onboarding_stage");
            expect(OnboardingClientMock).toHaveBeenCalledWith(
                {
                    prefetchedQuestions: mockQuestions,
                    initialStage: mockStage
                },
                undefined
            );
        });

        expect(screen.getByText("Onboarding Client")).toBeInTheDocument();
        expect(screen.getByText(`Stage: ${mockStage}`)).toBeInTheDocument();
        expect(screen.getByText(`Questions: ${JSON.stringify(mockQuestions)}`)).toBeInTheDocument();
    });
});
