import { render, screen } from "@testing-library/react";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { ReactNode } from "react";

import OnboardingLayout from "@/app/onboarding/layout";
import { type CustomJwtSessionClaims } from "@/types/auth";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("next/navigation", () => ({
    redirect: jest.fn()
}));

jest.mock("@/components/auth/logout-button", () => ({
    LogoutButton: () => <button>Logout</button>
}));

jest.mock("@/components/layout/site-logo", () => ({
    SiteLogo: () => <div>Site Logo</div>
}));

jest.mock("@/components/ui/banner", () => ({
    Banner: () => <div>Banner</div>
}));

const authMock = auth as unknown as jest.Mock;
const redirectMock = redirect as unknown as jest.Mock;

describe("OnboardingLayout", () => {
    const mockChildren = <div>Onboarding Content</div>;

    const renderLayout = async (children: ReactNode) => {
        const LayoutComponent = await OnboardingLayout({ children });
        return render(LayoutComponent);
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should render children and layout components when onboarding is not complete", async () => {
        authMock.mockReturnValue({
            sessionClaims: {
                metadata: { onboardingComplete: false }
            } as CustomJwtSessionClaims
        });

        await renderLayout(mockChildren);

        expect(screen.getByText("Onboarding Content")).toBeInTheDocument();
        expect(screen.getByText("Site Logo")).toBeInTheDocument();
        expect(screen.getByText("Logout")).toBeInTheDocument();
        expect(screen.getByText("Banner")).toBeInTheDocument();
        expect(redirectMock).not.toHaveBeenCalled();
    });

    it("should redirect to home when onboarding is complete", async () => {
        authMock.mockReturnValue({
            sessionClaims: {
                metadata: { onboardingComplete: true }
            } as CustomJwtSessionClaims
        });

        await renderLayout(mockChildren);
        expect(redirectMock).toHaveBeenCalledWith("/");
    });

    it("should handle session with no metadata gracefully", async () => {
        authMock.mockReturnValue({
            sessionClaims: {} as CustomJwtSessionClaims
        });

        await renderLayout(mockChildren);
        expect(screen.getByText("Onboarding Content")).toBeInTheDocument();
        expect(redirectMock).not.toHaveBeenCalled();
    });

    it("should handle session with empty metadata gracefully", async () => {
        authMock.mockReturnValue({
            sessionClaims: { metadata: {} } as CustomJwtSessionClaims
        });

        await renderLayout(mockChildren);
        expect(screen.getByText("Onboarding Content")).toBeInTheDocument();
        expect(redirectMock).not.toHaveBeenCalled();
    });
});
