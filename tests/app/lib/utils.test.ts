import { sanitizeHtml } from "@/app/lib/utils";

describe("sanitizeHtml", () => {
    it("should escape ampersands", () => {
        expect(sanitizeHtml("a & b")).toBe("a &amp; b");
    });

    it("should escape less than signs", () => {
        expect(sanitizeHtml("a < b")).toBe("a &lt; b");
    });

    it("should escape greater than signs", () => {
        expect(sanitizeHtml("a > b")).toBe("a &gt; b");
    });

    it("should escape double quotes", () => {
        expect(sanitizeHtml('a " b')).toBe("a &quot; b");
    });

    it("should escape single quotes", () => {
        expect(sanitizeHtml("a ' b")).toBe("a &#039; b");
    });

    it("should escape multiple special characters in a script tag", () => {
        const input = "<script>alert('XSS & injection')</script>";
        const expected = "&lt;script&gt;alert(&#039;XSS &amp; injection&#039;)&lt;/script&gt;";
        expect(sanitizeHtml(input)).toBe(expected);
    });

    it("should return an empty string for an empty input", () => {
        expect(sanitizeHtml("")).toBe("");
    });

    it("should not alter a string with no special HTML characters", () => {
        const input = "This is a safe string with numbers 123.";
        expect(sanitizeHtml(input)).toBe(input);
    });
});
