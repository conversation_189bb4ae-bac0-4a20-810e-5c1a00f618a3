import React from "react";
import { render, screen, cleanup } from "@testing-library/react";

jest.mock("@/components/auth/auth-form", () => {
    return {
        AuthForm: ({ mode }: { mode: "signin" | "signup" }) => (
            <div data-testid="auth-form" data-mode={mode}>
                Mocked AuthForm - {mode}
            </div>
        )
    };
});

jest.mock("@/components/auth/auth-layout", () => {
    return {
        AuthLayout: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-layout">{children}</div>
    };
});

jest.mock("@/components/layout/site-logo", () => {
    return {
        SiteLogo: ({ href }: { href: string }) => (
            <a href={href} data-testid="site-logo">
                <img alt="Milgapo Logo" />
            </a>
        )
    };
});

jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
    usePathname: jest.fn()
}));

import Signup from "@/app/(auth)/signup/[[...sign-up]]/page";

describe("Signup Page", () => {
    afterEach(cleanup);

    it("should render the signup page with the site logo and auth form", () => {
        render(<Signup />);
        expect(screen.getByTestId("auth-layout")).toBeInTheDocument();
        expect(screen.getByTestId("site-logo")).toBeInTheDocument();
        expect(screen.getByTestId("auth-form")).toBeInTheDocument();
    });

    it("should render the AuthForm component with signup mode", () => {
        render(<Signup />);
        const authForm = screen.getByTestId("auth-form");
        expect(authForm).toBeInTheDocument();
        expect(authForm).toHaveAttribute("data-mode", "signup");
    });

    it("should render the site logo with a link to the homepage", () => {
        render(<Signup />);
        const logoLink = screen.getByTestId("site-logo");
        expect(logoLink).toBeInTheDocument();
        expect(logoLink).toHaveAttribute("href", "/");
    });
});
