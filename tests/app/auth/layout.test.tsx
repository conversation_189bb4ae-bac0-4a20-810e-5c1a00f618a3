import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import "@testing-library/jest-dom";

import AuthLayout from "../../../app/(auth)/layout";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("next/navigation", () => ({
    redirect: jest.fn()
}));

jest.mock("@/components/PostHogProvider", () => ({
    PostHogProvider: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="posthog-provider">{children}</div>
    )
}));

jest.mock("@vercel/analytics/react", () => ({
    Analytics: () => <div data-testid="analytics">Analytics</div>
}));

jest.mock("@vercel/speed-insights/next", () => ({
    SpeedInsights: () => <div data-testid="speed-insights">SpeedInsights</div>
}));

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockRedirect = redirect as jest.MockedFunction<typeof redirect>;

describe("AuthLayout", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("redirects to home when user is authenticated", async () => {
        mockAuth.mockResolvedValue({ userId: "user123" } as any);

        await AuthLayout({
            children: <div>Test content</div>
        });

        expect(mockRedirect).toHaveBeenCalledWith("/");
    });

    it("renders layout when user is not authenticated", async () => {
        mockAuth.mockResolvedValue({ userId: null } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        expect(mockRedirect).not.toHaveBeenCalled();
        expect(screen.getByTestId("posthog-provider")).toBeInTheDocument();
        expect(screen.getByTestId("auth-children")).toBeInTheDocument();
        expect(screen.getByTestId("analytics")).toBeInTheDocument();
        expect(screen.getByTestId("speed-insights")).toBeInTheDocument();
    });

    it("renders with correct CSS classes and RTL direction", async () => {
        mockAuth.mockResolvedValue({ userId: null } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        const mainContainer = screen.getByTestId("auth-children").closest('div[dir="rtl"]');
        expect(mainContainer).toBeInTheDocument();
        expect(mainContainer).toHaveClass("text-foreground", "flex", "flex-col", "min-h-screen", "overflow-hidden");
        expect(mainContainer).toHaveClass("bg-gradient-to-b", "from-secondary/70", "to-background/95");
        expect(mainContainer).toHaveAttribute("dir", "rtl");
    });

    it("wraps children with PostHogProvider", async () => {
        mockAuth.mockResolvedValue({ userId: null } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        const posthogProvider = screen.getByTestId("posthog-provider");
        expect(posthogProvider).toBeInTheDocument();
        expect(posthogProvider).toContainElement(screen.getByTestId("auth-children"));
    });

    it("includes analytics and speed insights components", async () => {
        mockAuth.mockResolvedValue({ userId: null } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        expect(screen.getByTestId("analytics")).toBeInTheDocument();
        expect(screen.getByTestId("speed-insights")).toBeInTheDocument();
    });

    it("handles undefined userId as not authenticated", async () => {
        mockAuth.mockResolvedValue({ userId: null } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        expect(mockRedirect).not.toHaveBeenCalled();
        expect(screen.getByTestId("auth-children")).toBeInTheDocument();
    });

    it("handles empty string userId as not authenticated", async () => {
        mockAuth.mockResolvedValue({ userId: "" } as any);

        const result = await AuthLayout({
            children: <div data-testid="auth-children">Test content</div>
        });

        render(result);

        expect(mockRedirect).not.toHaveBeenCalled();
        expect(screen.getByTestId("auth-children")).toBeInTheDocument();
    });
});
