import { GET } from "@/app/auth/callback/route";
import { auth } from "@clerk/nextjs/server";
import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { isAdminFromSessionClaims } from "@/lib/org-role";
import { NextResponse } from "next/server";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    getCurrentUserSubscription: jest.fn()
}));

jest.mock("@/lib/org-role", () => ({
    isAdminFromSessionClaims: jest.fn()
}));

const mockedRedirect = jest.fn();
jest.mock(
    "next/server",
    () => ({
        NextResponse: {
            redirect: (url: string) => mockedRedirect(url)
        }
    }),
    { virtual: true }
);

const mockedAuth = auth as unknown as jest.Mock;
const mockedGetCurrentUserSubscription = getCurrentUserSubscription as unknown as jest.Mock;
const mockedIsAdminFromSessionClaims = isAdminFromSessionClaims as unknown as jest.Mock;

const VERCEL_DEPLOY_URL = process.env.NEXT_PUBLIC_VERCEL_URL || process.env.VERCEL_URL;
const BASE_URL = VERCEL_DEPLOY_URL ? `https://${VERCEL_DEPLOY_URL}` : "http://localhost:3000";

describe("Auth Callback Route - GET", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should redirect to /login if userId is not present", async () => {
        mockedAuth.mockResolvedValue({ userId: null, sessionClaims: null });

        await GET();

        expect(mockedRedirect).toHaveBeenCalledWith(`${BASE_URL}/login`);
    });

    it("should redirect an admin user to /admin", async () => {
        mockedAuth.mockResolvedValue({ userId: "user_admin_123", sessionClaims: {} });
        mockedIsAdminFromSessionClaims.mockReturnValue(true);

        await GET();

        expect(mockedRedirect).toHaveBeenCalledWith(`${BASE_URL}/admin`);
    });

    it("should redirect a non-admin user with a free plan to /subscriptions", async () => {
        mockedAuth.mockResolvedValue({ userId: "user_free_123", sessionClaims: {} });
        mockedIsAdminFromSessionClaims.mockReturnValue(false);
        mockedGetCurrentUserSubscription.mockResolvedValue({ planType: "free" });

        await GET();

        expect(mockedRedirect).toHaveBeenCalledWith(`${BASE_URL}/subscriptions`);
    });

    it("should redirect a non-admin user without a subscription to /subscriptions", async () => {
        mockedAuth.mockResolvedValue({ userId: "user_no_sub_123", sessionClaims: {} });
        mockedIsAdminFromSessionClaims.mockReturnValue(false);
        mockedGetCurrentUserSubscription.mockResolvedValue(null);

        await GET();

        expect(mockedRedirect).toHaveBeenCalledWith(`${BASE_URL}/subscriptions`);
    });

    it("should redirect a non-admin user with a paid subscription to /dashboard", async () => {
        mockedAuth.mockResolvedValue({ userId: "user_paid_123", sessionClaims: {} });
        mockedIsAdminFromSessionClaims.mockReturnValue(false);
        mockedGetCurrentUserSubscription.mockResolvedValue({ planType: "paid" });

        await GET();

        expect(mockedRedirect).toHaveBeenCalledWith(`${BASE_URL}/dashboard`);
    });
});
