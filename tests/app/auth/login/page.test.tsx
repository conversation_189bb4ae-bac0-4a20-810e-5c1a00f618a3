import React from "react";
import { render, screen, cleanup } from "@testing-library/react";

jest.mock("@/components/auth/auth-form", () => {
    return {
        AuthForm: ({ mode }: { mode: "signin" | "signup" }) => (
            <div data-testid="auth-form" data-mode={mode}>
                Mocked AuthForm - {mode}
            </div>
        )
    };
});

jest.mock("@/components/auth/auth-layout", () => {
    return {
        AuthLayout: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-layout">{children}</div>
    };
});

jest.mock("@/components/layout/site-logo", () => {
    return {
        SiteLogo: ({ href }: { href: string }) => (
            <a href={href} data-testid="site-logo">
                <img alt="Milgapo Logo" />
            </a>
        )
    };
});

jest.mock("next/navigation", () => ({
    useRouter: jest.fn(),
    usePathname: jest.fn()
}));

import Login from "@/app/(auth)/login/[[...sign-in]]/page";

const originalConsoleError = console.error;

beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation((...args) => {
        const messageText = args.map((arg) => String(arg)).join(" ");
        if (messageText.includes("non-boolean attribute")) {
            return;
        }
        originalConsoleError(...(args as Parameters<typeof console.error>));
    });
});

afterAll(() => {
    (console.error as jest.Mock).mockRestore();
});

describe("Login Page", () => {
    afterEach(cleanup);

    it("should render the login page with the site logo and auth form", () => {
        render(<Login />);
        expect(screen.getByTestId("auth-layout")).toBeInTheDocument();
        expect(screen.getByTestId("site-logo")).toBeInTheDocument();
        expect(screen.getByTestId("auth-form")).toBeInTheDocument();
    });

    it("should render the AuthForm component with signin mode", () => {
        render(<Login />);
        const authForm = screen.getByTestId("auth-form");
        expect(authForm).toBeInTheDocument();
        expect(authForm).toHaveAttribute("data-mode", "signin");
    });

    it("should render the site logo with a link to the homepage", () => {
        render(<Login />);
        const logoLink = screen.getByTestId("site-logo");
        expect(logoLink).toBeInTheDocument();
        expect(logoLink).toHaveAttribute("href", "/");
    });
});
