import { useUser } from "@clerk/nextjs";
import { render, screen, waitFor, act } from "@testing-library/react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import "@testing-library/jest-dom";

import { UserSubscriptionWithPlan } from "@/lib/subscription-constants";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";

import AccountLayout from "../../../app/(dashboard)/layout";

jest.mock("@clerk/nextjs", () => ({
    useUser: jest.fn()
}));

jest.mock("@/app/actions/subscriptions-actions", () => ({
    getCurrentUserSubscription: jest.fn()
}));

jest.mock("@/components/common/client-only", () => ({
    ClientOnly: ({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) => {
        return <div data-testid="client-only">{children}</div>;
    }
}));

jest.mock("@/components/common/loading-icon", () => ({
    LoadingIcon: ({ text }: { text: string }) => <div data-testid="loading-icon">{text}</div>
}));

jest.mock("@/components/layout/plan-access-guard", () => ({
    PlanAccessGuard: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="plan-access-guard">{children}</div>
    )
}));

jest.mock("@/components/layout/sidebar/app-sidebar", () => ({
    AppSidebar: ({ navMain, navGroups, navSecondary }: { navMain: any; navGroups: any; navSecondary: any }) => (
        <div data-testid="app-sidebar">
            <div data-testid="nav-main">{JSON.stringify(navMain)}</div>
            <div data-testid="nav-groups">{JSON.stringify(navGroups)}</div>
            <div data-testid="nav-secondary">{JSON.stringify(navSecondary)}</div>
        </div>
    )
}));

jest.mock("@/components/layout/sidebar/sidebar-skeleton", () => ({
    SidebarSkeleton: () => <div data-testid="sidebar-skeleton">Loading sidebar...</div>
}));

jest.mock("@/components/ui/banner", () => ({
    Banner: () => <div data-testid="banner">Banner</div>
}));

jest.mock("@/components/ui/subscription-banner", () => ({
    SubscriptionBanner: () => <div data-testid="subscription-banner">Subscription Banner</div>
}));

jest.mock("@/components/ui/sidebar", () => ({
    SidebarProvider: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="sidebar-provider">{children}</div>
    ),
    SidebarTrigger: ({ children }: { children: React.ReactNode }) => (
        <button data-testid="sidebar-trigger">{children}</button>
    )
}));

jest.mock("@/config/dashboard", () => ({
    sidebarData: {
        navMain: [{ title: "Dashboard", url: "/dashboard", name: "Dashboard" }],
        personalDataGroup: {
            title: "Personal Data",
            items: [{ name: "Profile", url: "/profile", requiredPlan: "free" }]
        },
        scholarshipTrackingGroup: {
            title: "Scholarships",
            items: [{ name: "Applications", url: "/applications", requiredPlan: "milgapro" }]
        },
        navSecondary: [{ title: "Settings", url: "/settings", name: "Settings" }]
    }
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockGetCurrentUserSubscription = getCurrentUserSubscription as jest.MockedFunction<
    typeof getCurrentUserSubscription
>;

describe("AccountLayout", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("shows sidebar skeleton when user is not loaded", () => {
        mockUseUser.mockReturnValue({
            isLoaded: false,
            user: null,
            isSignedIn: false
        } as any);

        render(
            <AccountLayout>
                <div>Test content</div>
            </AccountLayout>
        );

        expect(screen.getByTestId("sidebar-skeleton")).toBeInTheDocument();
        expect(screen.getByTestId("sidebar-skeleton")).toHaveTextContent("Loading sidebar...");
    });

    it("shows sidebar skeleton when subscription is loading", () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        render(
            <AccountLayout>
                <div>Test content</div>
            </AccountLayout>
        );

        expect(screen.getByTestId("sidebar-skeleton")).toBeInTheDocument();
    });

    it("fetches user subscription when user is loaded", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapo-elite",
            planType: "elite",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 350,
            paid_amount: 350,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        await act(async () => {
            render(
                <AccountLayout>
                    <div>Test content</div>
                </AccountLayout>
            );
        });

        await waitFor(() => {
            expect(mockGetCurrentUserSubscription).toHaveBeenCalledTimes(1);
        });
    });

    it("defaults to free plan when subscription fetch fails", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        mockGetCurrentUserSubscription.mockRejectedValue(new Error("Network error"));

        const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

        await act(async () => {
            render(
                <AccountLayout>
                    <div>Test content</div>
                </AccountLayout>
            );
        });

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith("Error fetching subscription in AccountLayout:", expect.any(Error));
        });

        consoleSpy.mockRestore();
    });

    it("defaults to free plan when subscription is null", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue(null);

        await act(async () => {
            render(
                <AccountLayout>
                    <div>Test content</div>
                </AccountLayout>
            );
        });

        await waitFor(() => {
            expect(mockGetCurrentUserSubscription).toHaveBeenCalledTimes(1);
        });
    });

    it("renders all layout components when loaded", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapro",
            planType: "milgapro",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 280,
            paid_amount: 280,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        await act(async () => {
            render(
                <AccountLayout>
                    <div data-testid="children">Test content</div>
                </AccountLayout>
            );
        });

        await waitFor(() => {
            expect(screen.getByTestId("app-sidebar")).toBeInTheDocument();
        });

        expect(screen.getByTestId("banner")).toBeInTheDocument();
        expect(screen.getByTestId("subscription-banner")).toBeInTheDocument();
        expect(screen.getByTestId("sidebar-trigger")).toBeInTheDocument();
        expect(screen.getByTestId("plan-access-guard")).toBeInTheDocument();
        expect(screen.getByTestId("children")).toBeInTheDocument();
    });

    it("processes nav groups correctly based on user plan", async () => {
        mockUseUser.mockReturnValue({
            isLoaded: true,
            user: { id: "user123" },
            isSignedIn: true
        } as any);

        mockGetCurrentUserSubscription.mockResolvedValue({
            id: "sub123",
            user_id: "user123",
            plan_id: "milgapro",
            planType: "milgapro",
            is_active: true,
            start_date: "2023-01-01",
            expiration_date: "2024-01-01",
            created_at: "2023-01-01",
            updated_at: "2023-01-01",
            coupon_id: null,
            plan_price: 280,
            paid_amount: 280,
            payment_details: null,
            transaction_id: null,
            order_id: null
        });

        await act(async () => {
            render(
                <AccountLayout>
                    <div>Test content</div>
                </AccountLayout>
            );
        });

        await waitFor(() => {
            expect(screen.getByTestId("app-sidebar")).toBeInTheDocument();
        });

        const navGroups = screen.getByTestId("nav-groups");
        expect(navGroups).toBeInTheDocument();
    });
});
