/** @jest-environment node */

import { POST } from "@/app/api/validate-coupon/route";
import { validateAndApplyCoupon } from "@/app/actions/subscriptions-actions";
import { getPaymentErrorMessage } from "@/utils/payment-errors";
import { NextRequest } from "next/server";

jest.mock("@/app/actions/subscriptions-actions");
jest.mock("@/utils/payment-errors");

describe("POST /api/validate-coupon", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (getPaymentErrorMessage as jest.Mock).mockImplementation((err) => err || "An unexpected error occurred.");
    });

    const createMockRequest = (body: object) => {
        return new NextRequest("http://localhost/api/validate-coupon", {
            method: "POST",
            body: JSON.stringify(body)
        });
    };

    it("successfully validates a coupon and returns discount details", async () => {
        const mockResult = {
            success: true,
            discountValue: 10,
            couponType: "fixed",
            discountApplied: 10,
            finalAmount: 90,
            couponCode: "TEST"
        };
        (validateAndApplyCoupon as jest.Mock).mockResolvedValue(mockResult);

        const req = createMockRequest({ coupon_code: "TEST", total_amount: 100 });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(200);
        expect(json).toEqual(mockResult);
        expect(validateAndApplyCoupon).toHaveBeenCalledWith("TEST", 100);
    });

    it("returns 400 for invalid input parameters", async () => {
        const req = createMockRequest({ coupon_code: "", total_amount: -10 });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.error).toBe("Invalid input parameters.");
    });

    it("returns 400 for invalid JSON format", async () => {
        const req = new NextRequest("http://localhost/api/validate-coupon", {
            method: "POST"
        });
        jest.spyOn(req, "json").mockRejectedValue(new SyntaxError("Invalid JSON"));

        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.error).toBe("Invalid JSON format.");
    });

    it("returns a friendly error if coupon validation fails", async () => {
        (validateAndApplyCoupon as jest.Mock).mockResolvedValue({ success: false, error: "coupon_expired" });

        const req = createMockRequest({ coupon_code: "EXPIRED", total_amount: 100 });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.error).toBe("coupon_expired");
        expect(getPaymentErrorMessage).toHaveBeenCalledWith("coupon_expired");
    });

    it("returns 500 for an unexpected server error", async () => {
        (validateAndApplyCoupon as jest.Mock).mockRejectedValue(new Error("DB error"));

        const req = createMockRequest({ coupon_code: "FAIL", total_amount: 100 });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.error).toBe("An unexpected error occurred.");
        expect(getPaymentErrorMessage).toHaveBeenCalledWith(undefined);
    });
});
