/** @jest-environment node */

import { POST } from "@/app/api/email/route";
import { resend } from "@/lib/resend";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

jest.mock("@/lib/resend", () => ({
    resend: {
        emails: {
            send: jest.fn()
        }
    }
}));

global.fetch = jest.fn();

describe("POST /api/email", () => {
    const mockSupabase = {
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockReturnValue(mockSupabase);
        console.error = jest.fn();
    });

    it("sends email successfully with valid data", async () => {
        mockSupabase.select.mockResolvedValue({
            data: [{ email: "<EMAIL>" }],
            error: null
        });
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: async () => ({ data: { format_valid: true, mx_found: true }, disposable: false })
        });
        (resend.emails.send as jest.Mock).mockResolvedValue({ data: { id: "123" }, error: null });

        const request = new Request("http://localhost/api/email", {
            method: "POST",
            body: JSON.stringify({
                email: "<EMAIL>",
                subject: "Test Subject",
                message: "Test Message"
            })
        });

        const response = await POST(request);
        const json = await response.json();

        expect(response.status).toBe(200);
        expect(json.success).toBe(true);
        expect(resend.emails.send).toHaveBeenCalled();
    });

    it("returns 400 for invalid email format", async () => {
        mockSupabase.select.mockResolvedValue({
            data: [{ email: "<EMAIL>" }],
            error: null
        });
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: async () => ({ data: { format_valid: false, mx_found: true }, disposable: false })
        });

        const request = new Request("http://localhost/api/email", {
            method: "POST",
            body: JSON.stringify({ email: "invalid-email" })
        });

        const response = await POST(request);
        expect(response.status).toBe(400);
    });

    it("returns 500 if supabase fetch fails", async () => {
        mockSupabase.select.mockResolvedValue({
            data: null,
            error: new Error("Supabase error")
        });

        const request = new Request("http://localhost/api/email", {
            method: "POST",
            body: JSON.stringify({ email: "<EMAIL>" })
        });

        const response = await POST(request);
        expect(response.status).toBe(500);
    });

    it("returns 500 if resend fails", async () => {
        mockSupabase.select.mockResolvedValue({
            data: [{ email: "<EMAIL>" }],
            error: null
        });
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: async () => ({ data: { format_valid: true, mx_found: true }, disposable: false })
        });
        (resend.emails.send as jest.Mock).mockResolvedValue({
            data: null,
            error: { message: "Resend error" }
        });

        const request = new Request("http://localhost/api/email", {
            method: "POST",
            body: JSON.stringify({ email: "<EMAIL>" })
        });

        const response = await POST(request);
        expect(response.status).toBe(500);
    });
});
