/** @jest-environment node */

import { GET } from "@/app/api/admin/stats/users/route";
import { clerkClient } from "@clerk/nextjs/server";

interface MockClerkClientUsers {
    getCount: jest.MockedFunction<() => Promise<number>>;
}

interface MockClerkClient {
    users: MockClerkClientUsers;
}

jest.mock("@clerk/nextjs/server", () => ({
    clerkClient: jest.fn()
}));

const mockClerkClient = jest.mocked(clerkClient);

describe("GET /api/admin/stats/users", () => {
    const mockClient: MockClerkClient = {
        users: {
            getCount: jest.fn<Promise<number>, []>()
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockClerkClient.mockResolvedValue(mockClient as unknown as Awaited<ReturnType<typeof clerkClient>>);
        console.error = jest.fn();
    });

    it("returns user count successfully", async () => {
        const expectedCount = 150;
        mockClient.users.getCount.mockResolvedValue(expectedCount);

        const response = await GET();
        const json = await response.json();

        expect(response.status).toBe(200);
        expect(json).toEqual({
            total: expectedCount
        });
        expect(mockClerkClient).toHaveBeenCalledTimes(1);
        expect(mockClient.users.getCount).toHaveBeenCalledTimes(1);
    });

    it("returns zero when no users exist", async () => {
        mockClient.users.getCount.mockResolvedValue(0);

        const response = await GET();
        const json = await response.json();

        expect(response.status).toBe(200);
        expect(json).toEqual({
            total: 0
        });
    });

    it("handles clerkClient initialization error", async () => {
        const errorMessage = "Failed to initialize Clerk client";
        mockClerkClient.mockRejectedValue(new Error(errorMessage));

        const response = await GET();

        expect(response.status).toBe(500);
        expect(await response.text()).toBe("Internal Server Error");
        expect(console.error).toHaveBeenCalledWith("Error fetching Clerk user stats:", expect.any(Error));
    });

    it("handles getCount API error", async () => {
        const errorMessage = "Clerk API error";
        mockClient.users.getCount.mockRejectedValue(new Error(errorMessage));

        const response = await GET();

        expect(response.status).toBe(500);
        expect(await response.text()).toBe("Internal Server Error");
        expect(console.error).toHaveBeenCalledWith("Error fetching Clerk user stats:", expect.any(Error));
    });

    it("handles large user counts", async () => {
        const largeCount = 999999;
        mockClient.users.getCount.mockResolvedValue(largeCount);

        const response = await GET();
        const json = await response.json();

        expect(response.status).toBe(200);
        expect(json).toEqual({
            total: largeCount
        });
    });

    it("handles network timeout error", async () => {
        const timeoutError = new Error("Request timeout");
        timeoutError.name = "TimeoutError";
        mockClient.users.getCount.mockRejectedValue(timeoutError);

        const response = await GET();

        expect(response.status).toBe(500);
        expect(await response.text()).toBe("Internal Server Error");
        expect(console.error).toHaveBeenCalledWith("Error fetching Clerk user stats:", timeoutError);
    });

    it("handles Clerk rate limiting error", async () => {
        const rateLimitError = new Error("Rate limit exceeded");
        rateLimitError.name = "RateLimitError";
        mockClient.users.getCount.mockRejectedValue(rateLimitError);

        const response = await GET();

        expect(response.status).toBe(500);
        expect(await response.text()).toBe("Internal Server Error");
        expect(console.error).toHaveBeenCalledWith("Error fetching Clerk user stats:", rateLimitError);
    });

    it("ensures response headers are set correctly for JSON", async () => {
        mockClient.users.getCount.mockResolvedValue(100);

        const response = await GET();

        expect(response.headers.get("content-type")).toContain("application/json");
    });

    it("verifies error response has correct content type", async () => {
        mockClerkClient.mockRejectedValue(new Error("Test error"));

        const response = await GET();

        expect(response.status).toBe(500);
        expect(response.headers.get("content-type")).toContain("text/plain");
    });
});
