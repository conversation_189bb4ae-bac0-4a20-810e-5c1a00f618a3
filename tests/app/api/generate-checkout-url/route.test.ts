/** @jest-environment node */

import { NextRequest } from "next/server";

jest.mock("@/utils/payment-errors");

global.fetch = jest.fn();

describe("POST /api/generate-checkout-url", () => {
    const mockCustomerInfo = { name: "test", email: "<EMAIL>" };
    const originalEnv = process.env;

    beforeEach(() => {
        jest.resetModules();
        process.env = { ...originalEnv };
        (fetch as jest.Mock).mockClear();
        const { getPaymentErrorMessage, extractCCode } = require("@/utils/payment-errors");
        (extractCCode as jest.Mock).mockReturnValue("err001");
        (getPaymentErrorMessage as jest.Mock).mockImplementation((err) => err || "שגיאה כללית");
        console.error = jest.fn();
    });

    afterAll(() => {
        process.env = originalEnv;
    });

    const createMockRequest = (body: object) =>
        new NextRequest("http://localhost/api/generate-checkout-url", {
            method: "POST",
            body: JSON.stringify(body)
        });

    const setEnvVars = () => {
        process.env.PAYMENT_GATEWAY_MERCHANT_ID = "merchant123";
        process.env.PAYMENT_GATEWAY_API_KEY = "key123";
        process.env.PAYMENT_GATEWAY_PASSP = "pass123";
        process.env.PAYMENT_GATEWAY_SIGN_URL = "https://test-gateway.com/sign";
    };

    it("returns a signed URL on successful request", async () => {
        setEnvVars();
        const { POST } = await import("@/app/api/generate-checkout-url/route");

        const mockSignature = "signature=abcdef123456";
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            text: async () => mockSignature
        });

        const req = createMockRequest({
            total_amount: 100,
            order_id: "order1",
            planName: "Premium",
            customer_info: mockCustomerInfo
        });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(200);
        expect(json.success).toBe(true);
        expect(json.signedUrl).toBe(`https://test-gateway.com/sign?${mockSignature}`);
    });

    it("returns 500 if payment gateway env vars are missing", async () => {
        const { POST } = await import("@/app/api/generate-checkout-url/route");
        const req = createMockRequest({
            total_amount: 100,
            order_id: "order1",
            planName: "Premium",
            customer_info: mockCustomerInfo
        });
        const res = await POST(req);
        const json = await res.json();
        expect(res.status).toBe(500);
        expect(json.error).toBe("שגיאת תצורה בשער התשלומים.");
    });

    it("returns 400 for invalid input parameters", async () => {
        setEnvVars();
        const { POST } = await import("@/app/api/generate-checkout-url/route");
        const req = createMockRequest({ total_amount: 0, order_id: "", planName: "", customer_info: {} });
        const res = await POST(req);
        const json = await res.json();
        expect(res.status).toBe(400);
        expect(json.error).toContain("פרמטרים לא חוקיים");
    });

    it("returns 500 when fetch to gateway fails", async () => {
        setEnvVars();
        const { POST } = await import("@/app/api/generate-checkout-url/route");
        (fetch as jest.Mock).mockRejectedValue(new Error("Network error"));
        const req = createMockRequest({
            total_amount: 100,
            order_id: "order1",
            planName: "Premium",
            customer_info: mockCustomerInfo
        });
        const res = await POST(req);
        const json = await res.json();
        expect(res.status).toBe(500);
        expect(json.error).toContain("נכשל בהתחברות לשירות התשלומים");
    });

    it("returns 500 when gateway response is not ok", async () => {
        setEnvVars();
        const { POST } = await import("@/app/api/generate-checkout-url/route");
        (fetch as jest.Mock).mockResolvedValue({ ok: false, text: async () => "error_code=123" });
        const req = createMockRequest({
            total_amount: 100,
            order_id: "order1",
            planName: "Premium",
            customer_info: mockCustomerInfo
        });
        const res = await POST(req);
        const json = await res.json();
        expect(res.status).toBe(500);
    });

    it("returns 400 for invalid JSON", async () => {
        setEnvVars();
        const { POST } = await import("@/app/api/generate-checkout-url/route");
        const req = new NextRequest("http://localhost/api/generate-checkout-url", {
            method: "POST"
        });
        jest.spyOn(req, "json").mockRejectedValue(new SyntaxError());
        const res = await POST(req);
        const json = await res.json();
        expect(res.status).toBe(400);
        expect(json.error).toBe("פורמט JSON לא חוקי בבקשה.");
    });
});
