/** @jest-environment node */

import { GET } from "@/app/api/geocode/route";
import { NextRequest } from "next/server";

global.fetch = jest.fn();

describe("GET /api/geocode", () => {
    const originalMapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

    beforeAll(() => {
        process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN = "test-token";
    });

    afterAll(() => {
        process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN = originalMapboxToken;
    });

    beforeEach(() => {
        (fetch as jest.Mock).mockClear();
    });

    it("returns geocoding results for a valid query", async () => {
        const mockResponse = { features: [{ place_name: "New York, USA" }] };
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: async () => mockResponse
        });

        const req = new NextRequest("http://localhost/api/geocode?query=new%20york");
        const res = await GET(req);
        const json = await res.json();

        expect(res.status).toBe(200);
        expect(json).toEqual(mockResponse);
    });

    it("returns 400 if query parameter is missing", async () => {
        const req = new NextRequest("http://localhost/api/geocode");
        const res = await GET(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.error).toBe("Query parameter is required");
    });

    it("returns 500 if Mapbox token is missing", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        delete process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
        const req = new NextRequest("http://localhost/api/geocode?query=test");
        const res = await GET(req);
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.error).toBe("Geocoding service configuration error");
        process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN = "test-token";
        consoleErrorSpy.mockRestore();
    });

    it("returns 500 if fetch to Mapbox fails", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        (fetch as jest.Mock).mockResolvedValue({
            ok: false,
            status: 502,
            statusText: "Bad Gateway",
            text: async () => "Mapbox error"
        });

        const req = new NextRequest("http://localhost/api/geocode?query=test");
        const res = await GET(req);
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.error).toBe("Failed to fetch geocoding results");
        consoleErrorSpy.mockRestore();
    });
});
