/** @jest-environment node */

import { GET } from "@/app/api/banks/route";

describe("GET /api/banks", () => {
    beforeEach(() => {
        global.fetch = jest.fn();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("returns sorted banks and branches on success", async () => {
        const mockRecords = [
            { Bank_Code: 1, Bank_Name: "בנק א", Branch_Code: 101, Branch_Name: "סניף א" },
            { Bank_Code: 1, Bank_Name: "בנק א", Branch_Code: 102, Branch_Name: "סניף ב" },
            { Bank_Code: 2, Bank_Name: "בנק ב", Branch_Code: 201, Branch_Name: "סניף ג" }
        ];
        (fetch as jest.Mock).mockResolvedValue({
            ok: true,
            json: async () => ({ result: { records: mockRecords } })
        });

        const res = await GET();
        const json = await res.json();
        expect(res.status).toBe(200);
        expect(json.banks).toBeDefined();
        expect(json.banks.length).toBe(2);
        expect(json.banks[0].bankName <= json.banks[1].bankName).toBe(true);
        expect(json.banks[0].branches.length).toBe(2);
        expect(json.banks[1].branches.length).toBe(1);
        // Check sorting of branches
        expect(json.banks[0].branches[0].branchName < json.banks[0].branches[1].branchName).toBe(true);
    });

    it("returns 500 if fetch fails", async () => {
        (fetch as jest.Mock).mockResolvedValue({ ok: false });
        const res = await GET();
        expect(res.status).toBe(500);
        const json = await res.json();
        expect(json.error).toBe("Failed to fetch bank data");
    });

    it("returns 500 on exception", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        (fetch as jest.Mock).mockRejectedValue(new Error("network error"));
        const res = await GET();
        expect(res.status).toBe(500);
        const json = await res.json();
        expect(json.error).toBe("Internal server error");
        consoleErrorSpy.mockRestore();
    });
});
