/** @jest-environment node */

import { GET } from "@/app/api/scholarships/eligibility/route";
import { checkScholarshipEligibility } from "@/app/services/scholarship-eligibility";
import { auth } from "@clerk/nextjs/server";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn()
}));

jest.mock("@/app/services/scholarship-eligibility", () => ({
    checkScholarshipEligibility: jest.fn()
}));

describe("GET /api/scholarships/eligibility", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("returns 401 if user is not authenticated", async () => {
        (auth as unknown as jest.Mock).mockResolvedValue({ userId: null });

        const res = await GET();
        const json = await res.json();

        expect(res.status).toBe(401);
        expect(json.error).toBe("Unauthorized");
    });

    it("returns eligibility results on success", async () => {
        const mockEligibility = { eligible: true, count: 5 };
        (auth as unknown as jest.Mock).mockResolvedValue({ userId: "user_123" });
        (checkScholarshipEligibility as jest.Mock).mockResolvedValue({ data: mockEligibility });

        const res = await GET();
        const json = await res.json();

        expect(res.status).toBe(200);
        expect(json.eligibility).toEqual(mockEligibility);
        expect(checkScholarshipEligibility).toHaveBeenCalledWith("user_123");
    });

    it("returns 500 if the service returns an error", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        (auth as unknown as jest.Mock).mockResolvedValue({ userId: "user_123" });
        (checkScholarshipEligibility as jest.Mock).mockResolvedValue({ error: "Service failure" });

        const res = await GET();
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.error).toBe("Service failure");
        consoleErrorSpy.mockRestore();
    });

    it("returns 500 if the service throws an exception", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        (auth as unknown as jest.Mock).mockResolvedValue({ userId: "user_123" });
        (checkScholarshipEligibility as jest.Mock).mockRejectedValue(new Error("Something went wrong"));

        const res = await GET();
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.error).toBe("Failed to check eligibility");
        consoleErrorSpy.mockRestore();
    });
});
