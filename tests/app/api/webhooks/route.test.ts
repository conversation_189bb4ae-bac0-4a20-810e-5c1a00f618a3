/** @jest-environment node */

import { POST } from "@/app/api/webhooks/route";
import { createServiceRoleClient } from "@/utils/supabase/server";
import { setUserClaim, UserClaimKey } from "@/utils/user-claims-client";
import { verifyWebhook } from "@clerk/nextjs/webhooks";
import { NextRequest } from "next/server";

jest.mock("@clerk/nextjs/webhooks");
jest.mock("@/utils/supabase/server");
jest.mock("@/utils/user-claims-client");

describe("POST /api/webhooks", () => {
    const mockSupabase = { from: jest.fn() };

    beforeEach(() => {
        jest.clearAllMocks();
        (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
    });

    const createMockRequest = () => new NextRequest("http://localhost/api/webhooks", { method: "POST" });

    it("handles user.created event and sets claims", async () => {
        const mockEvent = {
            type: "user.created",
            data: { id: "user_123", email_addresses: [{ email_address: "<EMAIL>" }] }
        };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);

        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(200);
        expect(setUserClaim).toHaveBeenCalledWith(
            mockSupabase,
            "user_123",
            UserClaimKey.USER_EMAIL,
            "<EMAIL>"
        );
    });

    it("handles user.updated event and sets claims", async () => {
        const mockEvent = {
            type: "user.updated",
            data: { id: "user_123", email_addresses: [{ email_address: "<EMAIL>" }] }
        };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);
        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(200);
        expect(setUserClaim).toHaveBeenCalledTimes(1);
    });

    it("ignores other event types", async () => {
        const mockEvent = { type: "user.deleted", data: {} };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);
        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(200);
        expect(setUserClaim).not.toHaveBeenCalled();
    });

    it("returns 400 if user ID is missing", async () => {
        const consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation(() => {});
        const mockEvent = { type: "user.created", data: { email_addresses: [{ email_address: "<EMAIL>" }] } };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);
        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(400);
        consoleWarnSpy.mockRestore();
    });

    it("handles users with no email addresses gracefully", async () => {
        const consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation(() => {});
        const mockEvent = {
            type: "user.created",
            data: { id: "user_123", email_addresses: [] }
        };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);
        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(200);
        expect(setUserClaim).not.toHaveBeenCalled();
        consoleWarnSpy.mockRestore();
    });

    it("returns 500 if setUserClaim fails", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        const mockEvent = {
            type: "user.created",
            data: { id: "user_123", email_addresses: [{ email_address: "<EMAIL>" }] }
        };
        (verifyWebhook as jest.Mock).mockResolvedValue(mockEvent);
        (setUserClaim as jest.Mock).mockRejectedValue(new Error("DB error"));

        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(500);
        consoleErrorSpy.mockRestore();
    });

    it("returns 400 if webhook verification fails", async () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        (verifyWebhook as jest.Mock).mockRejectedValue(new Error("Verification failed"));
        const req = createMockRequest();
        const res = await POST(req);

        expect(res.status).toBe(400);
        consoleErrorSpy.mockRestore();
    });
});
