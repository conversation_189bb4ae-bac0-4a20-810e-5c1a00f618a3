/** @jest-environment node */

import { POST } from "@/app/api/verify-checkout/route";
import { verifyPaymentAndUpdateSubscription } from "@/app/actions/subscriptions-actions";
import { NextRequest } from "next/server";

jest.mock("@/app/actions/subscriptions-actions");

describe("POST /api/verify-checkout", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    const createMockRequest = (body: object) =>
        new NextRequest("http://localhost/api/verify-checkout", {
            method: "POST",
            body: JSON.stringify(body)
        });

    it("returns success when payment verification is successful", async () => {
        const mockResult = {
            success: true,
            message: "Payment verified",
            orderId: "order1",
            transactionId: "txn1",
            verificationDetails: { status: "verified" }
        };
        (verifyPaymentAndUpdateSubscription as jest.Mock).mockResolvedValue(mockResult);

        const req = createMockRequest({ param: "value" });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(200);
        expect(json.success).toBe(true);
        expect(json.message).toBe("Payment verified");
        expect(json.orderId).toBe("order1");
    });

    it("returns 400 when payment verification fails", async () => {
        const mockResult = {
            success: false,
            error: "Invalid signature",
            verificationDetails: { status: "failed" }
        };
        (verifyPaymentAndUpdateSubscription as jest.Mock).mockResolvedValue(mockResult);

        const req = createMockRequest({ param: "value" });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.success).toBe(false);
        expect(json.error).toBe("Invalid signature");
    });

    it("returns 400 for invalid JSON format", async () => {
        const req = new NextRequest("http://localhost/api/verify-checkout", {
            method: "POST"
        });
        jest.spyOn(req, "json").mockRejectedValue(new SyntaxError("Bad JSON"));

        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(400);
        expect(json.error).toBe("שגיאת פורמט בבקשה.");
    });

    it("returns 500 for unexpected errors", async () => {
        (verifyPaymentAndUpdateSubscription as jest.Mock).mockRejectedValue(new Error("Database error"));

        const req = createMockRequest({ param: "value" });
        const res = await POST(req);
        const json = await res.json();

        expect(res.status).toBe(500);
        expect(json.success).toBe(false);
        expect(json.error).toBe("Database error");
    });
});
