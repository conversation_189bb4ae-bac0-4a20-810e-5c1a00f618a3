import "@testing-library/jest-dom";

import sitemap from "@/app/sitemap";
import { createClientFromRequest } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server", () => ({
    createClientFromRequest: jest.fn()
}));

const mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});

const originalEnv = process.env;

describe("Sitemap", () => {
    const mockSupabaseClient = {
        from: jest.fn()
    };

    const mockScholarshipGroups = [
        { id: "1", slug: "tech-scholarships", name: "Tech Scholarships" },
        { id: "2", slug: "medical-scholarships", name: "Medical Scholarships" }
    ];

    const mockScholarships = [
        { id: "1", slug: "computer-science-grant", title: "Computer Science Grant", is_public: true },
        { id: "2", slug: "nursing-scholarship", title: "Nursing Scholarship", is_public: true },
        { id: "3", slug: "private-scholarship", title: "Private Scholarship", is_public: false }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        (createClientFromRequest as jest.Mock).mockResolvedValue(mockSupabaseClient);
        mockConsoleError.mockClear();

        process.env = { ...originalEnv };
    });

    afterAll(() => {
        process.env = originalEnv;
        mockConsoleError.mockRestore();
    });

    describe("Environment Configuration", () => {
        it("uses NEXT_PUBLIC_VERCEL_URL when available", async () => {
            process.env.NEXT_PUBLIC_VERCEL_URL = "http://localhost:3000";

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockScholarshipGroups })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: mockScholarships.filter((s) => s.is_public) })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result[0].url).toBe("http://localhost:3000");
        });

        it("falls back to localhost when NEXT_PUBLIC_VERCEL_URL is not set", async () => {
            delete process.env.NEXT_PUBLIC_VERCEL_URL;

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockScholarshipGroups })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: mockScholarships.filter((s) => s.is_public) })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result[0].url).toBe("http://localhost:3000");
        });
    });

    describe("Data Fetching", () => {
        describe("getScholarshipGroups", () => {
            it("fetches scholarship groups from database", async () => {
                const selectMock = jest.fn().mockResolvedValue({ data: mockScholarshipGroups });
                mockSupabaseClient.from.mockReturnValue({ select: selectMock });

                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return { select: selectMock };
                    }
                    if (table === "scholarships") {
                        return {
                            select: jest.fn().mockReturnValue({
                                eq: jest.fn().mockResolvedValue({ data: [] })
                            })
                        };
                    }
                });

                await sitemap();

                expect(mockSupabaseClient.from).toHaveBeenCalledWith("groups_scholarship");
                expect(selectMock).toHaveBeenCalledWith("*");
            });

            it("handles null data response gracefully", async () => {
                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return {
                            select: jest.fn().mockResolvedValue({ data: null })
                        };
                    }
                    if (table === "scholarships") {
                        return {
                            select: jest.fn().mockReturnValue({
                                eq: jest.fn().mockResolvedValue({ data: [] })
                            })
                        };
                    }
                });

                const result = await sitemap();

                expect(result).toHaveLength(1);
                expect(result[0].url).toContain("http://localhost:3000");
            });

            it("handles database errors gracefully", async () => {
                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return {
                            select: jest.fn().mockImplementation(() => {
                                throw new Error("Database error");
                            })
                        };
                    }
                    if (table === "scholarships") {
                        return {
                            select: jest.fn().mockReturnValue({
                                eq: jest.fn().mockResolvedValue({ data: [] })
                            })
                        };
                    }
                });

                const result = await sitemap();
                expect(result).toBeDefined();
                expect(result).toHaveLength(1);
                expect(mockConsoleError).toHaveBeenCalledWith("Error fetching scholarship groups:", expect.any(Error));
            });
        });

        describe("getScholarships", () => {
            it("fetches only public scholarships from database", async () => {
                const eqMock = jest.fn().mockResolvedValue({ data: mockScholarships.filter((s) => s.is_public) });
                const selectMock = jest.fn().mockReturnValue({ eq: eqMock });

                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return {
                            select: jest.fn().mockResolvedValue({ data: [] })
                        };
                    }
                    if (table === "scholarships") {
                        return { select: selectMock };
                    }
                });

                await sitemap();

                expect(mockSupabaseClient.from).toHaveBeenCalledWith("scholarships");
                expect(selectMock).toHaveBeenCalledWith("*");
                expect(eqMock).toHaveBeenCalledWith("is_public", true);
            });

            it("handles null data response gracefully", async () => {
                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return {
                            select: jest.fn().mockResolvedValue({ data: [] })
                        };
                    }
                    if (table === "scholarships") {
                        return {
                            select: jest.fn().mockReturnValue({
                                eq: jest.fn().mockResolvedValue({ data: null })
                            })
                        };
                    }
                });

                const result = await sitemap();

                expect(result).toHaveLength(1);
                expect(result[0].url).toContain("http://localhost:3000");
            });

            it("handles database errors gracefully", async () => {
                mockSupabaseClient.from.mockImplementation((table) => {
                    if (table === "groups_scholarship") {
                        return {
                            select: jest.fn().mockResolvedValue({ data: [] })
                        };
                    }
                    if (table === "scholarships") {
                        return {
                            select: jest.fn().mockReturnValue({
                                eq: jest.fn().mockImplementation(() => {
                                    throw new Error("Database error");
                                })
                            })
                        };
                    }
                });

                const result = await sitemap();
                expect(result).toBeDefined();
                expect(result).toHaveLength(1);
                expect(mockConsoleError).toHaveBeenCalledWith("Error fetching scholarships:", expect.any(Error));
            });
        });
    });

    describe("Sitemap Generation", () => {
        it("generates complete sitemap with all URL types", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockScholarshipGroups })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: mockScholarships.filter((s) => s.is_public) })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result).toHaveLength(5);

            const homepage = result.find((item) => item.url === "http://localhost:3000");
            expect(homepage).toBeDefined();
            expect(homepage!.priority).toBe(1);
            expect(homepage!.changeFrequency).toBe("yearly");
            expect(homepage!.lastModified).toBeInstanceOf(Date);

            const techGroup = result.find((item) => item.url.includes("/scholarships/groups/tech-scholarships"));
            expect(techGroup).toBeDefined();
            expect(techGroup!.priority).toBe(0.5);
            expect(techGroup!.changeFrequency).toBe("monthly");

            const medicalGroup = result.find((item) => item.url.includes("/scholarships/groups/medical-scholarships"));
            expect(medicalGroup).toBeDefined();

            const csScholarship = result.find((item) => item.url.includes("/scholarships/computer-science-grant"));
            expect(csScholarship).toBeDefined();
            expect(csScholarship!.priority).toBe(0.5);
            expect(csScholarship!.changeFrequency).toBe("monthly");

            const nursingScholarship = result.find((item) => item.url.includes("/scholarships/nursing-scholarship"));
            expect(nursingScholarship).toBeDefined();

            const privateScholarship = result.find((item) => item.url.includes("/scholarships/private-scholarship"));
            expect(privateScholarship).toBeUndefined();
        });

        it("generates sitemap with only homepage when no data exists", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: [] })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: [] })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result).toHaveLength(1);
            expect(result[0].url).toBe("http://localhost:3000");
        });

        it("generates correct URL structure for all entry types", async () => {
            process.env.NEXT_PUBLIC_VERCEL_URL = "http://localhost:3000";

            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({
                            data: [{ slug: "test-group" }]
                        })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({
                                data: [{ slug: "test-scholarship", is_public: true }]
                            })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        url: "http://localhost:3000",
                        priority: 1,
                        changeFrequency: "yearly"
                    }),
                    expect.objectContaining({
                        url: "http://localhost:3000/scholarships/groups/test-group",
                        priority: 0.5,
                        changeFrequency: "monthly"
                    }),
                    expect.objectContaining({
                        url: "http://localhost:3000/scholarships/test-scholarship",
                        priority: 0.5,
                        changeFrequency: "monthly"
                    })
                ])
            );
        });
    });

    describe("Sitemap Item Structure", () => {
        it("generates sitemap items with correct metadata structure", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({
                            data: [{ slug: "metadata-test" }]
                        })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: [] })
                        })
                    };
                }
            });

            const result = await sitemap();
            const groupItem = result.find((item) => item.url.includes("metadata-test"));

            expect(groupItem).toMatchObject({
                url: expect.stringContaining("/scholarships/groups/metadata-test"),
                lastModified: expect.any(Date),
                changeFrequency: "monthly",
                priority: 0.5
            });
        });

        it("ensures all sitemap items have required properties", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockScholarshipGroups })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({ data: mockScholarships.filter((s) => s.is_public) })
                        })
                    };
                }
            });

            const result = await sitemap();

            result.forEach((item) => {
                expect(item).toHaveProperty("url");
                expect(item).toHaveProperty("lastModified");
                expect(item).toHaveProperty("changeFrequency");
                expect(item).toHaveProperty("priority");

                expect(typeof item.url).toBe("string");
                expect(item.lastModified).toBeInstanceOf(Date);
                expect(typeof item.changeFrequency).toBe("string");
                expect(typeof item.priority).toBe("number");
            });
        });
    });

    describe("Error Handling", () => {
        it("handles partial data fetch failures gracefully", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({ data: mockScholarshipGroups })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockImplementation(() => {
                                throw new Error("Database error");
                            })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result.length).toBeGreaterThan(0);
            expect(result.some((item) => item.url.includes("/scholarships/groups/"))).toBe(true);
            expect(mockConsoleError).toHaveBeenCalledWith("Error fetching scholarships:", expect.any(Error));
        });

        it("continues execution when both data sources fail", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockImplementation(() => {
                            throw new Error("Complete database failure");
                        })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockImplementation(() => {
                                throw new Error("Complete database failure");
                            })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result).toHaveLength(1);
            expect(result[0].url).toContain("http://localhost:3000");
            expect(mockConsoleError).toHaveBeenCalledTimes(2);
        });

        it("handles malformed data gracefully", async () => {
            mockSupabaseClient.from.mockImplementation((table) => {
                if (table === "groups_scholarship") {
                    return {
                        select: jest.fn().mockResolvedValue({
                            data: [{ slug: "valid-group" }, { slug: null }, {}, null]
                        })
                    };
                }
                if (table === "scholarships") {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockResolvedValue({
                                data: [
                                    { slug: "valid-scholarship", is_public: true },
                                    { slug: null, is_public: true },
                                    { is_public: true }
                                ]
                            })
                        })
                    };
                }
            });

            const result = await sitemap();

            expect(result.length).toBe(3);

            result.forEach((item) => {
                expect(item.url).toBeTruthy();
                expect(typeof item.url).toBe("string");
                expect(item.url).not.toContain("null");
                expect(item.url).not.toContain("undefined");
            });

            expect(result.some((item) => item.url.includes("valid-group"))).toBe(true);
            expect(result.some((item) => item.url.includes("valid-scholarship"))).toBe(true);
        });
    });
});
