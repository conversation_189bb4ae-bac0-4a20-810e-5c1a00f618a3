import React from "react";
import { render, screen, cleanup } from "@testing-library/react";

import SSOCallback from "@/app/sso-callback/page";

jest.mock("@clerk/nextjs", () => ({
    AuthenticateWithRedirectCallback: () => (
        <div data-testid="authenticate-with-redirect-callback">OAuth callback processing...</div>
    )
}));

describe("SSO Callback Page", () => {
    afterEach(cleanup);

    it("should render the AuthenticateWithRedirectCallback component", () => {
        render(<SSOCallback />);

        const callbackComponent = screen.getByTestId("authenticate-with-redirect-callback");
        expect(callbackComponent).toBeInTheDocument();
        expect(callbackComponent).toHaveTextContent("OAuth callback processing...");
    });

    it("should handle OAuth redirect callback correctly", () => {
        render(<SSOCallback />);

        // Verify the component is rendered without errors
        expect(screen.getByTestId("authenticate-with-redirect-callback")).toBeInTheDocument();
    });
});
