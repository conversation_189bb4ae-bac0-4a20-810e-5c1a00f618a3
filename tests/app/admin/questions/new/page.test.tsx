import React from "react";
import { render, screen } from "@testing-library/react";
import NewQuestionPage from "@/app/(admin)/admin/questions/new/page";

// Mock the form component
jest.mock("@/components/forms/question-form", () => ({
    QuestionForm: jest.fn(() => <div data-testid="question-form-mock" />)
}));

describe("NewQuestionPage", () => {
    it("renders the QuestionForm component and the page title", () => {
        render(<NewQuestionPage />);
        expect(screen.getByRole("heading", { name: "שאלה חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("question-form-mock")).toBeInTheDocument();
    });
});
