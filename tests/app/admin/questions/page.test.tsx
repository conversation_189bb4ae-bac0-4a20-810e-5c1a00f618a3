import React, { Suspense } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import QuestionsPage from "@/app/(admin)/admin/questions/page";

// Mock the content component
jest.mock("@/app/(admin)/admin/questions/questions-content", () => ({
    QuestionsContent: jest.fn(() => <div data-testid="questions-content-mock">Questions Content</div>)
}));

const QuestionsContentMock = require("@/app/(admin)/admin/questions/questions-content").QuestionsContent as jest.Mock;

describe("QuestionsPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders QuestionsContent within Suspense", async () => {
        render(
            <Suspense fallback={<div>Loading...</div>}>
                <QuestionsPage />
            </Suspense>
        );

        // Wait for the lazy-loaded component to appear
        await waitFor(() => {
            expect(screen.getByTestId("questions-content-mock")).toBeInTheDocument();
        });
    });
});
