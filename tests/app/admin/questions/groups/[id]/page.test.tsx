import React from "react";
import { render, screen } from "@testing-library/react";
import EditQuestionGroupPage from "@/app/(admin)/admin/questions/groups/[id]/page";

// Mock dependencies
jest.mock("react", () => ({
    ...jest.requireActual("react"),
    use: jest.fn()
}));
jest.mock("@/components/forms/question-group-form", () => ({
    QuestionGroupForm: jest.fn(() => <div>Question Group Form Mock</div>)
}));

const useMock = React.use as jest.Mock;
const QuestionGroupFormMock = require("@/components/forms/question-group-form").QuestionGroupForm as jest.Mock;

describe("EditQuestionGroupPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the QuestionGroupForm with the correct id", () => {
        const mockParams = Promise.resolve({ id: "123" });
        useMock.mockReturnValue({ id: "123" });

        render(<EditQuestionGroupPage params={mockParams} />);

        expect(screen.getByRole("heading", { name: "עריכת קבוצת שאלות" })).toBeInTheDocument();
        expect(QuestionGroupFormMock).toHaveBeenCalledWith({ groupId: "123" }, undefined);
    });
});
