import React from "react";
import { render, screen } from "@testing-library/react";
import NewQuestionGroupPage from "@/app/(admin)/admin/questions/groups/new/page";

// Mock the form component
jest.mock("@/components/forms/question-group-form", () => ({
    QuestionGroupForm: jest.fn(() => <div data-testid="question-group-form-mock" />)
}));

describe("NewQuestionGroupPage", () => {
    it("renders the QuestionGroupForm component and the page title", () => {
        render(<NewQuestionGroupPage />);
        expect(screen.getByRole("heading", { name: "קבוצת שאלות חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("question-group-form-mock")).toBeInTheDocument();
    });
});
