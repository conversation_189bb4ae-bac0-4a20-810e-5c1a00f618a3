import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditQuestionPage from "@/app/(admin)/admin/questions/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/question-form", () => ({
    QuestionForm: jest.fn(() => <div data-testid="question-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const QuestionFormMock = require("@/components/forms/question-form").QuestionForm as jest.Mock;

describe("EditQuestionPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the QuestionForm with the correct questionId", () => {
        const questionId = "test-question-id";
        useParamsMock.mockReturnValue({ id: questionId });

        render(<EditQuestionPage />);

        expect(screen.getByRole("heading", { name: "עריכת שאלה" })).toBeInTheDocument();
        expect(QuestionFormMock).toHaveBeenCalledWith({ questionId }, undefined);
    });

    it("renders the QuestionForm even if id is not found in params", () => {
        useParamsMock.mockReturnValue({}); // No id in params

        render(<EditQuestionPage />);

        expect(screen.getByRole("heading", { name: "עריכת שאלה" })).toBeInTheDocument();
        // The form is rendered, and `questionId` will be undefined.
        // This matches the component's behavior.
        expect(QuestionFormMock).toHaveBeenCalledWith({ questionId: undefined }, undefined);
    });
});
