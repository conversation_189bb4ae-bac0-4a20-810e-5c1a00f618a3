import React from "react";
import { render, screen } from "@testing-library/react";
import { QuestionsContent } from "@/app/(admin)/admin/questions/questions-content";

// Mock the correct hooks
jest.mock("@/hooks/use-questions", () => ({
    useQuestions: jest.fn()
}));

jest.mock("@/hooks/use-question-groups", () => ({
    useQuestionGroups: jest.fn()
}));

// Mock components
jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(() => <div data-testid="admin-table-mock">Admin Table</div>)
}));

jest.mock("@/components/ui/tabs", () => ({
    Tabs: jest.fn(({ children }) => <div data-testid="tabs-mock">{children}</div>),
    TabsContent: jest.fn(({ children }) => <div data-testid="tabs-content-mock">{children}</div>),
    TabsList: jest.fn(({ children }) => <div data-testid="tabs-list-mock">{children}</div>),
    TabsTrigger: jest.fn(({ children }) => <button data-testid="tabs-trigger-mock">{children}</button>)
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useSearchParams: jest.fn(() => ({
        get: jest.fn(() => null)
    }))
}));

// Mock server actions
jest.mock("@/app/actions/question-actions", () => ({
    deleteQuestion: jest.fn()
}));

jest.mock("@/app/actions/question-group-actions", () => ({
    deleteQuestionGroup: jest.fn()
}));

const useQuestionsMock = require("@/hooks/use-questions").useQuestions as jest.Mock;
const useQuestionGroupsMock = require("@/hooks/use-question-groups").useQuestionGroups as jest.Mock;

describe("QuestionsContent", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders tabs and AdminTable when data is loaded", () => {
        const mockQuestions = [
            {
                id: "1",
                type: "single_select",
                section: "personal_details",
                metadata: {
                    label: "Test Question",
                    required: true
                },
                groups_question: {
                    name: "Test Group"
                }
            }
        ];

        const mockGroups = [
            {
                id: "1",
                name: "Test Group",
                questions_count: 1
            }
        ];

        useQuestionsMock.mockReturnValue({
            items: mockQuestions,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        useQuestionGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<QuestionsContent />);

        expect(screen.getByTestId("tabs-mock")).toBeInTheDocument();
        expect(screen.getAllByTestId("admin-table-mock")).toHaveLength(2); // One for each tab
    });
});
