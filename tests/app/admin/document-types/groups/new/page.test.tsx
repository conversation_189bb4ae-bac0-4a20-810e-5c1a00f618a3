import React from "react";
import { render, screen } from "@testing-library/react";
import NewDocumentTypeGroupPage from "@/app/(admin)/admin/document-types/groups/new/page";
import { ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS } from "@/lib/document-type-group-constants";

// Mock the form component
jest.mock("@/components/forms/document-type-group-form", () => ({
    DocumentTypeGroupForm: jest.fn(() => <div data-testid="document-type-group-form-mock" />)
}));

describe("NewDocumentTypeGroupPage", () => {
    it("renders the DocumentTypeGroupForm component and the page title", () => {
        render(<NewDocumentTypeGroupPage />);
        expect(screen.getByRole("heading", { name: ADMIN_DOCUMENT_TYPE_GROUPS_TEXTS.newGroup })).toBeInTheDocument();
        expect(screen.getByTestId("document-type-group-form-mock")).toBeInTheDocument();
    });
});
