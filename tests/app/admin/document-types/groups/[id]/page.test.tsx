import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditDocumentTypeGroupPage from "@/app/(admin)/admin/document-types/groups/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/document-type-group-form", () => ({
    DocumentTypeGroupForm: jest.fn(() => <div>Document Type Group Form Mock</div>)
}));

const useParamsMock = useParams as jest.Mock;
const DocumentTypeGroupFormMock = require("@/components/forms/document-type-group-form")
    .DocumentTypeGroupForm as jest.Mock;

describe("EditDocumentTypeGroupPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the DocumentTypeGroupForm with the correct id", () => {
        useParamsMock.mockReturnValue({ id: "123" });
        render(<EditDocumentTypeGroupPage />);
        expect(screen.getByRole("heading", { name: "עריכת קבוצת מסמכים" })).toBeInTheDocument();
        expect(DocumentTypeGroupFormMock).toHaveBeenCalledWith({ groupId: "123" }, undefined);
    });

    it("handles array params correctly", () => {
        useParamsMock.mockReturnValue({ id: ["123", "456"] });
        render(<EditDocumentTypeGroupPage />);
        expect(DocumentTypeGroupFormMock).toHaveBeenCalledWith({ groupId: "123" }, undefined);
    });
});
