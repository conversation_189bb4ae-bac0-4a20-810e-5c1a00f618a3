import React from "react";
import { render, screen } from "@testing-library/react";
import NewDocumentTypePage from "@/app/(admin)/admin/document-types/new/page";
import { TEXTS } from "@/lib/document-type-constants";

// Mock the form component
jest.mock("@/components/forms/document-type-form", () => ({
    DocumentTypeForm: jest.fn(() => <div data-testid="document-type-form-mock" />)
}));

describe("NewDocumentTypePage", () => {
    it("renders the DocumentTypeForm component and the page title", () => {
        render(<NewDocumentTypePage />);
        expect(screen.getByRole("heading", { name: TEXTS.newDocumentTypeTitle })).toBeInTheDocument();
        expect(screen.getByTestId("document-type-form-mock")).toBeInTheDocument();
    });
});
