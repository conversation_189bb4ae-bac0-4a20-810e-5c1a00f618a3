import React from "react";
import { render, screen } from "@testing-library/react";
import { type DocumentTypeWithUrls } from "@/app/actions/document-type-actions";
import { type DocumentTypeGroupWithCount } from "@/app/actions/document-type-group-actions";
import { type AdminTableProps } from "@/components/table/types";
// Mock the correct hooks
jest.mock("@/hooks/use-document-types", () => ({
    useDocumentTypes: jest.fn()
}));

jest.mock("@/hooks/use-document-type-groups", () => ({
    useDocumentTypeGroups: jest.fn()
}));

// Mock components
jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn((props: AdminTableProps<DocumentTypeWithUrls>) => (
        <div data-testid="admin-table-mock">Admin Table</div>
    ))
}));

jest.mock("@/components/ui/tabs", () => ({
    Tabs: jest.fn(({ children }) => <div data-testid="tabs-mock">{children}</div>),
    TabsContent: jest.fn(({ children }) => <div data-testid="tabs-content-mock">{children}</div>),
    TabsList: jest.fn(({ children }) => <div data-testid="tabs-list-mock">{children}</div>),
    TabsTrigger: jest.fn(({ children }) => <button data-testid="tabs-trigger-mock">{children}</button>)
}));

jest.mock("@/components/ui/badge", () => ({
    Badge: jest.fn(({ children }) => <span data-testid="badge-mock">{children}</span>)
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useSearchParams: jest.fn(() => ({
        get: jest.fn(() => null)
    }))
}));

// Mock server actions
jest.mock("@/app/actions/document-type-actions", () => ({
    deleteDocumentType: jest.fn()
}));

// Mock Supabase client
jest.mock("@/utils/supabase/client", () => ({
    getSupabaseClient: jest.fn(() => ({
        from: jest.fn(() => ({
            delete: jest.fn(() => ({
                eq: jest.fn(() => Promise.resolve({ error: null }))
            }))
        }))
    }))
}));

// Mock sonner
jest.mock("sonner", () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn()
    }
}));

// Now that all mocks are set up, import the component under test
const { DocumentTypesContent } = require("@/app/(admin)/admin/document-types/document-types-content");

const useDocumentTypesMock = require("@/hooks/use-document-types").useDocumentTypes as jest.Mock;
const useDocumentTypeGroupsMock = require("@/hooks/use-document-type-groups").useDocumentTypeGroups as jest.Mock;

describe("DocumentTypesContent", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders tabs and AdminTable when data is loaded", () => {
        const mockDocumentTypes: DocumentTypeWithUrls[] = [
            {
                id: "1",
                name: "Test Document Type",
                description: "Test description",
                allowed_mime_types: ["application/pdf", "image/jpeg"],
                example_file_path: null,
                group_id: "group-1",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z",
                link_url: null,
                max_file_size_mb: 10,
                example_file_url: undefined
            }
        ];

        const mockGroups: DocumentTypeGroupWithCount[] = [
            {
                id: "1",
                name: "Test Group",
                description: null,
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z",
                document_types_count: 1
            }
        ];

        useDocumentTypesMock.mockReturnValue({
            items: mockDocumentTypes,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        useDocumentTypeGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<DocumentTypesContent />);

        expect(screen.getByTestId("tabs-mock")).toBeInTheDocument();
        expect(screen.getAllByTestId("admin-table-mock")).toHaveLength(2); // One for each tab
    });

    it("calls refetchDocumentTypes after successful document type deletion", async () => {
        // Arrange mocks
        const refetchDocumentTypes = jest.fn();
        const mockDocumentTypes: DocumentTypeWithUrls[] = [
            {
                id: "doc-1",
                name: "Doc",
                description: "",
                allowed_mime_types: [],
                example_file_path: null,
                group_id: "group-1",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z",
                link_url: null,
                max_file_size_mb: 10,
                example_file_url: undefined
            }
        ];

        useDocumentTypesMock.mockReturnValue({
            items: mockDocumentTypes,
            loading: false,
            error: null,
            refetch: refetchDocumentTypes
        });

        const refetchGroups = jest.fn();
        useDocumentTypeGroupsMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: refetchGroups
        });

        // Mock the server action to succeed
        const deleteDocumentTypeMock = require("@/app/actions/document-type-actions").deleteDocumentType as jest.Mock;
        deleteDocumentTypeMock.mockResolvedValue({ success: true });

        // Render component
        render(<DocumentTypesContent />);

        // Access the first AdminTable call (document types tab)
        const AdminTableMock = require("@/components/table/admin-table").AdminTable as jest.Mock;
        const firstCallProps = AdminTableMock.mock.calls[0][0];

        // Act: trigger the onDelete callback
        await firstCallProps.onDelete("doc-1");

        // Assert
        expect(deleteDocumentTypeMock).toHaveBeenCalledWith("doc-1");
        expect(refetchDocumentTypes).toHaveBeenCalled();
    });

    it("calls refetchGroups and displays toast after successful group deletion", async () => {
        // Arrange mocks
        const refetchDocumentTypes = jest.fn();
        useDocumentTypesMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: refetchDocumentTypes
        });

        const refetchGroups = jest.fn();
        const mockGroups = [{ id: "grp-1", name: "Group", document_types_count: 0 }];
        useDocumentTypeGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: refetchGroups
        });

        // Reset toast mocks
        const toastMock = require("sonner").toast;
        toastMock.success.mockClear();

        // Render component
        render(<DocumentTypesContent />);

        // Access the second AdminTable call (groups tab)
        const AdminTableMock = require("@/components/table/admin-table").AdminTable as jest.Mock;
        const secondCallProps = AdminTableMock.mock.calls[1][0];

        // Act: trigger the onDelete callback for group
        await secondCallProps.onDelete("grp-1");

        // Assert: ensure both refetch functions were called and toast displayed
        expect(refetchGroups).toHaveBeenCalled();
        expect(refetchDocumentTypes).toHaveBeenCalled();
        expect(toastMock.success).toHaveBeenCalled();
    });
});
