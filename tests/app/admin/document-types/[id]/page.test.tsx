import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditDocumentTypePage from "@/app/(admin)/admin/document-types/[id]/page";
import { TEXTS } from "@/lib/document-type-constants";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/document-type-form", () => ({
    DocumentTypeForm: jest.fn(() => <div>Document Type Form Mock</div>)
}));

const useParamsMock = useParams as jest.Mock;
const DocumentTypeFormMock = require("@/components/forms/document-type-form").DocumentTypeForm as jest.Mock;

describe("EditDocumentTypePage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the DocumentTypeForm with the correct id", () => {
        useParamsMock.mockReturnValue({ id: "123" });
        render(<EditDocumentTypePage />);
        expect(screen.getByRole("heading", { name: TEXTS.editTitle })).toBeInTheDocument();
        expect(DocumentTypeFormMock).toHaveBeenCalledWith({ documentTypeId: "123" }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({ id: null });
        render(<EditDocumentTypePage />);
        expect(screen.getByText(TEXTS.errorNotFound)).toBeInTheDocument();
        expect(DocumentTypeFormMock).not.toHaveBeenCalled();
    });
});
