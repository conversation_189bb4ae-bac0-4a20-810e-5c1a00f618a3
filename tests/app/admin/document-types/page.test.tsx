import React, { Suspense } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import DocumentTypesPage from "@/app/(admin)/admin/document-types/page";

// Mock the content component
jest.mock("@/app/(admin)/admin/document-types/document-types-content", () => ({
    DocumentTypesContent: jest.fn(() => <div data-testid="document-types-content-mock">Document Types Content</div>)
}));

const DocumentTypesContentMock = require("@/app/(admin)/admin/document-types/document-types-content")
    .DocumentTypesContent as jest.Mock;

describe("DocumentTypesPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders DocumentTypesContent within Suspense", async () => {
        render(
            <Suspense fallback={<div>Loading...</div>}>
                <DocumentTypesPage />
            </Suspense>
        );

        // Wait for the lazy-loaded component to appear
        await waitFor(() => {
            expect(screen.getByTestId("document-types-content-mock")).toBeInTheDocument();
        });
    });
});
