import React from "react";
import { render, screen } from "@testing-library/react";
import AdminDashboardPage from "@/app/(admin)/admin/page";

// Mock the actual dashboard component since we don't need to test its internals here
jest.mock("@/components/admin/dashboard/admin-dashboard", () => ({
    AdminDashboard: jest.fn(() => <div data-testid="admin-dashboard-mock" />)
}));

describe("AdminDashboardPage", () => {
    it("renders the AdminDashboard component", () => {
        render(<AdminDashboardPage />);
        expect(screen.getByTestId("admin-dashboard-mock")).toBeInTheDocument();
    });
});
