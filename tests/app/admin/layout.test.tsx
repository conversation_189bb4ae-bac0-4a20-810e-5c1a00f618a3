import React from "react";
import { render, screen } from "@testing-library/react";
import AdminLayout from "@/app/(admin)/layout";

// Mock child components
jest.mock("@/components/layout/sidebar/app-sidebar", () => ({
    AppSidebar: jest.fn(() => <div data-testid="app-sidebar" />)
}));
jest.mock("@/components/layout/site-logo", () => ({
    SiteLogo: jest.fn(() => <div data-testid="site-logo" />)
}));
jest.mock("@/components/ui/banner", () => ({
    Banner: jest.fn(() => <div data-testid="banner" />)
}));
jest.mock("@/components/ui/sidebar", () => ({
    SidebarProvider: jest.fn(({ children }) => <div data-testid="sidebar-provider">{children}</div>),
    SidebarTrigger: jest.fn(({ children }) => <div data-testid="sidebar-trigger">{children}</div>)
}));

// Mock config data
jest.mock("@/config/admin", () => ({
    sidebarData: {
        navMain: { title: "Main", items: [] },
        generalManagement: { title: "General", items: [{ title: "general-item" }] },
        scholarshipManagement: { title: "Scholarships", items: [{ title: "scholarship-item" }] },
        contentManagement: { title: "Content", items: [] }, // Empty on purpose
        navSecondary: { title: "Secondary", items: [] }
    }
}));

const { AppSidebar } = require("@/components/layout/sidebar/app-sidebar");

describe("AdminLayout", () => {
    it("renders the main layout components", () => {
        render(
            <AdminLayout>
                <div>Child Content</div>
            </AdminLayout>
        );

        expect(screen.getByTestId("app-sidebar")).toBeInTheDocument();
        expect(screen.getByTestId("banner")).toBeInTheDocument();
        expect(screen.getByTestId("site-logo")).toBeInTheDocument();
        expect(screen.getByText("Child Content")).toBeInTheDocument();
    });

    it("correctly filters and passes navGroups to AppSidebar", () => {
        render(
            <AdminLayout>
                <div>Child Content</div>
            </AdminLayout>
        );

        const { sidebarData } = require("@/config/admin");

        const expectedNavGroups = [sidebarData.generalManagement, sidebarData.scholarshipManagement];

        expect(AppSidebar).toHaveBeenCalledWith(
            {
                navMain: sidebarData.navMain,
                navGroups: expectedNavGroups,
                navSecondary: sidebarData.navSecondary
            },
            undefined
        );
    });
});
