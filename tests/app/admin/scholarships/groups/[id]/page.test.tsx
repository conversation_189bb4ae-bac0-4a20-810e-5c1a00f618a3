import React from "react";
import { render, screen } from "@testing-library/react";
import EditScholarshipGroupPage from "@/app/(admin)/admin/scholarships/groups/[id]/page";

// Mock dependencies
jest.mock("react", () => ({
    ...jest.requireActual("react"),
    use: jest.fn()
}));
jest.mock("@/components/forms/scholarship-group-form", () => ({
    ScholarshipGroupForm: jest.fn(() => <div>Scholarship Group Form Mock</div>)
}));

const useMock = React.use as jest.Mock;
const ScholarshipGroupFormMock = require("@/components/forms/scholarship-group-form").ScholarshipGroupForm as jest.Mock;

describe("EditScholarshipGroupPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the ScholarshipGroupForm with the correct id", () => {
        const mockParams = Promise.resolve({ id: "123" });
        useMock.mockReturnValue({ id: "123" });

        render(<EditScholarshipGroupPage params={mockParams} />);

        expect(screen.getByRole("heading", { name: "עריכת קבוצת מלגות" })).toBeInTheDocument();
        expect(ScholarshipGroupFormMock).toHaveBeenCalledWith({ groupId: "123" }, undefined);
    });
});
