import React from "react";
import { render, screen } from "@testing-library/react";
import NewScholarshipGroupPage from "@/app/(admin)/admin/scholarships/groups/new/page";

// Mock the form component
jest.mock("@/components/forms/scholarship-group-form", () => ({
    ScholarshipGroupForm: jest.fn(() => <div data-testid="scholarship-group-form-mock" />)
}));

describe("NewScholarshipGroupPage", () => {
    it("renders the ScholarshipGroupForm component and the page title", () => {
        render(<NewScholarshipGroupPage />);
        expect(screen.getByRole("heading", { name: "קבוצת מלגות חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("scholarship-group-form-mock")).toBeInTheDocument();
    });
});
