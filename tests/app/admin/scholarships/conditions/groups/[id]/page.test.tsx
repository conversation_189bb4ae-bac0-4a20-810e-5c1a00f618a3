import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditScholarshipConditionGroupPage from "@/app/(admin)/admin/scholarships/conditions/groups/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/condition-group-form", () => ({
    ConditionGroupForm: jest.fn(() => <div data-testid="condition-group-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const ConditionGroupFormMock = require("@/components/forms/condition-group-form").ConditionGroupForm as jest.Mock;

describe("EditScholarshipConditionGroupPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the ConditionGroupForm with the correct groupId", () => {
        const groupId = "test-group-id";
        useParamsMock.mockReturnValue({ id: groupId });

        render(<EditScholarshipConditionGroupPage />);

        expect(ConditionGroupFormMock).toHaveBeenCalledWith({ groupId }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({}); // No id in params

        render(<EditScholarshipConditionGroupPage />);

        expect(screen.getByText("Error: Condition Group ID not found.")).toBeInTheDocument();
        expect(ConditionGroupFormMock).not.toHaveBeenCalled();
    });
});
