import React from "react";
import { render, screen } from "@testing-library/react";
import ScholarshipConditionGroupsPage from "@/app/(admin)/admin/scholarships/conditions/groups/page";
import { type Tables } from "@/types/database.types";

// Mock dependencies
jest.mock("@/hooks/use-scholarship-condition-groups", () => ({
    useScholarshipConditionGroups: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn((props) => {
        // Simulate onDelete calling onRefetch if successful
        const onDelete = async (id: string) => {
            const result = await props.onDelete(id);
            if (result.success && props.onRefetch) {
                props.onRefetch();
            }
            return result;
        };
        return (
            <div data-testid="admin-table-mock" {...props} onDelete={onDelete}>
                Admin Table
            </div>
        );
    })
}));

jest.mock("@/app/actions/scholarship-condition-group-actions", () => ({
    deleteScholarshipConditionGroup: jest.fn()
}));

const useScholarshipConditionGroupsMock = require("@/hooks/use-scholarship-condition-groups")
    .useScholarshipConditionGroups as jest.Mock;
const AdminTableMock = require("@/components/table/admin-table").AdminTable as jest.Mock;

describe("ScholarshipConditionGroupsPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props when data is loaded", () => {
        const mockGroups: Tables<"groups_condition">[] = [
            {
                id: "1",
                name: "Test Group",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            }
        ];

        useScholarshipConditionGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<ScholarshipConditionGroupsPage />);

        expect(screen.getByTestId("admin-table-mock")).toBeInTheDocument();
        expect(AdminTableMock).toHaveBeenCalledWith(
            expect.objectContaining({
                title: "קבוצות תנאים למלגות",
                items: mockGroups,
                loading: false,
                error: null,
                addButtonLabel: "קבוצת תנאים חדשה",
                addButtonHref: "/admin/scholarships/conditions/groups/new"
            }),
            undefined
        );
    });

    it("renders AdminTable with loading state", () => {
        useScholarshipConditionGroupsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        render(<ScholarshipConditionGroupsPage />);

        expect(AdminTableMock).toHaveBeenCalledWith(
            expect.objectContaining({
                loading: true,
                items: []
            }),
            undefined
        );
    });

    it("renders AdminTable with error state", () => {
        const mockError = "Failed to load condition groups";
        useScholarshipConditionGroupsMock.mockReturnValue({
            items: [],
            loading: false,
            error: mockError,
            refetch: jest.fn()
        });

        render(<ScholarshipConditionGroupsPage />);

        expect(AdminTableMock).toHaveBeenCalledWith(
            expect.objectContaining({
                loading: false,
                error: mockError,
                items: []
            }),
            undefined
        );
    });

    it("handles successful deletion of a scholarship condition group", async () => {
        const mockGroups: Tables<"groups_condition">[] = [
            {
                id: "1",
                name: "Test Group",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            }
        ];
        const refetchMock = jest.fn();
        useScholarshipConditionGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: refetchMock
        });

        const deleteScholarshipConditionGroupMock = require("@/app/actions/scholarship-condition-group-actions")
            .deleteScholarshipConditionGroup as jest.Mock;
        deleteScholarshipConditionGroupMock.mockResolvedValue({ success: true });

        render(<ScholarshipConditionGroupsPage />);

        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.onRefetch).toBe(refetchMock);
        const result = await adminTableProps.onDelete("1");

        expect(deleteScholarshipConditionGroupMock).toHaveBeenCalledWith("1");
        expect(result).toEqual({ success: true });
    });

    it("handles deletion error for a scholarship condition group", async () => {
        const mockGroups: Tables<"groups_condition">[] = [
            {
                id: "1",
                name: "Test Group",
                created_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z"
            }
        ];
        const refetchMock = jest.fn();
        useScholarshipConditionGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: refetchMock
        });

        const deleteScholarshipConditionGroupMock = require("@/app/actions/scholarship-condition-group-actions")
            .deleteScholarshipConditionGroup as jest.Mock;
        deleteScholarshipConditionGroupMock.mockResolvedValue({ success: false, error: "Deletion failed" });

        render(<ScholarshipConditionGroupsPage />);

        const adminTableProps = AdminTableMock.mock.calls[0][0];
        const result = await adminTableProps.onDelete("1");

        expect(deleteScholarshipConditionGroupMock).toHaveBeenCalledWith("1");
        expect(result).toEqual({ success: false, error: "Deletion failed" });
        expect(refetchMock).not.toHaveBeenCalled();
    });
});
