import React from "react";
import { render, screen } from "@testing-library/react";
import NewScholarshipConditionGroupPage from "@/app/(admin)/admin/scholarships/conditions/groups/new/page";

// Mock the form component
jest.mock("@/components/forms/condition-group-form", () => ({
    ConditionGroupForm: jest.fn(() => <div data-testid="condition-group-form-mock" />)
}));

describe("NewScholarshipConditionGroupPage", () => {
    it("renders the ConditionGroupForm component", () => {
        render(<NewScholarshipConditionGroupPage />);
        expect(screen.getByTestId("condition-group-form-mock")).toBeInTheDocument();
    });
});
