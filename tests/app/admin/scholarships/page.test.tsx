import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import ScholarshipsPage from "@/app/(admin)/admin/scholarships/page";
import * as groupActions from "@/app/actions/scholarship-group-actions";
import * as scholarshipActions from "@/app/actions/scholarship-actions";
import { ADMIN_SCHOLARSHIPS_TEXTS } from "@/lib/scholarship-constants";

// Mock dependencies
jest.mock("@/hooks/use-admin-scholarships", () => ({
    useAdminScholarships: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(() => <div data-testid="admin-table-mock">Admin Table</div>)
}));

const useAdminScholarshipsMock = require("@/hooks/use-admin-scholarships").useAdminScholarships as jest.Mock;
const AdminTableMock = require("@/components/table/admin-table").AdminTable as jest.Mock;

describe("ScholarshipsPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props when data is loaded", async () => {
        const mockScholarships = [
            {
                id: "1",
                title: "Test Scholarship",
                description: "Test description",
                amount: 1000,
                enabled: true,
                created_at: "2023-01-01T00:00:00Z"
            }
        ];

        useAdminScholarshipsMock.mockReturnValue({
            items: mockScholarships,
            loading: false,
            error: null,
            handleDelete: jest.fn(),
            refetch: jest.fn()
        });

        render(<ScholarshipsPage />);

        await waitFor(() => {
            expect(screen.getByTestId("admin-table-mock")).toBeInTheDocument();
        });

        expect(AdminTableMock).toHaveBeenCalled();
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.title).toBe(ADMIN_SCHOLARSHIPS_TEXTS.pageTitle);
        expect(adminTableProps.items).toEqual(mockScholarships);
        expect(adminTableProps.loading).toBe(false);
        expect(adminTableProps.error).toBe(null);
    });

    it("renders AdminTable with loading state", async () => {
        useAdminScholarshipsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            handleDelete: jest.fn(),
            refetch: jest.fn()
        });

        render(<ScholarshipsPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.loading).toBe(true);
    });

    it("renders AdminTable with error state", async () => {
        const mockError = "Failed to load scholarships";
        useAdminScholarshipsMock.mockReturnValue({
            items: [],
            loading: false,
            error: mockError,
            handleDelete: jest.fn(),
            refetch: jest.fn()
        });

        render(<ScholarshipsPage />);

        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.error).toBe(mockError);
    });
});
