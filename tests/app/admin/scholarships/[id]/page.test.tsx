import React from "react";
import { render, screen } from "@testing-library/react";
import EditScholarshipPage from "@/app/(admin)/admin/scholarships/[id]/page";

// Mock dependencies
jest.mock("react", () => ({
    ...jest.requireActual("react"),
    use: jest.fn()
}));
jest.mock("@/components/forms/scholarship-form", () => ({
    ScholarshipForm: jest.fn(() => <div>Scholarship Form Mock</div>)
}));

const useMock = React.use as jest.Mock;
const ScholarshipFormMock = require("@/components/forms/scholarship-form").ScholarshipForm as jest.Mock;

describe("EditScholarshipPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the ScholarshipForm with the correct id", () => {
        const mockParams = Promise.resolve({ id: "123" });
        useMock.mockReturnValue({ id: "123" });

        render(<EditScholarshipPage params={mockParams} />);

        expect(screen.getByRole("heading", { name: "עריכת מלגה" })).toBeInTheDocument();
        expect(ScholarshipFormMock).toHaveBeenCalledWith({ scholarshipId: "123" }, undefined);
    });
});
