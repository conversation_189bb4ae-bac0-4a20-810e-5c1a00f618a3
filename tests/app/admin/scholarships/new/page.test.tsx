import React from "react";
import { render, screen } from "@testing-library/react";
import NewScholarshipPage from "@/app/(admin)/admin/scholarships/new/page";
import { TEXTS } from "@/lib/scholarship-constants";

// Mock the form component
jest.mock("@/components/forms/scholarship-form", () => ({
    ScholarshipForm: jest.fn(() => <div data-testid="scholarship-form-mock" />)
}));

describe("NewScholarshipPage", () => {
    it("renders the ScholarshipForm component and the page title", () => {
        render(<NewScholarshipPage />);
        expect(screen.getByRole("heading", { name: TEXTS.newScholarshipPageTitle })).toBeInTheDocument();
        expect(screen.getByTestId("scholarship-form-mock")).toBeInTheDocument();
    });
});
