import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditBannerPage from "@/app/(admin)/admin/banners/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/banner-form", () => ({
    BannerForm: jest.fn(() => <div data-testid="banner-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const BannerFormMock = require("@/components/forms/banner-form").BannerForm as jest.Mock;

describe("EditBannerPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the BannerForm with the correct id", () => {
        const bannerId = "test-id";
        useParamsMock.mockReturnValue({ id: bannerId });

        render(<EditBannerPage />);

        expect(screen.getByRole("heading", { name: "עריכת באנר" })).toBeInTheDocument();
        expect(BannerFormMock).toHaveBeenCalledWith({ bannerId }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({}); // No id in params

        render(<EditBannerPage />);

        expect(screen.getByText("שגיאה: מספר באנר לא נמצא.")).toBeInTheDocument();
        expect(BannerFormMock).not.toHaveBeenCalled();
    });
});
