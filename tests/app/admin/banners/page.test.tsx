import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import BannersPage from "@/app/(admin)/admin/banners/page";
import { ADMIN_BANNERS_TEXTS } from "@/lib/banner-constants";

// Mock dependencies
jest.mock("@/hooks/use-admin-banners", () => ({
    useAdminBanners: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn((props) => (
        <div data-testid="admin-table-mock" {...props}>
            Admin Table
        </div>
    ))
}));

const useAdminBannersMock = require("@/hooks/use-admin-banners").useAdminBanners as jest.Mock;
const AdminTableMock = jest.requireMock("@/components/table/admin-table").AdminTable as jest.Mock;

describe("BannersPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props", async () => {
        const mockBanners = [{ id: "1", text: "Banner 1" }];
        useAdminBannersMock.mockReturnValue({
            items: mockBanners,
            loading: false,
            error: null,
            handleDelete: jest.fn()
        });

        render(<BannersPage />);

        await waitFor(() => {
            expect(screen.getByTestId("admin-table-mock")).toBeInTheDocument();
        });

        expect(AdminTableMock).toHaveBeenCalled();
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.title).toBe(ADMIN_BANNERS_TEXTS.pageTitle);
        expect(adminTableProps.items).toEqual(mockBanners);
        expect(adminTableProps.loading).toBe(false);
        expect(adminTableProps.error).toBe(null);
    });

    it("renders AdminTable with loading state", async () => {
        useAdminBannersMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            handleDelete: jest.fn(),
            refetch: jest.fn()
        });

        render(<BannersPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.loading).toBe(true);
    });

    it("renders AdminTable with error state", async () => {
        const mockError = "Failed to load banners";
        useAdminBannersMock.mockReturnValue({
            items: [],
            loading: false,
            error: mockError,
            handleDelete: jest.fn(),
            refetch: jest.fn()
        });

        render(<BannersPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.error).toBe(mockError);
    });

    it("handles banner deletion successfully", async () => {
        const mockBanners = [{ id: "1", text: "Banner 1" }];
        const handleDeleteMock = jest.fn().mockResolvedValue({ success: true });
        const refetchMock = jest.fn();

        useAdminBannersMock.mockReturnValue({
            items: mockBanners,
            loading: false,
            error: null,
            handleDelete: handleDeleteMock,
            refetch: refetchMock
        });

        render(<BannersPage />);

        // Wait for the component to render and pass props to AdminTable
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });

        const adminTableProps = AdminTableMock.mock.calls[0][0];
        // Simulate calling onDelete from AdminTable
        const result = await adminTableProps.onDelete("1");

        expect(handleDeleteMock).toHaveBeenCalledWith("1");
        expect(result).toEqual({ success: true });
        // Verify that refetch is called after successful deletion by the handleDeleteMock
        // (this is handled internally by useAdminBanners, not directly by BannersPage)
    });

    it("handles banner deletion with error", async () => {
        const mockBanners = [{ id: "1", text: "Banner 1" }];
        const handleDeleteMock = jest.fn().mockResolvedValue({ success: false, error: "Deletion failed" });
        const refetchMock = jest.fn();

        useAdminBannersMock.mockReturnValue({
            items: mockBanners,
            loading: false,
            error: null,
            handleDelete: handleDeleteMock,
            refetch: refetchMock
        });

        render(<BannersPage />);

        // Wait for the component to render and pass props to AdminTable
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });

        const adminTableProps = AdminTableMock.mock.calls[0][0];
        // Simulate calling onDelete from AdminTable
        const result = await adminTableProps.onDelete("1");

        expect(handleDeleteMock).toHaveBeenCalledWith("1");
        expect(result).toEqual({ success: false, error: "Deletion failed" });
    });
});
