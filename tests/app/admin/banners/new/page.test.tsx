import React from "react";
import { render, screen } from "@testing-library/react";
import NewBannerPage from "@/app/(admin)/admin/banners/new/page";
import { TEXTS } from "@/lib/banner-constants";

// Mock the form component
jest.mock("@/components/forms/banner-form", () => ({
    BannerForm: jest.fn(() => <div data-testid="banner-form-mock" />)
}));

describe("NewBannerPage", () => {
    it("renders the BannerForm component and the page title", () => {
        render(<NewBannerPage />);
        expect(screen.getByRole("heading", { name: TEXTS.pageTitle })).toBeInTheDocument();
        expect(screen.getByTestId("banner-form-mock")).toBeInTheDocument();
    });
});
