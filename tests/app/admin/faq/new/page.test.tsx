import React from "react";
import { render, screen } from "@testing-library/react";
import NewFaqPage from "@/app/(admin)/admin/faq/new/page";

// Mock the form component
jest.mock("@/components/forms/faq-form", () => ({
    FaqForm: jest.fn(() => <div data-testid="faq-form-mock" />)
}));

describe("NewFaqPage", () => {
    it("renders the FaqForm component and the page title", () => {
        render(<NewFaqPage />);
        expect(screen.getByRole("heading", { name: "שאלה חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("faq-form-mock")).toBeInTheDocument();
    });
});
