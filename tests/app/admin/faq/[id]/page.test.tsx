import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditFaqPage from "@/app/(admin)/admin/faq/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/faq-form", () => ({
    FaqForm: jest.fn(() => <div data-testid="faq-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const FaqFormMock = require("@/components/forms/faq-form").FaqForm as jest.Mock;

describe("EditFaqPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the FaqForm with the correct faqId", () => {
        const faqId = "test-faq-id";
        useParamsMock.mockReturnValue({ id: faqId });

        render(<EditFaqPage />);

        expect(screen.getByRole("heading", { name: "עריכת שאלה" })).toBeInTheDocument();
        expect(FaqFormMock).toHaveBeenCalledWith({ faqId }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({}); // No id in params

        render(<EditFaqPage />);

        expect(screen.getByText("שגיאה: מספר שאלה לא נמצא.")).toBeInTheDocument();
        expect(FaqFormMock).not.toHaveBeenCalled();
    });
});
