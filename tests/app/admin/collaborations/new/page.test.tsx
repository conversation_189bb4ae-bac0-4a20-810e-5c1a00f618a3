import React from "react";
import { render, screen } from "@testing-library/react";
import NewCollaborationPage from "@/app/(admin)/admin/collaborations/new/page";
import { TEXTS } from "@/lib/collaboration-constants";

// Mock the form component
jest.mock("@/components/forms/collaboration-form", () => ({
    CollaborationForm: jest.fn(() => <div data-testid="collaboration-form-mock" />)
}));

describe("NewCollaborationPage", () => {
    it("renders the CollaborationForm component and the page title", () => {
        render(<NewCollaborationPage />);
        expect(screen.getByRole("heading", { name: TEXTS.pageTitle.new })).toBeInTheDocument();
        expect(screen.getByTestId("collaboration-form-mock")).toBeInTheDocument();
    });
});
