import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditCollaborationPage from "@/app/(admin)/admin/collaborations/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/collaboration-form", () => ({
    CollaborationForm: jest.fn(() => <div data-testid="collaboration-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const CollaborationFormMock = require("@/components/forms/collaboration-form").CollaborationForm as jest.Mock;

describe("EditCollaborationPage", () => {
    it("renders the CollaborationForm with the correct id", () => {
        const collaborationId = "test-id";
        useParamsMock.mockReturnValue({ id: collaborationId });

        render(<EditCollaborationPage />);

        expect(screen.getByRole("heading", { name: "עריכת שיתוף פעולה" })).toBeInTheDocument();
        expect(CollaborationFormMock).toHaveBeenCalledWith({ collaborationId }, undefined);
    });
});
