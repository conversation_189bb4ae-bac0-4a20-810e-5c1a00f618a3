import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import CollaborationsPage from "@/app/(admin)/admin/collaborations/page";
import { ADMIN_COLLABORATIONS_TEXTS } from "@/lib/collaboration-constants";

// Mock dependencies
jest.mock("@/hooks/use-collaborations", () => ({
    useCollaborations: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(() => <div data-testid="admin-table-mock">Admin Table</div>)
}));

const useCollaborationsMock = require("@/hooks/use-collaborations").useCollaborations as jest.Mock;
const AdminTableMock = jest.requireMock("@/components/table/admin-table").AdminTable as jest.Mock;

describe("CollaborationsPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props", async () => {
        const mockCollaborations = [{ id: "1", name: "Test Collaboration" }];
        useCollaborationsMock.mockReturnValue({
            items: mockCollaborations,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<CollaborationsPage />);

        await waitFor(() => {
            expect(screen.getByTestId("admin-table-mock")).toBeInTheDocument();
        });

        expect(AdminTableMock).toHaveBeenCalled();
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.title).toBe(ADMIN_COLLABORATIONS_TEXTS.pageTitle);
        expect(adminTableProps.items).toEqual(mockCollaborations);
        expect(adminTableProps.loading).toBe(false);
        expect(adminTableProps.error).toBe(null);
    });

    it("renders AdminTable with loading state", async () => {
        useCollaborationsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        render(<CollaborationsPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.loading).toBe(true);
    });

    it("renders AdminTable with error state", async () => {
        const mockError = "Failed to load collaborations";
        useCollaborationsMock.mockReturnValue({
            items: [],
            loading: false,
            error: mockError,
            refetch: jest.fn()
        });

        render(<CollaborationsPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.error).toBe(mockError);
    });
});
