import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditTestimonialPage from "@/app/(admin)/admin/testimonials/[id]/page";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/testimonial-form", () => ({
    TestimonialForm: jest.fn(() => <div data-testid="testimonial-form-mock" />)
}));

const useParamsMock = useParams as jest.Mock;
const TestimonialFormMock = require("@/components/forms/testimonial-form").TestimonialForm as jest.Mock;

describe("EditTestimonialPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the TestimonialForm with the correct testimonialId", () => {
        const testimonialId = "test-testimonial-id";
        useParamsMock.mockReturnValue({ id: testimonialId });

        render(<EditTestimonialPage />);

        expect(screen.getByRole("heading", { name: "עריכת ביקורת" })).toBeInTheDocument();
        expect(TestimonialFormMock).toHaveBeenCalledWith({ testimonialId }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({}); // No id in params

        render(<EditTestimonialPage />);

        expect(screen.getByText("שגיאה: מספר ביקורת לא נמצא.")).toBeInTheDocument();
        expect(TestimonialFormMock).not.toHaveBeenCalled();
    });
});
