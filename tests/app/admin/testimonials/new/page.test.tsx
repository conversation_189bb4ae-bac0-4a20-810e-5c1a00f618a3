import React from "react";
import { render, screen } from "@testing-library/react";
import NewTestimonialPage from "@/app/(admin)/admin/testimonials/new/page";
import { TESTIMONIAL_TEXTS } from "@/lib/testimonial-constants";

// Mock the form component
jest.mock("@/components/forms/testimonial-form", () => ({
    TestimonialForm: jest.fn(() => <div data-testid="testimonial-form-mock" />)
}));

describe("NewTestimonialPage", () => {
    it("renders the TestimonialForm component and the page title", () => {
        render(<NewTestimonialPage />);
        expect(screen.getByRole("heading", { name: TESTIMONIAL_TEXTS.newTestimonialPageTitle })).toBeInTheDocument();
        expect(screen.getByTestId("testimonial-form-mock")).toBeInTheDocument();
    });
});
