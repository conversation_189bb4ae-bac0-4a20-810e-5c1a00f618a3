import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import TestimonialsPage from "@/app/(admin)/admin/testimonials/page";
import { TESTIMONIAL_TEXTS } from "@/lib/testimonial-constants";
import * as testimonialActions from "@/app/actions/testimonial-actions";
import { type Database } from "@/types/database.types";

// Mock dependencies
jest.mock("@/app/actions/testimonial-actions", () => ({
    getAllTestimonials: jest.fn(),
    deleteTestimonial: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(() => <div data-testid="admin-table-mock">Admin Table</div>)
}));

const getAllTestimonialsMock = testimonialActions.getAllTestimonials as jest.Mock;
const AdminTableMock = jest.requireMock("@/components/table/admin-table").AdminTable as jest.Mock;

describe("TestimonialsPage", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props", async () => {
        const mockTestimonials: { success: boolean; data: Database["public"]["Tables"]["testimonials"]["Row"][] } = {
            success: true,
            data: [
                {
                    id: "1",
                    name: "Test User",
                    text: "Great!",
                    type: "personal" as Database["public"]["Enums"]["testimonial_type"],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: "2",
                    name: "Test Institution",
                    text: "Awesome!",
                    type: "institution" as Database["public"]["Enums"]["testimonial_type"],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ]
        };
        getAllTestimonialsMock.mockResolvedValue(mockTestimonials);

        render(<TestimonialsPage />);

        await waitFor(() => {
            expect(getAllTestimonialsMock).toHaveBeenCalled();
            const lastCallArgs = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
            expect(lastCallArgs.items).toEqual(mockTestimonials.data);
        });

        const finalProps = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
        expect(finalProps.title).toBe(TESTIMONIAL_TEXTS.pageTitle);
        expect(finalProps.items).toEqual(mockTestimonials.data);
        expect(finalProps.loading).toBe(false);
        expect(finalProps.error).toBe(null);
    });

    it("renders AdminTable in loading state", async () => {
        // getAllTestimonials never resolves, so loading stays true
        getAllTestimonialsMock.mockReturnValue(new Promise(() => {}));
        render(<TestimonialsPage />);
        // Loading state is passed to AdminTable on first render
        const firstCallProps = AdminTableMock.mock.calls[0][0];
        expect(firstCallProps.loading).toBe(true);
    });

    it("renders AdminTable with error if fetch fails", async () => {
        getAllTestimonialsMock.mockResolvedValue({ success: false, error: "Some error" });
        render(<TestimonialsPage />);
        await waitFor(() => {
            const lastCallArgs = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
            expect(lastCallArgs.error).toBeInstanceOf(Error);
            expect(lastCallArgs.error.message).toBe("Some error");
        });
    });

    it("renders AdminTable with empty data set", async () => {
        const mockEmptyTestimonials: { success: boolean; data: Database["public"]["Tables"]["testimonials"]["Row"][] } =
            {
                success: true,
                data: []
            };
        getAllTestimonialsMock.mockResolvedValue(mockEmptyTestimonials);
        render(<TestimonialsPage />);
        await waitFor(() => {
            const lastCallArgs = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
            expect(lastCallArgs.items).toEqual([]);
            expect(lastCallArgs.loading).toBe(false);
            expect(lastCallArgs.error).toBe(null);
        });
    });

    it("calls deleteTestimonial and handles success", async () => {
        const mockTestimonials: { success: boolean; data: Database["public"]["Tables"]["testimonials"]["Row"][] } = {
            success: true,
            data: [
                {
                    id: "1",
                    name: "Test User",
                    text: "Great!",
                    type: "personal" as Database["public"]["Enums"]["testimonial_type"],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ]
        };
        getAllTestimonialsMock.mockResolvedValue(mockTestimonials);
        const deleteTestimonialMock = testimonialActions.deleteTestimonial as jest.Mock;
        deleteTestimonialMock.mockResolvedValue({ success: true });
        render(<TestimonialsPage />);
        await waitFor(() => {
            expect(getAllTestimonialsMock).toHaveBeenCalled();
        });
        const finalProps = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
        const result = await finalProps.onDelete("1");
        expect(deleteTestimonialMock).toHaveBeenCalledWith("1");
        expect(result).toEqual({ success: true });
    });

    it("calls deleteTestimonial and handles error", async () => {
        const mockTestimonials: { success: boolean; data: Database["public"]["Tables"]["testimonials"]["Row"][] } = {
            success: true,
            data: [
                {
                    id: "1",
                    name: "Test User",
                    text: "Great!",
                    type: "personal" as Database["public"]["Enums"]["testimonial_type"],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ]
        };
        getAllTestimonialsMock.mockResolvedValue(mockTestimonials);
        const deleteTestimonialMock = testimonialActions.deleteTestimonial as jest.Mock;
        deleteTestimonialMock.mockResolvedValue({ success: false, error: "Delete failed" });
        render(<TestimonialsPage />);
        await waitFor(() => {
            expect(getAllTestimonialsMock).toHaveBeenCalled();
        });
        const finalProps = AdminTableMock.mock.calls[AdminTableMock.mock.calls.length - 1][0];
        const result = await finalProps.onDelete("1");
        expect(deleteTestimonialMock).toHaveBeenCalledWith("1");
        expect(result.success).toBe(false);
        expect(result.error).toBe("Delete failed");
    });
});
