import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditContactPage from "@/app/(admin)/admin/contact/[id]/page";
import { TEXTS } from "@/lib/contact-constants";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/contact-form", () => ({
    ContactForm: jest.fn(() => <div>Contact Form Mock</div>)
}));

const useParamsMock = useParams as jest.Mock;
const ContactFormMock = require("@/components/forms/contact-form").ContactForm as jest.Mock;

describe("EditContactPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the ContactForm with the correct id", () => {
        useParamsMock.mockReturnValue({ id: "123" });
        render(<EditContactPage />);
        expect(screen.getByText(TEXTS.editPageTitle)).toBeInTheDocument();
        expect(ContactFormMock).toHaveBeenCalledWith({ contactId: "123" }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({ id: null });
        render(<EditContactPage />);
        expect(screen.getByText(TEXTS.editErrorNotFound)).toBeInTheDocument();
        expect(ContactFormMock).not.toHaveBeenCalled();
    });
});
