import React from "react";
import { render, screen } from "@testing-library/react";
import NewContactPage from "@/app/(admin)/admin/contact/new/page";

// Mock the form component
jest.mock("@/components/forms/contact-form", () => ({
    ContactForm: jest.fn(() => <div data-testid="contact-form-mock" />)
}));

describe("NewContactPage", () => {
    it("renders the ContactForm component and the page title", () => {
        render(<NewContactPage />);
        expect(screen.getByRole("heading", { name: "פנייה חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("contact-form-mock")).toBeInTheDocument();
    });
});
