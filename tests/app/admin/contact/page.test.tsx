import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import ContactPage from "@/app/(admin)/admin/contact/page";
import { ADMIN_CONTACTS_TEXTS } from "@/lib/contact-constants";

// Mock dependencies
jest.mock("@/hooks/use-contacts", () => ({
    useContacts: jest.fn()
}));

jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(() => <div data-testid="admin-table-mock">Admin Table</div>)
}));

const useContactsMock = require("@/hooks/use-contacts").useContacts as jest.Mock;
const AdminTableMock = jest.requireMock("@/components/table/admin-table").AdminTable as jest.Mock;

describe("ContactPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders AdminTable with correct props", async () => {
        const mockContacts = [{ id: "1", email: "<EMAIL>" }];
        useContactsMock.mockReturnValue({
            items: mockContacts,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<ContactPage />);

        await waitFor(() => {
            expect(screen.getByTestId("admin-table-mock")).toBeInTheDocument();
        });

        expect(AdminTableMock).toHaveBeenCalled();
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.title).toBe(ADMIN_CONTACTS_TEXTS.pageTitle);
        expect(adminTableProps.items).toEqual(mockContacts);
        expect(adminTableProps.loading).toBe(false);
        expect(adminTableProps.error).toBe(null);
    });

    it("renders AdminTable with loading state", async () => {
        useContactsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        render(<ContactPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.loading).toBe(true);
    });

    it("renders AdminTable with error state", async () => {
        const mockError = "Failed to load contacts";
        useContactsMock.mockReturnValue({
            items: [],
            loading: false,
            error: mockError,
            refetch: jest.fn()
        });

        render(<ContactPage />);
        await waitFor(() => {
            expect(AdminTableMock).toHaveBeenCalled();
        });
        const adminTableProps = AdminTableMock.mock.calls[0][0];
        expect(adminTableProps.error).toBe(mockError);
    });
});
