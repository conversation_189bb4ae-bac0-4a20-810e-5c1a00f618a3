import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditCouponPage from "@/app/(admin)/admin/coupons/[id]/page";
import { TEXTS } from "@/lib/coupon-constants";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/coupon-form", () => ({
    CouponForm: jest.fn(() => <div>Coupon Form Mock</div>)
}));

const useParamsMock = useParams as jest.Mock;
const CouponFormMock = require("@/components/forms/coupon-form").CouponForm as jest.Mock;

describe("EditCouponPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the CouponForm with the correct id", () => {
        useParamsMock.mockReturnValue({ id: "123" });
        render(<EditCouponPage />);
        expect(screen.getByText(TEXTS.formTitleEdit)).toBeInTheDocument();
        expect(CouponFormMock).toHaveBeenCalledWith({ couponId: "123" }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({ id: null });
        render(<EditCouponPage />);
        expect(screen.getByText(TEXTS.editErrorNotFound)).toBeInTheDocument();
        expect(CouponFormMock).not.toHaveBeenCalled();
    });
});
