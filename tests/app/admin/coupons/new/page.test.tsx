import React from "react";
import { render, screen } from "@testing-library/react";
import NewCouponPage from "@/app/(admin)/admin/coupons/new/page";

// Mock the form component
jest.mock("@/components/forms/coupon-form", () => ({
    CouponForm: jest.fn(() => <div data-testid="coupon-form-mock" />)
}));

describe("NewCouponPage", () => {
    it("renders the CouponForm component and the page title", () => {
        render(<NewCouponPage />);
        expect(screen.getByRole("heading", { name: "קופון חדש" })).toBeInTheDocument();
        expect(screen.getByTestId("coupon-form-mock")).toBeInTheDocument();
    });
});
