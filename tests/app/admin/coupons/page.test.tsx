import React, { Suspense } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import CouponsPage from "@/app/(admin)/admin/coupons/page";

// Mock the content component
jest.mock("@/app/(admin)/admin/coupons/coupons-content", () => ({
    CouponsContent: jest.fn(() => <div data-testid="coupons-content-mock">Coupons Content</div>)
}));

const CouponsContentMock = require("@/app/(admin)/admin/coupons/coupons-content").CouponsContent as jest.Mock;

describe("CouponsPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders CouponsContent within Suspense", async () => {
        render(
            <Suspense fallback={<div>Loading...</div>}>
                <CouponsPage />
            </Suspense>
        );

        // Wait for the lazy-loaded component to appear
        await waitFor(() => {
            expect(screen.getByTestId("coupons-content-mock")).toBeInTheDocument();
        });
    });
});
