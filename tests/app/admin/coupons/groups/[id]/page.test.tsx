import React from "react";
import { render, screen } from "@testing-library/react";
import { useParams } from "next/navigation";
import EditCouponGroupPage from "@/app/(admin)/admin/coupons/groups/[id]/page";
import { TEXTS as COUPON_GROUP_TEXTS } from "@/lib/coupon-group-constants";

// Mock dependencies
jest.mock("next/navigation", () => ({
    useParams: jest.fn()
}));
jest.mock("@/components/forms/coupon-group-form", () => ({
    CouponGroupForm: jest.fn(() => <div>Coupon Group Form Mock</div>)
}));

const useParamsMock = useParams as jest.Mock;
const CouponGroupFormMock = require("@/components/forms/coupon-group-form").CouponGroupForm as jest.Mock;

describe("EditCouponGroupPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the CouponGroupForm with the correct id", () => {
        useParamsMock.mockReturnValue({ id: "123" });
        render(<EditCouponGroupPage />);
        expect(screen.getByRole("heading", { name: COUPON_GROUP_TEXTS.editPageTitle })).toBeInTheDocument();
        expect(CouponGroupFormMock).toHaveBeenCalledWith({ groupId: "123" }, undefined);
    });

    it("renders an error message if id is not found", () => {
        useParamsMock.mockReturnValue({ id: null });
        render(<EditCouponGroupPage />);
        expect(screen.getByText(COUPON_GROUP_TEXTS.couponGroupNotFound)).toBeInTheDocument();
        expect(CouponGroupFormMock).not.toHaveBeenCalled();
    });
});
