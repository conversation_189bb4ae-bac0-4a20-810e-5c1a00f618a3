import React from "react";
import { render, screen } from "@testing-library/react";
import NewCouponGroupPage from "@/app/(admin)/admin/coupons/groups/new/page";

// Mock the form component
jest.mock("@/components/forms/coupon-group-form", () => ({
    CouponGroupForm: jest.fn(() => <div data-testid="coupon-group-form-mock" />)
}));

describe("NewCouponGroupPage", () => {
    it("renders the CouponGroupForm component and the page title", () => {
        render(<NewCouponGroupPage />);
        expect(screen.getByRole("heading", { name: "יצירת קבוצת קופונים חדשה" })).toBeInTheDocument();
        expect(screen.getByTestId("coupon-group-form-mock")).toBeInTheDocument();
    });
});
