import React from "react";
import { render, screen } from "@testing-library/react";
import { CouponsContent } from "@/app/(admin)/admin/coupons/coupons-content";
import { Database } from "@/types/database.types";
import { AdminTableProps } from "@/components/table/types";

// Mock the correct hooks
jest.mock("@/hooks/use-coupons", () => ({
    useCoupons: jest.fn()
}));

jest.mock("@/hooks/use-coupon-groups", () => ({
    useCouponGroups: jest.fn()
}));

// Mock components
jest.mock("@/components/table/admin-table", () => ({
    AdminTable: jest.fn(({ loading, error, loadingText, errorPrefix, noItemsText }) => {
        if (loading) {
            return <div data-testid="admin-table-mock">{loadingText}</div>;
        }
        if (error) {
            return (
                <div data-testid="admin-table-mock">
                    {errorPrefix} {error.message}
                </div>
            );
        }
        return <div data-testid="admin-table-mock">Admin Table</div>;
    })
}));

jest.mock("@/components/ui/tabs", () => ({
    Tabs: jest.fn(({ children }) => <div data-testid="tabs-mock">{children}</div>),
    TabsContent: jest.fn(({ children }) => <div data-testid="tabs-content-mock">{children}</div>),
    TabsList: jest.fn(({ children }) => <div data-testid="tabs-list-mock">{children}</div>),
    TabsTrigger: jest.fn(({ children }) => <button data-testid="tabs-trigger-mock">{children}</button>)
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
    useSearchParams: jest.fn(() => ({
        get: jest.fn(() => null)
    }))
}));

// Mock server actions
jest.mock("@/app/actions/coupon-actions", () => ({
    deleteCoupon: jest.fn()
}));

jest.mock("@/app/actions/coupon-group-actions", () => ({
    deleteCouponGroup: jest.fn()
}));

// Helpers to access mocked AdminTable calls
const getAdminTableCalls = () => {
    const adminTableMock = require("@/components/table/admin-table").AdminTable as jest.Mock;
    return adminTableMock.mock.calls as Array<[Record<string, unknown>]>;
};

const useCouponsMock = require("@/hooks/use-coupons").useCoupons as jest.Mock;
const useCouponGroupsMock = require("@/hooks/use-coupon-groups").useCouponGroups as jest.Mock;

const deleteCouponMock = require("@/app/actions/coupon-actions").deleteCoupon as jest.Mock;
const deleteCouponGroupMock = require("@/app/actions/coupon-group-actions").deleteCouponGroup as jest.Mock;

describe("CouponsContent", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders tabs and AdminTable when data is loaded", () => {
        const mockCoupons: Database["public"]["Tables"]["coupons"]["Row"][] = [
            {
                id: "1",
                coupon_code: "TEST10",
                discount_value: 10,
                coupon_type: "percentage",
                expiration_date: null,
                used_count: 0,
                usage_limit: null,
                coupon_group_id: "test-group-id",
                created_at: "2023-01-01T12:00:00Z",
                updated_at: "2023-01-01T12:00:00Z"
            }
        ];

        const mockGroups: Database["public"]["Tables"]["groups_coupon"]["Row"][] = [
            {
                id: "1",
                name: "Test Group",
                description: "Test description",
                created_at: "2023-01-01T12:00:00Z",
                updated_at: "2023-01-01T12:00:00Z"
            }
        ];

        useCouponsMock.mockReturnValue({
            items: mockCoupons,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        useCouponGroupsMock.mockReturnValue({
            items: mockGroups,
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<CouponsContent />);

        expect(screen.getByTestId("tabs-mock")).toBeInTheDocument();
        expect(screen.getAllByTestId("admin-table-mock")).toHaveLength(2); // One for each tab
    });

    it("renders loading state for coupons and groups", () => {
        useCouponsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        useCouponGroupsMock.mockReturnValue({
            items: [],
            loading: true,
            error: null,
            refetch: jest.fn()
        });

        render(<CouponsContent />);

        expect(screen.getByText("טוען קופונים...")).toBeInTheDocument();
        expect(screen.getByText("טוען קבוצות קופונים...")).toBeInTheDocument();
    });

    it("renders error state for coupons and groups", () => {
        useCouponsMock.mockReturnValue({
            items: [],
            loading: false,
            error: new Error("Failed to load coupons"),
            refetch: jest.fn()
        });

        useCouponGroupsMock.mockReturnValue({
            items: [],
            loading: false,
            error: new Error("Failed to load coupon groups"),
            refetch: jest.fn()
        });

        render(<CouponsContent />);

        expect(screen.getByText("שגיאה בטעינת הקופונים: Failed to load coupons")).toBeInTheDocument();
        expect(
            screen.getByText("שגיאה בטעינת קבוצות הקופונים: Error: Failed to load coupon groups")
        ).toBeInTheDocument();
    });

    it("calls deleteCoupon via onDelete from AdminTable", async () => {
        deleteCouponMock.mockResolvedValue({ success: true });

        useCouponsMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        useCouponGroupsMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<CouponsContent />);

        const adminCalls = getAdminTableCalls();
        const couponsTableProps = adminCalls[0][0] as unknown as AdminTableProps<{ id: string }>;

        await couponsTableProps.onDelete("42");

        expect(deleteCouponMock).toHaveBeenCalledWith("42");
    });

    it("calls deleteCouponGroup via onDelete from groups AdminTable", async () => {
        deleteCouponGroupMock.mockResolvedValue({ success: true });

        useCouponsMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        useCouponGroupsMock.mockReturnValue({
            items: [],
            loading: false,
            error: null,
            refetch: jest.fn()
        });

        render(<CouponsContent />);

        const adminCalls = getAdminTableCalls();
        const groupsTableProps = adminCalls[1][0] as unknown as AdminTableProps<{ id: string }>;

        await groupsTableProps.onDelete("99");

        expect(deleteCouponGroupMock).toHaveBeenCalledWith("99");
    });
});
