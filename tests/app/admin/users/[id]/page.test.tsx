import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useParams } from "next/navigation";

import UserDetailPage from "@/app/(admin)/admin/users/[id]/page";
import { getUserDetailsByUserId } from "@/app/actions/user-actions";
import { TEXTS } from "@/lib/user-constants";

jest.mock("@clerk/nextjs/server", () => ({
    auth: jest.fn(() => ({ userId: "test-user-id" }))
}));

jest.mock("next/navigation");
jest.mock("@/app/actions/user-actions");
jest.mock("@/components/common/copy-to-clipboard", () => ({
    CopyToClipboard: ({ text, onSuccessMessage }: { text: string; onSuccessMessage: string }) => (
        <button data-testid="copy-button" data-text={text} data-message={onSuccessMessage}>
            Copy
        </button>
    )
}));

jest.mock("@/app/(admin)/admin/users/[id]/components/user-questions-tab", () => ({
    UserQuestionsTab: ({ userId }: { userId: string }) => (
        <div data-testid="user-questions-tab">Questions content for user: {userId}</div>
    )
}));
jest.mock("@/app/(admin)/admin/users/[id]/components/user-documents-tab", () => ({
    UserDocumentsTab: ({ userId }: { userId: string }) => (
        <div data-testid="user-documents-tab">Documents content for user: {userId}</div>
    )
}));
jest.mock("@/app/(admin)/admin/users/[id]/components/user-package-tab", () => ({
    UserPackageTab: ({ userId }: { userId: string }) => (
        <div data-testid="user-package-tab">Package content for user: {userId}</div>
    )
}));
jest.mock("@/app/(admin)/admin/users/[id]/components/user-notes-tab", () => ({
    UserNotesTab: ({ userId }: { userId: string }) => (
        <div data-testid="user-notes-tab">Notes content for user: {userId}</div>
    )
}));

const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;
const mockGetUserDetailsByUserId = getUserDetailsByUserId as jest.MockedFunction<typeof getUserDetailsByUserId>;

describe("UserDetailPage", () => {
    const mockUserId = "user-123";
    const mockUserDetails = {
        id: "claim-id",
        email: "<EMAIL>",
        user_id: mockUserId
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseParams.mockReturnValue({ id: mockUserId });

        // Mock successful user details fetch by default
        mockGetUserDetailsByUserId.mockResolvedValue({
            success: true,
            user: mockUserDetails
        });
    });

    describe("Rendering", () => {
        it("shows loading state initially", () => {
            render(<UserDetailPage />);

            // Check for skeleton loading elements
            const skeletonElements = screen.getAllByText("", { selector: ".animate-pulse" });
            expect(skeletonElements.length).toBeGreaterThan(0);

            // Check for user details card skeleton
            const userCardSkeleton = document.querySelector(".animate-pulse.h-8.w-48");
            expect(userCardSkeleton).toBeInTheDocument();

            // Check for tabs skeleton
            const tabsSkeleton = document.querySelector(".grid.grid-cols-4.h-12.bg-muted");
            expect(tabsSkeleton).toBeInTheDocument();
        });

        it("renders the page title correctly after loading", async () => {
            render(<UserDetailPage />);

            // Wait for user details to load
            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            expect(screen.getByText(mockUserDetails.email)).toBeInTheDocument();
        });

        it("displays the user ID and email", async () => {
            render(<UserDetailPage />);

            // Wait for user details to load
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${mockUserId}`)).toBeInTheDocument();
                expect(screen.getByText(mockUserDetails.email)).toBeInTheDocument();
            });
        });

        it("renders with proper RTL direction", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            const container = screen.getByText(TEXTS.userDetailsTitle).closest(".container");
            expect(container).toHaveAttribute("dir", "rtl");
        });

        it("renders the user info card", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            const title = screen.getByText(TEXTS.userDetailsTitle);
            const card = title.closest('[class*="card"]');
            expect(card).toBeInTheDocument();
        });

        it("applies proper styling to title", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            const title = screen.getByText(TEXTS.userDetailsTitle);
            expect(title).toHaveClass("text-2xl", "font-bold");
        });

        it("applies muted styling to user ID", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${mockUserId}`)).toBeInTheDocument();
            });

            const userIdElement = screen.getByText(`${TEXTS.userIdLabel}: ${mockUserId}`);
            expect(userIdElement).toHaveClass("text-muted-foreground");
        });
    });

    describe("Tab Navigation", () => {
        it("renders all four tab triggers", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toBeInTheDocument();
            });

            expect(screen.getByRole("tab", { name: TEXTS.documentsTab })).toBeInTheDocument();
            expect(screen.getByRole("tab", { name: TEXTS.packageTab })).toBeInTheDocument();
            expect(screen.getByRole("tab", { name: TEXTS.notesTab })).toBeInTheDocument();
        });

        it("shows questions tab as default selected", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toBeInTheDocument();
            });

            const questionsTab = screen.getByRole("tab", { name: TEXTS.questionsTab });
            expect(questionsTab).toHaveAttribute("aria-selected", "true");
        });

        it("renders tab list with proper grid layout", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tablist")).toBeInTheDocument();
            });

            const tabList = screen.getByRole("tablist");
            expect(tabList).toHaveClass("grid", "grid-cols-4");
        });

        it("applies proper styling to tab triggers", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toBeInTheDocument();
            });

            const questionsTab = screen.getByRole("tab", { name: TEXTS.questionsTab });
            expect(questionsTab).toHaveClass("text-base", "sm:text-sm", "bg-muted/80", "text-muted-foreground");
        });

        it("renders tabs with RTL direction", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tablist")).toBeInTheDocument();
            });

            const tabsContainer = screen.getByRole("tablist").closest('[dir="rtl"]');
            expect(tabsContainer).toBeInTheDocument();
        });
    });

    describe("Tab Content", () => {
        it("shows questions tab content by default", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByTestId("user-questions-tab")).toBeInTheDocument();
            });

            expect(screen.getByText(`Questions content for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("switches to documents tab when clicked", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.documentsTab })).toBeInTheDocument();
            });

            const documentsTab = screen.getByRole("tab", { name: TEXTS.documentsTab });
            await user.click(documentsTab);

            expect(screen.getByTestId("user-documents-tab")).toBeInTheDocument();
            expect(screen.getByText(`Documents content for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("switches to package tab when clicked", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.packageTab })).toBeInTheDocument();
            });

            const packageTab = screen.getByRole("tab", { name: TEXTS.packageTab });
            await user.click(packageTab);

            expect(screen.getByTestId("user-package-tab")).toBeInTheDocument();
            expect(screen.getByText(`Package content for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("switches to notes tab when clicked", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.notesTab })).toBeInTheDocument();
            });

            const notesTab = screen.getByRole("tab", { name: TEXTS.notesTab });
            await user.click(notesTab);

            expect(screen.getByTestId("user-notes-tab")).toBeInTheDocument();
            expect(screen.getByText(`Notes content for user: ${mockUserId}`)).toBeInTheDocument();
        });

        it("passes correct userId prop to all tab components", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete and check questions tab (default)
            await waitFor(() => {
                expect(screen.getByText(`Questions content for user: ${mockUserId}`)).toBeInTheDocument();
            });

            // Check documents tab
            await user.click(screen.getByRole("tab", { name: TEXTS.documentsTab }));
            expect(screen.getByText(`Documents content for user: ${mockUserId}`)).toBeInTheDocument();

            // Check package tab
            await user.click(screen.getByRole("tab", { name: TEXTS.packageTab }));
            expect(screen.getByText(`Package content for user: ${mockUserId}`)).toBeInTheDocument();

            // Check notes tab
            await user.click(screen.getByRole("tab", { name: TEXTS.notesTab }));
            expect(screen.getByText(`Notes content for user: ${mockUserId}`)).toBeInTheDocument();
        });
    });

    describe("URL Parameter Handling", () => {
        it("handles different user IDs from params", async () => {
            const differentUserId = "different-user-456";
            mockUseParams.mockReturnValue({ id: differentUserId });

            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${differentUserId}`)).toBeInTheDocument();
            });

            expect(screen.getByText(`Questions content for user: ${differentUserId}`)).toBeInTheDocument();
        });

        it("handles empty user ID", async () => {
            mockUseParams.mockReturnValue({ id: "" });
            mockGetUserDetailsByUserId.mockResolvedValue({
                success: true,
                user: { id: "empty-claim-id", email: "<EMAIL>", user_id: "" }
            });

            render(<UserDetailPage />);

            expect(screen.getByText(TEXTS.userIdInvalid)).toBeInTheDocument();
        });

        it("handles special characters in user ID", async () => {
            const specialUserId = "user@domain.com_123";
            mockUseParams.mockReturnValue({ id: specialUserId });

            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${specialUserId}`)).toBeInTheDocument();
            });

            expect(screen.getByText(`Questions content for user: ${specialUserId}`)).toBeInTheDocument();
        });

        it("handles long user IDs", async () => {
            const longUserId = "very-long-user-id-that-should-display-properly-123456789";
            mockUseParams.mockReturnValue({ id: longUserId });

            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${longUserId}`)).toBeInTheDocument();
            });
        });
    });

    describe("Loading and Error States", () => {
        it("shows loading icon during data fetching", () => {
            render(<UserDetailPage />);

            // Check for skeleton loading elements
            const skeletonElements = screen.getAllByText("", { selector: ".animate-pulse" });
            expect(skeletonElements.length).toBeGreaterThan(0);

            // Check for user details card skeleton
            const userCardSkeleton = document.querySelector(".animate-pulse.h-8.w-48");
            expect(userCardSkeleton).toBeInTheDocument();

            // Check for tabs skeleton
            const tabsSkeleton = document.querySelector(".grid.grid-cols-4.h-12.bg-muted");
            expect(tabsSkeleton).toBeInTheDocument();
        });

        it("shows error message when user details fail to load", async () => {
            mockGetUserDetailsByUserId.mockResolvedValue({
                success: false,
                error: "User not found"
            });

            render(<UserDetailPage />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            expect(screen.getByText("User not found")).toBeInTheDocument();
        });

        it("shows generic error when an exception occurs", async () => {
            mockGetUserDetailsByUserId.mockRejectedValue(new Error("Network error"));

            render(<UserDetailPage />);

            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            expect(screen.getByText(TEXTS.genericError)).toBeInTheDocument();
        });
    });

    describe("Text Constants Integration", () => {
        it("uses centralized constants for all text content", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByText(TEXTS.userDetailsTitle)).toBeInTheDocument();
            });

            // Check user ID label
            expect(screen.getByText(`${TEXTS.userIdLabel}: ${mockUserId}`)).toBeInTheDocument();

            // Check all tab titles
            expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toBeInTheDocument();
            expect(screen.getByRole("tab", { name: TEXTS.documentsTab })).toBeInTheDocument();
            expect(screen.getByRole("tab", { name: TEXTS.packageTab })).toBeInTheDocument();
            expect(screen.getByRole("tab", { name: TEXTS.notesTab })).toBeInTheDocument();
        });
    });

    describe("Tab State Management", () => {
        it("maintains tab state during interactions", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toBeInTheDocument();
            });

            // Start on questions tab
            expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toHaveAttribute("aria-selected", "true");

            // Switch to documents tab
            await user.click(screen.getByRole("tab", { name: TEXTS.documentsTab }));
            expect(screen.getByRole("tab", { name: TEXTS.documentsTab })).toHaveAttribute("aria-selected", "true");
            expect(screen.getByRole("tab", { name: TEXTS.questionsTab })).toHaveAttribute("aria-selected", "false");

            // Switch to package tab
            await user.click(screen.getByRole("tab", { name: TEXTS.packageTab }));
            expect(screen.getByRole("tab", { name: TEXTS.packageTab })).toHaveAttribute("aria-selected", "true");
            expect(screen.getByRole("tab", { name: TEXTS.documentsTab })).toHaveAttribute("aria-selected", "false");
        });

        it("shows only one tab content at a time", async () => {
            const user = userEvent.setup();
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByTestId("user-questions-tab")).toBeInTheDocument();
            });

            // Initially only questions tab content should be visible
            expect(screen.getByTestId("user-questions-tab")).toBeInTheDocument();
            expect(screen.queryByTestId("user-documents-tab")).not.toBeInTheDocument();
            expect(screen.queryByTestId("user-package-tab")).not.toBeInTheDocument();
            expect(screen.queryByTestId("user-notes-tab")).not.toBeInTheDocument();

            // Switch to documents tab
            await user.click(screen.getByRole("tab", { name: TEXTS.documentsTab }));
            expect(screen.queryByTestId("user-questions-tab")).not.toBeInTheDocument();
            expect(screen.getByTestId("user-documents-tab")).toBeInTheDocument();
            expect(screen.queryByTestId("user-package-tab")).not.toBeInTheDocument();
            expect(screen.queryByTestId("user-notes-tab")).not.toBeInTheDocument();
        });
    });

    describe("Accessibility", () => {
        it("provides proper ARIA attributes for tabs", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tablist")).toBeInTheDocument();
            });

            const tabList = screen.getByRole("tablist");
            expect(tabList).toBeInTheDocument();

            const tabs = screen.getAllByRole("tab");
            expect(tabs).toHaveLength(4);

            tabs.forEach((tab) => {
                expect(tab).toHaveAttribute("aria-selected");
            });
        });

        it("provides proper tab panels", async () => {
            render(<UserDetailPage />);

            // Wait for loading to complete
            await waitFor(() => {
                expect(screen.getByRole("tabpanel")).toBeInTheDocument();
            });

            const tabPanel = screen.getByRole("tabpanel");
            expect(tabPanel).toBeInTheDocument();
        });
    });

    describe("UserId Validation", () => {
        afterEach(() => {
            mockUseParams.mockReturnValue({ id: mockUserId });
        });
        it("shows error when userId param is missing", () => {
            mockUseParams.mockReturnValue({});
            render(<UserDetailPage />);
            expect(screen.getByText(TEXTS.userIdInvalid)).toBeInTheDocument();
        });
        it("shows error when userId param is an empty string", () => {
            mockUseParams.mockReturnValue({ id: "" });
            render(<UserDetailPage />);
            expect(screen.getByText(TEXTS.userIdInvalid)).toBeInTheDocument();
        });
        it("shows error when userId param is only whitespace", () => {
            mockUseParams.mockReturnValue({ id: "   " });
            render(<UserDetailPage />);
            expect(screen.getByText(TEXTS.userIdInvalid)).toBeInTheDocument();
        });
        it("accepts a valid string userId", async () => {
            const validId = "user-abc-123";
            mockUseParams.mockReturnValue({ id: validId });
            mockGetUserDetailsByUserId.mockResolvedValue({
                success: true,
                user: { id: "id", email: "<EMAIL>", user_id: validId }
            });
            render(<UserDetailPage />);
            await waitFor(() => {
                expect(screen.getByText(`${TEXTS.userIdLabel}: ${validId}`)).toBeInTheDocument();
            });
        });
    });
});
