import React from "react";
import { render, screen } from "@testing-library/react";

import UsersPage from "@/app/(admin)/admin/users/page";
import { TEXTS } from "@/lib/user-constants";

// Mock dependencies
jest.mock("@/components/forms/user-search-form", () => ({
    UserSearchForm: () => <div data-testid="user-search-form">User Search Form Component</div>
}));

describe("UsersPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Rendering", () => {
        it("renders the page title correctly", () => {
            render(<UsersPage />);
            expect(screen.getByText(TEXTS.pageTitle)).toBeInTheDocument();
        });

        it("renders the UserSearchForm component", () => {
            render(<UsersPage />);
            expect(screen.getByTestId("user-search-form")).toBeInTheDocument();
            expect(screen.getByText("User Search Form Component")).toBeInTheDocument();
        });

        it("renders with proper layout structure", () => {
            render(<UsersPage />);
            const container = screen.getByTestId("user-search-form").closest("div");
            expect(container).toBeInTheDocument();
        });

        it("renders within a card component", () => {
            render(<UsersPage />);
            const cardContainer = screen.getByText(TEXTS.pageTitle).closest("div");
            expect(cardContainer).toHaveClass("bg-white", "rounded-lg", "shadow", "p-6");
        });

        it("renders title with correct styling", () => {
            render(<UsersPage />);
            const title = screen.getByText(TEXTS.pageTitle);
            expect(title).toHaveClass("text-right", "text-2xl", "font-semibold", "mb-6");
        });

        it("renders in proper container structure", () => {
            render(<UsersPage />);
            const container = screen.getByText(TEXTS.pageTitle).closest("div");
            expect(container).toBeInTheDocument();
        });
    });

    describe("Component Integration", () => {
        it("integrates with UserSearchForm component correctly", () => {
            render(<UsersPage />);

            // Verify the form component is rendered
            expect(screen.getByTestId("user-search-form")).toBeInTheDocument();

            // Verify the page structure is correct
            expect(screen.getByText(TEXTS.pageTitle)).toBeInTheDocument();
        });
    });

    describe("Layout Structure", () => {
        it("maintains proper page structure", () => {
            render(<UsersPage />);

            // Check that we have the main container
            const mainContainer = screen.getByText(TEXTS.pageTitle).closest("div");
            expect(mainContainer).toBeInTheDocument();

            // Check that the form is rendered within the structure
            expect(screen.getByTestId("user-search-form")).toBeInTheDocument();
        });
    });
});
