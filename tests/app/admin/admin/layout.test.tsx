import React from "react";
import { render } from "@testing-library/react";
import AdminSubLayout from "@/app/(admin)/admin/layout";
import { useUserOrgRole } from "@/hooks/use-user-org-role";
import { useRouter } from "next/navigation";

// Mock the dependencies
jest.mock("next/navigation", () => ({
    useRouter: jest.fn()
}));
jest.mock("@/hooks/use-user-org-role");

const useUserOrgRoleMock = useUserOrgRole as jest.Mock;
const useRouterMock = useRouter as jest.Mock;

describe("AdminSubLayout", () => {
    let mockReplace: jest.Mock;

    beforeEach(() => {
        mockReplace = jest.fn();
        useRouterMock.mockReturnValue({ replace: mockReplace });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("does not redirect while role is loading", () => {
        useUserOrgRoleMock.mockReturnValue({ role: null, isLoaded: false });
        render(
            <AdminSubLayout>
                <div>child</div>
            </AdminSubLayout>
        );
        expect(mockReplace).not.toHaveBeenCalled();
    });

    it("renders children for admin role", () => {
        useUserOrgRoleMock.mockReturnValue({ role: "admin", isLoaded: true });
        render(
            <AdminSubLayout>
                <div>child</div>
            </AdminSubLayout>
        );
        expect(mockReplace).not.toHaveBeenCalled();
    });

    it("renders children for employee role", () => {
        useUserOrgRoleMock.mockReturnValue({ role: "employee", isLoaded: true });
        render(
            <AdminSubLayout>
                <div>child</div>
            </AdminSubLayout>
        );
        expect(mockReplace).not.toHaveBeenCalled();
    });

    it("redirects non-admin/non-employee roles", () => {
        useUserOrgRoleMock.mockReturnValue({ role: "user", isLoaded: true });
        render(
            <AdminSubLayout>
                <div>child</div>
            </AdminSubLayout>
        );
        expect(mockReplace).toHaveBeenCalledWith("/dashboard");
    });

    it("redirects when role is null and loading is finished", () => {
        useUserOrgRoleMock.mockReturnValue({ role: null, isLoaded: true });
        render(
            <AdminSubLayout>
                <div>child</div>
            </AdminSubLayout>
        );
        expect(mockReplace).toHaveBeenCalledWith("/dashboard");
    });
});
