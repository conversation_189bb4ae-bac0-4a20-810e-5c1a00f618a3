import dynamic from "next/dynamic";
import React from "react";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import failureAnimationData from "@/public/payments/failure.json";
import successAnimationData from "@/public/payments/success.json";

type PaymentStatus = "loading" | "success" | "error";

interface PaymentDetails {
    orderId?: string;
    transactionId?: string;
}

interface PaymentStatusCardProps {
    title: string;
    status: PaymentStatus;
    loadingText: string;
    successTitle: string;
    successDescription: string;
    successDetailsPrefix?: string;
    successTransactionPrefix?: string;
    errorTitle: string;
    errorDescription: string;
    details?: PaymentDetails;
    actions?: React.ReactNode[];
}

function AnimationSkeleton() {
    return <Skeleton className="h-48 w-full sm:h-56 md:h-64 rounded-xl" />;
}

const Lottie = dynamic(() => import("lottie-react"), {
    ssr: false,
    loading: () => <AnimationSkeleton />
});

export function PaymentStatusCard({
    title,
    status,
    loadingText,
    successTitle,
    successDescription,
    successDetailsPrefix,
    successTransactionPrefix,
    errorTitle,
    errorDescription,
    details,
    actions = []
}: PaymentStatusCardProps) {
    let animationData = null;
    if (status === "success") {
        animationData = successAnimationData;
    } else if (status === "error") {
        animationData = failureAnimationData;
    }

    const currentTitle = status === "success" ? successTitle : status === "error" ? errorTitle : title;
    const currentDescription =
        status === "success" ? successDescription : status === "error" ? errorDescription : loadingText;

    return (
        <div dir="rtl" className="container flex min-h-[calc(100vh-4rem)] items-center justify-center py-8">
            <Card className="w-full max-w-2xl">
                <CardHeader className="pb-0">
                    <div className="w-full max-w-md mx-auto flex justify-center items-center h-48 sm:h-56 md:h-64">
                        {status === "loading" ? (
                            <AnimationSkeleton />
                        ) : animationData ? (
                            <Lottie animationData={animationData} loop={status === "error"} className="h-full w-full" />
                        ) : (
                            <AnimationSkeleton />
                        )}
                    </div>
                </CardHeader>
                <CardContent className="space-y-4 text-center pt-6">
                    <h1 className="text-3xl sm:text-4xl scroll-m-20 tracking-tight">{currentTitle}</h1>
                    <p className="text-base text-muted-foreground sm:text-lg leading-7 [&:not(:first-child)]:mt-6">
                        {currentDescription}
                    </p>

                    {status === "success" && details && (
                        <div className="text-sm text-muted-foreground mt-2 space-y-1">
                            {details.orderId && (
                                <div>
                                    {successDetailsPrefix} {details.orderId}
                                </div>
                            )}
                            {details.transactionId && (
                                <div>
                                    {successTransactionPrefix} {details.transactionId}
                                </div>
                            )}
                        </div>
                    )}

                    {actions && actions.length > 0 && (
                        <div className="mt-6 flex flex-col sm:flex-row justify-center gap-2">{actions}</div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
