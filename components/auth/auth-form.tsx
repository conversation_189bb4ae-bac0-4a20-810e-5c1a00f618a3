"use client";

import * as Clerk from "@clerk/elements/common";
import * as SignIn from "@clerk/elements/sign-in";
import * as SignUp from "@clerk/elements/sign-up";
import { useUser } from "@clerk/nextjs";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { OTPVerification } from "@/components/auth/otp-verification";
import { SocialSignInButton } from "@/components/auth/social-signin-button";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSignupPreferences } from "@/contexts/signup-preferences-context";

export const TEXTS = {
    createAccount: "יצירת חשבון",
    email: "אימייל",
    emailPlaceholder: "הזן כתובת אימייל",
    signUp: "הרשמה",
    signUpWithGoogle: "הרשמה עם Google",
    signUpWithFacebook: "הרשמה עם Facebook",
    signingUp: "טוען...",

    signInTitle: "התחבר לחשבון",
    signInWithGoogle: "התחבר עם Google",
    signInWithFacebook: "התחבר עם Facebook",
    signingIn: "מתחבר...",

    termsLabel: {
        before: "אני מסכים ל",
        termsText: "תנאי השירות",
        termsLink: "/terms-of-use",
        middle: " ול",
        privacyText: "מדיניות הפרטיות",
        privacyLink: "/privacy-policy"
    },
    newsletterLabel: "אני מסכימ/ה לקבל עדכונים באימייל",
    termsRequired: "יש להסכים לתנאי השירות",

    checkEmail: "בדקו את האימייל שלכם",
    emailCodeDescription: "הזן את קוד האימות שנשלח לאימייל שלך",
    verify: "אימות",
    resendCode: "לא קיבלת קוד? שלח שוב",
    resendCodeCountdown: "לא קיבלת קוד? שלח שוב",

    alreadyHaveAccount: "יש לך כבר חשבון?",
    noAccount: "אין לך חשבון?",
    signIn: "התחבר",
    signUpLink: "הרשם",
    orDivider: "או",

    savePreferencesError: "שגיאה בשמירת ההעדפות",
    emailExistsError: "האימייל הזה כבר רשום במערכת. אנא התחבר במקום",
    emailNotFound: "האימייל לא נמצא במערכת. אנא הרשם תחילה",
    invalidPassword: "סיסמה שגויה. אנא נסה שוב",
    accountLocked: "החשבון נחסם זמנית. אנא נסה שוב מאוחר יותר",
    invalidCode: "קוד האימות שגוי. אנא נסה שוב",
    expiredCode: "קוד האימות פג תוקף. אנא בקש קוד חדש",
    invalidEmailFormat: "כתובת האימייל אינה תקינה. אנא בדוק שנית",
    signUpValidationError: "שגיאה בבדיקת הנתונים. אנא בדוק את הפרטים שוב",
    tooManyRequests: "יותר מדי בקשות. אנא המתן מעט ונסה שוב",
    requiredFieldMissing: "נא למלא את כל השדות הנדרשים"
};

const getHebrewErrorMessage = (englishErrorText: string, isSignUp: boolean = false): string | null => {
    const text = englishErrorText.toLowerCase();

    if (isSignUp) {
        if (
            text.includes("email address is taken") ||
            text.includes("that email address is taken") ||
            text.includes("identifier_exists")
        ) {
            return TEXTS.emailExistsError;
        }
        if (
            text.includes("invalid email") ||
            text.includes("email format") ||
            text.includes("form_email_invalid") ||
            text.includes("must be a valid email address")
        ) {
            return TEXTS.invalidEmailFormat;
        }
        if (text.includes("required") || text.includes("missing") || text.includes("form_param_missing")) {
            return TEXTS.requiredFieldMissing;
        }
        if (text.includes("rate limit") || text.includes("too many requests") || text.includes("too_many_requests")) {
            return TEXTS.tooManyRequests;
        }
        if (text.includes("validation") || text.includes("form_validation_failed")) {
            return TEXTS.signUpValidationError;
        }
    } else {
        if (
            text.includes("not found") ||
            text.includes("doesn't exist") ||
            text.includes("identifier_not_found") ||
            text.includes("couldn't find your account")
        ) {
            return TEXTS.emailNotFound;
        }
        if (text.includes("password") || text.includes("credentials") || text.includes("credential_invalid")) {
            return TEXTS.invalidPassword;
        }
        if (text.includes("locked") || text.includes("too many") || text.includes("requests")) {
            return TEXTS.accountLocked;
        }
        if (text.includes("expired")) {
            return TEXTS.expiredCode;
        }
        if (
            text.includes("invalid email") ||
            text.includes("email format") ||
            text.includes("form_email_invalid") ||
            text.includes("must be a valid email address")
        ) {
            return TEXTS.invalidEmailFormat;
        }
        if (text.includes("incorrect") || text.includes("invalid") || text.includes("code")) {
            return TEXTS.invalidCode;
        }
    }

    return null;
};

interface AuthFormProps {
    mode: "signin" | "signup";
}

export function AuthForm({ mode }: AuthFormProps) {
    const { user } = useUser();
    const { state, setAcceptedTerms, setSubscribeNewsletter } = useSignupPreferences();
    const [isGoogleLoading, setIsGoogleLoading] = useState(false);
    const [isFacebookLoading, setIsFacebookLoading] = useState(false);
    const mountedRef = useRef(true);

    const isSignUp = mode === "signup";
    const isSignIn = mode === "signin";

    // Extract preferences from context for easier access
    const { acceptedTerms, subscribeNewsletter } = state.preferences;

    useEffect(() => {
        mountedRef.current = true;
        return () => {
            mountedRef.current = false;
        };
    }, []);

    // Note: Preference saving moved to onboarding first stage

    useEffect(() => {
        if (user && user.id) {
            setIsGoogleLoading(false);
            setIsFacebookLoading(false);
        }
    }, [user]);

    useEffect(() => {
        const replaceEnglishErrors = () => {
            const errorElements = document.querySelectorAll(
                "[data-clerk-field-error], .cl-formFieldError, .cl-field__error, [role='alert'], .text-destructive"
            );
            errorElements.forEach((element) => {
                const text = element.textContent || "";
                if (text && !text.includes("אימייל") && !text.includes("סיסמה") && !text.includes("קוד")) {
                    const hebrewError = getHebrewErrorMessage(text, isSignUp);
                    if (hebrewError) {
                        element.textContent = hebrewError;
                        element.className = element.className.replace("text-left", "") + " text-right";
                    }
                }
            });
        };

        const resetLoadingOnError = () => {
            const errorElements = document.querySelectorAll(
                "[data-clerk-field-error], .cl-formFieldError, .cl-field__error, [role='alert'], .text-destructive"
            );
            if (errorElements.length > 0) {
                setIsGoogleLoading(false);
                setIsFacebookLoading(false);
            }
        };

        const observer = new MutationObserver(() => {
            replaceEnglishErrors();
            resetLoadingOnError();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        replaceEnglishErrors();
        resetLoadingOnError();

        return () => observer.disconnect();
    }, [isSignIn, isSignUp]);

    const handleTermsChange = (checked: boolean) => {
        setAcceptedTerms(checked);
    };

    const handleNewsletterChange = (checked: boolean) => {
        setSubscribeNewsletter(checked);
    };

    const getFieldName = () => (isSignIn ? "identifier" : "emailAddress");
    const getTitle = () => (isSignIn ? TEXTS.signInTitle : TEXTS.createAccount);
    const getGoogleButtonText = () => (isSignIn ? TEXTS.signInWithGoogle : TEXTS.signUpWithGoogle);
    const getFacebookButtonText = () => (isSignIn ? TEXTS.signInWithFacebook : TEXTS.signUpWithFacebook);
    const getSubmitButtonText = (isLoading: boolean) => {
        if (isSignIn) {
            return isLoading ? TEXTS.signingIn : TEXTS.signIn;
        }
        return isLoading ? TEXTS.signingUp : TEXTS.signUp;
    };
    const getBottomLinkText = () => (isSignIn ? TEXTS.noAccount : TEXTS.alreadyHaveAccount);
    const getBottomLinkAction = () => (isSignIn ? TEXTS.signUpLink : TEXTS.signIn);
    const getBottomLinkHref = () => (isSignIn ? "/signup" : "/login");

    const renderEmailField = () => (
        <Clerk.Field name={getFieldName()} className="space-y-2">
            <Clerk.Label asChild>
                <Label className="text-right">{TEXTS.email}</Label>
            </Clerk.Label>
            <Clerk.Input type="email" asChild>
                <Input
                    className="text-right direction-rtl placeholder:text-right"
                    placeholder={TEXTS.emailPlaceholder}
                />
            </Clerk.Input>
            <Clerk.FieldError className="block text-sm text-destructive text-right" />
        </Clerk.Field>
    );

    const renderSignUpCheckboxes = () => {
        if (!isSignUp) return null;

        return (
            <div className="space-y-3 pt-2">
                <div className="flex items-start space-x-2 rtl:space-x-reverse">
                    <Checkbox id="terms" checked={acceptedTerms} onCheckedChange={handleTermsChange} className="ml-2" />
                    <Label
                        htmlFor="terms"
                        className="text-sm font-medium leading-none text-right flex-1 cursor-pointer"
                    >
                        <span>
                            {TEXTS.termsLabel.before}
                            <Link
                                href={TEXTS.termsLabel.termsLink}
                                className="underline hover:text-primary"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {TEXTS.termsLabel.termsText}
                            </Link>
                            {TEXTS.termsLabel.middle}
                            <Link
                                href={TEXTS.termsLabel.privacyLink}
                                className="underline hover:text-primary"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {TEXTS.termsLabel.privacyText}
                            </Link>
                        </span>
                    </Label>
                </div>

                <div className="flex items-start space-x-2 rtl:space-x-reverse">
                    <Checkbox
                        id="newsletter"
                        checked={subscribeNewsletter}
                        onCheckedChange={(checked) => handleNewsletterChange(checked === true)}
                        className="ml-2"
                    />
                    <Label
                        htmlFor="newsletter"
                        className="text-sm font-medium leading-none text-right flex-1 cursor-pointer"
                    >
                        {TEXTS.newsletterLabel}
                    </Label>
                </div>
            </div>
        );
    };

    const renderVerificationStep = () => {
        if (isSignIn) {
            return (
                <SignIn.Step name="verifications">
                    <SignIn.Strategy name="email_code">
                        <OTPVerification
                            type="signin"
                            title={TEXTS.checkEmail}
                            description={TEXTS.emailCodeDescription}
                            verifyButtonText={TEXTS.verify}
                            resendText={TEXTS.resendCode}
                            resendCountdownText={TEXTS.resendCodeCountdown}
                        />
                    </SignIn.Strategy>
                </SignIn.Step>
            );
        }

        return (
            <SignUp.Step name="verifications">
                <SignUp.Strategy name="email_code">
                    <OTPVerification
                        type="signup"
                        title={TEXTS.checkEmail}
                        description={TEXTS.emailCodeDescription}
                        verifyButtonText={TEXTS.verify}
                        resendText={TEXTS.resendCode}
                        resendCountdownText={TEXTS.resendCodeCountdown}
                    />
                </SignUp.Strategy>
            </SignUp.Step>
        );
    };

    const handleSubmit = async (event: React.FormEvent) => {
        if (isSignUp && !acceptedTerms) {
            event.preventDefault();
            toast.error(TEXTS.termsRequired);
            return;
        }
    };

    const handleSocialButtonClick = (provider: "google" | "facebook") => {
        if (isSignUp && !acceptedTerms) {
            toast.error(TEXTS.termsRequired);
            return;
        }

        if (provider === "google") {
            setIsGoogleLoading(true);
        } else if (provider === "facebook") {
            setIsFacebookLoading(true);
        }
    };

    const renderSocialButtons = () => (
        <div className="grid gap-y-2">
            <SocialSignInButton
                provider="google"
                disabled={isGoogleLoading || isFacebookLoading}
                loading={isGoogleLoading}
                className="w-full"
                onClick={() => handleSocialButtonClick("google")}
            >
                {getGoogleButtonText()}
            </SocialSignInButton>
            <SocialSignInButton
                provider="facebook"
                disabled={isFacebookLoading || isGoogleLoading}
                loading={isFacebookLoading}
                className="w-full"
                onClick={() => handleSocialButtonClick("facebook")}
            >
                {getFacebookButtonText()}
            </SocialSignInButton>
        </div>
    );

    const AuthRoot = isSignIn ? SignIn.Root : SignUp.Root;
    const AuthStep = isSignIn ? SignIn.Step : SignUp.Step;
    const AuthAction = isSignIn ? SignIn.Action : SignUp.Action;
    const authPath = isSignIn ? "/login" : "/signup";

    return (
        <AuthRoot path={authPath} routing="path">
            <Clerk.Loading>
                {() => (
                    <>
                        <AuthStep name="start">
                            <Card className="w-full sm:w-96">
                                <CardHeader className="text-center">
                                    <CardTitle className="text-right">{getTitle()}</CardTitle>
                                </CardHeader>
                                <CardContent className="grid gap-y-4">
                                    {renderSocialButtons()}

                                    <div className="relative">
                                        <div className="absolute inset-0 flex items-center">
                                            <span className="w-full border-t" />
                                        </div>
                                        <div className="relative flex justify-center text-xs uppercase">
                                            <span className="bg-background px-2 text-muted-foreground">
                                                {TEXTS.orDivider}
                                            </span>
                                        </div>
                                    </div>

                                    {renderEmailField()}
                                    {renderSignUpCheckboxes()}

                                    <div id="clerk-captcha" style={{ minHeight: "78px" }} />
                                </CardContent>
                                <CardFooter>
                                    <div className="grid w-full gap-y-4">
                                        <AuthAction submit asChild>
                                            <Clerk.Loading>
                                                {(clerkLoading) => (
                                                    <Button disabled={clerkLoading} onClick={handleSubmit}>
                                                        {getSubmitButtonText(clerkLoading)}
                                                    </Button>
                                                )}
                                            </Clerk.Loading>
                                        </AuthAction>

                                        <Button variant="link" size="sm" asChild>
                                            <Link href={getBottomLinkHref()}>
                                                {getBottomLinkText()} {getBottomLinkAction()}
                                            </Link>
                                        </Button>
                                    </div>
                                </CardFooter>
                            </Card>
                        </AuthStep>

                        {renderVerificationStep()}
                    </>
                )}
            </Clerk.Loading>
        </AuthRoot>
    );
}
