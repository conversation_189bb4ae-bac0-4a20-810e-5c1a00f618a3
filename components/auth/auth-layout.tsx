"use client";

import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { useRout<PERSON> } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";

import { AuthFooter } from "@/components/auth/auth-footer";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

type AuthLayoutProps = {
    children: ReactNode;
    title?: string;
    subtitle?: string;
    logoSrc?: string;
    logoAlt?: string;
    className?: string;
    backButtonHref?: string;
    backButtonCallback?: () => void;
    isSubmitting?: boolean;
    error?: string;
    showSkeleton?: boolean;
    skeletonMode?: "signin" | "signup";
};

function BackButton({
    href,
    callback,
    disabled = false
}: {
    href?: string;
    callback?: () => void;
    disabled?: boolean;
}) {
    const router = useRouter();

    const handleClick = () => {
        if (callback) {
            callback();
        } else if (href) {
            router.push(href);
        }
    };

    return (
        <Button
            type="button"
            onClick={handleClick}
            disabled={disabled}
            variant="ghost"
            size="icon"
            className="h-8 w-8 sm:h-7 sm:w-7 rounded-full hover:bg-secondary/80"
            aria-label="Back"
        >
            <ArrowRight className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
        </Button>
    );
}

function AuthSkeleton({ mode = "signin" }: { mode?: "signin" | "signup" }) {
    const isSignup = mode === "signup";

    return (
        <div className="relative flex flex-col items-center w-full max-w-md mx-auto">
            <Skeleton className="h-12 w-32 mx-auto mb-6 mt-6" />
            <Card className="w-full sm:w-96 mx-auto">
                <CardHeader className="text-center">
                    <Skeleton className="h-8 w-2/3 mx-auto mb-2" />
                    <Skeleton className="h-4 w-1/2 mx-auto" />
                </CardHeader>
                <CardContent className="grid gap-y-4">
                    <div className="grid gap-y-2">
                        <Skeleton className="h-10 w-full mb-2" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                    <div className="h-4 w-1/3 mx-auto mb-2">
                        <Skeleton className="h-4 w-full" />
                    </div>
                    <Skeleton className="h-10 w-full mb-2" />
                    {isSignup && (
                        <div className="space-y-3 pt-2">
                            <Skeleton className="h-4 w-2/3 mx-auto mb-2" />
                            <Skeleton className="h-4 w-1/2 mx-auto" />
                        </div>
                    )}
                    <Skeleton className="h-8 w-full mt-2" />
                </CardContent>
                <CardFooter>
                    <div className="grid w-full gap-y-4">
                        <Skeleton className="h-10 w-full mb-2" />
                        <Skeleton className="h-6 w-1/2 mx-auto" />
                    </div>
                </CardFooter>
            </Card>
        </div>
    );
}

export function AuthLayout({
    children,
    className,
    backButtonHref,
    backButtonCallback,
    isSubmitting,
    error,
    showSkeleton = false,
    skeletonMode = "signin"
}: AuthLayoutProps) {
    const [isInitialLoading, setIsInitialLoading] = useState(showSkeleton);

    useEffect(() => {
        if (showSkeleton) {
            const timer = setTimeout(() => {
                setIsInitialLoading(false);
            }, 500);
            return () => clearTimeout(timer);
        }
    }, [showSkeleton]);

    return (
        <div
            className={cn(
                "min-h-screen w-full relative flex flex-col items-center justify-center px-2 py-4 sm:px-4 sm:py-6 md:py-12 dark:bg-neutral-900 bg-gradient-to-b from-secondary/70 to-background/95",
                className
            )}
        >
            <div className="w-full sm:max-w-md md:max-w-lg space-y-4 sm:space-y-6 mt-16 sm:mt-0">
                <div className="space-y-5 sm:space-y-6 relative">
                    {(backButtonHref || backButtonCallback) && !isInitialLoading && (
                        <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
                            <BackButton href={backButtonHref} callback={backButtonCallback} disabled={isSubmitting} />
                        </div>
                    )}
                    {error && !isInitialLoading && (
                        <Alert
                            variant="destructive"
                            className="border bg-destructive/5 text-destructive py-4 px-3 flex flex-col items-center justify-center"
                        >
                            <AlertDescription className="text-sm sm:text-xs font-medium text-center">
                                {error}
                            </AlertDescription>
                        </Alert>
                    )}
                    {isInitialLoading ? <AuthSkeleton mode={skeletonMode} /> : children}
                </div>
                {!isInitialLoading && (
                    <div className="text-sm sm:text-xs md:text-sm">
                        <AuthFooter />
                    </div>
                )}
            </div>
        </div>
    );
}
