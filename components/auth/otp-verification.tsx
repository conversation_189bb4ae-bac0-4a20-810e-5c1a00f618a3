"use client";

import * as Clerk from "@clerk/elements/common";
import * as SignIn from "@clerk/elements/sign-in";
import * as SignUp from "@clerk/elements/sign-up";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { TEXTS } from "@/lib/auth-constants";

interface OTPVerificationProps {
    type: "signup" | "signin";
    title: string;
    description: string;
    verifyButtonText: string;
    resendText: string;
    resendCountdownText: string;
    isGlobalLoading?: boolean;
    customErrors?: React.ReactNode;
}

export function OTPVerification({
    type,
    title,
    description,
    verifyButtonText,
    resendText,
    resendCountdownText,
    isGlobalLoading = false,
    customErrors
}: OTPVerificationProps) {
    const ActionComponent = type === "signup" ? SignUp.Action : SignIn.Action;

    return (
        <Card className="w-full sm:w-96">
            <CardHeader className="text-center">
                <CardTitle className="text-right">{title}</CardTitle>
                <CardDescription className="text-right">{description}</CardDescription>
            </CardHeader>

            <CardContent className="grid gap-y-4">
                <Clerk.Field name="code">
                    <Clerk.Label className="sr-only">{TEXTS.emailOtpLabel}</Clerk.Label>
                    <div className="grid gap-y-2 items-center justify-center">
                        <div className="flex justify-center text-center" dir="ltr">
                            <Clerk.Input
                                type="otp"
                                autoSubmit
                                className="flex justify-center has-[:disabled]:opacity-50"
                                render={({ value, status }: { value: string; status: string }) => {
                                    return (
                                        <div
                                            data-status={status}
                                            className="relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md data-[status=selected]:ring-1 data-[status=selected]:ring-ring data-[status=cursor]:ring-1 data-[status=cursor]:ring-ring"
                                        >
                                            {value}
                                        </div>
                                    );
                                }}
                            />
                        </div>

                        <Clerk.FieldError className="block text-sm text-destructive text-center" />

                        {customErrors}

                        <ActionComponent
                            asChild
                            resend
                            className="text-muted-foreground"
                            fallback={({ resendableAfter }: { resendableAfter: number }) => (
                                <Button variant="link" size="sm" disabled>
                                    {resendCountdownText} (<span className="tabular-nums">{resendableAfter}</span>)
                                </Button>
                            )}
                        >
                            <Button variant="link" size="sm">
                                {resendText}
                            </Button>
                        </ActionComponent>
                    </div>
                </Clerk.Field>
            </CardContent>

            <CardFooter>
                <ActionComponent submit asChild>
                    <Button disabled={isGlobalLoading} className="w-full">
                        <Clerk.Loading>
                            {(isLoading: boolean) => {
                                return isLoading ? TEXTS.signingIn : verifyButtonText;
                            }}
                        </Clerk.Loading>
                    </Button>
                </ActionComponent>
            </CardFooter>
        </Card>
    );
}
