import { Body, Container, Html, Section, Text } from "@react-email/components";

interface ContactFormTemplateProps {
    email: string;
    subject: string;
    message: string;
}

export const ContactFormTemplate: React.FC<Readonly<ContactFormTemplateProps>> = ({ email, subject, message }) => (
    <Html dir="rtl" lang="he">
        <Body style={{ direction: "rtl", textAlign: "right" }} className="bg-background my-auto mx-auto font-sans">
            <Container
                style={{ direction: "rtl", textAlign: "right" }}
                className="border border-border rounded-lg my-[40px] mx-auto p-[20px] max-w-[465px] bg-card"
            >
                <Section style={{ direction: "rtl", textAlign: "right" }} className="mt-[32px]">
                    <Text
                        style={{ direction: "rtl", textAlign: "right" }}
                        className="text-muted-foreground text-base leading-[24px]"
                    >
                        {subject}
                    </Text>
                </Section>
                <Section style={{ direction: "rtl", textAlign: "right" }} className="mt-[32px]">
                    <Text
                        style={{ direction: "rtl", textAlign: "right" }}
                        className="text-muted-foreground text-base leading-[24px]"
                    >
                        {message}
                    </Text>
                </Section>
                <Section style={{ direction: "rtl", textAlign: "right" }} className="mt-[32px]">
                    <Text
                        style={{ direction: "rtl", textAlign: "right" }}
                        className="text-muted-foreground text-base leading-[24px]"
                    >
                        {email}
                    </Text>
                </Section>
            </Container>
        </Body>
    </Html>
);
