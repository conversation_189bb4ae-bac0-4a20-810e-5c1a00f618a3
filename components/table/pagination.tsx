import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";

import { PaginationProps } from "@/components/table/types";
import { Button } from "@/components/ui/button";

const TEXTS = {
    totalItems: "{start}-{end} מתוך {total}",
    firstPage: "דף ראשון",
    previousPage: "דף קודם",
    nextPage: "דף הבא",
    lastPage: "דף אחרון"
};

export function Pagination({ currentPage, totalPages, pageSize, totalItems, onPageChange }: PaginationProps) {
    const handlePageChange = (page: number) => {
        if (page < 1 || page > totalPages) return;
        onPageChange(page);
    };

    const goToFirstPage = () => handlePageChange(1);
    const goToLastPage = () => handlePageChange(totalPages);

    if (totalPages <= 1) {
        return null;
    }

    return (
        <div className="py-5 px-6 border-t">
            <div className="flex justify-between items-center">
                <div className="flex gap-1">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={goToFirstPage}
                        disabled={currentPage === 1}
                        className={currentPage === 1 ? "opacity-50" : ""}
                    >
                        <ChevronsRight className="h-4 w-4" />
                        <span className="sr-only">{TEXTS.firstPage}</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => (currentPage > 1 ? handlePageChange(currentPage - 1) : undefined)}
                        disabled={currentPage === 1}
                        className={currentPage === 1 ? "opacity-50" : ""}
                    >
                        <ChevronRight className="h-4 w-4" />
                        <span className="sr-only">{TEXTS.previousPage}</span>
                    </Button>
                </div>

                <div className="flex items-center gap-4">
                    {totalItems !== undefined && (
                        <span className="text-sm text-gray-500">
                            {TEXTS.totalItems
                                .replace("{start}", String((currentPage - 1) * pageSize + 1))
                                .replace("{end}", String(Math.min(currentPage * pageSize, totalItems)))
                                .replace("{total}", String(totalItems))}
                        </span>
                    )}
                </div>

                <div className="flex gap-1">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => (currentPage < totalPages ? handlePageChange(currentPage + 1) : undefined)}
                        disabled={currentPage === totalPages}
                        className={currentPage === totalPages ? "opacity-50" : ""}
                    >
                        <ChevronLeft className="h-4 w-4" />
                        <span className="sr-only">{TEXTS.nextPage}</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={goToLastPage}
                        disabled={currentPage === totalPages}
                        className={currentPage === totalPages ? "opacity-50" : ""}
                    >
                        <ChevronsLeft className="h-4 w-4" />
                        <span className="sr-only">{TEXTS.lastPage}</span>
                    </Button>
                </div>
            </div>
        </div>
    );
}
