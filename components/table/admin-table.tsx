"use client";

import { useCallback, useState } from "react";
import { toast } from "sonner";

import { LoadingIcon } from "@/components/common/loading-icon";
import { Option } from "@/components/forms/fields/dropdown-base";
import { FilterValue } from "@/components/table/types";

import { Pagination } from "./pagination";
import { TableBody } from "./table-body";
import { TableHeader } from "./table-header";
import { AdminTableProps } from "./types";

const TEXTS = {
    loading: "טוען...",
    errorPrefix: "שגיאה בטעינה:",
    noItems: "לא נמצאו פריטים",
    actions: "פעולות",
    edit: "עריכה",
    deleteConfirmTitle: "אישור מחיקה",
    deleteConfirmDescription: "האם אתה בטוח שברצונך למחוק פריט זה? לא ניתן לשחזר פעולה זו.",
    cancel: "ביטול",
    confirmDelete: "מחק",
    deleteError: "שגיאה במחיקה",
    deleteSuccess: "הפריט נמחק בהצלחה",
    deleting: "מוחק..."
};

const DEFAULT_PAGE_SIZE_OPTIONS: Option[] = [
    { id: "5", label: "5" },
    { id: "10", label: "10" },
    { id: "20", label: "20" },
    { id: "50", label: "50" }
];

export function AdminTable<T extends { id: string }>({
    title,
    items,
    columns,
    loading,
    error,
    onDelete,
    onRefetch,
    addButtonLabel,
    addButtonHref,
    deleteConfirmTitle = TEXTS.deleteConfirmTitle,
    deleteConfirmDescription = TEXTS.deleteConfirmDescription,
    confirmDeleteText = TEXTS.confirmDelete,
    noItemsText = TEXTS.noItems,
    loadingText = TEXTS.loading,
    errorPrefix = TEXTS.errorPrefix,
    deleteErrorText = TEXTS.deleteError,
    deleteSuccessText = TEXTS.deleteSuccess,
    deletingText = TEXTS.deleting,
    reorderable = false,
    onReorder,

    totalItems,
    currentPage = 1,
    onPageChange,
    totalPages: externalTotalPages,
    pageSize = 10,
    onPageSizeChange,
    pageSizeOptions = DEFAULT_PAGE_SIZE_OPTIONS,

    onFilter,
    activeFilters = {}
}: AdminTableProps<T>) {
    const [isDeleting, setIsDeleting] = useState(false);
    const [localFilters, setLocalFilters] = useState<Record<string, FilterValue>>(activeFilters);

    const totalPages = externalTotalPages ?? (totalItems ? Math.ceil(totalItems / pageSize) : 1);

    const handleDeleteConfirm = async (id: string) => {
        setIsDeleting(true);
        const toastId = toast.loading(deletingText);
        try {
            const result = await onDelete(id);
            if (result.success) {
                toast.success(deleteSuccessText, { id: toastId });
                onRefetch();
            } else {
                throw new Error(result.error || deleteErrorText);
            }
        } catch (err) {
            console.error("Error deleting item:", err);
            toast.error(deleteErrorText, {
                description: err instanceof Error ? err.message : undefined,
                id: toastId
            });
        } finally {
            setIsDeleting(false);
        }
    };

    const handleFilterApply = useCallback(
        (newFilters: Record<string, FilterValue>) => {
            setLocalFilters(newFilters);
            if (onFilter) {
                onFilter(newFilters);
            }
        },
        [onFilter]
    );

    const clearFilter = useCallback(
        (key: string) => {
            const newFilters = { ...localFilters };
            delete newFilters[key];
            setLocalFilters(newFilters);

            if (onFilter) {
                onFilter(newFilters);
            }
        },
        [localFilters, onFilter]
    );

    const clearAllFilters = useCallback(() => {
        const emptyFilters: Record<string, FilterValue> = {};
        setLocalFilters(emptyFilters);
        if (onFilter) {
            onFilter(emptyFilters);
        }
    }, [onFilter]);

    const updateFilter = useCallback(
        (key: string, value: FilterValue) => {
            const newFilters = { ...localFilters, [key]: value };
            setLocalFilters(newFilters);
            if (onFilter) {
                onFilter(newFilters);
            }
        },
        [localFilters, onFilter]
    );

    if (loading) {
        return (
            <div className="container py-8 mx-auto max-w-7xl">
                <TableHeader
                    title={title}
                    columns={columns}
                    addButtonHref={addButtonHref}
                    addButtonLabel={addButtonLabel}
                    pageSize={pageSize}
                    onPageSizeChange={onPageSizeChange}
                    pageSizeOptions={pageSizeOptions}
                    activeFilters={localFilters}
                    onFilterApply={handleFilterApply}
                    onClearFilter={clearFilter}
                    onClearAllFilters={clearAllFilters}
                    onUpdateFilter={updateFilter}
                />
                <div className="bg-white rounded-lg shadow p-6 flex justify-center items-center" dir="rtl">
                    <LoadingIcon className="text-primary" text={loadingText} />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container py-8 mx-auto max-w-7xl">
                <TableHeader
                    title={title}
                    columns={columns}
                    addButtonHref={addButtonHref}
                    addButtonLabel={addButtonLabel}
                    pageSize={pageSize}
                    onPageSizeChange={onPageSizeChange}
                    pageSizeOptions={pageSizeOptions}
                    activeFilters={localFilters}
                    onFilterApply={handleFilterApply}
                    onClearFilter={clearFilter}
                    onClearAllFilters={clearAllFilters}
                    onUpdateFilter={updateFilter}
                />
                <div className="bg-white rounded-lg shadow p-6 text-right" dir="rtl">
                    <p className="text-red-500">
                        {errorPrefix} {error.message}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="container py-8 mx-auto max-w-7xl" dir="rtl">
            <TableHeader
                title={title}
                columns={columns}
                addButtonHref={addButtonHref}
                addButtonLabel={addButtonLabel}
                pageSize={pageSize}
                onPageSizeChange={onPageSizeChange}
                pageSizeOptions={pageSizeOptions}
                activeFilters={localFilters}
                onFilterApply={handleFilterApply}
                onClearFilter={clearFilter}
                onClearAllFilters={clearAllFilters}
                onUpdateFilter={updateFilter}
            />

            <div className="bg-white rounded-lg shadow overflow-hidden">
                {items.length === 0 ? (
                    <div className="p-6 text-center">
                        <p>{noItemsText}</p>
                    </div>
                ) : (
                    <div className="overflow-x-auto" dir="rtl">
                        <TableBody
                            items={items}
                            columns={columns}
                            reorderable={!!onReorder && reorderable}
                            onReorder={onReorder as (items: T[]) => void}
                            addButtonHref={addButtonHref}
                            isDeleting={isDeleting}
                            deleteConfirmTitle={deleteConfirmTitle}
                            deleteConfirmDescription={deleteConfirmDescription}
                            confirmDeleteText={confirmDeleteText}
                            onDeleteConfirm={handleDeleteConfirm}
                        />

                        {onPageChange && totalPages > 1 && (
                            <Pagination
                                currentPage={currentPage}
                                totalPages={totalPages}
                                pageSize={pageSize}
                                totalItems={totalItems}
                                onPageChange={onPageChange}
                            />
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
