import { Option } from "@/components/forms/fields/dropdown-base";

export type DateFilterValue = {
    startDate?: Date;
    endDate?: Date;
};

export type NumberFilterValue = {
    min?: number;
    max?: number;
};

export type FilterValue = string | number | string[] | DateFilterValue | NumberFilterValue;

export interface AdminTableColumn<T> {
    key: string;
    label: string;
    render?: (item: T) => React.ReactNode;
    width?: string;
    filterable?: boolean;
    filterType?: "text" | "select" | "date" | "number";
    filterOptions?: Option[];
    filterPlaceholder?: string;
}

export interface AdminTableProps<T> {
    title: string;
    items: T[];
    columns: AdminTableColumn<T>[];
    loading: boolean;
    error: Error | null;
    onDelete: (id: string) => Promise<{ success: boolean; error?: string }>;
    onRefetch: () => void;
    addButtonLabel?: string;
    addButtonHref?: string;
    deleteConfirmTitle?: string;
    deleteConfirmDescription?: string;
    confirmDeleteText?: string;
    noItemsText?: string;
    loadingText?: string;
    errorPrefix?: string;
    deleteErrorText?: string;
    deleteSuccessText?: string;
    deletingText?: string;
    reorderable?: boolean;
    onReorder?: (items: T[]) => void;

    totalItems?: number;
    currentPage?: number;
    onPageChange?: (page: number) => void;
    totalPages?: number;
    pageSize?: number;
    onPageSizeChange?: (size: number) => void;
    pageSizeOptions?: Option[];

    onFilter?: (filters: Record<string, FilterValue>) => void;
    activeFilters?: Record<string, FilterValue>;
}

export interface PaginationProps {
    currentPage: number;
    totalPages: number;
    pageSize: number;
    totalItems?: number;
    onPageChange: (page: number) => void;
    onPageSizeChange?: (size: number) => void;
    pageSizeOptions?: Option[];
}
