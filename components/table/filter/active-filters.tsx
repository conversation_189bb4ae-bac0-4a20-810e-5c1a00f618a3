import { X } from "lucide-react";
import { memo, useCallback } from "react";

import { AdminTableColumn, DateFilterValue, FilterValue, NumberFilterValue } from "@/components/table/types";
import { Badge } from "@/components/ui/badge";

interface ActiveFiltersProps<T = unknown> {
    columns: AdminTableColumn<T>[];
    activeFilters: Record<string, FilterValue>;
    onClearFilter: (key: string) => void;
    onClearAllFilters: () => void;
    onUpdateFilter?: (key: string, value: FilterValue) => void;
    onFilterClick?: (key: string) => void;
}

const TEXTS = {
    noValue: "ללא",
    startingFrom: "החל מ-",
    upTo: "עד ",
    removeFilter: "הסר סינון"
};

const ActiveFiltersComponent = <T = unknown,>({
    columns,
    activeFilters,
    onClearFilter,
    onUpdateFilter,
    onFilterClick
}: ActiveFiltersProps<T>) => {
    const hasActiveFilters = Object.keys(activeFilters).length > 0;

    const getFilterDisplayValue = useCallback((column: AdminTableColumn<T>, value: FilterValue): string => {
        if (column.filterType === "select") {
            if (value === "null") {
                return TEXTS.noValue;
            }

            const valueToCompare =
                typeof value === "object" && value !== null && "id" in value
                    ? String((value as { id: string }).id)
                    : String(value);

            const option = column.filterOptions?.find((opt) => opt.id === valueToCompare);
            return option?.label || valueToCompare;
        } else if (
            column.filterType === "date" &&
            typeof value === "object" &&
            !Array.isArray(value) &&
            value !== null
        ) {
            const dateValue = value as DateFilterValue;
            const formatDate = (date: Date | undefined) => (date ? new Date(date).toLocaleDateString("he-IL") : null);
            const fromStr = formatDate(dateValue.startDate);
            const toStr = formatDate(dateValue.endDate);

            if (fromStr && toStr) return `${toStr} - ${fromStr}`;
            if (fromStr) return `${TEXTS.startingFrom}${fromStr}`;
            if (toStr) return `${TEXTS.upTo}${toStr}`;
        } else if (
            column.filterType === "number" &&
            typeof value === "object" &&
            !Array.isArray(value) &&
            value !== null
        ) {
            const numValue = value as NumberFilterValue;
            const { min, max } = numValue;
            if (min !== undefined && max !== undefined) return `${min} - ${max}`;
            if (min !== undefined) return `≥ ${min}`;
            if (max !== undefined) return `≤ ${max}`;
        } else if (typeof value === "string" || typeof value === "number") {
            return String(value);
        }

        return JSON.stringify(value);
    }, []);

    if (!hasActiveFilters) {
        return null;
    }

    return (
        <div className="flex flex-wrap gap-2 mt-1" dir="rtl">
            {Object.entries(activeFilters).map(([key, value]) => {
                const column = columns.find((col) => col.key === key);
                if (!column) return null;

                if (column.filterType === "select" && Array.isArray(value)) {
                    return value.map((itemValue) => {
                        const displayValue = getFilterDisplayValue(column, itemValue);
                        return (
                            <Badge
                                key={`${key}-${itemValue}`}
                                variant="secondary"
                                className="px-3 py-1.5 rounded-md text-sm font-medium flex items-center justify-between gap-2 bg-accent text-accent-foreground hover:bg-accent/90 cursor-pointer border-none"
                                onClick={() => onFilterClick?.(key)}
                            >
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const newValue = value.filter((v) => v !== itemValue);
                                        if (newValue.length === 0) {
                                            onClearFilter(key);
                                        } else if (onUpdateFilter) {
                                            onUpdateFilter(key, newValue);
                                        } else {
                                            onClearFilter(key);
                                        }
                                    }}
                                    className="text-accent-foreground hover:opacity-70 transition-opacity focus:outline-none"
                                    aria-label={TEXTS.removeFilter}
                                >
                                    <X className="h-4 w-4" />
                                </button>
                                <span className="flex items-center gap-1">
                                    <span className="font-semibold">{column.label}:</span> {displayValue}
                                </span>
                            </Badge>
                        );
                    });
                }

                return (
                    <Badge
                        key={key}
                        variant="secondary"
                        className="px-3 py-1.5 rounded-md text-sm font-medium flex items-center justify-between gap-2 bg-accent text-accent-foreground hover:bg-accent/90 cursor-pointer border-none"
                        onClick={() => onFilterClick?.(key)}
                    >
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                onClearFilter(key);
                            }}
                            className="text-accent-foreground hover:opacity-70 transition-opacity focus:outline-none"
                            aria-label={TEXTS.removeFilter}
                        >
                            <X className="h-4 w-4" />
                        </button>
                        <span className="flex items-center gap-1">
                            <span className="font-semibold">{column.label}:</span>{" "}
                            {getFilterDisplayValue(column, value)}
                        </span>
                    </Badge>
                );
            })}
        </div>
    );
};

export const ActiveFilters = memo(ActiveFiltersComponent) as typeof ActiveFiltersComponent;
