import { Filter } from "lucide-react";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { DateRange } from "@/components/forms/fields/date-range";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberRange } from "@/components/forms/fields/number-range";
import { ShortText } from "@/components/forms/fields/short-text";
import { AdminTableColumn, DateFilterValue, FilterValue, NumberFilterValue } from "@/components/table/types";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";

const TEXTS = {
    filters: "סינון",
    apply: "החל",
    reset: "נקה הכל"
};

interface FilterButtonRef extends HTMLButtonElement {
    openWithFilter?: (key: string) => void;
}

type FilterDrawerProps<T = unknown> = {
    columns: AdminTableColumn<T>[];
    activeFilters: Record<string, FilterValue>;
    onApplyFilters: (filters: Record<string, FilterValue>) => void;
    buttonLabel?: string;
    triggerRef?: React.RefObject<FilterButtonRef | null>;
};

const FilterDrawerComponent = <T = unknown,>({
    columns,
    activeFilters,
    onApplyFilters,
    triggerRef
}: FilterDrawerProps<T>) => {
    const [isOpen, setIsOpen] = useState(false);
    const [focusedFilterKey, setFocusedFilterKey] = useState<string | null>(null);
    const initRef = useRef(false);
    const prevFiltersRef = useRef<Record<string, FilterValue>>(activeFilters);

    const formMethods = useForm<Record<string, FilterValue>>();

    const filterableColumns = columns.filter((col) => col.filterable);

    useEffect(() => {
        if (JSON.stringify(prevFiltersRef.current) !== JSON.stringify(activeFilters)) {
            formMethods.reset(activeFilters);
            prevFiltersRef.current = activeFilters;
        }
    }, [activeFilters, formMethods]);

    const handleOpenChange = useCallback(
        (open: boolean) => {
            if (open) {
                formMethods.reset(activeFilters);
                initRef.current = true;
            } else {
                initRef.current = false;
                setFocusedFilterKey(null);
            }
            setIsOpen(open);
        },
        [activeFilters, formMethods]
    );

    const openWithFilter = useCallback((filterKey: string) => {
        setFocusedFilterKey(filterKey);
        setIsOpen(true);
    }, []);

    useEffect(() => {
        if (triggerRef && triggerRef.current) {
            triggerRef.current.openWithFilter = openWithFilter;
        }
    }, [openWithFilter, triggerRef]);

    useEffect(() => {
        if (isOpen && focusedFilterKey) {
            const timeoutId = setTimeout(() => {
                const element = document.getElementById(`filter-${focusedFilterKey}`);
                if (element) {
                    element.scrollIntoView({ behavior: "smooth", block: "center" });

                    element.classList.add("highlight-filter");
                    const highlightTimeoutId = setTimeout(() => {
                        element.classList.remove("highlight-filter");
                    }, 2000);
                    return () => clearTimeout(highlightTimeoutId);
                }
            }, 300);
            return () => clearTimeout(timeoutId);
        }
    }, [isOpen, focusedFilterKey]);

    const handleApply = useCallback(() => {
        setIsOpen(false);

        const formValues = formMethods.getValues();
        const newFilters: Record<string, FilterValue> = {};

        Object.entries(formValues).forEach(([key, value]) => {
            const column = columns.find((col) => col.key === key);
            if (!column) return;

            if (value === null || value === undefined || value === "") {
                return;
            }

            if (typeof value === "object" && !Array.isArray(value) && Object.keys(value).length === 0) {
                return;
            }

            if (Array.isArray(value) && value.length === 0) {
                return;
            }

            switch (column.filterType) {
                case "select": {
                    if (Array.isArray(value) && value.length > 0) {
                        const processedValue = value.map((v) => {
                            if (typeof v === "boolean") {
                                return v;
                            }
                            return v as string;
                        });
                        newFilters[key] = processedValue;
                    }
                    break;
                }
                case "date": {
                    const dateVal = value as DateFilterValue;
                    if (dateVal && (dateVal.startDate || dateVal.endDate)) {
                        const startDate = dateVal.startDate ? new Date(dateVal.startDate) : undefined;
                        const endDate = dateVal.endDate ? new Date(dateVal.endDate) : undefined;

                        const isStartDateValid = startDate && !isNaN(startDate.getTime());
                        const isEndDateValid = endDate && !isNaN(endDate.getTime());

                        newFilters[key] = {
                            startDate: isStartDateValid ? startDate : undefined,
                            endDate: isEndDateValid ? endDate : undefined
                        } as DateFilterValue;
                    }
                    break;
                }
                case "number": {
                    const numValFromForm = value as { min?: string | number; max?: string | number };

                    let finalMin: number | undefined = undefined;
                    let finalMax: number | undefined = undefined;

                    if (
                        numValFromForm?.min !== undefined &&
                        numValFromForm.min !== null &&
                        String(numValFromForm.min).trim() !== ""
                    ) {
                        const parsedMin = Number(numValFromForm.min);
                        if (!isNaN(parsedMin)) {
                            finalMin = parsedMin;
                        }
                    }

                    if (
                        numValFromForm?.max !== undefined &&
                        numValFromForm.max !== null &&
                        String(numValFromForm.max).trim() !== ""
                    ) {
                        const parsedMax = Number(numValFromForm.max);
                        if (!isNaN(parsedMax)) {
                            finalMax = parsedMax;
                        }
                    }

                    if (finalMin !== undefined || finalMax !== undefined) {
                        newFilters[key] = { min: finalMin, max: finalMax } as NumberFilterValue;
                    }
                    break;
                }
                case "text":
                default: {
                    if (typeof value === "string" || typeof value === "number") {
                        newFilters[key] = value;
                    }
                    break;
                }
            }
        });

        setTimeout(() => {
            onApplyFilters(newFilters);
        }, 0);
    }, [columns, formMethods, onApplyFilters]);

    const handleReset = useCallback(() => {
        formMethods.reset({});
        setTimeout(() => {
            onApplyFilters({});
            setIsOpen(false);
        }, 0);
    }, [formMethods, onApplyFilters]);

    if (filterableColumns.length === 0) {
        return null;
    }

    return (
        <Sheet open={isOpen} onOpenChange={handleOpenChange}>
            <SheetTrigger asChild>
                <Button variant="outline" className="gap-2" ref={triggerRef}>
                    <Filter className="h-4 w-4" />
                    {TEXTS.filters}
                </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:max-w-md" dir="rtl">
                <SheetHeader>
                    <SheetTitle>{TEXTS.filters}</SheetTitle>
                </SheetHeader>
                <div className="py-6">
                    <FormProvider {...formMethods}>
                        <form
                            onSubmit={(e) => {
                                e.preventDefault();
                                handleApply();
                            }}
                            className="space-y-6"
                        >
                            {filterableColumns.map((column) => {
                                if (column.filterType === "select" && column.filterOptions) {
                                    return (
                                        <div key={column.key} className="space-y-2" id={`filter-${column.key}`}>
                                            <Label htmlFor={column.key}>{column.label}</Label>
                                            <MultiSelect
                                                name={column.key}
                                                options={column.filterOptions}
                                                placeholder={column.filterPlaceholder || `בחר ${column.label}`}
                                                required={false}
                                            />
                                        </div>
                                    );
                                } else if (column.filterType === "date") {
                                    return (
                                        <div key={column.key} className="space-y-2" id={`filter-${column.key}`}>
                                            <DateRange
                                                name={column.key}
                                                label={column.label}
                                                required={false}
                                                placeholder={column.filterPlaceholder || `בחר ${column.label}`}
                                            />
                                        </div>
                                    );
                                } else if (column.filterType === "number") {
                                    return (
                                        <div key={column.key} className="space-y-2" id={`filter-${column.key}`}>
                                            <NumberRange name={column.key} label={column.label} required={false} />
                                        </div>
                                    );
                                } else {
                                    return (
                                        <div key={column.key} className="space-y-2" id={`filter-${column.key}`}>
                                            <Label htmlFor={column.key}>{column.label}</Label>
                                            <ShortText
                                                name={column.key}
                                                placeholder={column.filterPlaceholder || `הזן ${column.label}`}
                                                required={false}
                                            />
                                        </div>
                                    );
                                }
                            })}
                            <div className="flex justify-between gap-2 pt-6">
                                <Button type="button" variant="outline" onClick={handleReset}>
                                    {TEXTS.reset}
                                </Button>
                                <Button type="submit" variant="default">
                                    {TEXTS.apply}
                                </Button>
                            </div>
                        </form>
                    </FormProvider>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export const FilterDrawer = memo(FilterDrawerComponent) as typeof FilterDrawerComponent;
