import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useRef } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { Option } from "@/components/forms/fields/dropdown-base";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { FilterValue } from "@/components/table/types";
import { Button } from "@/components/ui/button";

import { ActiveFilters } from "./filter/active-filters";
import { FilterDrawer } from "./filter/filter-drawer";
import { AdminTableColumn } from "./types";

interface TableHeaderProps<T> {
    title: string;
    columns: AdminTableColumn<T>[];
    addButtonHref?: string;
    addButtonLabel?: string;
    pageSize?: number;
    onPageSizeChange?: (size: number) => void;
    pageSizeOptions?: Option[];
    activeFilters: Record<string, FilterValue>;
    onFilterApply: (filters: Record<string, FilterValue>) => void;
    onClearFilter: (key: string) => void;
    onClearAllFilters: () => void;
    onUpdateFilter?: (key: string, value: FilterValue) => void;
}

interface PageSizeFormValues {
    pageSize?: Option;
}

interface FilterButtonRef extends HTMLButtonElement {
    openWithFilter?: (key: string) => void;
}

export function TableHeader<T>({
    title,
    columns,
    addButtonHref,
    addButtonLabel,
    pageSize,
    onPageSizeChange,
    pageSizeOptions,
    activeFilters,
    onFilterApply,
    onClearFilter,
    onClearAllFilters,
    onUpdateFilter
}: TableHeaderProps<T>) {
    const formMethods = useForm<PageSizeFormValues>({
        defaultValues: {
            pageSize: pageSizeOptions?.find((opt) => opt.id === String(pageSize))
        }
    });
    const filterDrawerTriggerRef = useRef<FilterButtonRef | null>(null);
    const hasFilterableColumns = columns.some((col) => col.filterable);

    useEffect(() => {
        if (pageSize && pageSizeOptions) {
            formMethods.setValue(
                "pageSize",
                pageSizeOptions.find((opt) => opt.id === String(pageSize))
            );
        }
    }, [pageSize, pageSizeOptions, formMethods]);

    useEffect(() => {
        const subscription = formMethods.watch((values) => {
            const newPageSize = values.pageSize;
            if (newPageSize?.id && onPageSizeChange && String(pageSize) !== newPageSize.id) {
                onPageSizeChange(Number(newPageSize.id));
            }
        });
        return () => subscription.unsubscribe();
    }, [formMethods, pageSize, onPageSizeChange]);

    const handleFilterClick = (key: string) => {
        if (filterDrawerTriggerRef.current && filterDrawerTriggerRef.current.openWithFilter) {
            filterDrawerTriggerRef.current.openWithFilter(key);
        }
    };

    return (
        <div className="flex flex-col mb-6" dir="rtl">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-semibold my-auto">{title}</h1>
                <div className="flex items-center gap-2">
                    {pageSizeOptions && onPageSizeChange && pageSize && (
                        <div className="w-20">
                            <FormProvider {...formMethods}>
                                <SingleSelect
                                    name="pageSize"
                                    options={pageSizeOptions}
                                    required={false}
                                    placeholder={String(pageSize)}
                                    showSearch={false}
                                />
                            </FormProvider>
                        </div>
                    )}
                    {hasFilterableColumns && (
                        <FilterDrawer
                            columns={columns}
                            activeFilters={activeFilters}
                            onApplyFilters={onFilterApply}
                            triggerRef={filterDrawerTriggerRef}
                        />
                    )}
                    {addButtonHref && (
                        <Button asChild>
                            <Link href={addButtonHref}>
                                {addButtonLabel}
                                <PlusIcon className="w-4 h-4 mr-2" />
                            </Link>
                        </Button>
                    )}
                </div>
            </div>
            {hasFilterableColumns && (
                <div className="mt-3">
                    <ActiveFilters
                        columns={columns}
                        activeFilters={activeFilters}
                        onClearFilter={onClearFilter}
                        onClearAllFilters={onClearAllFilters}
                        onUpdateFilter={onUpdateFilter}
                        onFilterClick={handleFilterClick}
                    />
                </div>
            )}
        </div>
    );
}
