"use client";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";

export interface DraggableRowProps {
    id: string;
    disabled?: boolean;
    children: React.ReactNode;
    showDragHandle?: boolean;
}

export function DraggableRow({ id, disabled = false, children, showDragHandle = false }: DraggableRowProps) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
        id,
        disabled
    });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition
    };

    return (
        <TableRow
            ref={setNodeRef}
            style={style}
            className={cn("group hover:bg-muted/50", isDragging && "bg-muted/50", !disabled && "cursor-move")}
            {...attributes}
        >
            {showDragHandle && (
                <TableCell className="w-[50px] text-center">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="cursor-grab text-muted-foreground size-7 hover:bg-transparent"
                        {...listeners}
                    >
                        <GripVertical className="size-4" />
                        <span className="sr-only">Drag handle</span>
                    </Button>
                </TableCell>
            )}
            {children}
        </TableRow>
    );
}
