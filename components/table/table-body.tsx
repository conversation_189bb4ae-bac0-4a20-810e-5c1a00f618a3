import {
    closestCenter,
    DndContext,
    DragEndEvent,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Pencil, Trash2 } from "lucide-react";
import Link from "next/link";

import { DeleteConfirmationDialog } from "@/components/common/delete-confirmation-dialog";
import { Button } from "@/components/ui/button";

import { DraggableRow } from "./draggable-row";
import { AdminTableColumn } from "./types";

const TEXTS = {
    actions: "פעולות"
};

interface TableBodyCommonProps<T extends { id: string }> {
    items: T[];
    columns: AdminTableColumn<T>[];
    addButtonHref?: string;
    isDeleting: boolean;
    deleteConfirmTitle: string;
    deleteConfirmDescription: string;
    confirmDeleteText: string;
    onDeleteConfirm: (id: string) => void;
}

interface NonReorderableTableProps<T extends { id: string }> extends TableBodyCommonProps<T> {
    reorderable: false;
}

interface ReorderableTableProps<T extends { id: string }> extends TableBodyCommonProps<T> {
    reorderable: true;
    onReorder: (items: T[]) => void;
}

type TableBodyProps<T extends { id: string }> = NonReorderableTableProps<T> | ReorderableTableProps<T>;

export function TableBody<T extends { id: string }>({
    items,
    columns,
    reorderable,
    addButtonHref,
    isDeleting,
    deleteConfirmTitle,
    deleteConfirmDescription,
    confirmDeleteText,
    onDeleteConfirm,
    ...props
}: TableBodyProps<T>) {
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates
        })
    );

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over || active.id === over.id) return;

        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        if (oldIndex === -1 || newIndex === -1) return;

        const newItems = [...items];
        const [movedItem] = newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, movedItem);

        if (reorderable) {
            (props as ReorderableTableProps<T>).onReorder(newItems);
        }
    };

    if (reorderable) {
        return (
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis]}
            >
                <table className="w-full border-collapse" style={{ direction: "rtl" }}>
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="p-4 text-center w-[50px]">
                                <span className="sr-only">סדר</span>
                            </th>
                            {columns.map((column) => (
                                <th
                                    key={String(column.key)}
                                    style={column.width ? { width: column.width } : {}}
                                    className="p-4 text-right text-xs font-medium text-gray-500 uppercase border-b"
                                >
                                    {column.label}
                                </th>
                            ))}
                            <th className="p-4 text-center text-xs font-medium text-gray-500 uppercase border-b w-[100px]">
                                {TEXTS.actions}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <SortableContext items={items.map((item) => item.id)} strategy={verticalListSortingStrategy}>
                            {items.map((item) => (
                                <DraggableRow key={item.id} id={item.id} showDragHandle={true}>
                                    {columns.map((column) => (
                                        <td
                                            key={`${item.id}-${String(column.key)}`}
                                            className="p-4 text-right text-sm text-gray-700"
                                        >
                                            {column.render ? column.render(item) : "-"}
                                        </td>
                                    ))}
                                    <td className="p-4 text-center text-sm w-[100px]">
                                        <div className="flex items-center justify-center gap-2">
                                            <Button variant="ghost" size="icon" asChild className="h-8 w-8">
                                                <Link href={`${addButtonHref?.replace("/new", "")}/${item.id}`}>
                                                    <Pencil className="h-4 w-4" />
                                                </Link>
                                            </Button>
                                            <DeleteConfirmationDialog
                                                onConfirm={() => onDeleteConfirm(item.id)}
                                                title={deleteConfirmTitle}
                                                description={deleteConfirmDescription}
                                                confirmText={confirmDeleteText}
                                            >
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-8 w-8"
                                                    disabled={isDeleting}
                                                    aria-label="Delete"
                                                >
                                                    <Trash2 className="h-4 w-4 text-red-600" />
                                                </Button>
                                            </DeleteConfirmationDialog>
                                        </div>
                                    </td>
                                </DraggableRow>
                            ))}
                        </SortableContext>
                    </tbody>
                </table>
            </DndContext>
        );
    }

    return (
        <table className="w-full border-collapse" style={{ direction: "rtl" }}>
            <thead className="bg-gray-50">
                <tr>
                    {columns.map((column) => (
                        <th
                            key={String(column.key)}
                            style={column.width ? { width: column.width } : {}}
                            className="p-4 text-right text-xs font-medium text-gray-500 uppercase border-b"
                        >
                            {column.label}
                        </th>
                    ))}
                    <th className="p-4 text-center text-xs font-medium text-gray-500 uppercase border-b w-[100px]">
                        {TEXTS.actions}
                    </th>
                </tr>
            </thead>
            <tbody>
                {items.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50 border-b">
                        {columns.map((column) => (
                            <td
                                key={`${item.id}-${String(column.key)}`}
                                className="p-4 text-right text-sm text-gray-700"
                            >
                                {column.render ? column.render(item) : "-"}
                            </td>
                        ))}
                        <td className="p-4 text-center text-sm w-[100px]">
                            <div className="flex items-center justify-center gap-2">
                                <Button variant="ghost" size="icon" asChild className="h-8 w-8">
                                    <Link href={`${addButtonHref?.replace("/new", "")}/${item.id}`}>
                                        <Pencil className="h-4 w-4" />
                                    </Link>
                                </Button>
                                <DeleteConfirmationDialog
                                    onConfirm={() => onDeleteConfirm(item.id)}
                                    title={deleteConfirmTitle}
                                    description={deleteConfirmDescription}
                                    confirmText={confirmDeleteText}
                                >
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8"
                                        disabled={isDeleting}
                                        aria-label="Delete"
                                    >
                                        <Trash2 className="h-4 w-4 text-red-600" />
                                    </Button>
                                </DeleteConfirmationDialog>
                            </div>
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
}
