import dynamic from "next/dynamic";
import React, { useEffect, useRef, useState } from "react";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import loadingAnimation from "../../public/onboarding/loading-animation.json";

const TEXTS = {
    progressSteps: ["בודקים את הפרטים שלך", "מחפשים מלגות מתאימות", "כמעט סיימנו"]
};

interface OnboardingLoaderProps {
    onComplete?: () => void;
    className?: string;
}

export const OnboardingLoader: React.FC<OnboardingLoaderProps> = ({ onComplete, className }) => {
    const [progressIdx, setProgressIdx] = useState(0);
    const onCompleteRef = useRef(onComplete);
    onCompleteRef.current = onComplete;
    const isMounted = useRef(true);

    useEffect(() => {
        isMounted.current = true;
        let timer: NodeJS.Timeout | null = null;
        if (progressIdx < TEXTS.progressSteps.length - 1) {
            timer = setTimeout(() => {
                if (isMounted.current) setProgressIdx((idx) => idx + 1);
            }, 1500);
        } else if (progressIdx === TEXTS.progressSteps.length - 1) {
            timer = setTimeout(() => {
                onCompleteRef.current?.();
                if (isMounted.current) setProgressIdx(0);
            }, 1500);
        }
        return () => {
            isMounted.current = false;
            if (timer) clearTimeout(timer);
        };
    }, [progressIdx]);

    return (
        <div className={`flex flex-col items-center justify-center flex-1 ${className || ""}`}>
            <div className="w-48 h-48">
                <Lottie animationData={loadingAnimation} loop autoplay />
            </div>
            <div className="mt-8 text-lg md:text-xl font-medium text-gray-700 bg-blue-50 px-6 py-3 rounded-xl transition-all duration-500 text-center min-h-[2.5rem] inline-block mx-auto">
                {TEXTS.progressSteps[progressIdx]}
                <span
                    className="inline-block animate-pulse text-2xl align-middle mb-1"
                    style={{ animationDuration: "1.2s" }}
                >
                    ...
                </span>
            </div>
        </div>
    );
};
