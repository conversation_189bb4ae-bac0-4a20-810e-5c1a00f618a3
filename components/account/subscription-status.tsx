"use client";

import { useUser } from "@clerk/nextjs";
import { InfoIcon } from "lucide-react";
import { useEffect, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { PRICING_PLANS } from "@/config/subscriptions";
import { PlanType, TEXTS as SUBSCRIPTION_TEXTS, UserSubscriptionWithPlan } from "@/lib/subscription-constants";
import { cn } from "@/lib/utils";

const TEXTS = {
    planNames: {
        free: "חבילה חינמית",
        milgapro: "Milga-Pro",
        elite: "Elite Student",
        vip: "VIP"
    } as Record<PlanType, string>
};

interface SubscriptionStatusProps {
    className?: string;
    currentSubscription?: UserSubscriptionWithPlan | null;
}

export function SubscriptionStatus({ className, currentSubscription: propSubscription }: SubscriptionStatusProps) {
    const { isSignedIn } = useUser();
    const [fetchedSubscription, setFetchedSubscription] = useState<UserSubscriptionWithPlan | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const shouldFetch = propSubscription === undefined && isSignedIn;
    const currentSubscription = propSubscription || fetchedSubscription;

    useEffect(() => {
        if (!shouldFetch) {
            setIsLoading(false);
            return;
        }

        let isMounted = true;
        async function fetchSubscription() {
            if (isMounted) setIsLoading(true);

            try {
                const subscription = await getCurrentUserSubscription();
                if (isMounted) setFetchedSubscription(subscription);
            } catch (error) {
                console.error("Error fetching subscription:", error);
            } finally {
                if (isMounted) setIsLoading(false);
            }
        }

        fetchSubscription();

        return () => {
            isMounted = false;
        };
    }, [shouldFetch]);

    const plan: PlanType = currentSubscription?.planType || "free";

    const planDetails = PRICING_PLANS.find((p) => p.planType === plan);
    const planTextColor = planDetails?.colors.text || "";

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString("he-IL", {
            year: "numeric",
            month: "long",
            day: "numeric"
        });
    };

    if (isLoading && shouldFetch) {
        return (
            <div className={cn("mb-4", className)}>
                <div className="flex animate-pulse items-center gap-2 rounded-md bg-gray-50 p-4 text-sm" dir="rtl">
                    <div className="h-4 w-4 rounded-full bg-muted" />
                    <div className="h-4 w-2/3 rounded bg-muted" />
                </div>
            </div>
        );
    }

    if (!isSignedIn || !currentSubscription || !currentSubscription.is_active) {
        return null;
    }

    return (
        <div className={cn("mb-4", className)}>
            <div className="flex items-center gap-2 text-sm text-gray-600 p-4 bg-gray-50 rounded-md" dir="rtl">
                <InfoIcon className="h-4 w-4" />
                <p>
                    {SUBSCRIPTION_TEXTS.CURRENT_SUBSCRIPTION.YOUR_CURRENT_PLAN_IS}{" "}
                    <span className={cn("px-2 py-1 rounded-full text-sm", planTextColor, planDetails?.colors.bg || "")}>
                        {planDetails?.title || TEXTS.planNames[plan]}
                    </span>
                    {currentSubscription && currentSubscription.expiration_date && (
                        <>
                            {" "}
                            {SUBSCRIPTION_TEXTS.CURRENT_SUBSCRIPTION.UNTIL_DATE}{" "}
                            {formatDate(currentSubscription.expiration_date)}
                        </>
                    )}
                </p>
            </div>
        </div>
    );
}
