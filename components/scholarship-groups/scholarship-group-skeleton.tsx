"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON> } from "@/components/ui/card";

export function ScholarshipGroupSkeleton() {
    return (
        <Card className="h-full border border-gray-200 shadow-sm animate-pulse flex flex-col">
            <CardHeader className="pb-0 mb-4">
                <div className="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center" />
            </CardHeader>
            <CardContent className="space-y-2 flex-grow">
                <div className="h-7 w-3/4 rounded bg-gray-200" />
                <div className="space-y-2">
                    <div className="h-4 w-full rounded bg-gray-200" />
                    <div className="h-4 w-5/6 rounded bg-gray-200" />
                    <div className="h-4 w-2/3 rounded bg-gray-200" />
                </div>
            </CardContent>
            <CardFooter>
                <div className="flex items-center">
                    <div className="h-5 w-16 rounded bg-gray-200" />
                    <div className="h-4 w-4 rounded bg-gray-200 mr-1" />
                </div>
            </CardFooter>
        </Card>
    );
}

export function ScholarshipGroupSkeletonList() {
    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
                <ScholarshipGroupSkeleton key={i} />
            ))}
        </div>
    );
}
