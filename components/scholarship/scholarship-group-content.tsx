"use client";

import React from "react";

import FinalCTA from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import ScholarshipGroupHero from "@/components/scholarship/scholarship-group-hero";
import ScholarshipList from "@/components/scholarship/scholarship-list";
import { ScholarshipListSkeleton } from "@/components/scholarship/scholarship-list-skeleton";
import { useScholarships } from "@/hooks/use-scholarships";

export const TEXTS = {
    availableScholarships: "המלגות הזמינות",
    error: "אירעה שגיאה בטעינת המלגות. אנא נסו שוב מאוחר יותר.",
    noScholarships: "אין כרגע מלגות זמינות בקטגוריה זו. אנא בדקו שוב בקרוב.",
    applyButton: "לחצו וקבלו רשימת מלגות מותאמת אישית"
};

interface ScholarshipGroupContentProps {
    group: {
        title: string;
        description: string;
        image_url?: string;
        slug: string;
    };
    testimonialIds?: string[];
}

export default function ScholarshipGroupContent({ group, testimonialIds }: ScholarshipGroupContentProps) {
    const { displayedScholarships, loading, error, hasMore, loadMore } = useScholarships(group.slug);

    return (
        <main className="relative flex-1">
            <div className="-mx-4 md:-mx-0">
                <ScholarshipGroupHero title={group.title} description={group.description} imageUrl={group.image_url} />
            </div>

            <div className="h-4 bg-gradient-to-b from-primary/10 to-white shadow-sm"></div>

            <div className="bg-white">
                <div className="container relative">
                    <div className="px-4 md:px-0 space-y-12 md:space-y-16 py-20">
                        <section className="max-w-4xl mx-auto w-full">
                            <div className="text-center mb-12">
                                <h2 className="text-3xl md:text-4xl font-bold mb-4">{TEXTS.availableScholarships}</h2>
                                <div className="w-24 h-1 bg-primary/70 mx-auto rounded-full"></div>
                            </div>
                            {loading && <ScholarshipListSkeleton />}
                            {error && (
                                <div className="text-center py-10 rounded-lg bg-red-50 border border-red-100">
                                    <p className="text-red-600">{TEXTS.error}</p>
                                </div>
                            )}
                            {!loading && !error && displayedScholarships.length === 0 && (
                                <div className="text-center py-10 rounded-lg bg-gray-50 border border-gray-100">
                                    <p className="text-gray-600">{TEXTS.noScholarships}</p>
                                </div>
                            )}
                            {!loading && !error && displayedScholarships.length > 0 && (
                                <>
                                    <ScholarshipList
                                        scholarships={displayedScholarships}
                                        hasMore={hasMore}
                                        onLoadMore={loadMore}
                                    />
                                    <div className="justify-center mt-16">
                                        <FinalCTA />
                                    </div>
                                </>
                            )}
                        </section>

                        <section className="max-w-4xl mx-auto w-full pt-8">
                            <Testimonials ids={testimonialIds} />
                        </section>
                    </div>
                </div>
            </div>
        </main>
    );
}
