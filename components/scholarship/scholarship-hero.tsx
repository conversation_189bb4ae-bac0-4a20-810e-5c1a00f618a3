"use client";

import { useUser } from "@clerk/nextjs";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface ScholarshipHeroProps {
    title: string;
    shortDescription: string;
    applyButtonText: string;
    imageUrl?: string;
    scholarshipType: "submission" | "guidance";
}

export const TEXTS = {
    guest: "לחצו לקבלת רשימת מלגות אישית",
    submission: "להרשמה וקבלת הגשה אוטומטית",
    guidance: "לקבלת סיוע אישי בהגשת המלגה"
};

export default function ScholarshipHero({
    title,
    shortDescription,
    applyButtonText,
    imageUrl = "/images/scholarship/hero.webp",
    scholarshipType
}: ScholarshipHeroProps) {
    const [imgSrc, setImgSrc] = useState(imageUrl);
    const { user, isLoaded: isUserLoaded } = useUser();
    const isAuthenticated = !!user && isUserLoaded;

    const getButtonText = () => {
        if (!isAuthenticated) return TEXTS.guest;
        return TEXTS[scholarshipType];
    };

    const getButtonLink = () => {
        if (!isAuthenticated) return "/login";
        return "/subscriptions";
    };

    return (
        <div className="relative h-[500px] w-full overflow-hidden">
            <div className="absolute inset-0">
                <Image
                    src={imgSrc}
                    alt={title}
                    fill
                    className="object-cover"
                    priority
                    onError={() => setImgSrc("/images/scholarship/hero.webp")}
                />
                <div className="absolute inset-0 bg-gradient-to-b from-blue-900/85 to-primary/75" />

                <div
                    className="absolute inset-0 opacity-5"
                    style={{
                        backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)`,
                        backgroundSize: "20px 20px"
                    }}
                />
            </div>

            <div className="container relative h-full flex flex-col justify-center">
                <div className="flex justify-center w-full">
                    <motion.div
                        className="max-w-2xl text-right"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                    >
                        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight text-shadow-sm">
                            {title}
                        </h1>
                        <p className="text-xl md:text-2xl text-white/90 mb-8 font-light">{shortDescription}</p>

                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.98 }}
                            className="flex justify-center"
                        >
                            <Link href={getButtonLink()}>
                                <button className="bg-white hover:bg-white/90 text-primary px-8 py-3 rounded-lg text-lg font-semibold shadow-lg transition-all">
                                    {!isUserLoaded ? applyButtonText : getButtonText()}
                                </button>
                            </Link>
                        </motion.div>
                    </motion.div>
                </div>
            </div>
        </div>
    );
}
