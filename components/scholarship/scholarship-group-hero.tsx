"use client";

import { useUser } from "@clerk/nextjs";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/ui/button";

export const TEXTS = {
    button: {
        guest: "להרשמה וקבלת הגשה אוטומטית",
        authenticated: "רוצה שנגיש אותך בצורה אוטומטית?",
        loading: "לחצו וקבלו רשימת מלגות מותאמת אישית"
    }
};

interface ScholarshipGroupHeroProps {
    title: string;
    description: string;
    imageUrl?: string;
}

export default function ScholarshipGroupHero({
    title,
    description,
    imageUrl = "/images/scholarships/groups/default-hero.webp"
}: ScholarshipGroupHeroProps) {
    const [imgSrc, setImgSrc] = useState(imageUrl);
    const { user, isLoaded: isUserLoaded } = useUser();
    const isAuthenticated = !!user && isUserLoaded;

    const getButtonText = () => {
        if (!isUserLoaded) return TEXTS.button.loading;
        return isAuthenticated ? TEXTS.button.authenticated : TEXTS.button.guest;
    };

    const getButtonLink = () => {
        if (!isAuthenticated) return "/login";
        return "/subscriptions";
    };

    const scrollToContent = () => {
        window.scrollTo({
            top: window.innerHeight - 100,
            behavior: "smooth"
        });
    };

    return (
        <div className="relative h-[80vh] min-h-[600px] w-full overflow-hidden">
            <div className="absolute inset-0">
                <Image
                    src={imgSrc}
                    alt={title}
                    fill
                    className="object-cover"
                    priority
                    onError={() => setImgSrc("/images/scholarships/groups/default-hero.webp")}
                />
                <div className="absolute inset-0 bg-gradient-to-b from-blue-900/85 to-primary/75" />

                <div
                    className="absolute inset-0 opacity-5"
                    style={{
                        backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)`,
                        backgroundSize: "20px 20px"
                    }}
                />
            </div>

            <div className="container relative h-full flex flex-col justify-center">
                <div className="flex justify-center w-full">
                    <motion.div
                        className="max-w-2xl text-right"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                    >
                        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight text-shadow-sm">
                            {title}
                        </h1>
                        <p className="text-xl md:text-2xl text-white/90 mb-8 font-light">{description}</p>

                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.98 }}
                            className="flex justify-center"
                        >
                            <Link href={getButtonLink()}>
                                <Button
                                    size="lg"
                                    className="bg-white hover:bg-white/90 text-blue-900 font-medium text-lg px-8 shadow-lg"
                                >
                                    {getButtonText()}
                                </Button>
                            </Link>
                        </motion.div>
                    </motion.div>
                </div>
            </div>

            <motion.div
                className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white cursor-pointer"
                animate={{ y: [0, 10, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
                onClick={scrollToContent}
            >
                <ChevronDown size={32} className="mx-auto" />
            </motion.div>
        </div>
    );
}
