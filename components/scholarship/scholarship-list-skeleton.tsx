"use client";

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export function ScholarshipListSkeleton() {
    return (
        <Accordion type="single" collapsible className="w-full">
            {Array.from({ length: 3 }).map((_, i) => (
                <AccordionItem
                    key={i}
                    value={`item-${i}`}
                    className="bg-white border border-gray-200 rounded-lg mb-4 last:mb-0 px-6 py-2 animate-pulse"
                >
                    <AccordionTrigger className="hover:no-underline">
                        <div className="flex flex-col w-full gap-6">
                            <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 md:gap-8">
                                <div className="text-right">
                                    <div className="h-7 w-48 bg-gray-200/60 rounded-md"></div>
                                </div>
                                <div className="flex flex-wrap gap-6">
                                    <div className="flex items-center gap-2">
                                        <div className="h-4 w-4 bg-gray-200/60 rounded-md"></div>
                                        <div className="h-5 w-32 bg-gray-200/60 rounded-md"></div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="h-4 w-4 bg-gray-200/60 rounded-md"></div>
                                        <div className="h-5 w-24 bg-gray-200/60 rounded-md"></div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="h-4 w-4 bg-gray-200/60 rounded-md"></div>
                                        <div className="h-5 w-40 bg-gray-200/60 rounded-md"></div>
                                    </div>
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="h-5 w-full bg-gray-200/60 rounded-md"></div>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent>
                        <div className="space-y-6 pt-4">
                            <div className="h-5 w-full bg-gray-200/60 rounded-md"></div>
                            <div className="h-5 w-5/6 bg-gray-200/60 rounded-md"></div>
                            <div className="flex justify-end">
                                <div className="h-10 w-32 bg-gray-200/60 rounded-lg"></div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    );
}
