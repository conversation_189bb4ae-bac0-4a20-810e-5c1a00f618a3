"use client";

import { CalendarIcon, Clock, Coins, Users } from "lucide-react";
import Link from "next/link";
import { ElementType, useEffect, useState } from "react";

import { sanitizeHtml } from "@/app/lib/utils";
import FinalCTA from "@/components/home/<USER>";
import ScholarshipGroups from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import ScholarshipHero from "@/components/scholarship/scholarship-hero";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Database } from "@/types/database.types";

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"];

export const TEXTS = {
    page: {
        whenToApply: "מתי להגיש",
        howMuch: "סכום המלגה",
        whoIsItFor: "למי מיועדת",
        needToVolunteer: "שעות התנדבות",
        currency: "₪",
        description: "תיאור המלגה",
        requirements: "דרישות",
        benefits: "הטבות",
        applyButton: "לחצו וקבלו רשימת מלגות מותאמת אישית",
        excellentStudents: "סטודנטים מצטיינים"
    },
    list: {
        otherTypesDescription: "גלו עוד סוגי מלגות שעשויות להתאים לכם ולעזור לכם במימון הלימודים",
        volunteerHours: "שעות התנדבות",
        otherTypes: "סוגי מלגות נוספות"
    }
} as const;

function formatDateRange(startDate: string, endDate: string) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "long",
        day: "numeric"
    };

    const startFormatted = start.toLocaleDateString("he-IL", options);
    const endFormatted = end.toLocaleDateString("he-IL", options);

    return `${startFormatted} - ${endFormatted}`;
}

interface ScholarshipDetailProps {
    scholarshipData: Scholarship | null;
    serverError?: string;
}

interface SafeHTMLProps {
    content: string;
    className?: string;
    as?: ElementType;
}

function SafeHTML({ content, className, as: Component = "span" }: SafeHTMLProps) {
    const [sanitized, setSanitized] = useState(() => {
        try {
            return sanitizeHtml(content);
        } catch (error) {
            console.error("Sanitization failed:", error);
            return "";
        }
    });

    useEffect(() => {
        try {
            const newSanitized = sanitizeHtml(content);
            if (newSanitized !== sanitized) {
                setSanitized(newSanitized);
            }
        } catch (error) {
            console.error("Sanitization failed:", error);
            setSanitized("");
        }
    }, [content, sanitized]);

    return <Component className={className} dangerouslySetInnerHTML={{ __html: sanitized }} />;
}

interface SafeListItemProps {
    content: string;
    icon?: React.ReactNode;
    className?: string;
}

function SafeListItem({ content, icon, className }: SafeListItemProps) {
    return (
        <li className={cn("flex items-start gap-3 text-right", className)}>
            {icon || (
                <span className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center mt-0.5 flex-shrink-0">
                    ✓
                </span>
            )}
            <SafeHTML content={content} />
        </li>
    );
}

export default function ScholarshipDetail({ scholarshipData, serverError }: ScholarshipDetailProps) {
    const [scholarship, setScholarship] = useState<Scholarship | null>(scholarshipData || null);
    const [testimonialIds, setTestimonialIds] = useState<string[]>([]);
    const [loading, setLoading] = useState(!scholarshipData);
    const [error, setError] = useState<string | null>(serverError || null);

    useEffect(() => {
        if (scholarshipData) {
            fetchTestimonialIds().catch(console.error);
            return;
        }

        async function fetchScholarship() {
            try {
                setLoading(true);
                const { getSupabaseClient } = await import("@/utils/supabase/client");
                const supabase = getSupabaseClient();
                const { data, error } = await supabase
                    .from("scholarships")
                    .select("*")
                    .eq("id", scholarship?.id)
                    .single();

                if (error) {
                    if (error.code === "user_not_found" || error.code === "invalid_jwt") {
                        console.error("Authentication error:", error);
                        return;
                    }
                    throw error;
                }

                if (!data) {
                    throw new Error("Scholarship not found");
                }

                setScholarship(data);
                await fetchTestimonialIds();
            } catch (err) {
                setError(err instanceof Error ? err.message : "An error occurred");
            } finally {
                setLoading(false);
            }
        }

        async function fetchTestimonialIds() {
            try {
                const { getSupabaseClient } = await import("@/utils/supabase/client");
                const supabase = getSupabaseClient();
                const { data, error } = await supabase
                    .from("link_scholarship_to_testimonial")
                    .select("testimonial_id")
                    .eq("scholarship_id", scholarship?.id);

                if (error) {
                    if (error.code === "user_not_found" || error.code === "invalid_jwt") {
                        console.error("Authentication error:", error);
                        return;
                    }
                    throw error;
                }

                setTestimonialIds(data?.map((item) => item.testimonial_id) || []);
            } catch (err) {
                console.error("Error fetching testimonial IDs:", err);
                setTestimonialIds([]);
            }
        }

        fetchScholarship();
    }, [scholarshipData, scholarship?.id]);

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50/50">
                <div className="relative h-[500px] w-full overflow-hidden bg-gradient-to-b from-blue-900/50 to-primary/50">
                    <div className="container relative h-full flex flex-col justify-center">
                        <div className="flex justify-center w-full">
                            <div className="max-w-2xl text-center space-y-6">
                                <div className="h-12 w-80 bg-white/20 rounded-lg animate-pulse"></div>
                                <div className="h-6 w-80 bg-white/20 rounded-lg animate-pulse"></div>
                                <div className="h-12 w-48 bg-white/30 rounded-lg animate-pulse mt-8 mx-auto"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="h-4 bg-gradient-to-b from-primary/10 to-white shadow-sm"></div>

                <div className="container py-16">
                    <div className="max-w-4xl mx-auto">
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                            {Array.from({ length: 4 }).map((_, i) => (
                                <div
                                    key={i}
                                    className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 text-center"
                                >
                                    <div className="flex flex-col items-center gap-3">
                                        <div className="bg-primary/10 p-3 rounded-full h-14 w-14 flex items-center justify-center">
                                            <div className="h-7 w-7 bg-primary/20 rounded-full animate-pulse"></div>
                                        </div>
                                        <div className="h-6 w-24 bg-gray-200 rounded-md animate-pulse"></div>
                                        <div className="h-5 w-32 bg-gray-200 rounded-md animate-pulse"></div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
                            {Array.from({ length: 2 }).map((_, i) => (
                                <div key={i} className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                                    <div className="flex justify-center mb-6">
                                        <div className="h-8 w-32 bg-gray-200 rounded-md animate-pulse"></div>
                                    </div>
                                    <div className="space-y-3">
                                        {Array.from({ length: 4 }).map((_, j) => (
                                            <div key={j} className="flex items-start gap-3 text-right">
                                                <div className="bg-primary/10 rounded-full h-6 w-6 flex-shrink-0 animate-pulse"></div>
                                                <div className="h-5 w-full bg-gray-200 rounded-md animate-pulse"></div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 mb-16">
                            <div className="flex justify-center mb-6">
                                <div className="h-8 w-32 bg-gray-200 rounded-md animate-pulse"></div>
                            </div>
                            <div className="space-y-4">
                                <div className="h-5 w-full bg-gray-200 rounded-md animate-pulse"></div>
                                <div className="h-5 w-full bg-gray-200 rounded-md animate-pulse"></div>
                                <div className="h-5 w-4/5 bg-gray-200 rounded-md animate-pulse"></div>
                                <div className="h-5 w-full bg-gray-200 rounded-md animate-pulse"></div>
                                <div className="h-5 w-3/4 bg-gray-200 rounded-md animate-pulse"></div>
                            </div>
                            <div className="flex justify-center mt-8">
                                <div className="h-14 w-64 bg-primary/30 rounded-lg animate-pulse"></div>
                            </div>
                        </div>

                        <div className="mb-8 text-center">
                            <div className="h-8 w-48 bg-gray-200 rounded-md animate-pulse mx-auto mb-4"></div>
                            <div className="h-5 w-96 bg-gray-200 rounded-md animate-pulse mx-auto"></div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error || !scholarship) {
        return <div className="min-h-screen flex items-center justify-center">שגיאה: {error}</div>;
    }

    const requirements = Array.isArray(scholarship.requirements) ? scholarship.requirements : [];
    const benefits = Array.isArray(scholarship.benefits) ? scholarship.benefits : [];

    return (
        <div className="min-h-screen bg-gray-50/50">
            <ScholarshipHero
                title={scholarship.title}
                shortDescription={scholarship.short_description}
                applyButtonText={TEXTS.page.applyButton}
                imageUrl={scholarship.image_url || undefined}
                scholarshipType={scholarship.scholarship_type}
            />

            <div className="h-4 bg-gradient-to-b from-primary/10 to-white shadow-sm"></div>

            <div className="container py-16">
                <div className="max-w-4xl mx-auto">
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                        <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 p-6 text-center">
                            <div className="flex flex-col items-center gap-3">
                                <div className="bg-primary/10 p-3 rounded-full">
                                    <CalendarIcon className="h-7 w-7 text-primary" />
                                </div>
                                <h3 className="font-bold text-lg text-gray-800">{TEXTS.page.whenToApply}</h3>
                                <p className="text-gray-600">
                                    {formatDateRange(scholarship.start_date, scholarship.end_date)}
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 p-6 text-center">
                            <div className="flex flex-col items-center gap-3">
                                <div className="bg-primary/10 p-3 rounded-full">
                                    <Coins className="h-7 w-7 text-primary" />
                                </div>
                                <h3 className="font-bold text-lg text-gray-800">{TEXTS.page.howMuch}</h3>
                                <p className="text-gray-600 font-medium text-lg">
                                    {scholarship.min_amount === scholarship.max_amount
                                        ? `${scholarship.min_amount.toLocaleString()} ${TEXTS.page.currency}`
                                        : `${scholarship.max_amount.toLocaleString()} - ${scholarship.min_amount.toLocaleString()} ${TEXTS.page.currency}`}
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 p-6 text-center">
                            <div className="flex flex-col items-center gap-3">
                                <div className="bg-primary/10 p-3 rounded-full">
                                    <Users className="h-7 w-7 text-primary" />
                                </div>
                                <h3 className="font-bold text-lg text-gray-800">{TEXTS.page.whoIsItFor}</h3>
                                <p className="text-gray-600">
                                    {scholarship.target_audience || TEXTS.page.excellentStudents}
                                </p>
                            </div>
                        </div>
                        <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 p-6 text-center">
                            <div className="flex flex-col items-center gap-3">
                                <div className="bg-primary/10 p-3 rounded-full">
                                    <Clock className="h-7 w-7 text-primary" />
                                </div>
                                <h3 className="font-bold text-lg text-gray-800">{TEXTS.page.needToVolunteer}</h3>
                                <p className="text-gray-600 font-medium text-lg">{scholarship.volunteer_hours}</p>
                            </div>
                        </div>
                    </div>

                    {(requirements.length > 0 || benefits.length > 0) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
                            {requirements.length > 0 && (
                                <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                                    <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
                                        <span className="relative">
                                            {TEXTS.page.requirements}
                                            <span className="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 rounded-full"></span>
                                        </span>
                                    </h2>
                                    <ul className="space-y-3 text-gray-700">
                                        {requirements.map((req, index) => (
                                            <SafeListItem key={index} content={req as string} />
                                        ))}
                                    </ul>
                                </div>
                            )}
                            {benefits.length > 0 && (
                                <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                                    <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
                                        <span className="relative">
                                            {TEXTS.page.benefits}
                                            <span className="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 rounded-full"></span>
                                        </span>
                                    </h2>
                                    <ul className="space-y-3 text-gray-700">
                                        {benefits.map((benefit, index) => (
                                            <SafeListItem key={index} content={benefit as string} />
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    )}

                    <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 mb-16">
                        <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
                            <span className="relative">
                                {TEXTS.page.description}
                                <span className="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 rounded-full"></span>
                            </span>
                        </h2>
                        <SafeHTML
                            content={scholarship.description}
                            className="text-gray-700 leading-relaxed mb-8"
                            as="p"
                        />
                        <div className="flex justify-center mt-8 gap-4">
                            <Link href="/#scholarship-groups">
                                <Button
                                    variant="default"
                                    className="bg-primary text-white px-10 py-4 rounded-lg text-lg hover:bg-primary/90 transition-all shadow-md hover:shadow-lg"
                                >
                                    {TEXTS.page.applyButton}
                                </Button>
                            </Link>
                        </div>
                    </div>

                    <div className="mb-16">
                        <div className="text-center mb-10">
                            <h2 className="text-3xl font-bold mb-4 text-gray-800">
                                <span className="relative inline-block">
                                    {TEXTS.list.otherTypes}
                                    <span className="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 rounded-full"></span>
                                </span>
                            </h2>
                            <p className="text-gray-600 max-w-xl mx-auto">{TEXTS.list.otherTypesDescription}</p>
                        </div>
                        <ScholarshipGroups />
                    </div>

                    <div className="mb-16">
                        <Testimonials ids={testimonialIds} />
                    </div>

                    <FinalCTA scholarshipType={scholarship.scholarship_type} />
                </div>
            </div>
        </div>
    );
}
