"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { motion } from "framer-motion";
import { CalendarIcon, Clock, Coins } from "lucide-react";
import Link from "next/link";

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Database } from "@/types/database.types";

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"];

export const TEXTS = {
    shortDescription: "תיאור קצר",
    volunteerHours: "שעות התנדבות",
    availableScholarships: "המלגות הזמינות",
    chooseScholarship: "בחרו את המלגה המתאימה לכם והגישו מועמדות",
    otherTypes: "סוגי מלגות נוספות",
    viewDetails: "לפרטים נוספים",
    testimonials: {
        title: "מה אומרים עלינו",
        subtitle: "הצטרפו לאלפי הסטודנטים שכבר מצאו את המלגה המתאימה להם"
    },
    loadMore: "טען עוד"
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "MMMM yyyy", { locale: he });
};

interface ScholarshipListProps {
    scholarships: Scholarship[];
    hasMore?: boolean;
    onLoadMore?: () => void;
}

const containerVariants = {
    hidden: {},
    visible: {
        transition: {
            staggerChildren: 0.1
        }
    }
};

const itemVariants = {
    hidden: {
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 200,
            damping: 20
        }
    }
};

export default function ScholarshipList({ scholarships, hasMore, onLoadMore }: ScholarshipListProps) {
    return (
        <div className="space-y-8">
            <motion.div variants={containerVariants} initial="hidden" animate="visible" className="w-full">
                <Accordion
                    type="single"
                    collapsible
                    className="w-full space-y-6"
                    defaultValue={scholarships.length > 0 ? scholarships[0].id : undefined}
                >
                    {scholarships.map((scholarship, index) => (
                        <motion.div
                            key={scholarship.id}
                            variants={itemVariants}
                            initial="hidden"
                            animate="visible"
                            transition={{
                                delay: index * 0.05
                            }}
                            layout
                            className="mb-6"
                        >
                            <AccordionItem
                                value={scholarship.id}
                                className="bg-white border border-gray-200 hover:border-primary/30 rounded-lg px-6 py-4 transition-all hover:shadow-lg overflow-hidden relative group"
                            >
                                <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary transform scale-y-0 group-hover:scale-y-100 transition-transform origin-top" />

                                <AccordionTrigger className="hover:no-underline">
                                    <div className="flex flex-col w-full gap-6">
                                        <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 md:gap-8">
                                            <div className="text-right">
                                                <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors">
                                                    {scholarship.title}
                                                </h3>
                                            </div>
                                            <div className="flex flex-wrap gap-6 text-sm text-gray-600">
                                                <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                                                    <Clock className="h-4 w-4 text-primary" />
                                                    <span>
                                                        {scholarship.volunteer_hours} {TEXTS.volunteerHours}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                                                    <Coins className="h-4 w-4 text-primary" />
                                                    <span>
                                                        {scholarship.min_amount === scholarship.max_amount
                                                            ? `${scholarship.min_amount.toLocaleString()} ₪`
                                                            : `${scholarship.max_amount.toLocaleString()} - ${scholarship.min_amount.toLocaleString()} ₪`}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                                                    <CalendarIcon className="h-4 w-4 text-primary" />
                                                    <span>
                                                        {formatDate(scholarship.start_date)} -{" "}
                                                        {formatDate(scholarship.end_date)}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-gray-600 leading-relaxed">
                                                {scholarship.short_description}
                                            </p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="text-gray-600 pt-6 pb-2 border-t border-gray-100 mt-4">
                                    <div className="space-y-6">
                                        <p className="text-gray-600 leading-relaxed">{scholarship.description}</p>
                                        <div className="flex justify-end">
                                            <Link
                                                href={`/scholarships/${scholarship.slug}`}
                                                className="bg-primary text-white px-6 py-2.5 rounded-lg text-sm font-semibold hover:bg-primary/90 transition-colors hover:shadow-md"
                                            >
                                                {TEXTS.viewDetails}
                                            </Link>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </AccordionItem>
                        </motion.div>
                    ))}
                </Accordion>
            </motion.div>

            {hasMore && onLoadMore && (
                <motion.div
                    className="flex justify-center mt-8"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                >
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-white border-primary border text-primary px-8 py-3 rounded-lg text-base font-semibold hover:bg-primary/5 transition-all shadow-sm hover:shadow-md"
                        onClick={onLoadMore}
                    >
                        {TEXTS.loadMore}
                    </motion.button>
                </motion.div>
            )}
        </div>
    );
}
