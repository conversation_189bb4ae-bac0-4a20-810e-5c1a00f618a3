import { motion } from "framer-motion";
import {
    FacebookIcon,
    FacebookShareButton,
    TelegramIcon,
    TelegramShareButton,
    TwitterShareButton,
    WhatsappIcon,
    WhatsappShareButton,
    XIcon
} from "react-share";

const MotionSection = motion.create("section");
const MotionDiv = motion.create("div");

export const TEXTS = {
    eligibleCountPrefix: "נמצאת זכאי ל-",
    eligibleCountSuffix: "מלגות!",
    moneyRangeTitle: "סכום המלגות הפוטנציאלי שלך",
    facebookHashtag: "#מלגות",
    subtitle: "המערכת מצאה עבורך מלגות מתאימות. אל תפספס את ההזדמנות לקבל תמיכה כלכלית!",
    shareTitle: "שתף את ההצלחה שלך עם חברים",
    noScholarships: "לא נמצאו מלגות מתאימות כרגע. נסה לעדכן את הפרטים שלך או לבדוק שוב מאוחר יותר.",
    currency: "₪"
};

type Props = {
    eligibleCount: number;
    totalMinAmount: number;
    totalMaxAmount: number;
    shareUrl: string;
    shareTitle: string;
};

export function EligibleScholarshipsSummaryBanner({
    eligibleCount,
    totalMinAmount,
    totalMaxAmount,
    shareUrl,
    shareTitle
}: Props) {
    const eligibleText = `${TEXTS.eligibleCountPrefix}${eligibleCount} ${TEXTS.eligibleCountSuffix}`;

    const showAmount = eligibleCount > 0 && (totalMinAmount > 0 || totalMaxAmount > 0);
    const amountText =
        totalMinAmount === totalMaxAmount
            ? `${totalMinAmount.toLocaleString()} ${TEXTS.currency}`
            : `${totalMinAmount.toLocaleString()} - ${totalMaxAmount.toLocaleString()} ${TEXTS.currency}`;

    return (
        <MotionSection
            initial={{ opacity: 0, scale: 0.97 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mb-12 bg-card rounded-lg text-center border shadow-sm"
            aria-label="סיכום המלגות שלך"
        >
            <div className="p-8 space-y-6">
                <MotionDiv
                    className="flex flex-col items-center gap-3"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                >
                    <div className="w-8 h-0.5 bg-primary/40 rounded-full" />
                    <h1 className="text-2xl font-semibold text-foreground">{eligibleText}</h1>
                    {eligibleCount > 0 && <p className="text-muted-foreground max-w-xl">{TEXTS.subtitle}</p>}
                </MotionDiv>

                {showAmount && (
                    <MotionDiv
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                        className="bg-muted/20 rounded-md p-4 mx-auto max-w-sm"
                    >
                        <h2 className="text-xs font-medium mb-2 text-muted-foreground uppercase tracking-wide">
                            {TEXTS.moneyRangeTitle}
                        </h2>
                        <p className="text-3xl font-bold text-primary">{amountText}</p>
                    </MotionDiv>
                )}

                {eligibleCount > 0 && (
                    <MotionDiv
                        className="flex flex-col items-center gap-3 pt-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                    >
                        <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                            {TEXTS.shareTitle}
                        </span>
                        <div className="flex justify-center gap-3">
                            {[
                                {
                                    Component: WhatsappShareButton,
                                    Icon: WhatsappIcon,
                                    props: { url: shareUrl, title: shareTitle }
                                },
                                {
                                    Component: FacebookShareButton,
                                    Icon: FacebookIcon,
                                    props: { url: shareUrl, hashtag: TEXTS.facebookHashtag }
                                },
                                {
                                    Component: TwitterShareButton,
                                    Icon: XIcon,
                                    props: { url: shareUrl, title: shareTitle }
                                },
                                {
                                    Component: TelegramShareButton,
                                    Icon: TelegramIcon,
                                    props: { url: shareUrl, title: shareTitle }
                                }
                            ].map((ShareButton, index) => (
                                <MotionDiv
                                    key={index}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{
                                        delay: 0.5 + index * 0.1,
                                        duration: 0.3
                                    }}
                                    className="transition-transform duration-150 hover:scale-105"
                                >
                                    <ShareButton.Component {...ShareButton.props} aria-label={TEXTS.shareTitle}>
                                        <ShareButton.Icon size={36} round />
                                    </ShareButton.Component>
                                </MotionDiv>
                            ))}
                        </div>
                    </MotionDiv>
                )}

                {eligibleCount === 0 && (
                    <MotionDiv
                        className="mt-4"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6, duration: 0.5 }}
                    >
                        <p className="text-muted-foreground">{TEXTS.noScholarships}</p>
                    </MotionDiv>
                )}
            </div>
        </MotionSection>
    );
}
