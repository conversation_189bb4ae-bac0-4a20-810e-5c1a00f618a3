import { motion } from "framer-motion";
import { <PERSON>, ExternalLink, HandCoins, HeartHandshake, Mail, Phone, User } from "lucide-react";
import Link from "next/link";
import { useEffect, useMemo } from "react";
import { FormProvider, useForm } from "react-hook-form";

import type { ScholarshipEligibility } from "@/app/services/scholarship-eligibility";
import { CopyToClipboard } from "@/components/common/copy-to-clipboard";
import { EmptyState } from "@/components/common/empty-state";
import { DynamicQuestionsForm } from "@/components/forms/dynamic-questions-form/dynamic-questions-form";
import { Option } from "@/components/forms/fields/dropdown-base";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { useApplicationQuestions } from "@/hooks/use-application-questions";
import {
    DEFAULT_APPLICATION_OPTIONS,
    getApplyText,
    isPositiveAnswer,
    TEXTS,
    UserPlanVariant
} from "@/lib/eligible-scholarship-card-constants";
import type { Database } from "@/types/database.types";

const MotionCard = motion.create(Card);

const ERROR_TEXTS = {
    applicationQuestionsError: "שגיאה בטעינת שאלות הבקשה",
    applicationQuestionsErrorDescription: "לא ניתן לטעון את שאלות הבקשה כרגע. אנא נסה לרענן את הדף.",
    retryButton: "נסה שוב"
};

interface ApplicationFormData {
    shouldApply: Option | null;
}

function determineFormOptionFromShouldApply(
    shouldApply: boolean | undefined,
    applicationOptions: Option[]
): Option | null {
    if (shouldApply === undefined || shouldApply === null) {
        return null;
    }

    if (shouldApply === true) {
        return (
            applicationOptions.find((opt) => isPositiveAnswer(opt.id, applicationOptions)) ||
            applicationOptions[0] ||
            null
        );
    }

    if (shouldApply === false) {
        return (
            applicationOptions.find((opt) => !isPositiveAnswer(opt.id, applicationOptions)) ||
            applicationOptions[1] ||
            null
        );
    }

    return null;
}

export interface EligibleScholarshipCardProps {
    scholarship: ScholarshipEligibility;
    isEligible: boolean;
    index?: number;
    shouldApply: boolean | undefined;
    onShouldApplyChange: (checked: boolean) => void;
    scholarshipType: Database["public"]["Enums"]["scholarship_type"];
    userPlan?: UserPlanVariant;
}

export interface EligibleScholarshipCardWithSkeletonProps {
    scholarship?: ScholarshipEligibility;
    isEligible?: boolean;
    index?: number;
    shouldApply?: boolean | undefined;
    onShouldApplyChange?: (checked: boolean) => void;
    scholarshipType?: Database["public"]["Enums"]["scholarship_type"];
    userPlan?: UserPlanVariant;
}

function EligibleScholarshipCardSkeleton({ userPlan = "premium" }: { userPlan?: UserPlanVariant }) {
    return (
        <div className="flex flex-col w-full break-inside-avoid mb-6 bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            {/* Header */}
            <div className="flex flex-row items-center justify-between bg-white py-4 px-6 border-b border-gray-100">
                <div className="flex-1 text-right">
                    <Skeleton className="h-6 w-48 bg-gray-200" />
                </div>
                {userPlan === "premium" && <Skeleton className="h-6 w-16 rounded-full bg-gray-200" />}
            </div>

            {/* Content */}
            <div className="flex flex-col gap-4 flex-1 px-6 py-5">
                {/* Description */}
                <Skeleton className="h-4 w-full bg-gray-200" />
                <Skeleton className="h-4 w-3/4 bg-gray-200" />

                {/* Info sections */}
                <div className="flex flex-col gap-4 mt-2">
                    {/* Date range */}
                    <div className="flex flex-col items-start">
                        <Skeleton className="h-3 w-20 bg-gray-200 mb-1" />
                        <div className="flex flex-row items-center gap-2">
                            <Skeleton className="w-5 h-5 bg-gray-200" />
                            <Skeleton className="h-4 w-32 bg-gray-200" />
                        </div>
                    </div>

                    {/* Amount */}
                    <div className="flex flex-col items-start">
                        <Skeleton className="h-3 w-16 bg-gray-200 mb-1" />
                        <div className="flex flex-row items-center gap-2">
                            <Skeleton className="w-5 h-5 bg-gray-200" />
                            <Skeleton className="h-4 w-24 bg-gray-200" />
                        </div>
                    </div>

                    {/* Volunteer hours */}
                    <div className="flex flex-col items-start">
                        <Skeleton className="h-3 w-20 bg-gray-200 mb-1" />
                        <div className="flex flex-row items-center gap-2">
                            <Skeleton className="w-5 h-5 bg-gray-200" />
                            <Skeleton className="h-4 w-8 bg-gray-200" />
                        </div>
                    </div>

                    {/* Buttons */}
                    <div className="flex flex-row gap-2 mt-4">
                        <Skeleton className="h-10 flex-1 bg-gray-200" />
                        <Skeleton className="h-10 flex-1 bg-gray-200" />
                    </div>
                </div>
            </div>

            {/* Footer - only show for premium users */}
            {userPlan === "premium" && (
                <div className="border-0 bg-transparent flex flex-col p-6">
                    <div className="bg-gray-50 rounded-xl p-4 flex flex-col border border-gray-100 w-full">
                        <Skeleton className="h-4 w-40 bg-gray-200 mb-2" />
                        <Skeleton className="h-10 w-full bg-gray-200" />
                    </div>
                </div>
            )}
        </div>
    );
}

export function EligibleScholarshipCardWithSkeleton({
    scholarship,
    isEligible = true,
    index,
    shouldApply,
    onShouldApplyChange,
    scholarshipType = "submission",
    userPlan = "premium"
}: EligibleScholarshipCardWithSkeletonProps) {
    const isLoading = !scholarship || !onShouldApplyChange;

    if (isLoading) {
        return <EligibleScholarshipCardSkeleton userPlan={userPlan} />;
    }

    return (
        <EligibleScholarshipCard
            scholarship={scholarship}
            isEligible={isEligible}
            index={index}
            shouldApply={shouldApply}
            onShouldApplyChange={onShouldApplyChange}
            scholarshipType={scholarshipType}
            userPlan={userPlan}
        />
    );
}

export function EligibleScholarshipCard({
    scholarship,
    isEligible,
    shouldApply,
    onShouldApplyChange,
    scholarshipType,
    userPlan = "premium"
}: EligibleScholarshipCardProps) {
    const { applicationQuestion, loading: questionLoading, error, refetch } = useApplicationQuestions();

    const type: Database["public"]["Enums"]["scholarship_type"] =
        scholarship.scholarship_type === "guidance"
            ? "guidance"
            : scholarship.scholarship_type === "submission"
              ? "submission"
              : scholarshipType;

    const applyText = useMemo(() => {
        if (applicationQuestion?.label) {
            return applicationQuestion.label;
        }
        return getApplyText(type);
    }, [applicationQuestion, type]);

    const applicationOptions: Option[] = useMemo(() => {
        if (applicationQuestion?.options && applicationQuestion.options.length > 0) {
            return applicationQuestion.options;
        }
        return DEFAULT_APPLICATION_OPTIONS;
    }, [applicationQuestion]);

    const form = useForm<ApplicationFormData>({
        defaultValues: {
            shouldApply: null
        }
    });

    const selectedValue = form.watch("shouldApply");

    useEffect(() => {
        if (selectedValue) {
            const newShouldApply = isPositiveAnswer(selectedValue.id, applicationOptions);
            if (newShouldApply !== shouldApply) {
                onShouldApplyChange(newShouldApply);
            }
        }
    }, [selectedValue, shouldApply, onShouldApplyChange, applicationOptions]);

    // Synchronize form value with shouldApply state when options load or shouldApply changes
    useEffect(() => {
        if (applicationOptions.length === 0) return;

        const expectedFormValue = determineFormOptionFromShouldApply(shouldApply, applicationOptions);
        const currentFormValue = form.getValues("shouldApply");

        if (currentFormValue?.id !== expectedFormValue?.id) {
            form.setValue("shouldApply", expectedFormValue);
        }
    }, [shouldApply, form, applicationOptions]);

    if (error && !questionLoading) {
        return (
            <EmptyState message={ERROR_TEXTS.applicationQuestionsError} variant="error" className="shadow-lg">
                <p className="text-sm text-muted-foreground mb-4">{ERROR_TEXTS.applicationQuestionsErrorDescription}</p>
                <Button onClick={refetch} variant="outline" size="sm" className="w-fit">
                    {ERROR_TEXTS.retryButton}
                </Button>
            </EmptyState>
        );
    }

    const formatDate = (dateString: string | undefined) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        return date.toLocaleDateString("he-IL");
    };

    const formatAmount = () => {
        const minAmount = scholarship.min_amount || 0;
        const maxAmount = scholarship.max_amount || 0;
        if (minAmount === maxAmount) {
            return `${maxAmount.toLocaleString()} ₪`;
        }
        return `${maxAmount.toLocaleString()} - ${minAmount.toLocaleString()} ₪`;
    };

    const typeBadge = (
        <span
            className={`px-3 py-1 rounded-full text-xs font-semibold tracking-wide
                ${
                    type === "guidance"
                        ? "bg-blue-100 text-blue-700 border border-blue-200"
                        : "bg-green-100 text-green-700 border border-green-200"
                }
                shadow-sm mr-2`}
        >
            {type === "guidance" ? TEXTS.scholarshipTypeGuidance : TEXTS.scholarshipTypeSubmission}
        </span>
    );

    return (
        <MotionCard
            key={scholarship.scholarshipId}
            initial={false}
            animate={false}
            dir="rtl"
            className="flex flex-col w-full break-inside-avoid mb-6 bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transition-shadow hover:shadow-xl"
        >
            <CardHeader className="flex flex-row items-center justify-between bg-white py-4 px-6 border-b border-gray-100">
                <div className="flex-1 text-right">
                    <CardTitle className="text-right text-lg font-bold leading-tight text-gray-900">
                        {scholarship.title}
                    </CardTitle>
                </div>
                {userPlan === "premium" && <div>{typeBadge}</div>}
            </CardHeader>

            <CardContent className="flex flex-col gap-4 flex-1 px-6 py-5">
                {scholarship.description && (
                    <p className="text-sm text-muted-foreground text-right leading-relaxed">
                        {scholarship.description}
                    </p>
                )}

                <div className="flex flex-col gap-4 mt-2 flex-1">
                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-400 mb-1">{TEXTS.dateRangeLabel}</span>
                        <div className="flex flex-row items-center gap-2">
                            <Clock className="w-5 h-5 text-primary" />
                            <span className="text-base font-medium text-gray-700">
                                {scholarship.start_date || scholarship.end_date
                                    ? `${formatDate(scholarship.end_date)} - ${formatDate(scholarship.start_date)}`
                                    : ""}
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-400 mb-1">{TEXTS.amountLabel}</span>
                        <div className="flex flex-row items-center gap-2">
                            <HandCoins className="w-5 h-5 text-primary" />
                            <span className="text-base font-medium text-gray-700">{formatAmount()}</span>
                        </div>
                    </div>
                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-400 mb-1">{TEXTS.volunteerHoursLabel}</span>
                        <div className="flex flex-row items-center gap-2">
                            <HeartHandshake className="w-5 h-5 text-primary" />
                            <span className="text-base font-medium text-gray-700">
                                {typeof scholarship.volunteer_hours === "number" ? scholarship.volunteer_hours : 0}
                            </span>
                        </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-2 mt-4">
                        {isEligible && scholarship.url && (
                            <Button
                                asChild
                                variant="ghost"
                                className="w-full sm:flex-1 flex flex-row items-center justify-center gap-1 border border-gray-200"
                            >
                                <Link href={scholarship.url} target="_blank" rel="noopener noreferrer">
                                    {TEXTS.externalLinkLabel}
                                    <ExternalLink className="w-4 h-4" />
                                </Link>
                            </Button>
                        )}
                        {(scholarship.contact_person || scholarship.contact_email || scholarship.contact_phone) && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className="w-full sm:flex-1 flex flex-row items-center justify-center gap-1 border border-gray-200"
                                    >
                                        <User className="w-4 h-4" />
                                        <span>{TEXTS.contactInfoLabel}</span>
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-60 text-right p-4 rounded-xl shadow-lg bg-white">
                                    {scholarship.contact_person && (
                                        <div className="flex items-center gap-2 mb-3">
                                            <User className="w-5 h-5 text-primary" />
                                            <span className="text-sm">{scholarship.contact_person}</span>
                                        </div>
                                    )}
                                    {scholarship.contact_email && (
                                        <div className="flex items-center justify-between mb-3 border-t border-gray-100 pt-3">
                                            <div className="flex items-center gap-2">
                                                <Mail className="w-5 h-5 text-primary" />
                                                <a
                                                    href={`mailto:${scholarship.contact_email}`}
                                                    className="text-sm hover:underline"
                                                    dir="ltr"
                                                    style={{ direction: "ltr", unicodeBidi: "plaintext" }}
                                                >
                                                    {scholarship.contact_email}
                                                </a>
                                            </div>
                                            <CopyToClipboard text={scholarship.contact_email} />
                                        </div>
                                    )}
                                    {scholarship.contact_phone && (
                                        <div className="flex items-center justify-between border-t border-gray-100 pt-3">
                                            <div className="flex items-center gap-2">
                                                <Phone className="w-5 h-5 text-primary" />
                                                <a
                                                    href={`tel:${scholarship.contact_phone}`}
                                                    className="text-sm hover:underline"
                                                    dir="ltr"
                                                    style={{ direction: "ltr", unicodeBidi: "plaintext" }}
                                                >
                                                    {scholarship.contact_phone}
                                                </a>
                                            </div>
                                            <CopyToClipboard text={scholarship.contact_phone} />
                                        </div>
                                    )}
                                </PopoverContent>
                            </Popover>
                        )}
                    </div>
                </div>
            </CardContent>

            <CardFooter className="border-0 bg-transparent flex flex-col">
                {isEligible && userPlan === "premium" && !questionLoading && (
                    <span className="bg-gray-50 rounded-xl p-4 flex flex-col border border-gray-100 w-full">
                        <FormProvider {...form}>
                            <SingleSelect
                                name="shouldApply"
                                label={applyText}
                                placeholder={applicationQuestion?.placeholder || TEXTS.applicationStatusPlaceholder}
                                options={applicationOptions}
                                required={false}
                            />
                        </FormProvider>
                        <span className="text-xs text-gray-500 mt-4">
                            {shouldApply && (
                                <DynamicQuestionsForm
                                    sections={["specific_scholarship"]}
                                    scholarshipId={scholarship.scholarshipId}
                                    columns={1}
                                />
                            )}
                        </span>
                    </span>
                )}

                {!isEligible && (
                    <div className="bg-red-50 rounded-xl p-4 flex flex-col gap-2 border border-red-100 w-full">
                        <p className="text-sm text-red-700 font-medium">{TEXTS.missingDataLabel}</p>
                        {scholarship.missingAnswers && scholarship.missingAnswers.length > 0 && (
                            <p className="text-xs text-red-500">
                                {TEXTS.missingFieldsLabel.replace(
                                    "{count}",
                                    scholarship.missingAnswers.length.toString()
                                )}
                            </p>
                        )}
                    </div>
                )}
            </CardFooter>
        </MotionCard>
    );
}
