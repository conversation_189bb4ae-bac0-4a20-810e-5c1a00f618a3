"use client";

"use client";

import { Users } from "lucide-react";

import { useAdminDashboard } from "@/hooks/use-admin-dashboard";

import { StatCard } from "./stat-card";
import { SubscriptionsCard } from "./subscriptions-card";

const TEXTS = {
    dashboardTitle: "לוח בקרה",
    lastUpdated: "עודכן לאחרונה:",
    totalUsers: "סה״כ משתמשים"
};

export function AdminDashboard() {
    const { stats } = useAdminDashboard();

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">{TEXTS.dashboardTitle}</h1>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <StatCard
                    title={TEXTS.totalUsers}
                    value={stats.users.data?.total ?? 0}
                    loading={stats.users.loading}
                    error={stats.users.error}
                    icon={<Users className="h-4 w-4 text-muted-foreground" />}
                />
            </div>
            <div className="grid grid-cols-1 gap-6">
                <SubscriptionsCard
                    data={stats.subscriptions.data}
                    loading={stats.subscriptions.loading}
                    error={stats.subscriptions.error}
                />
            </div>
        </div>
    );
}
