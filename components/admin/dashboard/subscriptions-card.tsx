import { CreditCardIcon } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { getPlanBgColor, getPlanTextColor, PRICING_PLANS } from "@/config/subscriptions";
import { SubscriptionStat } from "@/lib/admin-dashboard-constants";
import { PlanType } from "@/lib/subscription-constants";

import { DashboardCard } from "./dashboard-card";

const TEXTS = {
    title: "מנויים לפי תכנית",
    description: "התפלגות ונתוני מנויים פעילים",
    loading: "טוען נתונים...",
    error: "שגיאה בטעינת נתונים",
    noData: "אין נתונים להצגה",
    plan: "תכנית",
    activeSubscriptions: "מנויים פעילים",
    percentage: "אחוז מהכלל",
    newLast24h: "חדשים (24 ש')",
    requiresBackendData: "(דורש מידע נוסף מהשרת)"
};

interface SubscriptionsCardProps {
    data: SubscriptionStat[];
    loading: boolean;
    error: Error | null;
    className?: string;
}

export function SubscriptionsCard({ data, loading, error, className }: SubscriptionsCardProps) {
    function getPlanTypeFromId(planId: string): PlanType | undefined {
        const plan = PRICING_PLANS.find((p) => p.id === planId);
        return plan?.planType;
    }

    function getTextColorString(planId: string): string {
        const planType = getPlanTypeFromId(planId);
        if (planType) {
            return getPlanTextColor(planType);
        }
        return "text-gray-400";
    }

    function getBgColorString(planId: string): string {
        const planType = getPlanTypeFromId(planId);
        if (planType) {
            return getPlanBgColor(planType);
        }
        return "bg-gray-100";
    }

    const tableData: SubscriptionStat[] = data;

    const renderSkeletons = () =>
        Array.from({ length: 4 }).map((_, index) => (
            <TableRow key={`skeleton-${index}`}>
                <TableCell>
                    <Skeleton className="h-5 w-24" />
                </TableCell>
                <TableCell>
                    <Skeleton className="h-5 w-16" />
                </TableCell>
                <TableCell>
                    <Skeleton className="h-5 w-16" />
                </TableCell>
                <TableCell>
                    <Skeleton className="h-5 w-20" />
                </TableCell>
            </TableRow>
        ));

    return (
        <DashboardCard title={TEXTS.title} description={TEXTS.description} className={className}>
            {error ? (
                <div className="text-red-500 py-4">{TEXTS.error}</div>
            ) : data.length === 0 && !loading ? (
                <div className="text-gray-500 py-4">{TEXTS.noData}</div>
            ) : (
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="text-right">{TEXTS.plan}</TableHead>
                            <TableHead className="text-right">{TEXTS.activeSubscriptions}</TableHead>
                            <TableHead className="text-right">{TEXTS.percentage}</TableHead>
                            <TableHead className="text-right">{TEXTS.newLast24h}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading
                            ? renderSkeletons()
                            : tableData.map((stat) => (
                                  <TableRow key={stat.planId}>
                                      <TableCell>
                                          <div className="flex items-center gap-2">
                                              <div className={`p-1.5 rounded-md ${getBgColorString(stat.planId)}`}>
                                                  <CreditCardIcon
                                                      className={`h-4 w-4 ${getTextColorString(stat.planId)}`}
                                                  />
                                              </div>
                                              <span className="font-medium">{stat.planName}</span>
                                          </div>
                                      </TableCell>
                                      <TableCell className="text-right">{stat.count}</TableCell>
                                      <TableCell className="text-right">{stat.percentage}%</TableCell>
                                      <TableCell className="text-right">{stat.newLast24h}</TableCell>
                                  </TableRow>
                              ))}
                    </TableBody>
                </Table>
            )}
        </DashboardCard>
    );
}
