"use client";

import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

import React from "react";
import { Document, Page, pdfjs } from "react-pdf";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
const TEXTS = {
    loadingDocuments: "טוען רשימת מסמכים...",
    cannotDisplayPreview: "לא ניתן להציג כרגע.",
    exampleDocumentLabel: "מסמך לדוגמא"
};

pdfjs.GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.min.mjs", import.meta.url).toString();

interface PdfExampleModalProps {
    open: boolean;
    url: string | null;
    onOpenChange: (open: boolean) => void;
}

export function PdfExampleModal({ open, url, onOpenChange }: PdfExampleModalProps) {
    const [numPages, setNumPages] = React.useState<number>(1);

    React.useEffect(() => {
        if (!open) setNumPages(1);
    }, [open]);

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-full w-[95vw] sm:max-w-2xl p-0 flex flex-col items-center">
                <DialogTitle className="w-full px-6 pt-6 pb-2 text-center">{TEXTS.exampleDocumentLabel}</DialogTitle>
                <div
                    className="w-full flex-1 overflow-auto flex justify-center items-center px-2 pb-6"
                    style={{ minHeight: 400, maxHeight: "80vh" }}
                >
                    {url && (
                        <Document
                            file={url}
                            onLoadSuccess={({ numPages }) => setNumPages(numPages)}
                            loading={
                                <div dir="rtl" className="text-center w-full py-8">
                                    {TEXTS.loadingDocuments}
                                </div>
                            }
                            error={
                                <div dir="rtl" className="text-center w-full py-8 text-red-500">
                                    {TEXTS.cannotDisplayPreview}
                                </div>
                            }
                        >
                            {Array.from(new Array(numPages), (el, index) => (
                                <Page
                                    key={`page_${index + 1}`}
                                    pageNumber={index + 1}
                                    width={
                                        typeof window !== "undefined" && window.innerWidth < 600
                                            ? window.innerWidth - 32
                                            : 500
                                    }
                                />
                            ))}
                        </Document>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
}
