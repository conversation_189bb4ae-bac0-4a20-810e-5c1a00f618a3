import { GraduationCap } from "lucide-react";

import { cn } from "@/lib/utils";

interface LoadingIconProps {
    className?: string;
    size?: number;
    text?: string;
}

export function LoadingIcon({ className, size = 48, text }: LoadingIconProps) {
    return (
        <div className="flex flex-col items-center justify-center">
            <div className={cn("text-primary/70", className)}>
                <GraduationCap size={size} />
            </div>
            {text && (
                <div dir="rtl" className="mt-2 text-sm text-gray-500">
                    {text}
                </div>
            )}
        </div>
    );
}
