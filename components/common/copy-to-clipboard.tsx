"use client";

import { Check, Copy } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";

const TEXTS = {
    copy: "העתק",
    copied: "הועתק!",
    copySuccess: "הטקסט הועתק בהצלחה",
    copyError: "שגיאה בהעתקת הטקסט, אנא נסה שוב"
};

interface CopyToClipboardProps {
    text: string;
    className?: string;
    size?: "sm" | "md" | "lg";
    onSuccessMessage?: string;
}

export function CopyToClipboard({ text, className = "", size = "sm", onSuccessMessage }: CopyToClipboardProps) {
    const [copied, setCopied] = useState(false);

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(text);
            toast.success(onSuccessMessage || TEXTS.copySuccess);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            toast.error(TEXTS.copyError);
            console.error("Failed to copy text: ", err);
        }
    };

    const iconSize = {
        sm: "w-4 h-4",
        md: "w-5 h-5",
        lg: "w-6 h-6"
    }[size];

    return (
        <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className={`p-1 h-auto hover:bg-gray-100 transition-colors ${className}`}
            title={copied ? TEXTS.copied : TEXTS.copy}
            aria-label={copied ? TEXTS.copied : TEXTS.copy}
        >
            {copied ? (
                <Check className={`${iconSize} text-green-600`} />
            ) : (
                <Copy className={`${iconSize} text-gray-500 hover:text-gray-700`} />
            )}
        </Button>
    );
}
