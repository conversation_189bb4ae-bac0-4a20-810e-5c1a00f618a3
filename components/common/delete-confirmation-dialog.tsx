"use client";

import * as React from "react";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

const DIALOG_TEXTS = {
    title: "אישור פעולה",
    description: "האם אתה בטוח שברצונך לבצע פעולה זו? לא ניתן לשחזר פעולה זו.",
    cancel: "ביטול",
    confirm: "אישור"
};

interface DeleteConfirmationDialogProps {
    children: React.ReactNode;
    onConfirm: () => void | Promise<void>;
    title?: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
    confirmVariant?: "default" | "destructive";
    confirmClassName?: string;
}

export function DeleteConfirmationDialog({
    children,
    onConfirm,
    title = DIALOG_TEXTS.title,
    description = DIALOG_TEXTS.description,
    confirmText = DIALOG_TEXTS.confirm,
    cancelText = DIALOG_TEXTS.cancel,
    confirmVariant = "destructive",
    confirmClassName
}: DeleteConfirmationDialogProps) {
    const handleTriggerClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    const handleContentClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    const finalConfirmClassName =
        confirmClassName ?? (confirmVariant === "destructive" ? "bg-red-600 hover:bg-red-700" : "");

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild onClick={handleTriggerClick}>
                {children}
            </AlertDialogTrigger>
            <AlertDialogContent dir="rtl" onClick={handleContentClick}>
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-right">{title}</AlertDialogTitle>
                    <AlertDialogDescription className="text-right">{description}</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="gap-2">
                    <AlertDialogCancel asChild>
                        <Button variant="outline">{cancelText}</Button>
                    </AlertDialogCancel>
                    <AlertDialogAction onClick={onConfirm} className={finalConfirmClassName}>
                        {confirmText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
