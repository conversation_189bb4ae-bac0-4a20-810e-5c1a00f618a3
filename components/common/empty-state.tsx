import { AlertCircle, CheckCircle2, XCircle } from "lucide-react";

import { Card, CardContent, CardTitle } from "@/components/ui/card";

interface EmptyStateProps {
    message: string;
    children?: React.ReactNode;
    className?: string;
    variant?: "success" | "warning" | "error";
}

const ICONS = {
    success: {
        icon: CheckCircle2,
        bg: "bg-green-100 dark:bg-green-900",
        color: "text-green-600 dark:text-green-400"
    },
    warning: {
        icon: AlertCircle,
        bg: "bg-yellow-100 dark:bg-yellow-900",
        color: "text-yellow-600 dark:text-yellow-400"
    },
    error: {
        icon: XCircle,
        bg: "bg-red-100 dark:bg-red-900",
        color: "text-red-600 dark:text-red-400"
    }
};

export function EmptyState({ message, children, className = "", variant = "success" }: EmptyStateProps) {
    const { icon: Icon, bg, color } = ICONS[variant];
    return (
        <Card className={`w-full ${className}`} dir="rtl">
            <CardContent className="p-6">
                <div className="flex items-center gap-4 w-full">
                    <div className={`${bg} rounded-full p-3 shrink-0`}>
                        <Icon className={`h-10 w-10 ${color}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg">{message}</CardTitle>
                        {children && <div className="mt-4">{children}</div>}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
