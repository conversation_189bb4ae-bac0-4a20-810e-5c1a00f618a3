"use client";

import posthog from "posthog-js";
import { Post<PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> } from "posthog-js/react";
import { useEffect } from "react";

function isLocalhost() {
    if (typeof window === "undefined") return false;
    return (
        window.location.hostname === "localhost" ||
        window.location.hostname === "127.0.0.1" ||
        window.location.hostname === "[::1]"
    );
}

export function PostHogProvider({ children }: { children: React.ReactNode }) {
    useEffect(() => {
        if (isLocalhost()) {
            return;
        }

        try {
            if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) {
                console.warn("PostHog: API key not found in environment variables");
                return;
            }

            posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
                api_host: "/ingest",
                ui_host: "https://us.posthog.com",
                capture_exceptions: true,
                debug: process.env.NODE_ENV === "development"
            });

            console.log("PostHog initialized successfully");
        } catch (error) {
            console.error("Failed to initialize PostHog:", error);

            if (process.env.NODE_ENV === "development") {
                console.error("PostHog initialization error details:", {
                    error: error instanceof Error ? error.message : String(error),
                    apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY ? "present" : "missing",
                    timestamp: new Date().toISOString()
                });
            }
        }
    }, []);

    return <PHProvider client={posthog}>{children}</PHProvider>;
}
