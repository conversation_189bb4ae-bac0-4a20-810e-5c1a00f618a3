import { ArrowLeft, Tag } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { TEXTS } from "@/lib/subscription-constants";

interface CouponSectionProps {
    onCouponApplied: (couponInfo: {
        couponCode: string;
        couponType: "fixed_amount" | "percentage";
        discountValue: number;
        discountApplied: number;
        finalAmount: number;
    }) => void;
    onCouponInvalid: (errorMessage: string) => void;
    currentTotalAmount?: number;
    hideTitle?: boolean;
}

export function CouponSection({
    onCouponApplied,
    onCouponInvalid,
    currentTotalAmount = 0,
    hideTitle = false
}: CouponSectionProps) {
    const [couponCode, setCouponCode] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handleApplyCoupon = async () => {
        if (!couponCode) {
            toast.error(TEXTS.ENTER_COUPON_CODE);
            return;
        }
        setIsLoading(true);

        try {
            const response = await fetch("/api/validate-coupon", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    coupon_code: couponCode,
                    total_amount: currentTotalAmount
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                onCouponApplied(result);
                toast.success(TEXTS.COUPON_APPLIED_SUCCESS(result.couponCode));
            } else {
                const errorMessage = result.error || "Failed to validate coupon.";
                onCouponInvalid(errorMessage);
                toast.error(errorMessage);
            }
        } catch (error) {
            console.error("Error validating coupon:", error);
            const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
            onCouponInvalid(errorMessage);
            toast.error("An error occurred while validating the coupon.");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="mb-10">
            <Card className="w-full bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50 shadow-sm">
                <CardContent className="p-6">
                    <div className="text-center space-y-4 max-w-md mx-auto">
                        {!hideTitle && (
                            <div className="flex items-center justify-center gap-2">
                                <Tag className="w-5 h-5 text-blue-600" />
                                <h3 className="text-lg font-semibold text-gray-800">{TEXTS.COUPON_TITLE}</h3>
                            </div>
                        )}
                        <div className="flex items-center gap-2">
                            <Input
                                dir="rtl"
                                className="placeholder:text-right bg-white/80 border-blue-200 focus:border-blue-400 focus:ring-blue-400/20"
                                placeholder={TEXTS.COUPON_PLACEHOLDER}
                                value={couponCode}
                                onChange={(e) => setCouponCode(e.target.value)}
                                disabled={isLoading}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                        e.preventDefault();
                                        handleApplyCoupon();
                                    }
                                }}
                            />
                            <Button
                                variant="outline"
                                onClick={handleApplyCoupon}
                                disabled={isLoading}
                                className="bg-blue-600 hover:bg-blue-700 text-white hover:text-white border-blue-600 hover:border-blue-700 transition-colors"
                            >
                                <ArrowLeft className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
