import { Check, X } from "lucide-react";

import { PRICING_PLANS } from "@/config/subscriptions";
import { FeatureComparison } from "@/lib/subscription-constants";
import { cn } from "@/lib/utils";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";

interface FeatureComparisonTableProps {
    comparisonData: FeatureComparison[];
    planNames: string[];
}

export function FeatureComparisonTable({ comparisonData, planNames }: FeatureComparisonTableProps) {
    const isFeaturedPlan = (planName: string) => {
        const plan = PRICING_PLANS.find((p) => p.title === planName);
        return plan?.isFeatured || false;
    };

    const getPlanBgClass = (planName: string): string => {
        const plan = PRICING_PLANS.find((p) => p.title === planName);
        return plan?.colors.bg10 || "";
    };

    return (
        <div className="overflow-hidden mt-8">
            <Table>
                <TableHeader>
                    <TableRow className="border-b">
                        <TableHead className="w-1/3 text-right font-bold py-6 px-6">תכונה</TableHead>
                        {planNames.map((planName) => (
                            <TableHead
                                key={planName}
                                className={cn(
                                    "text-center font-bold py-5 px-4",
                                    isFeaturedPlan(planName) && getPlanBgClass(planName)
                                )}
                            >
                                {planName}
                            </TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {comparisonData.map((feature: FeatureComparison, index: number) => (
                        <TableRow
                            key={index}
                            className={cn("border-b last:border-b-0 transition-colors", "hover:bg-gray-50/50")}
                        >
                            <TableCell className="text-right py-5 px-6 align-middle">
                                <span>{feature.name}</span>
                            </TableCell>
                            {planNames.map((planName) => (
                                <TableCell
                                    key={planName}
                                    className={cn(
                                        "text-center py-5 px-4 align-middle",
                                        isFeaturedPlan(planName) && getPlanBgClass(planName)
                                    )}
                                >
                                    {typeof feature.plans[planName] === "string" ? (
                                        feature.plans[planName]
                                    ) : feature.plans[planName] === true ? (
                                        <Check className="h-5 w-5 text-green-500 mx-auto transition-transform hover:scale-110" />
                                    ) : (
                                        <X className="h-5 w-5 text-red-300 mx-auto" />
                                    )}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
}
