import React from "react";

import type { Feature, SubscriptionsItem } from "@/lib/subscription-constants";

interface OnboardingSubscriptionCardProps {
    item: SubscriptionsItem;
    onSelect: (id: string) => void;
    getFeatureByKey: (featureId: string) => Feature;
    selected?: boolean;
    appliedDiscount?: number;
}

export function OnboardingSubscriptionCard({
    item,
    onSelect,
    getFeatureByKey,
    selected = false,
    appliedDiscount = 0
}: OnboardingSubscriptionCardProps) {
    const hasDiscount = appliedDiscount > 0 && item.originalPrice && item.price < item.originalPrice;

    return (
        <div
            className={`relative flex flex-col items-center justify-between border rounded-xl bg-white/90 p-6 shadow-sm transition-transform duration-200 hover:scale-105 hover:shadow-lg cursor-pointer select-none ${item.isFeatured ? "border-primary border-2" : "border-gray-200"} ${selected ? "ring-2 ring-primary border-primary" : ""}`}
            onClick={() => onSelect(item.id)}
            onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    onSelect(item.id);
                }
            }}
            aria-pressed={selected}
            tabIndex={0}
            role="button"
        >
            {selected && (
                <div className="absolute top-2 left-2 bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center shadow">
                    <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
                        <path
                            d="M4 8.5l3 3 5-5"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </svg>
                </div>
            )}
            {item.isFeatured && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full shadow">
                    מומלץ
                </div>
            )}
            <div className="w-full flex flex-col items-center gap-1 mb-2">
                <div className="text-lg font-extrabold text-primary mb-1">{item.title}</div>
                <div className="text-3xl font-extrabold text-gray-900">₪{item.price}</div>
                {hasDiscount && item.originalPrice && (
                    <div className="text-sm text-gray-500 line-through">₪{item.originalPrice}</div>
                )}
                <div className="text-xs text-gray-500 mb-4 text-center min-h-[32px]">{item.description}</div>
            </div>
            <ul className="text-sm text-gray-700 space-y-1 mb-6 w-full max-h-[230px] overflow-y-auto pr-1">
                {item.features.map((feature) => (
                    <li key={feature.featureId} className="flex items-center gap-2">
                        {feature.isApplicable ? (
                            <span className="text-green-500 text-lg">✔</span>
                        ) : (
                            <span className="text-gray-300 text-lg">✖</span>
                        )}
                        <span className={feature.isApplicable ? "" : "text-gray-400 line-through"}>
                            {getFeatureByKey(feature.featureId)?.name}
                        </span>
                    </li>
                ))}
            </ul>
        </div>
    );
}
