import { Check, X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Feature, PlanFeature, SubscriptionsItem, TEXTS, UserSubscriptionWithPlan } from "@/lib/subscription-constants";
import { cn } from "@/lib/utils";

interface AdminTexts {
    changePlan: string;
    currentPlan: string;
    changingPlan: string;
}

interface SubscriptionsCardProps {
    item: SubscriptionsItem;
    handleCtaClick: (itemId: string) => void;
    getFeatureByKey: (featureId: string) => Feature;
    appliedDiscount?: number;
    isLoading?: boolean;
    isAuthenticated?: boolean;
    isCurrentPlan?: boolean;
    currentSubscription?: UserSubscriptionWithPlan | null;
    adminMode?: boolean;
    adminTexts?: AdminTexts;
}

export function SubscriptionsCard({
    item,
    handleCtaClick,
    getFeatureByKey,
    appliedDiscount = 0,
    isLoading = false,
    isAuthenticated = false,
    isCurrentPlan = false,
    currentSubscription = null,
    adminMode = false,
    adminTexts
}: SubscriptionsCardProps) {
    const originalPrice = item.price;
    const discountedPrice = Math.max(0, originalPrice - appliedDiscount);
    const hasDiscount = appliedDiscount > 0 && discountedPrice < originalPrice;

    const formattedDiscountedPrice = discountedPrice.toFixed(2);
    const displayDiscountedPrice = formattedDiscountedPrice.endsWith(".00")
        ? formattedDiscountedPrice.slice(0, -3)
        : formattedDiscountedPrice;

    return (
        <Card
            key={item.id}
            className={cn(
                "flex flex-col rounded-2xl border border-gray-100/80",
                item.colors.cardGradient,
                item.isFeatured && "relative"
            )}
        >
            {item.isFeatured && (
                <Badge className="absolute -top-3 right-6 bg-primary text-white px-4 py-1 rounded-full font-normal">
                    {TEXTS.MOST_POPULAR}
                </Badge>
            )}

            <CardHeader className="text-right pb-2 pt-6">
                <CardTitle className="text-2xl mb-2 flex items-center justify-center gap-2">{item.title}</CardTitle>
                <CardDescription className="text-gray-600">{item.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow grid gap-4 text-center px-6">
                <div>
                    <div className="text-4xl font-bold mb-1 flex items-center justify-center tracking-tight">
                        <span className="font-mono">
                            {adminMode ? "0" : originalPrice > 0 ? displayDiscountedPrice : "0"}
                        </span>
                        <span className="text-2xl mr-0.5">₪</span>
                    </div>
                    {adminMode && originalPrice > 0 && (
                        <div className="text-sm text-gray-500 mb-1">
                            מחיר רגיל: <span className="font-mono">{originalPrice}</span>₪
                        </div>
                    )}
                    {!adminMode && hasDiscount && (
                        <div className="text-sm text-gray-500 line-through mb-1">
                            <span className="font-mono">{originalPrice}</span>
                            <span className="mr-0.5">₪</span>
                        </div>
                    )}
                    {item.duration_days && (
                        <div className="text-sm text-gray-500">
                            {TEXTS.ONE_TIME_PAYMENT} {item.duration_days} {TEXTS.DAYS}
                        </div>
                    )}
                </div>
                <ul className="space-y-2.5">
                    {item.features.map((planFeature: PlanFeature, index: number) => {
                        const feature = getFeatureByKey(planFeature.featureId);
                        if (!planFeature.isApplicable) {
                            return (
                                <li key={index} className="flex items-center justify-start text-gray-400">
                                    <X className="ml-2 h-4 w-4 shrink-0 text-red-300" />
                                    <span className="line-through">{feature.name}</span>
                                </li>
                            );
                        }
                        return (
                            <li key={index} className="flex items-center justify-start">
                                <Check className="ml-2 h-4 w-4 shrink-0 text-green-400" />
                                <span className="text-gray-700">
                                    {feature.name}
                                    {planFeature.value && <span className="text-gray-500"> ({planFeature.value})</span>}
                                </span>
                            </li>
                        );
                    })}
                </ul>
            </CardContent>
            <CardFooter className="px-6 pb-6">
                <Button
                    className={cn(
                        "w-full h-11 text-base font-normal",
                        item.isFeatured
                            ? "bg-primary hover:bg-primary"
                            : "bg-blue-100 hover:bg-blue-300 text-gray-600 border-0"
                    )}
                    variant={item.isFeatured ? "default" : "outline"}
                    onClick={() => handleCtaClick(item.id)}
                    disabled={isLoading || (adminMode ? false : isCurrentPlan)}
                >
                    {adminMode
                        ? isLoading
                            ? adminTexts?.changingPlan || "משנה..."
                            : isCurrentPlan
                              ? adminTexts?.currentPlan || "תוכנית נוכחית"
                              : adminTexts?.changePlan || "שנה תוכנית"
                        : isCurrentPlan
                          ? item.planType === currentSubscription?.planType
                              ? TEXTS.ACTIVE_PLAN
                              : TEXTS.INCLUDED_IN_CURRENT_PLAN
                          : isAuthenticated
                            ? TEXTS.PAYMENT
                            : TEXTS.REGISTER}
                </Button>
            </CardFooter>
        </Card>
    );
}
