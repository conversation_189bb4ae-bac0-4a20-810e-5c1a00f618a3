import { FeatureComparisonTableSkeleton } from "@/components/subscriptions/skeleton/feature-comparison-table-skeleton";
import { SubscriptionsCardSkeleton } from "@/components/subscriptions/skeleton/subscriptions-card-skeleton";

import { CouponSectionSkeleton } from "./coupon-section-skeleton";

interface SubscriptionsSkeletonProps {
    cardCount?: number;
    hideComparisonTable?: boolean;
}

export function SubscriptionsSkeleton({ cardCount = 4, hideComparisonTable = false }: SubscriptionsSkeletonProps) {
    return (
        <div dir="rtl" className="w-full space-y-12">
            <CouponSectionSkeleton />
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: cardCount }).map((_, index) => (
                    <SubscriptionsCardSkeleton key={index} />
                ))}
            </div>
            {!hideComparisonTable && <FeatureComparisonTableSkeleton columnCount={cardCount + 1} />}
        </div>
    );
}
