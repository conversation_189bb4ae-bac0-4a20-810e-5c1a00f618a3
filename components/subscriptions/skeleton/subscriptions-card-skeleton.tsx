import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ead<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function SubscriptionsCardSkeleton() {
    return (
        <Card className="flex flex-col">
            <CardHeader>
                <Skeleton className="h-7 w-32 mb-2" />
                <Skeleton className="h-5 w-full" />
            </CardHeader>
            <CardContent className="flex-grow grid gap-4">
                <div className="mb-4">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-4 w-40 mt-2" />
                </div>
                <ul className="space-y-2">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <li key={index} className="flex items-center">
                            <Skeleton className="h-4 w-4 ml-2" />
                            <Skeleton className="h-4 w-full max-w-[180px]" />
                        </li>
                    ))}
                </ul>
            </CardContent>
            <CardFooter>
                <Skeleton className="h-10 w-full" />
            </CardFooter>
        </Card>
    );
}
