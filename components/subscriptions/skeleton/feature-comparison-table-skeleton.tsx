import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface FeatureComparisonTableSkeletonProps {
    columnCount?: number;
    rowCount?: number;
}

export function FeatureComparisonTableSkeleton({ columnCount = 4, rowCount = 6 }: FeatureComparisonTableSkeletonProps) {
    return (
        <div className="mt-16">
            <div className="text-center mb-6">
                <Skeleton className="h-8 w-64 mx-auto" />
            </div>
            <div className="rounded-lg border overflow-hidden overflow-x-auto" dir="rtl">
                <Table dir="rtl">
                    <TableHeader>
                        <TableRow className="text-right">
                            <TableHead className="w-[300px] text-right">
                                <Skeleton className="h-6 w-24" />
                            </TableHead>
                            {Array.from({ length: columnCount - 1 }).map((_, index) => (
                                <TableHead key={index} className="text-right">
                                    <Skeleton className="h-6 w-20" />
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {Array.from({ length: rowCount }).map((_, rowIndex) => (
                            <TableRow key={rowIndex} className="text-right">
                                <TableCell className="font-medium text-right">
                                    <div className="flex items-center">
                                        <Skeleton className="h-4 w-4 mr-1" />
                                        <Skeleton className="h-5 w-40 ms-2" />
                                    </div>
                                </TableCell>
                                {Array.from({ length: columnCount - 1 }).map((_, colIndex) => (
                                    <TableCell key={colIndex} className="text-right">
                                        <Skeleton className="h-5 w-5 mr-auto" />
                                    </TableCell>
                                ))}
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
