"use client";

import { useUser } from "@clerk/nextjs";
import * as React from "react";
import { toast } from "sonner";

import { updateUserPlanByAdmin } from "@/app/actions/user-actions";
import { SubscriptionStatus } from "@/components/account/subscription-status";
import { LoadingIcon } from "@/components/common/loading-icon";
import { SubscriptionsCard } from "@/components/subscriptions/subscriptions-card";
import { FEATURES, PRICING_PLANS } from "@/config/subscriptions";
import { Feature, UserSubscriptionWithPlan } from "@/lib/subscription-constants";

const ADMIN_TEXTS = {
    changingPlan: "משנה תוכנית...",
    planChangedSuccess: (planName: string) => `התוכנית שונתה בהצלחה ל-${planName}`,
    planChangeError: "שגיאה בשינוי תוכנית",
    changePlan: "שנה תוכנית",
    adminNote: "מצב אדמין - כל התוכניות ללא עלות",
    currentPlan: "תוכנית נוכחית"
};

interface AdminSubscriptionsTableProps {
    targetUserId: string;
    currentSubscription?: UserSubscriptionWithPlan | null;
}

export function AdminSubscriptionsTable({ targetUserId, currentSubscription = null }: AdminSubscriptionsTableProps) {
    const { user } = useUser();
    const [isChangingPlan, setIsChangingPlan] = React.useState(false);
    const [loadingItemId, setLoadingItemId] = React.useState<string | null>(null);

    const handlePlanChange = async (planId: string) => {
        if (!user?.id) {
            toast.error("Admin user not authenticated");
            return;
        }

        const selectedPlan = PRICING_PLANS.find((plan) => plan.id === planId);
        if (!selectedPlan) {
            toast.error("Plan not found");
            return;
        }

        setLoadingItemId(planId);
        setIsChangingPlan(true);

        try {
            const result = await updateUserPlanByAdmin(user.id, targetUserId, planId);

            if (!result.success) {
                toast.error(result.error || ADMIN_TEXTS.planChangeError);
                return;
            }

            toast.success(ADMIN_TEXTS.planChangedSuccess(selectedPlan.title));

            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } catch (error) {
            console.error("Error changing user plan:", error);
            toast.error(ADMIN_TEXTS.planChangeError);
        } finally {
            setIsChangingPlan(false);
            setLoadingItemId(null);
        }
    };

    const getFeatureById = (featureId: string): Feature => {
        return (
            FEATURES[featureId] || {
                id: featureId,
                name: featureId
            }
        );
    };

    if (!user) {
        return <LoadingIcon text="Loading admin..." />;
    }

    return (
        <div dir="rtl" className="w-full space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 font-medium text-center">{ADMIN_TEXTS.adminNote}</p>
            </div>

            <SubscriptionStatus currentSubscription={currentSubscription} />

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                {PRICING_PLANS.map((item) => {
                    const isCurrentPlan = currentSubscription?.planType === item.planType;

                    const adminItem = {
                        ...item,
                        price: 0,
                        originalPrice: item.price
                    };

                    return (
                        <SubscriptionsCard
                            key={item.id}
                            item={adminItem}
                            handleCtaClick={handlePlanChange}
                            getFeatureByKey={getFeatureById}
                            appliedDiscount={0}
                            isLoading={isChangingPlan && loadingItemId === item.id}
                            isAuthenticated={true}
                            isCurrentPlan={isCurrentPlan}
                            currentSubscription={currentSubscription}
                            adminMode={true}
                            adminTexts={{
                                changePlan: ADMIN_TEXTS.changePlan,
                                currentPlan: ADMIN_TEXTS.currentPlan,
                                changingPlan: ADMIN_TEXTS.changingPlan
                            }}
                        />
                    );
                })}
            </div>
        </div>
    );
}
