"use client";

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";

import { applyFreePlan } from "@/app/actions/subscriptions-actions";
import { SubscriptionStatus } from "@/components/account/subscription-status";
import { CouponSection } from "@/components/subscriptions/coupon-section";
import { FeatureComparisonTable } from "@/components/subscriptions/feature-comparison-table";
import { SubscriptionsSkeleton } from "@/components/subscriptions/skeleton/subscriptions-skeleton";
import { SubscriptionsCard } from "@/components/subscriptions/subscriptions-card";
import { FEATURES, PLAN_HIERARCHY, PRICING_PLANS } from "@/config/subscriptions";
import {
    CouponAppliedInfo,
    CouponType,
    Feature,
    FeatureComparison,
    PlanFeature,
    TEXTS,
    UserSubscriptionWithPlan
} from "@/lib/subscription-constants";

interface SubscriptionsTableProps {
    showFeaturesTable?: boolean;
    currentSubscription?: UserSubscriptionWithPlan | null;
}

interface ValidatedCoupon {
    code: string;
    couponType: CouponType;
    discountValue: number;
}

export function SubscriptionsTable({ showFeaturesTable = false, currentSubscription = null }: SubscriptionsTableProps) {
    const router = useRouter();
    const { user, isLoaded: isUserLoaded } = useUser();
    const isAuthenticated = !!user && isUserLoaded;
    const [validatedCoupon, setValidatedCoupon] = React.useState<ValidatedCoupon | null>(null);
    const [isCheckoutLoading, setIsCheckoutLoading] = React.useState(false);
    const [loadingItemId, setLoadingItemId] = React.useState<string | null>(null);

    const handleCouponApplied = (couponInfo: CouponAppliedInfo) => {
        const couponData: ValidatedCoupon = {
            code: couponInfo.couponCode,
            couponType: couponInfo.couponType,
            discountValue: couponInfo.discountValue
        };
        setValidatedCoupon(couponData);
    };

    const handleCouponInvalid = () => {
        setValidatedCoupon(null);
    };

    const handleCheckout = async (itemId: string) => {
        const selectedItem = PRICING_PLANS.find((item) => item.id === itemId);
        if (!selectedItem) return;

        setLoadingItemId(itemId);
        setIsCheckoutLoading(true);

        if (!isAuthenticated || !user) {
            router.push("/login");
            setIsCheckoutLoading(false);
            setLoadingItemId(null);
            return;
        }
        const userId = user.id;
        let discountApplied = 0;
        if (validatedCoupon) {
            if (validatedCoupon.couponType === "percentage") {
                discountApplied = (selectedItem.price * validatedCoupon.discountValue) / 100;
            } else {
                discountApplied = Math.min(selectedItem.price, validatedCoupon.discountValue);
            }
            discountApplied = Math.min(selectedItem.price, discountApplied);
        }
        const finalAmount = Math.max(0, selectedItem.price - discountApplied);

        if (finalAmount === 0) {
            try {
                const applyResult = await applyFreePlan(userId, itemId, validatedCoupon?.code);

                if (!applyResult.success) {
                    console.error("Failed to apply free plan:", applyResult.error);
                    toast.error(applyResult.error || TEXTS.FREE_PLAN_ADD_ERROR);
                    setIsCheckoutLoading(false);
                    setLoadingItemId(null);
                    return;
                }

                if (validatedCoupon) {
                    toast.success(TEXTS.PLAN_ADDED_FREE_SUCCESS(selectedItem.title));
                    setValidatedCoupon(null);
                } else {
                    toast.success(TEXTS.FREE_PLAN_ADDED_SUCCESS(selectedItem.title));
                }

                setIsCheckoutLoading(false);
                setLoadingItemId(null);

                setTimeout(() => {
                    router.push("/dashboard");
                }, 1000);

                return;
            } catch (error) {
                console.error("Error applying free plan:", error);
                toast.error(TEXTS.FREE_PLAN_ADD_ERROR);
                setIsCheckoutLoading(false);
                setLoadingItemId(null);
                return;
            }
        }

        const order_id = itemId;

        let customer_info: {
            name?: string;
            lastName?: string;
            email?: string;
            phone?: string;
        } = {
            name: "",
            lastName: "",
            email: "",
            phone: ""
        };

        if (user) {
            const fullName = user.fullName || "";
            const nameParts = fullName.split(" ");
            const firstName = nameParts[0] || "";
            const lastName = nameParts.slice(1).join(" ") || "";

            customer_info = {
                name: firstName,
                lastName: lastName,
                email: user.emailAddresses?.[0]?.emailAddress || "",
                phone: user.phoneNumbers?.[0]?.phoneNumber || ""
            };
        }

        const requestBody = {
            total_amount: finalAmount,
            order_id: order_id,
            coupon_code: validatedCoupon?.code,
            planName: selectedItem.title,
            customer_info: customer_info
        };

        try {
            const response = await fetch("/api/generate-checkout-url", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(requestBody)
            });

            const result = await response.json();

            if (response.ok && result.success && result.signedUrl) {
                window.location.href = result.signedUrl;
            } else {
                console.error("Failed to generate checkout URL:", result.error);
                toast.error(result.error || TEXTS.CHECKOUT_INITIATION_FAILED);
                setIsCheckoutLoading(false);
                setLoadingItemId(null);
            }
        } catch (fetchError) {
            console.error("Fetch error:", fetchError);
            toast.error(TEXTS.CHECKOUT_INITIATION_FAILED);
            setIsCheckoutLoading(false);
            setLoadingItemId(null);
        }
    };

    const getFeatureById = (featureId: string): Feature => {
        return (
            FEATURES[featureId] || {
                id: featureId,
                name: featureId
            }
        );
    };

    const generateComparisonData = (): FeatureComparison[] => {
        const allFeatureIds = new Set<string>();
        PRICING_PLANS.forEach((item) => {
            item.features.forEach((feature: PlanFeature) => {
                allFeatureIds.add(feature.featureId);
            });
        });

        const getPlansFeatureValue = (planTitle: string, featureId: string) => {
            const plan = PRICING_PLANS.find((item) => item.title === planTitle) || PRICING_PLANS[0];

            const planFeature = plan.features.find((f: PlanFeature) => f.featureId === featureId);

            if (!planFeature) return false;
            if (!planFeature.isApplicable) return false;
            if (planFeature.value) return planFeature.value;
            return true;
        };

        return Array.from(allFeatureIds).map((featureId) => {
            const feature = getFeatureById(featureId);

            const plans = PRICING_PLANS.reduce(
                (acc, item) => {
                    acc[item.title] = getPlansFeatureValue(item.title, featureId);
                    return acc;
                },
                {} as Record<string, boolean | string>
            );

            return {
                name: feature.name,
                plans
            };
        });
    };

    const comparisonData = generateComparisonData();
    const planNames = PRICING_PLANS.map((item) => item.title);

    const representativeItem = PRICING_PLANS.find((item) => item.planType === "milgapro") || PRICING_PLANS[0];
    const representativePrice = representativeItem?.price || 0;

    if (!isUserLoaded) {
        return <SubscriptionsSkeleton />;
    }

    return (
        <div dir="rtl" className="w-full space-y-12">
            <SubscriptionStatus currentSubscription={currentSubscription} />

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                {PRICING_PLANS.map((item) => {
                    const isCurrentPlan = currentSubscription?.planType === item.planType;

                    const isLowerPlan = currentSubscription?.planType
                        ? PLAN_HIERARCHY[item.planType] < PLAN_HIERARCHY[currentSubscription.planType]
                        : false;

                    const adjustedPrice = item.price;

                    let itemDiscount = 0;
                    if (validatedCoupon) {
                        if (validatedCoupon.couponType === "percentage") {
                            itemDiscount = (adjustedPrice * validatedCoupon.discountValue) / 100;
                        } else {
                            itemDiscount = Math.min(adjustedPrice, validatedCoupon.discountValue);
                        }
                        itemDiscount = Math.min(adjustedPrice, itemDiscount);
                    }

                    const displayItem =
                        isCurrentPlan || isLowerPlan || !currentSubscription?.planType
                            ? item
                            : {
                                  ...item,
                                  price: adjustedPrice
                              };

                    return (
                        <SubscriptionsCard
                            key={item.id}
                            item={displayItem}
                            handleCtaClick={handleCheckout}
                            getFeatureByKey={getFeatureById}
                            appliedDiscount={itemDiscount}
                            isLoading={isCheckoutLoading && loadingItemId === item.id}
                            isAuthenticated={isAuthenticated}
                            isCurrentPlan={isCurrentPlan || isLowerPlan}
                            currentSubscription={currentSubscription}
                        />
                    );
                })}
            </div>

            <CouponSection
                onCouponApplied={handleCouponApplied}
                onCouponInvalid={handleCouponInvalid}
                currentTotalAmount={representativePrice}
            />

            {showFeaturesTable && (
                <div className="mt-16">
                    <h2 className="text-2xl font-bold text-center mb-8">{TEXTS.FEATURE_COMPARISON}</h2>
                    <FeatureComparisonTable comparisonData={comparisonData} planNames={planNames} />
                </div>
            )}
        </div>
    );
}
