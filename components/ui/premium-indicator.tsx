"use client";

import { SparklesIcon } from "lucide-react";
import { HTMLAttributes } from "react";

import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { getPlanTextColor, PLAN_ICONS } from "@/config/subscriptions";
import { PlanType } from "@/lib/subscription-constants";
import { cn } from "@/lib/utils";

const TEXTS = {
    planLabel: {
        free: "חינם",
        milgapro: "MilgaPro",
        elite: "Elite",
        vip: "VIP"
    },
    tooltipPrefix: "זמין בתוכנית",
    upgradeText: "שדרג עכשיו",
    upgradeLink: "/subscriptions"
};

interface PremiumIndicatorProps extends HTMLAttributes<HTMLDivElement> {
    requiredPlan: Exclude<PlanType, "free">;
    showLabel?: boolean;
    size?: "sm" | "md" | "lg";
    className?: string;
}

export function PremiumIndicator({
    requiredPlan,
    showLabel = false,
    size = "md",
    className,
    ...props
}: PremiumIndicatorProps) {
    const PlanIcon = PLAN_ICONS[requiredPlan] || SparklesIcon;
    const planColor = getPlanTextColor(requiredPlan);

    const iconSizes = {
        sm: "h-3 w-3",
        md: "h-4 w-4",
        lg: "h-5 w-5"
    };

    const textSizes = {
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base"
    };

    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <div className={cn("inline-flex items-center gap-1.5", planColor, className)} {...props}>
                    <PlanIcon className={iconSizes[size]} />
                    {showLabel && (
                        <span className={cn("font-medium", textSizes[size])}>{TEXTS.planLabel[requiredPlan]}</span>
                    )}
                </div>
            </TooltipTrigger>
            <TooltipContent side="top" dir="rtl">
                <p>
                    {TEXTS.tooltipPrefix} {TEXTS.planLabel[requiredPlan]}
                </p>
            </TooltipContent>
        </Tooltip>
    );
}
