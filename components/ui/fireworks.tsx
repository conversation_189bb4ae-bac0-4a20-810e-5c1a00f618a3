"use client";

import { useEffect, useRef } from "react";

interface FireworksProps {
    /**
     * Whether to show the fireworks animation
     */
    show: boolean;

    /**
     * Duration in milliseconds after which the fireworks will automatically stop
     * @default 5000
     */
    duration?: number;

    /**
     * Custom options for the fireworks animation
     */
    options?: {
        autoresize?: boolean;
        opacity?: number;
        acceleration?: number;
        friction?: number;
        gravity?: number;
        particles?: number;
        traceLength?: number;
        traceSpeed?: number;
        explosion?: number;
        intensity?: number;
        flickering?: number;
        lineStyle?: "round" | "square";
        hue?: {
            min: number;
            max: number;
        };
        delay?: {
            min: number;
            max: number;
        };
        rocketsPoint?: {
            min: number;
            max: number;
        };
        lineWidth?: {
            explosion?: {
                min: number;
                max: number;
            };
            trace?: {
                min: number;
                max: number;
            };
        };
        brightness?: {
            min: number;
            max: number;
        };
        decay?: {
            min: number;
            max: number;
        };
        mouse?: {
            click?: boolean;
            move?: boolean;
            max?: number;
        };
    };

    /**
     * CSS class name for the container
     */
    className?: string;

    /**
     * Inline styles for the container
     */
    style?: React.CSSProperties;
}

export function Fireworks({ show, duration = 5000, options, className = "", style = {} }: FireworksProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const fireworksRef = useRef<import("fireworks-js").Fireworks | null>(null);
    const prefersReducedMotion = useRef<boolean>(false);

    // Check for prefers-reduced-motion
    useEffect(() => {
        if (typeof window !== "undefined") {
            const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
            prefersReducedMotion.current = mediaQuery.matches;

            const handleChange = (e: MediaQueryListEvent) => {
                prefersReducedMotion.current = e.matches;
                // Stop fireworks if user preference changes to reduced motion
                if (e.matches && fireworksRef.current) {
                    fireworksRef.current.stop();
                }
            };

            mediaQuery.addEventListener("change", handleChange);
            return () => mediaQuery.removeEventListener("change", handleChange);
        }
    }, []);

    // Initialize and cleanup fireworks
    useEffect(() => {
        if (show && containerRef.current && !prefersReducedMotion.current) {
            let cleanupFunction: (() => void) | undefined;

            const initFireworks = async () => {
                try {
                    const { Fireworks } = await import("fireworks-js");

                    if (!containerRef.current) return;

                    const container = containerRef.current;
                    const fireworks = new Fireworks(container, {
                        autoresize: true,
                        opacity: 0.5,
                        acceleration: 1.05,
                        friction: 0.97,
                        gravity: 1.5,
                        particles: 50,
                        traceLength: 3,
                        traceSpeed: 10,
                        explosion: 5,
                        intensity: 30,
                        flickering: 50,
                        lineStyle: "round",
                        hue: {
                            min: 0,
                            max: 360
                        },
                        delay: {
                            min: 30,
                            max: 60
                        },
                        rocketsPoint: {
                            min: 50,
                            max: 50
                        },
                        lineWidth: {
                            explosion: {
                                min: 1,
                                max: 3
                            },
                            trace: {
                                min: 1,
                                max: 2
                            }
                        },
                        brightness: {
                            min: 50,
                            max: 80
                        },
                        decay: {
                            min: 0.015,
                            max: 0.03
                        },
                        mouse: {
                            click: false,
                            move: false,
                            max: 1
                        },
                        ...options
                    });

                    fireworksRef.current = fireworks;
                    fireworks.start();

                    const id = setTimeout(() => fireworks.stop(), duration);
                    return () => {
                        clearTimeout(id);
                        fireworks.stop();
                    };
                } catch (error) {
                    console.error("Failed to load fireworks:", error);
                }
            };

            initFireworks().then((cleanup) => {
                cleanupFunction = cleanup;
            });

            return () => {
                if (cleanupFunction) {
                    cleanupFunction();
                }
                if (fireworksRef.current) {
                    fireworksRef.current.stop();
                }
            };
        }
    }, [show, options, duration]);

    if (!show) return null;

    return (
        <div
            ref={containerRef}
            className={className}
            style={{
                position: "fixed",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                pointerEvents: "none",
                zIndex: 100,
                ...style
            }}
        />
    );
}
