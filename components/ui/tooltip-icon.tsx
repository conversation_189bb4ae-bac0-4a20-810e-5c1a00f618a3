"use client";

import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { Info, X } from "lucide-react";
import * as React from "react";

import {
    Drawer,
    DrawerClose,
    DrawerContent,
    DrawerDescription,
    DrawerHeader,
    DrawerTitle,
    DrawerTrigger
} from "@/components/ui/drawer";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

const TEXTS = {
    additionalInfo: "מידע נוסף",
    close: "סגירה"
};

const TooltipProvider = TooltipPrimitive.Provider;
const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipContent = React.forwardRef<
    React.ElementRef<typeof TooltipPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
    <TooltipPrimitive.Content
        ref={ref}
        sideOffset={sideOffset}
        className={cn(
            "z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
            "dir-rtl",
            className
        )}
        {...props}
    />
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

interface TooltipIconProps {
    text: string;
}

const TooltipIcon = ({ text }: TooltipIconProps) => {
    const isMobile = useIsMobile();

    if (isMobile) {
        return (
            <Drawer>
                <DrawerTrigger asChild>
                    <button type="button" className="inline-flex items-center justify-center">
                        <Info className="h-4 w-4 text-muted-foreground" />
                    </button>
                </DrawerTrigger>
                <DrawerContent>
                    <DrawerHeader className="relative">
                        <DrawerClose asChild>
                            <button
                                type="button"
                                className="absolute left-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                            >
                                <X className="h-4 w-4" />
                                <span className="sr-only">{TEXTS.close}</span>
                            </button>
                        </DrawerClose>
                        <DrawerTitle className="text-right pr-12">{TEXTS.additionalInfo}</DrawerTitle>
                        <DrawerDescription className="text-right pr-12">{text}</DrawerDescription>
                    </DrawerHeader>
                </DrawerContent>
            </Drawer>
        );
    }

    return (
        <TooltipProvider delayDuration={300}>
            <TooltipPrimitive.Root>
                <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>{text}</TooltipContent>
            </TooltipPrimitive.Root>
        </TooltipProvider>
    );
};

export { TooltipIcon };
