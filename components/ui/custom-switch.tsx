import React from "react";
import { Control, FieldValues, useController, useFormContext } from "react-hook-form";

import { Label } from "@/components/ui/label";
import { Switch as BaseSwitch } from "@/components/ui/switch";

interface CustomSwitchProps {
    label?: string;
    checked?: boolean;
    onCheckedChange?: (checked: boolean) => void;
    name: string;
}

function ControlledSwitch({ label, name, control }: CustomSwitchProps & { control: Control<FieldValues> }) {
    const {
        field: { value, onChange }
    } = useController({
        name,
        control,
        defaultValue: false
    });

    return (
        <div className="flex items-center justify-between w-full" dir="rtl">
            {label && <Label>{label}</Label>}
            <BaseSwitch checked={value} onCheckedChange={onChange} name={name} dir="rtl" />
        </div>
    );
}

function UncontrolledSwitch({ label, checked, onCheckedChange, name }: CustomSwitchProps) {
    const [internalChecked, setInternalChecked] = React.useState(!!checked);
    const isControlled = typeof checked === "boolean" && typeof onCheckedChange === "function";

    const handleChange = (val: boolean) => {
        if (isControlled) {
            onCheckedChange?.(val);
        } else {
            setInternalChecked(val);
        }
    };

    return (
        <div className="flex items-center justify-between w-full" dir="rtl">
            {label && <Label>{label}</Label>}
            <BaseSwitch
                checked={isControlled ? checked : internalChecked}
                onCheckedChange={handleChange}
                name={name}
                dir="rtl"
            />
        </div>
    );
}

export function CustomSwitch(props: CustomSwitchProps) {
    const formContext = useFormContext();
    const control = formContext?.control;

    if (control) {
        return <ControlledSwitch {...props} control={control} />;
    }

    return <UncontrolledSwitch {...props} />;
}
