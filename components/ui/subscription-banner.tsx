"use client";

import { useUser } from "@clerk/nextjs";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { But<PERSON> } from "@/components/ui/button";
import { TEXTS } from "@/config/dashboard";
import { PlanType } from "@/lib/subscription-constants";

export function SubscriptionBanner() {
    const { user, isLoaded: isUserLoaded } = useUser();
    const isAuthenticated = !!user && isUserLoaded;
    const [userPlan, setUserPlan] = useState<PlanType | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        async function fetchSubscription() {
            if (isAuthenticated) {
                try {
                    const subscription = await getCurrentUserSubscription();
                    setUserPlan(subscription?.planType || "free");
                } catch (error) {
                    console.error("Error fetching subscription:", error);
                    setUserPlan("free");
                }
            } else if (isUserLoaded) {
                setUserPlan(null);
            }
            setIsLoading(false);
        }

        fetchSubscription();
    }, [isAuthenticated, isUserLoaded]);

    if (isLoading || !isUserLoaded || !isAuthenticated || userPlan !== "free") {
        return null;
    }

    return (
        <div
            className="w-full py-3 px-6 flex items-center justify-between bg-blue-50 text-blue-800 shadow-sm border-b"
            dir="rtl"
        >
            <div className="flex-1 flex items-center justify-center">
                <p className="text-sm font-medium">{TEXTS.subscribeBanner}</p>
            </div>
            <Button asChild variant="outline" size="sm" className="ml-4">
                <Link href="/subscriptions" className="flex items-center gap-1">
                    שדרג עכשיו
                    <ArrowRightIcon className="h-3 w-3 rtl:rotate-180" />
                </Link>
            </Button>
        </div>
    );
}
