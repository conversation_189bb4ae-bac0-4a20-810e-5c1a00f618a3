"use client";

import { AnimatePresence, motion } from "framer-motion";
import * as LucideIcons from "lucide-react";
import { LucideIcon } from "lucide-react";
import { X } from "lucide-react";
import { useEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { useBanners } from "@/hooks/use-banners";
import { cn } from "@/lib/utils";

const TEXTS = {
    closeButton: "סגור"
};

export function Banner() {
    const [visibleBanners, setVisibleBanners] = useState<Set<string>>(new Set());
    const { items: banners, loading, error } = useBanners();

    useEffect(() => {
        if (banners && banners.length > 0) {
            let dismissedBanners: Record<string, string> = {};
            try {
                dismissedBanners = JSON.parse(localStorage.getItem("dismissedBanners") || "{}");
            } catch {
                localStorage.removeItem("dismissedBanners");
            }

            const visibleBannerIds = banners
                .filter((banner) => {
                    if (!dismissedBanners[banner.id]) return true;

                    const dismissedDate = new Date(dismissedBanners[banner.id]);
                    const daysSinceDismissed = Math.floor(
                        (Date.now() - dismissedDate.getTime()) / (1000 * 60 * 60 * 24)
                    );

                    return daysSinceDismissed >= banner.days_to_live;
                })
                .map((banner) => banner.id);

            setVisibleBanners(new Set(visibleBannerIds));

            const timers: NodeJS.Timeout[] = [];
            banners.forEach((banner) => {
                if (visibleBannerIds.includes(banner.id) && banner.seconds_before_show > 0) {
                    setVisibleBanners((prev) => {
                        const newSet = new Set(prev);
                        newSet.delete(banner.id);
                        return newSet;
                    });

                    const t = setTimeout(() => {
                        setVisibleBanners((prev) => {
                            const newSet = new Set(prev);
                            newSet.add(banner.id);
                            return newSet;
                        });
                    }, banner.seconds_before_show * 1000);
                    timers.push(t);
                }
            });

            return () => timers.forEach(clearTimeout);
        } else {
            setVisibleBanners(new Set());
        }
    }, [banners]);

    if (loading) return null;
    if (error) return null;
    if (banners.length === 0) return null;

    const handleClose = (bannerId: string) => {
        setVisibleBanners((prev) => {
            const newSet = new Set(prev);
            newSet.delete(bannerId);
            return newSet;
        });

        let dismissedBanners: Record<string, string> = {};
        try {
            dismissedBanners = JSON.parse(localStorage.getItem("dismissedBanners") || "{}");
        } catch {
            localStorage.removeItem("dismissedBanners");
        }
        dismissedBanners[bannerId] = new Date().toISOString();
        localStorage.setItem("dismissedBanners", JSON.stringify(dismissedBanners));
    };

    const handleCtaClick = (link: string) => {
        if (link) {
            window.open(link, "_blank", "noopener,noreferrer");
        }
    };

    if (visibleBanners.size === 0) return null;

    return (
        <div className="w-full flex flex-col">
            <AnimatePresence>
                {banners.map(
                    (banner) =>
                        visibleBanners.has(banner.id) && (
                            <motion.div
                                key={banner.id}
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.2 }}
                                className={cn(
                                    "w-full py-3.5 px-6 flex items-center justify-between",
                                    "shadow-sm hover:shadow-md transition-shadow duration-200",
                                    "border-b border-black/5 last:border-b-0"
                                )}
                                style={{
                                    backgroundColor: banner.background_color,
                                    color: banner.text_color
                                }}
                                dir="rtl"
                            >
                                <div className="flex items-center gap-2">
                                    {banner.icon &&
                                        (() => {
                                            const IconComponent =
                                                (LucideIcons as unknown as Record<string, LucideIcon>)[banner.icon] ||
                                                LucideIcons.Info;
                                            return <IconComponent className="h-5 w-5 flex-shrink-0" />;
                                        })()}
                                    <div className="flex flex-col text-right">
                                        {banner.title && <p className="text-sm font-bold">{banner.title}</p>}
                                        <p className="text-base">{banner.text}</p>
                                    </div>
                                </div>

                                <div className="flex-1"></div>

                                {banner.cta_text && banner.cta_link && (
                                    <Button
                                        onClick={() => handleCtaClick(banner.cta_link!)}
                                        variant="outline"
                                        className="mx-4"
                                        style={{
                                            borderColor: banner.text_color,
                                            color: banner.text_color,
                                            backgroundColor: "transparent"
                                        }}
                                    >
                                        {banner.cta_text}
                                    </Button>
                                )}

                                {banner.enable_dismiss && (
                                    <button
                                        onClick={() => handleClose(banner.id)}
                                        className="p-1.5 rounded-full hover:bg-black/10 transition-colors duration-200"
                                        aria-label={TEXTS.closeButton}
                                    >
                                        <X className="h-4 w-4" />
                                    </button>
                                )}
                            </motion.div>
                        )
                )}
            </AnimatePresence>
        </div>
    );
}
