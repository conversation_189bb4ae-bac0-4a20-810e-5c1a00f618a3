"use client";

import type { Locale } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import * as React from "react";
import { DayPicker, useNavigation } from "react-day-picker";

import { buttonVariants } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";

const TEXTS = {
    monthSelectLabel: "בחר חודש",
    yearSelectLabel: "בחר שנה"
};

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

// Custom caption component with year selection
function CustomCaption({ displayMonth }: { displayMonth: Date; locale?: Locale }) {
    const { goToMonth } = useNavigation();

    // Generate years for dropdown (100 years before and 20 years after current year)
    const currentYear = new Date().getFullYear(); // stable reference year
    const years = Array.from({ length: 121 }, (_, i) => currentYear - 100 + i);

    // Generate months for dropdown
    const months = Array.from({ length: 12 }, (_, i) => {
        const date = new Date();
        date.setMonth(i);
        return {
            value: i,
            // Use a string locale code for toLocaleString
            label: date.toLocaleString("he", { month: "long" })
        };
    });

    const handleYearChange = (year: string) => {
        const newDate = new Date(displayMonth);
        newDate.setFullYear(parseInt(year));
        goToMonth(newDate);
    };

    const handleMonthChange = (month: string) => {
        const newDate = new Date(displayMonth);
        newDate.setMonth(parseInt(month));
        goToMonth(newDate);
    };

    return (
        <div className="flex justify-center items-center w-full">
            <div className="flex items-center w-full gap-1">
                <div className="flex-1">
                    <Select value={displayMonth.getMonth().toString()} onValueChange={handleMonthChange}>
                        <SelectTrigger
                            aria-label={TEXTS.monthSelectLabel}
                            className="h-7 text-xs border-none focus:ring-0 flex-row-reverse text-right w-full"
                        >
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="text-right">
                            {months.map((month) => (
                                <SelectItem key={month.value} value={month.value.toString()} className="text-right">
                                    {month.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="flex-1">
                    <Select value={displayMonth.getFullYear().toString()} onValueChange={handleYearChange}>
                        <SelectTrigger
                            aria-label={TEXTS.yearSelectLabel}
                            className="h-7 text-xs border-none focus:ring-0 flex-row-reverse text-right w-full"
                        >
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="text-right">
                            {years.map((year) => (
                                <SelectItem key={year} value={year.toString()} className="text-right">
                                    {year}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>
        </div>
    );
}

function Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {
    return (
        <DayPicker
            showOutsideDays={showOutsideDays}
            className={cn("p-3", className)}
            classNames={{
                months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                month: "space-y-4",
                caption: "flex justify-center pt-1 relative items-center gap-1",
                caption_label: "text-sm font-medium",
                caption_dropdowns: "flex justify-center gap-1",
                dropdown:
                    "border border-input bg-background rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary",
                dropdown_month: "rtl:ml-1 ltr:mr-1",
                dropdown_year: "rtl:mr-1 ltr:ml-1",
                nav: "space-x-1 flex items-center",
                nav_button: cn(
                    buttonVariants({ variant: "outline" }),
                    "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
                ),
                nav_button_previous: "absolute right-1",
                nav_button_next: "absolute left-1",
                table: "w-full border-collapse space-y-1",
                head_row: "flex",
                head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
                row: "flex w-full mt-2",
                cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                day: cn(buttonVariants({ variant: "ghost" }), "h-9 w-9 p-0 font-normal aria-selected:opacity-100"),
                day_range_end: "day-range-end",
                day_selected:
                    "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                day_today: "bg-accent text-accent-foreground",
                day_outside:
                    "day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
                day_disabled: "text-muted-foreground opacity-50",
                day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                day_hidden: "invisible",
                ...classNames
            }}
            components={{
                IconLeft: ({ className, ...props }) => <ChevronLeft className={cn("h-4 w-4", className)} {...props} />,
                IconRight: ({ className, ...props }) => (
                    <ChevronRight className={cn("h-4 w-4", className)} {...props} />
                ),
                Caption: ({ displayMonth }) => <CustomCaption displayMonth={displayMonth} locale={props.locale} />
            }}
            {...props}
        />
    );
}
Calendar.displayName = "Calendar";

export { Calendar };
