import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface TextFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: string;
    pattern?: RegExp;
    patternMessage?: string;
    requiredText?: string;
    maxLength?: number;
    minLength?: number;
}

export const TEXTS = {
    required: "שדה זה נדרש",
    minLength: (min: number) => `אורך מינימלי הוא ${min} תווים`,
    maxLength: (max: number) => `אורך מקסימלי הוא ${max} תווים`
} as const;

export function LongText({
    name,
    label,
    placeholder,
    maxLength,
    minLength,
    required = true,
    requiredText = TEXTS.required,
    tooltip
}: TextFieldProps) {
    const {
        register,
        formState: { errors, isSubmitted }
    } = useFormContext();

    return (
        <div dir="rtl">
            <div className="flex items-center justify-start gap-1">
                {tooltip && <TooltipIcon text={tooltip} />}
                {label && (
                    <Label htmlFor={name} className="text-right mb-2">
                        {label}
                    </Label>
                )}
            </div>
            <Textarea
                id={name}
                placeholder={placeholder}
                className={cn(
                    "text-right placeholder:text-right resize-none",
                    isSubmitted && errors[name] && "border-red-500 focus-visible:ring-red-500"
                )}
                dir="rtl"
                {...register(name, {
                    required: required ? requiredText : undefined,
                    minLength: minLength ? { value: minLength, message: TEXTS.minLength(minLength) } : undefined,
                    maxLength: maxLength ? { value: maxLength, message: TEXTS.maxLength(maxLength) } : undefined
                })}
            />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
