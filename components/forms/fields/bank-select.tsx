import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { DropdownBase, Option } from "@/components/forms/fields/dropdown-base";
import { CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { TooltipIcon } from "@/components/ui/tooltip-icon";

export const TEXTS = {
    bankPlaceholder: "בחר בנק",
    branchPlaceholder: "בחר סניף",
    required: "שדה זה נדרש",
    loading: "טוען בנקים...",
    noResults: "לא נמצאו תוצאות",
    error: "שגיאה בטעינת הבנקים"
};

type Bank = {
    bankCode: number;
    bankName: string;
    branches: {
        branchCode: number;
        branchName: string;
    }[];
};

interface BankSelectFieldProps extends BaseFieldProps {
    name: string;
    requiredText?: string;
    tooltip?: string;
    defaultValue?: { bankCode: string | number; branchCode: string | number };
}

export function BankSelect({
    name,
    label,
    required = true,
    requiredText = TEXTS.required,
    tooltip,
    defaultValue
}: BankSelectFieldProps) {
    const {
        register,
        setValue,
        watch,
        formState: { errors, isSubmitted }
    } = useFormContext();

    const value = watch(name) || { bankCode: "", branchCode: "" };

    function isEmptyBankValue(val: unknown): boolean {
        if (!val || typeof val !== "object") return true;
        const v = val as { bankCode?: string | number; branchCode?: string | number };
        return !v.bankCode && !v.branchCode;
    }

    function isBankValueEqual(a: unknown, b: unknown): boolean {
        if (!a || !b || typeof a !== "object" || typeof b !== "object") return false;
        const va = a as { bankCode?: string | number; branchCode?: string | number };
        const vb = b as { bankCode?: string | number; branchCode?: string | number };
        return (
            String(va.bankCode ?? "") === String(vb.bankCode ?? "") &&
            String(va.branchCode ?? "") === String(vb.branchCode ?? "")
        );
    }

    useEffect(() => {
        if (defaultValue && isEmptyBankValue(value) && !isBankValueEqual(value, defaultValue)) {
            setValue(name, defaultValue, { shouldValidate: false });
        }
    });

    const [banks, setBanks] = useState<Bank[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const selectedBankCode = value.bankCode;
    const selectedBranchCode = value.branchCode;

    useEffect(() => {
        setLoading(true);
        fetch("/api/banks")
            .then((res) => res.json())
            .then((data) => {
                setBanks(data.banks || []);
                setLoading(false);
            })
            .catch(() => {
                setError(TEXTS.error);
                setLoading(false);
            });
    }, []);

    const bankOptions: Option[] = banks
        .map((bank) => ({
            id: String(bank.bankCode),
            label: bank.bankName,
            subtitle: String(bank.bankCode)
        }))
        .sort((a, b) => Number(a.subtitle) - Number(b.subtitle));

    const selectedBank = banks.find((b) => b.bankCode === selectedBankCode);

    const branchOptions: Option[] =
        selectedBank?.branches
            .map((branch) => ({
                id: String(branch.branchCode),
                label: branch.branchName,
                subtitle: String(branch.branchCode)
            }))
            .sort((a, b) => Number(a.subtitle) - Number(b.subtitle)) || [];

    const handleBankSelect = (bankCode: number, setOpen: (open: boolean) => void) => {
        setValue(name, { bankCode, branchCode: "" }, { shouldValidate: true });
        setOpen(false);
    };

    const handleBranchSelect = (branchCode: number, setOpen: (open: boolean) => void) => {
        setValue(name, { bankCode: selectedBankCode, branchCode }, { shouldValidate: true });
        setOpen(false);
    };

    const displayBank = selectedBankCode && bankOptions.find((o) => o.id === String(selectedBankCode))?.label;

    const displayBranch = selectedBranchCode && branchOptions.find((o) => o.id === String(selectedBranchCode))?.label;

    return (
        <div className="space-y-2" dir="rtl">
            {label && (
                <div className="flex items-center justify-start gap-1 mb-1">
                    {tooltip && <TooltipIcon text={tooltip} />}
                    <Label className="text-right">{label}</Label>
                </div>
            )}

            <div className="flex flex-col md:flex-row gap-2">
                <div className="flex-1">
                    <DropdownBase
                        name={`${name}_bank`}
                        label={undefined}
                        placeholder={TEXTS.bankPlaceholder}
                        options={bankOptions}
                        tooltip={undefined}
                        searchPlaceholder={TEXTS.bankPlaceholder}
                        noResultsText={TEXTS.noResults}
                        displayValue={displayBank || ""}
                        showSearch
                    >
                        {(_, { setOpen, searchQuery }) => {
                            const filteredBankOptions =
                                searchQuery === ""
                                    ? bankOptions
                                    : bankOptions.filter(
                                          (option) =>
                                              option.label.includes(searchQuery) ||
                                              option.subtitle?.includes(searchQuery)
                                      );
                            return (
                                <div>
                                    <div>
                                        <CommandList>
                                            <CommandGroup>
                                                {filteredBankOptions.map((option) => (
                                                    <CommandItem
                                                        key={option.id}
                                                        value={option.id}
                                                        onSelect={() => handleBankSelect(Number(option.id), setOpen)}
                                                        className="w-full px-2 py-1.5 cursor-pointer"
                                                    >
                                                        <div className="flex items-center w-full">
                                                            <div className="flex flex-col items-start flex-1">
                                                                <span className="text-sm">{option.label}</span>
                                                                {option.subtitle && (
                                                                    <span className="text-xs text-muted-foreground">
                                                                        {option.subtitle}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </CommandItem>
                                                ))}
                                            </CommandGroup>
                                        </CommandList>
                                    </div>
                                </div>
                            );
                        }}
                    </DropdownBase>
                </div>
                <div className="flex-1">
                    <DropdownBase
                        name={`${name}_branch`}
                        label={undefined}
                        placeholder={TEXTS.branchPlaceholder}
                        options={branchOptions}
                        tooltip={undefined}
                        searchPlaceholder={TEXTS.branchPlaceholder}
                        noResultsText={TEXTS.noResults}
                        displayValue={displayBranch || ""}
                        showSearch
                    >
                        {(_, { setOpen, searchQuery }) => {
                            const filteredBranchOptions =
                                searchQuery === ""
                                    ? branchOptions
                                    : branchOptions.filter(
                                          (option) =>
                                              option.label.includes(searchQuery) ||
                                              option.subtitle?.includes(searchQuery)
                                      );
                            return (
                                <div>
                                    <div>
                                        <CommandList>
                                            <CommandGroup>
                                                {filteredBranchOptions.map((option) => (
                                                    <CommandItem
                                                        key={option.id}
                                                        value={option.id}
                                                        onSelect={() => handleBranchSelect(Number(option.id), setOpen)}
                                                        className="w-full px-2 py-1.5 cursor-pointer"
                                                        disabled={!selectedBankCode}
                                                    >
                                                        <div className="flex items-center w-full">
                                                            <div className="flex flex-col items-start flex-1">
                                                                <span className="text-sm">{option.label}</span>
                                                                {option.subtitle && (
                                                                    <span className="text-xs text-muted-foreground">
                                                                        {option.subtitle}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </CommandItem>
                                                ))}
                                            </CommandGroup>
                                        </CommandList>
                                    </div>
                                </div>
                            );
                        }}
                    </DropdownBase>
                </div>
            </div>

            <input
                type="hidden"
                {...register(name, {
                    required: required ? requiredText : undefined,
                    validate: (val) => !required || (!!val.bankCode && !!val.branchCode) || requiredText
                })}
                value={JSON.stringify(value)}
                readOnly
            />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 text-right">{errors[name]?.message as string}</p>
            )}
            {loading && <div className="text-sm text-muted-foreground">{TEXTS.loading}</div>}
            {error && <div className="text-sm text-red-500">{error}</div>}
        </div>
    );
}
