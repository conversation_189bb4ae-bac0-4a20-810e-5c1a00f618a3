"use client";

import { format } from "date-fns";
import { he } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react";
import { memo, useCallback, useEffect, useState } from "react";
import type { DateRange as DayPickerDateRange } from "react-day-picker";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface DateRangeProps extends BaseFieldProps {
    startBeforeEndText?: string;
    isFutureAllowed?: boolean;
    placeholder?: string;
}

export const TEXTS = {
    placeholder: "בחר תאריכים",
    required: "שדה זה נדרש"
} as const;

const DateRange = memo(
    ({ name, label, tooltip, isFutureAllowed = true, placeholder = TEXTS.placeholder }: DateRangeProps) => {
        const {
            setValue,
            watch,
            formState: { errors, isSubmitted }
        } = useFormContext();

        const formValue = watch(name);
        const [date, setDate] = useState<DayPickerDateRange | undefined>();
        const [isOpen, setIsOpen] = useState(false);
        const [isInitialized, setIsInitialized] = useState(false);

        useEffect(() => {
            if (!isInitialized || formValue !== undefined) {
                if (formValue && typeof formValue === "object") {
                    const { startDate, endDate } = formValue as { startDate?: Date; endDate?: Date };
                    setDate({
                        from: startDate instanceof Date ? startDate : undefined,
                        to: endDate instanceof Date ? endDate : undefined
                    });
                } else if (formValue == null) {
                    setDate(undefined);
                }
                setIsInitialized(true);
            }
        }, [formValue, isInitialized]);

        const handleSelect = useCallback(
            (value: DayPickerDateRange | undefined) => {
                setDate(value);

                if (value) {
                    setValue(
                        name,
                        {
                            startDate: value.from,
                            endDate: value.to
                        },
                        { shouldValidate: true }
                    );
                } else {
                    setValue(name, undefined, { shouldValidate: true });
                }
            },
            [name, setValue]
        );

        const formatDateRange = useCallback(
            (from?: Date, to?: Date): React.ReactNode => {
                if (!from) return <span className="text-muted-foreground text-base md:text-sm">{placeholder}</span>;
                if (!to) return format(from, "dd/MM/yyyy", { locale: he });

                return (
                    <>
                        {format(to, "dd/MM/yyyy", { locale: he })} - {format(from, "dd/MM/yyyy", { locale: he })}
                    </>
                );
            },
            [placeholder]
        );

        const handleOpenChange = useCallback((open: boolean) => {
            setIsOpen(open);
        }, []);

        return (
            <div className="space-y-2" dir="rtl">
                {label && (
                    <div className="flex items-center justify-start gap-1">
                        {tooltip && <TooltipIcon text={tooltip} />}
                        <Label className="text-right font-medium">{label}</Label>
                    </div>
                )}

                <Popover open={isOpen} onOpenChange={handleOpenChange}>
                    <PopoverTrigger asChild>
                        <Button
                            id={name}
                            variant="outline"
                            className={cn(
                                "w-full justify-between text-right font-normal",
                                !date?.from && "text-muted-foreground",
                                isSubmitted && errors[name] && "border-red-500 text-red-500 focus-visible:ring-red-500"
                            )}
                        >
                            <span className="flex-1">{formatDateRange(date?.from, date?.to)}</span>
                            <CalendarIcon className="h-4 w-4 ms-2" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="end">
                        <Calendar
                            mode="range"
                            selected={date}
                            onSelect={handleSelect}
                            initialFocus
                            defaultMonth={date?.from}
                            locale={he}
                            dir="rtl"
                            disabled={(date) => !isFutureAllowed && date > new Date()}
                        />
                    </PopoverContent>
                </Popover>

                {isSubmitted && errors[name] && (
                    <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
                )}
            </div>
        );
    }
);

DateRange.displayName = "DateRange";

export { DateRange };
