import { Check, X } from "lucide-react";
import { useEffect, useState } from "react";
import React from "react";
import { useFormContext } from "react-hook-form";

import { DropdownBase, Option } from "@/components/forms/fields/dropdown-base";
import { CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { cn } from "@/lib/utils";

import { BaseFieldProps } from "./base-field";

const TEXTS = {
    required: "יש לבחור לפחות אפשרות אחת",
    placeholder: "בחר אפשרויות"
};

interface MultiSelectProps extends BaseFieldProps {
    placeholder?: string;
    options: Option[];
    requiredText?: string;
    searchPlaceholder?: string;
    noResultsText?: string;
    showSearch?: boolean;
}

export function MultiSelect({
    name,
    label,
    placeholder = TEXTS.placeholder,
    options,
    required = true,
    requiredText = TEXTS.required,
    tooltip,
    searchPlaceholder,
    noResultsText,
    showSearch = false
}: MultiSelectProps) {
    const {
        register,
        setValue,
        watch,
        formState: { errors, isSubmitted }
    } = useFormContext();

    const [selectedIds, setSelectedIds] = useState<string[]>([]);

    const fieldValue = watch(name);

    useEffect(() => {
        if (Array.isArray(fieldValue)) {
            const ids = fieldValue
                .map((item) => {
                    if (typeof item === "object" && item && "id" in item) {
                        return item.id;
                    }

                    return null;
                })
                .filter(Boolean) as string[];
            setSelectedIds(ids);
        } else {
            setSelectedIds([]);
        }
    }, [fieldValue]);

    const handleSelect = (id: string) => {
        const selectedOption = options.find((option) => option.id === id);
        if (!selectedOption) return;

        const existingIndex = selectedIds.findIndex((selectedId) => selectedId === id);
        const newIds =
            existingIndex !== -1 ? selectedIds.filter((_, index) => index !== existingIndex) : [...selectedIds, id];

        const newValues = newIds.map((selectedId) => {
            const option = options.find((opt) => opt.id === selectedId);
            return option ? { id: option.id, label: option.label } : { id: selectedId, label: selectedId };
        });

        setSelectedIds(newIds);
        setValue(name, newValues, { shouldValidate: true, shouldTouch: true });
    };

    const handleRemove = (id: string) => {
        const newIds = selectedIds.filter((selectedId) => selectedId !== id);

        const newValues = newIds.map((selectedId) => {
            const option = options.find((opt) => opt.id === selectedId);
            return option ? { id: option.id, label: option.label } : { id: selectedId, label: selectedId };
        });

        setSelectedIds(newIds);
        setValue(name, newValues, { shouldValidate: true, shouldTouch: true });
    };

    const selectedOptions = options.filter((option) => {
        return option.id && selectedIds.includes(option.id);
    });

    return (
        <>
            <DropdownBase
                name={name}
                label={label}
                placeholder={placeholder}
                options={options}
                tooltip={tooltip}
                searchPlaceholder={searchPlaceholder}
                noResultsText={noResultsText}
                displayValue={
                    selectedOptions.length > 0 ? (
                        <div className="flex flex-wrap gap-1.5 max-h-32 overflow-y-auto w-full">
                            {selectedOptions.map((option) => (
                                <div
                                    key={option.id}
                                    className="bg-primary-50 border border-primary-200 rounded-md px-2 py-1 text-sm flex items-center gap-1.5 my-0.5"
                                >
                                    <div
                                        role="button"
                                        tabIndex={0}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            if (option.id) {
                                                handleRemove(option.id);
                                            }
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === "Enter" || e.key === " ") {
                                                e.preventDefault();
                                                if (option.id) {
                                                    handleRemove(option.id);
                                                }
                                            }
                                        }}
                                        className="text-gray-500 hover:text-gray-700 focus:outline-none cursor-pointer"
                                    >
                                        <X className="h-3 w-3" />
                                    </div>
                                    <span>{option.label}</span>
                                </div>
                            ))}
                        </div>
                    ) : null
                }
                showSearch={showSearch}
            >
                {(filteredOptions) => (
                    <CommandList>
                        <CommandGroup>
                            {filteredOptions.map((option) => (
                                <CommandItem
                                    key={option.id}
                                    value={option.id}
                                    onSelect={() => option.id && handleSelect(option.id)}
                                    className="w-full px-2 py-1.5 cursor-pointer"
                                >
                                    <div className="flex items-center w-full">
                                        <div className="flex flex-col items-start flex-1">
                                            <span className="text-sm">{option.label}</span>
                                            {option.subtitle && (
                                                <span className="text-xs text-muted-foreground">{option.subtitle}</span>
                                            )}
                                        </div>
                                        <Check
                                            className={cn(
                                                "h-4 w-4 flex-shrink-0 mr-2",
                                                option.id && selectedIds.includes(option.id)
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                            )}
                                        />
                                    </div>
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                )}
            </DropdownBase>
            <input
                type="hidden"
                {...register(name, {
                    required: required ? requiredText : false,

                    validate: (value) => !required || (Array.isArray(value) && value.length > 0) || requiredText
                })}
            />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 text-right">{errors[name]?.message as string}</p>
            )}
        </>
    );
}
