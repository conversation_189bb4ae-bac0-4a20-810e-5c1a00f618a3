"use client";

import { UploadCloud } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { toast } from "sonner";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Label } from "@/components/ui/label";

export interface FileUploadFieldProps extends BaseFieldProps {
    defaultValue?: File;
    onFileSelect?: (file: File | null) => void;
    acceptedFileType: string;
    acceptedFileTypeDescription: string;
    maxSizeInMB: number;
    fileTypeErrorMessage: string;
    fileSizeErrorMessage: (maxSize: number) => string;
    dragDropText: string;
    fileFormatDescription: (maxSize: number) => string;
    selectedFileText: string;
    requiredText?: string;
}

export function FileUpload({
    name,
    label,
    required,
    requiredText,
    defaultValue,
    onFileSelect,
    acceptedFileType,
    acceptedFileTypeDescription,
    maxSizeInMB,
    fileTypeErrorMessage,
    fileSizeErrorMessage,
    dragDropText,
    fileFormatDescription,
    selectedFileText
}: FileUploadFieldProps) {
    const { register, setValue, watch } = useFormContext();
    const [isDragging, setIsDragging] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(defaultValue || null);

    const fieldValue = watch(name);
    useEffect(() => {
        if (!fieldValue) {
            setSelectedFile(null);

            const fileInput = document.getElementById(name) as HTMLInputElement;
            if (fileInput) {
                fileInput.value = "";
            }
        }
    }, [fieldValue, name, onFileSelect]);

    const handleFileSelect = useCallback(
        (file: File) => {
            if (!file) return;

            const allowedTypesArray = acceptedFileType.split(",").map((type) => type.trim().toLowerCase());
            const fileTypeLower = file.type.toLowerCase();

            if (
                !allowedTypesArray.includes(fileTypeLower) &&
                !allowedTypesArray.includes(fileTypeLower.split("/")[0] + "/*") &&
                !allowedTypesArray.find((type) => type.endsWith("/*") && fileTypeLower.startsWith(type.slice(0, -1)))
            ) {
                if (acceptedFileType.trim() !== "" && acceptedFileType.trim() !== "*/*") {
                    toast.error(fileTypeErrorMessage, {
                        duration: 5000,
                        description: acceptedFileTypeDescription
                    });
                    return;
                }
            }

            const fileSizeInMB = file.size / (1024 * 1024);
            if (fileSizeInMB > maxSizeInMB) {
                toast.error(fileSizeErrorMessage(maxSizeInMB));
                return;
            }

            setSelectedFile(file);
            setValue(name, file, { shouldValidate: true });
            if (onFileSelect) {
                onFileSelect(file);
            }
        },
        [
            name,
            setValue,
            onFileSelect,
            acceptedFileType,
            acceptedFileTypeDescription,
            maxSizeInMB,
            fileTypeErrorMessage,
            fileSizeErrorMessage
        ]
    );

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDrop = useCallback(
        (e: React.DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDragging(false);

            const file = e.dataTransfer.files?.[0];
            if (file) {
                handleFileSelect(file);
            }
        },
        [handleFileSelect]
    );

    return (
        <div className="space-y-2" dir="rtl">
            <Label htmlFor={name} className="font-medium">
                {label}
            </Label>

            <div
                className={`relative flex flex-col items-center justify-center gap-2 p-6 border-2 border-dashed rounded-lg transition-colors ${
                    isDragging ? "border-primary bg-primary/5" : "border-muted"
                } cursor-pointer`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                <input
                    type="file"
                    id={name}
                    accept={acceptedFileType}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                            handleFileSelect(file);
                        }
                    }}
                />

                <UploadCloud className="w-10 h-10 text-muted-foreground" />
                <div className="text-center">
                    <p className="text-sm font-medium">{dragDropText}</p>
                    <p className="text-xs text-muted-foreground mt-1">{fileFormatDescription(maxSizeInMB)}</p>
                </div>

                <input type="hidden" {...register(name, { required: required ? requiredText : false })} />

                {selectedFile && (
                    <div className="text-sm text-muted-foreground mt-2 text-center">
                        {selectedFileText}: {selectedFile.name}
                    </div>
                )}
            </div>
        </div>
    );
}
