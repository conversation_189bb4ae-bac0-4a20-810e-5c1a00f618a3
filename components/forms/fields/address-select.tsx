import { Check, MapPin } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

const TEXTS = {
    required: "שדה זה נדרש",
    placeholder: "הזן כתובת",
    loading: "טוען...",
    noResults: "לא נמצאו תוצאות",
    requiredText: "יש להזין כתובת"
};

interface Suggestion {
    place_name: string;
    center: [number, number];
}

export interface AddressFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: string;
    requiredText?: string;
}

export function AddressSelect({
    name,
    label,
    placeholder,
    required = true,
    requiredText = TEXTS.requiredText,
    tooltip,
    defaultValue = ""
}: AddressFieldProps) {
    const {
        register,
        setValue,
        formState: { errors, isSubmitted }
    } = useFormContext();

    const [open, setOpen] = useState(false);
    const [value, setValueState] = useState(defaultValue);
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (defaultValue) {
            setValue(name, defaultValue);
        }
    }, [defaultValue, name, setValue]);

    const handleSearch = useCallback(async (search: string) => {
        const trimmedSearch = search.trim();
        setValueState(search);

        if (trimmedSearch.length < 2) {
            setSuggestions([]);
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch(`/api/geocode?query=${encodeURIComponent(trimmedSearch)}&language=he`);
            if (!response.ok) {
                throw new Error("Failed to fetch address suggestions");
            }
            const data = await response.json();
            setSuggestions(
                data.features.map((feature: Suggestion) => ({
                    place_name: feature.place_name,
                    center: feature.center
                }))
            );
        } catch (error) {
            console.error("Error fetching address suggestions:", error);
            setSuggestions([]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    return (
        <div className="space-y-2" dir="rtl">
            <div className="flex items-center justify-start gap-1">
                {tooltip && <TooltipIcon text={tooltip} />}
                <Label htmlFor={name} className="text-right block">
                    {label}
                </Label>
            </div>
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between text-right",
                            isSubmitted && errors[name] && "border-red-500 focus-visible:ring-red-500"
                        )}
                    >
                        <MapPin className="h-4 w-4 shrink-0 opacity-50" />
                        <span className={cn("flex-1 text-right truncate", !value && "text-muted-foreground")}>
                            {value || TEXTS.placeholder}
                        </span>
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[--radix-popover-trigger-width] max-h-[300px] overflow-y-auto">
                    <Command dir="rtl" shouldFilter={false} className="w-full">
                        <div className="flex items-center border-b px-3">
                            <CommandInput
                                placeholder={TEXTS.placeholder}
                                value={value}
                                onValueChange={handleSearch}
                                className="text-right placeholder:text-right ps-2"
                            />
                        </div>
                        <CommandList>
                            <CommandEmpty>{isLoading ? TEXTS.loading : TEXTS.noResults}</CommandEmpty>
                            <CommandGroup>
                                {suggestions.map((suggestion) => (
                                    <CommandItem
                                        key={suggestion.place_name}
                                        onSelect={() => {
                                            setValueState(suggestion.place_name);
                                            setValue(name, suggestion.place_name);
                                            setOpen(false);
                                        }}
                                        value={suggestion.place_name}
                                        className="text-right cursor-pointer"
                                    >
                                        <Check
                                            className={cn(
                                                "mr-2 h-4 w-4",
                                                value === suggestion.place_name ? "opacity-100" : "opacity-0"
                                            )}
                                        />
                                        {suggestion.place_name}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
            <input
                type="hidden"
                {...register(name, { required: required ? requiredText : undefined })}
                value={value}
                placeholder={placeholder}
            />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 text-right">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
