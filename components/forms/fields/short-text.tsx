import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface TextFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: string;
    pattern?: RegExp;
    patternMessage?: string;
    requiredText?: string;
    maxLength?: number;
    minLength?: number;
    type?: string;
    description?: string;
}

export const TEXTS = {
    required: "שדה זה נדרש",
    pattern: "הערך אינו תואם לתבנית הנדרשת",
    requiredText: "יש להזין תיאור"
};

export function ShortText({
    name,
    label,
    placeholder,
    defaultValue = "",
    pattern,
    required = true,
    patternMessage = TEXTS.pattern,
    tooltip,
    requiredText = TEXTS.requiredText,
    type = "text",
    description
}: TextFieldProps) {
    const {
        register,
        formState: { errors, isSubmitted }
    } = useFormContext();

    return (
        <div dir="rtl" className="space-y-2">
            <div className="flex items-center justify-start gap-1">
                {tooltip && <TooltipIcon text={tooltip} />}
                <Label htmlFor={name} className="text-right">
                    {label}
                </Label>
            </div>
            <Input
                id={name}
                type={type}
                defaultValue={defaultValue}
                {...register(name, {
                    required: required ? requiredText : undefined,
                    pattern: pattern ? { value: pattern, message: patternMessage } : undefined
                })}
                placeholder={placeholder}
                className={cn(
                    "text-right placeholder:text-right",
                    isSubmitted && errors[name] && "border-red-500 focus-visible:ring-red-500"
                )}
                dir="rtl"
            />
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
