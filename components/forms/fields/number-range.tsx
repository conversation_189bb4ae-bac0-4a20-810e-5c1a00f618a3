"use client";

import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { NumberInput } from "@/components/forms/fields/number-input";
import { Label } from "@/components/ui/label";
import { TooltipIcon } from "@/components/ui/tooltip-icon";

export interface NumberRangeProps extends BaseFieldProps {
    minName?: string;
    maxName?: string;
    minLabel?: string;
    maxLabel?: string;
    minSmallerThanMaxText?: string;
    minPlaceholder?: string;
    maxPlaceholder?: string;
}

export const TEXTS = {
    min: "מינימום",
    max: "מקסימום",
    minSmallerThanMax: "ערך מינימום חייב להיות קטן מערך מקסימום",
    minPlaceholder: "מינימום",
    maxPlaceholder: "מקסימום"
};

export function NumberRange({
    name,
    label,
    tooltip,
    required = false,
    minName,
    maxName,
    minLabel = TEXTS.min,
    maxLabel = TEXTS.max,
    minSmallerThanMaxText = TEXTS.minSmallerThanMax,
    minPlaceholder = TEXTS.minPlaceholder,
    maxPlaceholder = TEXTS.maxPlaceholder
}: NumberRangeProps) {
    const {
        watch,
        setValue,
        formState: { errors, isSubmitted },
        register,
        clearErrors
    } = useFormContext();

    const finalMinName = minName || `${name}.min`;
    const finalMaxName = maxName || `${name}.max`;

    const minValue = watch(finalMinName);
    const maxValue = watch(finalMaxName);

    useEffect(() => {
        register(name, {
            validate: () => {
                const min = minValue !== undefined && minValue !== "" ? Number(minValue) : undefined;
                const max = maxValue !== undefined && maxValue !== "" ? Number(maxValue) : undefined;

                if (min !== undefined && max !== undefined) {
                    if (min > max) {
                        return minSmallerThanMaxText;
                    }
                }

                return true;
            }
        });
    }, [register, name, minValue, maxValue, minSmallerThanMaxText]);

    useEffect(() => {
        if (errors[name]) {
            clearErrors(name);
        }

        const min = minValue !== undefined && minValue !== "" ? Number(minValue) : undefined;
        const max = maxValue !== undefined && maxValue !== "" ? Number(maxValue) : undefined;

        if (min !== undefined || max !== undefined) {
            setValue(name, { min, max }, { shouldValidate: true });
        } else {
            setValue(name, undefined);
        }
    }, [minValue, maxValue, setValue, name, errors, clearErrors]);

    return (
        <div className="space-y-3" dir="rtl">
            {label && (
                <div className="flex items-center justify-start gap-1">
                    {tooltip && <TooltipIcon text={tooltip} />}
                    <Label className="text-right font-medium">{label}</Label>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <NumberInput name={finalMinName} label={minLabel} required={required} placeholder={minPlaceholder} />

                <NumberInput name={finalMaxName} label={maxLabel} required={required} placeholder={maxPlaceholder} />
            </div>

            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
