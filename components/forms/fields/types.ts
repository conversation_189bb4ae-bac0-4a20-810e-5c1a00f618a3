interface BaseFieldProps {
    name: string;
    label: string;
    required?: boolean;
    tooltip?: string;
}

export interface TextFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: string;
    pattern?: RegExp;
    patternMessage?: string;
    requiredText?: string;
    maxLength?: number;
    minLength?: number;
}

export interface SelectFieldProps extends BaseFieldProps {
    options: { value: string; label: string }[];
    placeholder?: string;
    defaultValue?: string | string[];
    requiredText?: string;
}

export interface NumberFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: number;
    min?: number;
    max?: number;
    requiredText?: string;
}

export interface DateFieldProps extends BaseFieldProps {
    defaultValue?: Date;
    isFutureAllowed?: boolean;
    requiredText?: string;
    invalidText?: string;
    futureDateText?: string;
}

export interface AddressFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: string;
    requiredText?: string;
}

export interface FileUploadFieldProps extends BaseFieldProps {
    defaultValue?: File;
    onFileSelect?: (file: File | null) => void;
    acceptedFileType: string;
    acceptedFileTypeDescription: string;
    maxSizeInMB: number;
    fileTypeErrorMessage: string;
    fileSizeErrorMessage: (maxSize: number) => string;
    dragDropText: string;
    fileFormatDescription: (maxSize: number) => string;
    selectedFileText: string;
    requiredText?: string;
}

export type FieldType =
    | "short_text"
    | "long_text"
    | "single_select"
    | "multi_select"
    | "number_input"
    | "date_picker"
    | "address_select"
    | "bank_select";

export const FIELD_TYPE_DISPLAY: Record<FieldType, string> = {
    short_text: "שאלה פתוחה - טקסט קצר",
    long_text: "שאלה פתוחה - טקסט ארוך",
    single_select: "שאלה סגורה - בחירה יחידה",
    number_input: "מספר",
    multi_select: "שאלה סגורה - בחירה מרובה",
    date_picker: "תאריך",
    address_select: "כתובת - השלמה אוטומטית",
    bank_select: "בחירת בנק וסניף"
} as const;

type ToMetadata<T> = {
    [K in keyof T]: T[K] extends undefined ? never : T[K] extends { type: string } ? never : T[K];
} & { required: boolean };

type TextMetadata = ToMetadata<Omit<TextFieldProps, "name">>;
type SelectMetadata = ToMetadata<Omit<SelectFieldProps, "name">>;
type NumberMetadata = ToMetadata<Omit<NumberFieldProps, "name">>;
type DateMetadata = ToMetadata<Omit<DateFieldProps, "name">>;
type AddressMetadata = ToMetadata<Omit<AddressFieldProps, "name">>;

export type Dependency = {
    fieldId: string;
    selectedOptions: string[];
};

export type FieldMetadata = {
    type: FieldType;
    groupId: string;
    metadata: {
        short_text: TextMetadata;
        long_text: TextMetadata;
        single_select: SelectMetadata;
        multi_select: SelectMetadata;
        number_input: NumberMetadata;
        date_picker: DateMetadata;
        address_select: AddressMetadata;
        bank_select: TextMetadata;
    }[FieldType];
    hasDependency: boolean;
    dependentOn: string | null;
    dependencyValue: string;
    isFilter: boolean;
    dependencies: Dependency[];
};
