import { Control } from "react-hook-form";

import type { FormValues, Option, Question } from "@/components/forms/dynamic-questions-form/types";
import { AddressSelect } from "@/components/forms/fields/address-select";
import { BankSelect } from "@/components/forms/fields/bank-select";
import { DatePicker } from "@/components/forms/fields/date-picker";
import { LongText } from "@/components/forms/fields/long-text";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";

const renderField = (question: Question, control: Control<FormValues>, formValues: FormValues) => {
    const { type, metadata, id } = question;
    const commonProps = {
        name: id,
        label: metadata.label,
        required: metadata.required ?? false,
        placeholder: metadata.placeholder ?? "",
        tooltip: metadata.tooltip ?? "",
        description: metadata.description ?? "",
        control: control
    };

    const getOptions = (): Option[] => {
        if (!Array.isArray(metadata.options)) return [];
        return metadata.options as Option[];
    };

    switch (type) {
        case "short_text": {
            return <ShortText {...commonProps} />;
        }
        case "long_text": {
            return <LongText {...commonProps} />;
        }
        case "single_select": {
            return <SingleSelect {...commonProps} options={getOptions()} showSearch={metadata.showSearch} />;
        }
        case "multi_select": {
            return <MultiSelect {...commonProps} options={getOptions()} showSearch={metadata.showSearch} />;
        }
        case "number_input": {
            return <NumberInput {...commonProps} max={metadata.max} min={metadata.min} />;
        }
        case "date_picker": {
            return (
                <DatePicker
                    {...commonProps}
                    isFutureAllowed={metadata.isFutureAllowed}
                    allowPast={metadata.allowPast}
                />
            );
        }
        case "address_select": {
            return (
                <AddressSelect
                    {...commonProps}
                    defaultValue={typeof formValues[id] === "string" ? formValues[id] : undefined}
                />
            );
        }
        case "bank_select": {
            return (
                <BankSelect
                    {...commonProps}
                    defaultValue={
                        formValues[id] && typeof formValues[id] === "object" && !Array.isArray(formValues[id])
                            ? (formValues[id] as { bankCode: number; branchCode: number })
                            : undefined
                    }
                />
            );
        }
        default: {
            return null;
        }
    }
};

export { renderField };
