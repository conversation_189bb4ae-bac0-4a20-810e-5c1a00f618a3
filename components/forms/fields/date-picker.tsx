"use client";

import { format, isFuture, isValid } from "date-fns";
import { he } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react";
import { useCallback, useEffect, useLayoutEffect, useState } from "react";
import type { DateRange } from "react-day-picker";
import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface DateFieldProps extends BaseFieldProps {
    defaultValue?: Date;
    isFutureAllowed?: boolean;
    allowPast?: boolean;
    requiredText?: string;
    invalidText?: string;
    futureDateText?: string;
    pastDateText?: string;
    mode?: "single" | "range";
    placeholder?: string;
}

export const TEXTS = {
    required: "שדה זה נדרש",
    invalid: "תאריך לא תקין",
    futureDate: "לא ניתן להזין תאריך עתידי",
    pastDate: "לא ניתן להזין תאריך עבר",
    placeholder: "DD/MM/YYYY",
    from: "מ",
    to: "עד"
} as const;

export function DatePicker({
    name,
    label,
    tooltip,
    required = false,
    isFutureAllowed = true,
    allowPast = true,
    requiredText,
    pastDateText,
    mode = "single",
    placeholder
}: DateFieldProps) {
    const {
        setValue,
        watch,
        setError,
        clearErrors,
        formState: { errors, isSubmitted }
    } = useFormContext();

    const formValue = watch(name);

    const [selectedDate, setSelectedDate] = useState<Date | undefined>();
    const [dateRange, setDateRange] = useState<DateRange | undefined>({ from: undefined, to: undefined });
    const [tempRange, setTempRange] = useState<DateRange | undefined>({ from: undefined, to: undefined });

    const [open, setOpen] = useState(false);
    const [, setInputValue] = useState("");
    const [isInitialized, setIsInitialized] = useState(false);

    const formatDateRange = useCallback((from?: Date, to?: Date): string => {
        if (!from) return "";
        if (!to) return format(from, "dd/MM/yyyy");

        return `${format(from, "dd/MM/yyyy")} - ${format(to, "dd/MM/yyyy")}`;
    }, []);

    const validateDate = useCallback(
        (date: Date | null): string | null => {
            if (!date) {
                if (required) {
                    return requiredText || TEXTS.required;
                }
                return null;
            }

            if (!allowPast) {
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Start of today
                if (date < today) {
                    return pastDateText || TEXTS.pastDate;
                }
            }

            return null;
        },
        [required, allowPast, requiredText, pastDateText]
    );

    useEffect(() => {
        if (!isInitialized || formValue !== undefined) {
            if (mode === "single") {
                if (formValue instanceof Date && isValid(formValue)) {
                    setSelectedDate(formValue);
                    setInputValue(format(formValue, "dd/MM/yyyy"));
                } else if (formValue == null) {
                    setSelectedDate(undefined);
                    setInputValue("");
                }
            } else if (mode === "range") {
                if (formValue && typeof formValue === "object") {
                    const { from, to } = formValue as DateRange;

                    if (!open) {
                        setDateRange({
                            from: from instanceof Date && isValid(from) ? from : undefined,
                            to: to instanceof Date && isValid(to) ? to : undefined
                        });
                    }

                    if ((from instanceof Date && isValid(from)) || (to instanceof Date && isValid(to))) {
                        setInputValue(from ? formatDateRange(from, to) : to ? format(to, "dd/MM/yyyy") : "");
                    }
                } else if (formValue == null) {
                    setDateRange({ from: undefined, to: undefined });
                    setInputValue("");
                }
            }
            setIsInitialized(true);
        }
    }, [formValue, mode, formatDateRange, isInitialized, open]);

    useLayoutEffect(() => {
        if (open && mode === "range") {
            setTempRange({ from: undefined, to: undefined });
        }
    }, [open, mode]);

    const handleSelectDate = useCallback(
        (value: Date | undefined) => {
            setSelectedDate(value);
            setInputValue(value ? format(value, "dd/MM/yyyy") : "");

            // Validate the selected date
            const validationError = validateDate(value || null);
            if (validationError) {
                setError(name, { type: "manual", message: validationError });
            } else {
                clearErrors(name);
            }

            setValue(name, value, { shouldValidate: true });
            setOpen(false);
        },
        [name, setValue, setError, clearErrors, validateDate]
    );

    const handleTempRangeSelect = useCallback(
        (value: DateRange | undefined) => {
            if (!value) return;

            setTempRange(value);

            if (value.from && value.to) {
                // Validate the date range
                const fromError = validateDate(value.from);
                const toError = validateDate(value.to);
                const validationError = fromError || toError;

                if (validationError) {
                    setError(name, { type: "manual", message: validationError });
                } else {
                    clearErrors(name);
                }

                setDateRange(value);
                setValue(name, value, { shouldValidate: true });
                setInputValue(formatDateRange(value.from, value.to));

                setOpen(false);
            }
        },
        [name, setValue, setError, clearErrors, validateDate, formatDateRange]
    );

    const handleOpenChange = useCallback((newOpen: boolean) => {
        setOpen(newOpen);
    }, []);

    const formatDisplayValue = useCallback(() => {
        const getPlaceholder = () => {
            return placeholder || TEXTS.placeholder;
        };

        if (mode === "single") {
            if (!selectedDate)
                return <span className="text-muted-foreground text-base md:text-sm">{getPlaceholder()}</span>;
            return format(selectedDate, "dd/MM/yyyy");
        } else {
            if (!dateRange?.from)
                return <span className="text-muted-foreground text-base md:text-sm">{getPlaceholder()}</span>;
            if (!dateRange.to) return format(dateRange.from, "dd/MM/yyyy");
            return (
                <>
                    {format(dateRange.from, "dd/MM/yyyy")} - {format(dateRange.to, "dd/MM/yyyy")}
                </>
            );
        }
    }, [mode, selectedDate, dateRange, placeholder]);

    const singleDateCalendar = (
        <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelectDate}
            disabled={(date) => {
                if (!isFutureAllowed && isFuture(date)) return true;
                if (!allowPast) {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return date < today;
                }
                return false;
            }}
            initialFocus
            locale={he}
            dir="rtl"
        />
    );

    const rangeDateCalendar = (
        <Calendar
            mode="range"
            selected={tempRange}
            onSelect={handleTempRangeSelect}
            disabled={(date) => {
                if (!isFutureAllowed && isFuture(date)) return true;
                if (!allowPast) {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return date < today;
                }
                return false;
            }}
            initialFocus
            defaultMonth={dateRange?.from ? dateRange.from : undefined}
            locale={he}
            dir="rtl"
            modifiers={
                dateRange?.from && dateRange?.to
                    ? { selected: [{ from: dateRange.from, to: dateRange.to }] }
                    : { selected: [] }
            }
        />
    );

    return (
        <div className="space-y-2" dir="rtl">
            {label && (
                <div className="flex items-center justify-start gap-1">
                    {tooltip && <TooltipIcon text={tooltip} />}
                    <Label htmlFor={name} className="text-right">
                        {label}
                    </Label>
                </div>
            )}

            <Popover open={open} onOpenChange={handleOpenChange}>
                <PopoverTrigger asChild>
                    <Button
                        id={name}
                        variant="outline"
                        className={cn(
                            "w-full justify-between text-right font-normal",
                            ((mode === "single" && !selectedDate) || (mode === "range" && !dateRange?.from)) &&
                                "text-muted-foreground",
                            isSubmitted && errors[name] && "border-red-500 text-red-500 focus-visible:ring-red-500"
                        )}
                        type="button"
                    >
                        <span className="flex-1">{formatDisplayValue()}</span>
                        <CalendarIcon className="h-4 w-4 ms-2" />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                    {mode === "single" ? singleDateCalendar : rangeDateCalendar}
                </PopoverContent>
            </Popover>

            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
