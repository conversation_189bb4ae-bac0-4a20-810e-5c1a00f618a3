"use client";

import { <PERSON>, FileText, Link as <PERSON><PERSON><PERSON>, Upload } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { toast } from "sonner";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export interface FileUploadApprovalProps extends BaseFieldProps {
    defaultValue?: File;
    onFileSelect?: (file: File | null) => void;
    acceptedFileType: string[];
    acceptedFileTypeDescription: string;
    maxSizeInMB: number;
    fileTypeErrorMessage: string;
    fileSizeErrorMessage: (maxSize: number) => string;
    dragDropText: string;
    fileFormatDescription: (maxSize: number) => string;
    selectedFileText: string;
    requiredText?: string;
    documentTypeName: string;
    documentTypeDescription?: string;
    isUploaded?: boolean;
    uploadedFileUrl?: string;
    exampleFileUrl?: string;
    linkUrl?: string;
    statusText?: string;
    onExampleClick?: () => void;
    onLinkClick?: () => void;
}

const TEXTS = {
    uploadingFile: "מעלה קובץ...",
    uploadButton: "העלה קובץ",
    replaceButton: "החלף קובץ",
    viewDocument: "צפה במסמך",
    exampleDocument: "מסמך לדוגמא",
    exampleLink: "קישור להורדת המסמך",
    documentStatusUploaded: "הועלה",
    documentStatusPending: "ממתין להעלאה",
    selectFile: "בחר קובץ",
    uploadFile: "העלה קובץ",
    replaceFile: "החלף קובץ"
};

export function FileUploadApproval({
    name,
    required,
    requiredText,
    defaultValue,
    onFileSelect,
    acceptedFileType,
    acceptedFileTypeDescription,
    maxSizeInMB,
    fileTypeErrorMessage,
    fileSizeErrorMessage,
    fileFormatDescription,
    selectedFileText,
    documentTypeName,
    documentTypeDescription,
    isUploaded = false,
    uploadedFileUrl,
    exampleFileUrl,
    linkUrl,
    statusText,
    onExampleClick,
    onLinkClick
}: FileUploadApprovalProps) {
    const { register, setValue, watch } = useFormContext();
    const [selectedFile, setSelectedFile] = useState<File | null>(defaultValue || null);
    const [isUploading, setIsUploading] = useState(false);

    const fieldValue = watch(name);
    useEffect(() => {
        if (!fieldValue) {
            setSelectedFile(null);
            const fileInput = document.getElementById(name) as HTMLInputElement;
            if (fileInput) {
                fileInput.value = "";
            }
        }
    }, [fieldValue, name]);

    const handleFileSelect = useCallback(
        (file: File) => {
            if (!file) return;

            const allowedTypesArray = acceptedFileType.map((type) => type.toLowerCase());
            const fileTypeLower = file.type.toLowerCase();

            if (
                !allowedTypesArray.includes(fileTypeLower) &&
                !allowedTypesArray.includes(fileTypeLower.split("/")[0] + "/*") &&
                !allowedTypesArray.find((type) => type.endsWith("/*") && fileTypeLower.startsWith(type.slice(0, -1)))
            ) {
                if (allowedTypesArray.length > 0 && !allowedTypesArray.includes("*/*")) {
                    toast.error(fileTypeErrorMessage, {
                        duration: 5000,
                        description: acceptedFileTypeDescription
                    });
                    return;
                }
            }

            const fileSizeInMB = file.size / (1024 * 1024);
            if (fileSizeInMB > maxSizeInMB) {
                toast.error(fileSizeErrorMessage(maxSizeInMB));
                return;
            }

            setSelectedFile(file);
            setValue(name, file, { shouldValidate: true });
        },
        [
            name,
            setValue,
            acceptedFileType,
            acceptedFileTypeDescription,
            maxSizeInMB,
            fileTypeErrorMessage,
            fileSizeErrorMessage
        ]
    );

    const handleUpload = useCallback(async () => {
        if (!selectedFile || !onFileSelect) return;

        setIsUploading(true);
        try {
            await onFileSelect(selectedFile);
        } finally {
            setIsUploading(false);
        }
    }, [selectedFile, onFileSelect]);

    const triggerFileInput = () => {
        const fileInput = document.getElementById(name) as HTMLInputElement;
        if (fileInput) {
            fileInput.click();
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <FileText className="h-6 w-6 text-primary" />
                        <div>
                            <CardTitle className="text-lg">{documentTypeName}</CardTitle>
                            {documentTypeDescription && (
                                <CardDescription className="mt-1">{documentTypeDescription}</CardDescription>
                            )}
                        </div>
                    </div>
                    <TooltipProvider>
                        <div className="flex items-center gap-2">
                            {linkUrl && (
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={onLinkClick}
                                            className="h-8 w-8"
                                            aria-label={TEXTS.exampleLink}
                                        >
                                            <LinkIcon className="h-4 w-4" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{TEXTS.exampleLink}</p>
                                    </TooltipContent>
                                </Tooltip>
                            )}
                            {exampleFileUrl && (
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={onExampleClick}
                                            className="h-8 w-8"
                                            aria-label={TEXTS.exampleDocument}
                                        >
                                            <FileText className="h-4 w-4" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{TEXTS.exampleDocument}</p>
                                    </TooltipContent>
                                </Tooltip>
                            )}
                            <div className="flex items-center gap-2">
                                {isUploaded && uploadedFileUrl && (
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button variant="ghost" size="icon" asChild className="h-8 w-8">
                                                <Link
                                                    href={uploadedFileUrl}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    aria-label={TEXTS.viewDocument}
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{TEXTS.viewDocument}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                )}
                                {isUploaded ? (
                                    <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-300">
                                        {statusText || TEXTS.documentStatusUploaded}
                                    </span>
                                ) : (
                                    <span className="text-xs font-medium text-amber-600 bg-amber-100 px-2 py-1 rounded-full dark:bg-amber-900 dark:text-amber-300">
                                        {statusText || TEXTS.documentStatusPending}
                                    </span>
                                )}
                            </div>
                        </div>
                    </TooltipProvider>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                <input
                    type="file"
                    id={name}
                    accept={acceptedFileType.join(",")}
                    className="hidden"
                    data-testid="file-input"
                    onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                            handleFileSelect(file);
                        }
                    }}
                />

                {!selectedFile ? (
                    <div className="space-y-3">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={triggerFileInput}
                            className="w-full h-20 border-2 border-dashed"
                        >
                            <div className="flex items-center gap-2">
                                <Upload className="w-5 h-5" />
                                <div className="text-center">
                                    <p className="text-sm font-medium">{TEXTS.selectFile}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {fileFormatDescription(maxSizeInMB)}
                                    </p>
                                </div>
                            </div>
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-3">
                        <div className="text-sm text-muted-foreground p-3 bg-muted/30 rounded-lg">
                            {selectedFileText}: {selectedFile.name}
                        </div>
                        <div className="flex gap-2">
                            <Button type="button" variant="outline" onClick={triggerFileInput} className="flex-1">
                                {TEXTS.selectFile}
                            </Button>
                            <Button onClick={handleUpload} disabled={isUploading} className="flex-1">
                                <Upload className="w-4 h-4 mr-2" />
                                {isUploading ? TEXTS.uploadingFile : isUploaded ? TEXTS.replaceFile : TEXTS.uploadFile}
                            </Button>
                        </div>
                    </div>
                )}
                <input type="hidden" {...register(name, { required: required ? requiredText : false })} />
            </CardContent>
        </Card>
    );
}
