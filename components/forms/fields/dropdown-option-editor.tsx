"use client";

import {
    closestCenter,
    DndContext,
    DragEndEvent,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors
} from "@dnd-kit/core";
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TooltipIcon } from "@/components/ui/tooltip-icon";

export const TEXTS = {
    optionsLabel: "אפשרויות",
    addOption: "הוסף אפשרות",
    optionPlaceholder: "הזן תווית לאפשרות",
    noOptions: "לא הוגדרו אפשרויות עדיין",
    duplicateLabel: "תווית זו כבר קיימת"
};

export interface OptionWithId {
    id: string;
    label: string;
}

interface SortableOptionProps {
    option: OptionWithId;
    index: number;
    onUpdate: (index: number, newLabel: string) => void;
}

function SortableOption({ option, index, onUpdate }: SortableOptionProps) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: option.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={`flex items-center gap-2 p-3 border border-gray-200 rounded-lg bg-gray-50 ${
                isDragging ? "opacity-50" : ""
            }`}
        >
            <div {...attributes} {...listeners} className="cursor-grab active:cursor-grabbing">
                <GripVertical className="h-4 w-4 text-gray-400" />
            </div>

            <div className="flex-1">
                <Input
                    value={option.label}
                    onChange={(e) => onUpdate(index, e.target.value)}
                    placeholder={TEXTS.optionPlaceholder}
                    className="text-right placeholder:text-right"
                    dir="rtl"
                />
            </div>
        </div>
    );
}

interface DropdownOptionEditorProps {
    name: string;
    label?: string;
    tooltip?: string;
    required?: boolean;
}

export function DropdownOptionEditor({ name, label, tooltip, required = false }: DropdownOptionEditorProps) {
    const {
        setValue,
        watch,
        formState: { errors, isSubmitted }
    } = useFormContext();
    const [options, setOptions] = useState<OptionWithId[]>([]);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates
        })
    );

    const fieldValue = watch(name);

    useEffect(() => {
        if (Array.isArray(fieldValue)) {
            const formattedOptions = fieldValue.map((opt: unknown) => {
                if (typeof opt === "string") {
                    return { id: uuidv4(), label: opt };
                }
                if (typeof opt === "object" && opt && opt !== null && "label" in opt) {
                    const optObj = opt as { id?: string; label: string };
                    return {
                        id: optObj.id || uuidv4(),
                        label: optObj.label
                    };
                }
                return { id: uuidv4(), label: String(opt) };
            });
            setOptions(formattedOptions);
        } else if (!fieldValue) {
            setOptions([]);
        }
    }, [fieldValue]);

    const updateFormValue = (newOptions: OptionWithId[]) => {
        setValue(name, newOptions, { shouldValidate: true });
    };

    const addOption = () => {
        const newOption: OptionWithId = {
            id: uuidv4(),
            label: ""
        };
        const newOptions = [...options, newOption];
        setOptions(newOptions);
        updateFormValue(newOptions);
    };

    const updateOption = (index: number, newLabel: string) => {
        const trimmedLabel = newLabel.trim();
        const isDuplicate = options.some(
            (option, idx) => idx !== index && option.label.trim().toLowerCase() === trimmedLabel.toLowerCase()
        );

        if (isDuplicate && trimmedLabel !== "") {
            return;
        }

        const newOptions = [...options];
        newOptions[index] = { ...newOptions[index], label: newLabel };
        setOptions(newOptions);
        updateFormValue(newOptions);
    };

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            const oldIndex = options.findIndex((option) => option.id === active.id);
            const newIndex = options.findIndex((option) => option.id === over.id);

            const newOptions = arrayMove(options, oldIndex, newIndex);
            setOptions(newOptions);
            updateFormValue(newOptions);
        }
    };

    return (
        <div className="space-y-4" dir="rtl">
            {label && (
                <div className="flex items-center justify-start gap-1">
                    {tooltip && <TooltipIcon text={tooltip} />}
                    <Label className="text-right block">
                        {label}
                        {required && <span className="text-red-500 mr-1">*</span>}
                    </Label>
                </div>
            )}

            <div className="space-y-2">
                {options.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                        {TEXTS.noOptions}
                    </div>
                ) : (
                    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                        <SortableContext
                            items={options.map((option) => option.id)}
                            strategy={verticalListSortingStrategy}
                        >
                            <div className="space-y-2">
                                {options.map((option, index) => (
                                    <SortableOption
                                        key={option.id}
                                        option={option}
                                        index={index}
                                        onUpdate={updateOption}
                                    />
                                ))}
                            </div>
                        </SortableContext>
                    </DndContext>
                )}

                <Button type="button" variant="outline" onClick={addOption} className="w-full">
                    <Plus className="h-4 w-4 ml-2" />
                    {TEXTS.addOption}
                </Button>
            </div>

            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 text-right">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
