import { useFormContext } from "react-hook-form";

import { BaseFieldProps } from "@/components/forms/fields/base-field";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TooltipIcon } from "@/components/ui/tooltip-icon";
import { cn } from "@/lib/utils";

export interface NumberFieldProps extends BaseFieldProps {
    placeholder: string;
    defaultValue?: number;
    min?: number;
    max?: number;
    requiredText?: string;
}

export const TEXTS = {
    required: "שדה זה נדרש",
    min: (min: number) => `הערך חייב להיות גדול מ-${min}`,
    max: (max: number) => `הערך חייב להיות קטן מ-${max}`,
    type: "יש להזין מספר תקין",
    requiredText: "יש להזין מספר"
};

export function NumberInput({
    name,
    label,
    placeholder,
    defaultValue,
    min,
    max,
    required = true,
    requiredText = TEXTS.requiredText,
    tooltip
}: NumberFieldProps) {
    const {
        register,
        formState: { errors, isSubmitted }
    } = useFormContext();

    return (
        <div dir="rtl">
            <div className="flex items-center justify-start gap-1">
                {tooltip && <TooltipIcon text={tooltip} />}
                <Label htmlFor={name} className="text-right mb-2">
                    {label}
                </Label>
            </div>
            <Input
                type="number"
                id={name}
                defaultValue={defaultValue}
                onWheel={(e) => e.currentTarget.blur()}
                {...register(name, {
                    required: required ? requiredText : undefined,
                    valueAsNumber: true,
                    min:
                        min !== undefined
                            ? {
                                  value: min,
                                  message: TEXTS.min(min)
                              }
                            : undefined,
                    max:
                        max !== undefined
                            ? {
                                  value: max,
                                  message: TEXTS.max(max)
                              }
                            : undefined
                })}
                placeholder={placeholder}
                className={cn(
                    "text-right placeholder:text-right",
                    isSubmitted && errors[name] && "border-red-500 focus-visible:ring-red-500"
                )}
                dir="rtl"
            />
            {isSubmitted && errors[name] && (
                <p className="text-sm text-red-500 ms-1">{errors[name]?.message as string}</p>
            )}
        </div>
    );
}
