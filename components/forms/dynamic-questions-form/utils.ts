import type { Answer, Condition, FormValue, Question, QuestionConditionLink, QuestionGroup } from "./types";

const createDefaultFormValues = (questions: Question[]): Record<string, FormValue> => {
    return questions.reduce((acc: Record<string, FormValue>, q) => {
        switch (q.type) {
            case "multi_select":
                acc[q.id] = [];
                break;
            case "number_input":
                acc[q.id] = null;
                break;
            case "date_picker":
                acc[q.id] = null;
                break;
            case "bank_select":
                acc[q.id] = { bankCode: "", branchCode: "" };
                break;
            case "short_text":
            case "long_text":
            case "single_select":
            case "address_select":
            default:
                acc[q.id] = "";
                break;
        }
        return acc;
    }, {});
};

const processAnswers = (answers: Answer[], questions: Question[]): Record<string, FormValue> => {
    return (answers || []).reduce((acc: Record<string, FormValue>, answer) => {
        const question = questions.find((q) => q.id === answer.question_id);
        let value: FormValue = answer.answer;

        if (question && answer.answer != null && answer.answer !== "") {
            try {
                switch (question.type) {
                    case "number_input": {
                        const parsedNum = parseFloat(answer.answer);
                        value = isNaN(parsedNum) ? null : parsedNum;
                        break;
                    }
                    case "date_picker": {
                        if (typeof answer.answer === "string") {
                            try {
                                const dateStr = answer.answer;
                                const parsedDate = new Date(dateStr);
                                value = isNaN(parsedDate.getTime()) ? null : parsedDate;
                            } catch {
                                value = null;
                            }
                        } else {
                            value = null;
                        }
                        break;
                    }
                    case "single_select": {
                        try {
                            const parsed = JSON.parse(answer.answer);
                            if (parsed && typeof parsed === "object" && "id" in parsed && "label" in parsed) {
                                value = { id: parsed.id, label: parsed.label };
                            } else {
                                value = "";
                            }
                        } catch {
                            value = "";
                        }
                        break;
                    }
                    case "multi_select": {
                        try {
                            const parsed = JSON.parse(answer.answer);
                            if (Array.isArray(parsed)) {
                                value = parsed
                                    .filter(
                                        (item) => item && typeof item === "object" && "id" in item && "label" in item
                                    )
                                    .map((item) => ({ id: item.id, label: item.label }));
                            } else {
                                value = [];
                            }
                        } catch {
                            value = [];
                        }
                        break;
                    }
                    case "bank_select": {
                        try {
                            value = JSON.parse(answer.answer);
                        } catch {
                            value = answer.answer;
                        }
                        break;
                    }
                }
            } catch {
                value = answer.answer;
            }
        }

        if (value !== null && value !== undefined) {
            acc[answer.question_id] = value;
        }
        return acc;
    }, {});
};

const areConditionsMet = (
    question: Question,
    formValues: Record<string, unknown>,
    questionConditionLinks: QuestionConditionLink[],
    conditions: Condition[]
): boolean => {
    const links = questionConditionLinks.filter((link) => link.question_id === question.id);

    if (links.length === 0) return true;

    const questionConditions = links
        .map((link) => conditions.find((condition) => condition.id === link.condition_id))
        .filter(Boolean) as Condition[];

    if (questionConditions.length === 0) return true;

    return questionConditions.every((condition) => {
        const dependentValue = formValues[condition.question_id];

        if (condition.type === "in" && condition.value && typeof condition.value === "object") {
            if (Array.isArray(condition.value)) {
                const conditionIds = (condition.value as unknown[]).map((item) => {
                    if (typeof item === "object" && item && "id" in item) {
                        const itemObj = item as { id: string };
                        return itemObj.id;
                    }
                    return item;
                });

                if (typeof dependentValue === "object" && dependentValue && "id" in dependentValue) {
                    const dependentObj = dependentValue as { id: string };
                    return conditionIds.includes(dependentObj.id);
                }

                if (Array.isArray(dependentValue)) {
                    return dependentValue.some((item) => {
                        if (typeof item === "object" && item && "id" in item) {
                            const itemObj = item as { id: string };
                            return conditionIds.includes(itemObj.id);
                        }
                        return conditionIds.includes(item as string);
                    });
                }
                return conditionIds.includes(dependentValue as string);
            }
            return false;
        } else if (condition.type === "range" && condition.value && typeof condition.value === "object") {
            if (Array.isArray(condition.value)) {
                if (typeof dependentValue === "object" && dependentValue && "id" in dependentValue) {
                    const dependentObj = dependentValue as { id: string };
                    return (condition.value as string[]).includes(dependentObj.id);
                }

                if (Array.isArray(dependentValue)) {
                    return dependentValue.some((item) => {
                        if (typeof item === "object" && item && "id" in item) {
                            const itemObj = item as { id: string };
                            return (condition.value as string[]).includes(itemObj.id);
                        }
                        return (condition.value as string[]).includes(item as string);
                    });
                }
                return (condition.value as string[]).includes(dependentValue as string);
            }

            const min = (condition.value as { min?: number }).min;
            const max = (condition.value as { max?: number }).max;
            const numValue = Number(dependentValue);

            if (isNaN(numValue)) return false;

            if (min !== undefined && max !== undefined) {
                return numValue >= min && numValue <= max;
            } else if (min !== undefined) {
                return numValue >= min;
            } else if (max !== undefined) {
                return numValue <= max;
            }
            return true;
        } else if (condition.type === "date_range" && condition.value && typeof condition.value === "object") {
            const dateValue =
                dependentValue instanceof Date
                    ? dependentValue
                    : typeof dependentValue === "string"
                      ? new Date(dependentValue)
                      : null;

            if (!dateValue || isNaN(dateValue.getTime())) return false;

            const opVal = condition.value as { operator?: string; days_from_today?: number };
            if (opVal.operator && opVal.days_from_today !== undefined) {
                const operator = opVal.operator;
                const daysFromToday = Number(opVal.days_from_today);

                if (isNaN(daysFromToday)) return false;

                const today = new Date();
                const todayUTC = Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate());
                const dateValueUTC = Date.UTC(
                    dateValue.getUTCFullYear(),
                    dateValue.getUTCMonth(),
                    dateValue.getUTCDate()
                );
                const diffTime = Math.abs(dateValueUTC - todayUTC);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                return operator === "greater_than" ? diffDays > daysFromToday : diffDays < daysFromToday;
            }
            return true;
        }

        return true;
    });
};

const groupQuestionsByGroupId = (
    questions: Question[],
    questionGroups: Record<string, QuestionGroup>
): Record<string, { group: QuestionGroup; questions: Question[] }> => {
    return questions.reduce(
        (acc, question) => {
            const groupId = question.group_id;
            if (!acc[groupId]) {
                acc[groupId] = {
                    group: questionGroups[groupId],
                    questions: []
                };
            }
            acc[groupId].questions.push(question);
            return acc;
        },
        {} as Record<string, { group: QuestionGroup; questions: Question[] }>
    );
};

export { areConditionsMet, createDefaultFormValues, groupQuestionsByGroupId, processAnswers };
