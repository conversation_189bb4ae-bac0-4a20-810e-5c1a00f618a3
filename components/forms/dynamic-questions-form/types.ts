import type { Database } from "@/types/database.types";

export type Bank = {
    bankCode: number;
    bankName: string;
    branches: {
        branchCode: number;
        branchName: string;
    }[];
};

export type BankSelection = {
    bankCode: string | number;
    branchCode: string | number;
};

export type QuestionSection = Database["public"]["Enums"]["question_section"];

export interface QuestionMetadata {
    label: string;
    required?: boolean;
    placeholder?: string;
    tooltip?: string;
    options?: string[] | Array<{ id: string; label: string }>;
    description?: string;
    max?: number;
    min?: number;
    max_date?: string;
    min_date?: string;
    isFutureAllowed?: boolean;
    allowPast?: boolean;
    showSearch?: boolean;
}

export type QuestionType = Database["public"]["Enums"]["question_type"];

export interface Question extends Omit<Database["public"]["Tables"]["questions"]["Row"], "metadata"> {
    metadata: QuestionMetadata;
    type: QuestionType;
    section: QuestionSection;
}

export interface QuestionGroup {
    id: string;
    name: string;
    description?: string;
    created_at: string | null;
    updated_at: string | null;
}

export type Condition = Database["public"]["Tables"]["conditions"]["Row"] & {
    condition?: {
        condition_type: "equals" | "in" | "not" | "greater_than" | "less_than";
        condition_value: string | string[] | number;
    };
    type?: Database["public"]["Enums"]["condition_type"] | "greater_than" | "less_than";
    value?:
        | {
              selected_options?: string[];
              min?: number;
              max?: number;
              operator?: "greater_than" | "less_than";
              days_from_today?: number;
          }
        | number;
};

export type QuestionConditionLink = Database["public"]["Tables"]["link_question_to_condition"]["Row"];

export type Answer = Database["public"]["Tables"]["answers"]["Row"];

export type Option = { id: string; label: string };

export type DropdownOption = { id: string; label: string };
export type FormValue = string | number | Date | string[] | null | BankSelection | DropdownOption | DropdownOption[];
export type FormValues = Record<string, FormValue>;
