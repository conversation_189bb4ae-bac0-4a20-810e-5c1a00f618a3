"use client";

import { useUser } from "@clerk/nextjs";
import { forwardRef, useCallback, useImperative<PERSON><PERSON><PERSON>, useMemo, useRef } from "react";
import { FormProvider } from "react-hook-form";
import { toast } from "sonner";

import type { ShapedCondition, ShapedQuestion } from "@/app/actions/dynamic-questions-form-actions";
import { upsertAnswers } from "@/app/actions/dynamic-questions-form-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { ErrorState } from "@/components/forms/dynamic-questions-form/components/error-state";
import { FormHeader } from "@/components/forms/dynamic-questions-form/components/form-header";
import { areConditionsMet, groupQuestionsByGroupId } from "@/components/forms/dynamic-questions-form/utils";
import { renderField } from "@/components/forms/fields/render-field";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useBankData } from "@/hooks/dynamic-questions-form/use-bank-data";
import { useDynamicQuestionsState } from "@/hooks/dynamic-questions-form/use-dynamic-questions-state";
import { useQuestionsData } from "@/hooks/dynamic-questions-form/use-questions-data";

import type { Answer, FormValues, QuestionConditionLink, QuestionGroup, QuestionSection } from "./types";

const TEXTS = {
    submitButton: "שלח",
    loading: "טוען שאלות...",
    errorFetching: "שגיאה בטעינת השאלות",
    formSubmitSuccess: "הטופס נשלח בהצלחה!",
    formSubmitError: "שגיאה בשליחת הטופס",
    errorFetchingAnswers: "שגיאה בטעינת התשובות הקיימות",
    sending: "שולח..."
};

export interface DynamicQuestionsFormHandle {
    submit: () => void;
}

export interface DynamicQuestionsFormProps {
    sections: QuestionSection[];
    scholarshipId?: string;
    hideSubmit?: boolean;
    hideTitle?: boolean;
    titleStyle?: string;
    columns?: 1 | 2;
    onSubmitEnd?: (success: boolean) => void;
    alwaysSuccessOnNoChanges?: boolean;
    suppressSuccessToast?: boolean;
    overrideUserId?: string;
    prefetchedData?: {
        questions: ShapedQuestion[];
        questionGroups: Record<string, QuestionGroup>;
        conditions: ShapedCondition[];
        questionConditionLinks: QuestionConditionLink[];
        answers: Answer[];
    };
}

export const DynamicQuestionsForm = forwardRef<DynamicQuestionsFormHandle, DynamicQuestionsFormProps>(
    function DynamicQuestionsForm(
        {
            sections,
            scholarshipId,
            hideSubmit = false,
            hideTitle = false,
            columns = 2,
            titleStyle,
            onSubmitEnd,
            alwaysSuccessOnNoChanges,
            suppressSuccessToast = false,
            overrideUserId,
            prefetchedData
        },
        ref
    ) {
        const { user, isLoaded } = useUser();
        const isSubmittingRef = useRef(false);

        const effectiveUserId = overrideUserId || user?.id;

        const questionsState = useDynamicQuestionsState();
        const { banks: banksCache } = useBankData();

        const methods = useQuestionsData({
            sections,
            scholarshipId,
            isLoaded: overrideUserId ? true : isLoaded, // If overrideUserId is provided, consider it "loaded"
            user: user ?? null,
            overrideUserId,
            prefetchedData,
            dispatch: questionsState.dispatch,
            lastFetchedSections: questionsState.lastFetchedSections,
            lastFetchedScholarshipId: questionsState.lastFetchedScholarshipId,
            isSubmittingRef
        });

        const { questions, questionGroups, conditions, questionConditionLinks, loading, error } = questionsState;

        const formValues = methods.watch();

        const visibleQuestions = useMemo(
            () =>
                questions.filter((question) =>
                    areConditionsMet(question, formValues, questionConditionLinks, conditions)
                ),
            [questions, formValues, questionConditionLinks, conditions]
        );

        const questionById = useMemo(() => {
            const map = new Map<string, (typeof questions)[number]>();
            questions.forEach((q) => map.set(q.id, q));
            return map;
        }, [questions]);

        const groupedQuestions = useMemo(
            () => groupQuestionsByGroupId(questions, questionGroups),
            [questions, questionGroups]
        );

        const onSubmit = useCallback(
            async (values: FormValues) => {
                if (!effectiveUserId) {
                    toast.error("User not authenticated. Cannot submit.");
                    isSubmittingRef.current = false;
                    onSubmitEnd?.(false);
                    return;
                }
                const userId = effectiveUserId;

                try {
                    const visibleQuestionIds = visibleQuestions.map((question) => question.id);

                    const answeredQuestionIds = Object.keys(values).filter((questionId) => {
                        const value = values[questionId];
                        if (value === null || value === undefined || value === "") return false;
                        if (Array.isArray(value) && value.length === 0) return false;
                        return true;
                    });

                    const hiddenQuestionIds = answeredQuestionIds.filter(
                        (questionId) => !visibleQuestionIds.includes(questionId)
                    );

                    const answersToUpsert = Object.entries(values)
                        .filter(
                            ([questionId, value]) =>
                                visibleQuestionIds.includes(questionId) &&
                                value !== null &&
                                value !== undefined &&
                                value !== ""
                        )
                        .map(([questionId, value]) => {
                            const question = questionById.get(questionId);

                            if (
                                question?.type === "bank_select" &&
                                value &&
                                typeof value === "object" &&
                                !Array.isArray(value) &&
                                "bankCode" in value &&
                                "branchCode" in value
                            ) {
                                let bankName = "";
                                let branchName = "";
                                const bankCode = value.bankCode;
                                const branchCode = value.branchCode;

                                const banks = banksCache;
                                if (Array.isArray(banks) && banks.length > 0) {
                                    const bank = banks.find((b) => String(b.bankCode) === String(bankCode));
                                    if (bank) {
                                        bankName = bank.bankName;
                                        const branch = bank.branches?.find(
                                            (br) => String(br.branchCode) === String(branchCode)
                                        );
                                        if (branch) branchName = branch.branchName;
                                    }
                                }
                                return {
                                    question_id: questionId,
                                    user_id: userId,
                                    answer: JSON.stringify({
                                        bankCode,
                                        bankName,
                                        branchCode,
                                        branchName
                                    })
                                };
                            }

                            if (
                                question?.type === "single_select" &&
                                value &&
                                typeof value === "object" &&
                                !Array.isArray(value) &&
                                "id" in value &&
                                "label" in value
                            ) {
                                return {
                                    question_id: questionId,
                                    user_id: userId,
                                    answer: JSON.stringify({
                                        id: value.id,
                                        label: value.label
                                    })
                                };
                            }

                            if (question?.type === "multi_select" && Array.isArray(value)) {
                                const processedArray = value.map((item) => {
                                    if (typeof item === "object" && item && "id" in item && "label" in item) {
                                        return {
                                            id: item.id,
                                            label: item.label
                                        };
                                    }

                                    return {
                                        id: String(item),
                                        label: String(item)
                                    };
                                });
                                return {
                                    question_id: questionId,
                                    user_id: userId,
                                    answer: JSON.stringify(processedArray)
                                };
                            }

                            return {
                                question_id: questionId,
                                user_id: userId,
                                answer:
                                    value instanceof Date
                                        ? value.toISOString()
                                        : Array.isArray(value)
                                          ? JSON.stringify(value)
                                          : String(value)
                            };
                        });

                    if (answersToUpsert.length === 0 && hiddenQuestionIds.length === 0) {
                        if (typeof alwaysSuccessOnNoChanges !== "undefined" && alwaysSuccessOnNoChanges) {
                            isSubmittingRef.current = false;
                            onSubmitEnd?.(true);
                        } else {
                            toast.info("No changes detected or nothing to submit.");
                            isSubmittingRef.current = false;
                        }
                        return;
                    }

                    const result = await upsertAnswers(userId, answersToUpsert, hiddenQuestionIds);

                    if (result.success) {
                        if (!suppressSuccessToast) {
                            toast.success(TEXTS.formSubmitSuccess);
                        }
                        isSubmittingRef.current = false;
                        onSubmitEnd?.(true);
                    } else {
                        toast.error(result.error || TEXTS.formSubmitError);
                        isSubmittingRef.current = false;
                        onSubmitEnd?.(false);
                    }
                } catch (error) {
                    console.error("Error submitting form:", error);
                    toast.error(TEXTS.formSubmitError);
                    isSubmittingRef.current = false;
                    onSubmitEnd?.(false);
                }
            },
            [
                effectiveUserId,
                visibleQuestions,
                banksCache,
                questionById,
                onSubmitEnd,
                alwaysSuccessOnNoChanges,
                suppressSuccessToast,
                isSubmittingRef
            ]
        );

        useImperativeHandle(ref, () => ({
            submit: () => {
                isSubmittingRef.current = true;

                methods.handleSubmit(onSubmit, () => {
                    isSubmittingRef.current = false;
                    onSubmitEnd?.(false);
                })();
            }
        }));

        if (loading) {
            return <LoadingIcon text={TEXTS.loading} />;
        }

        if (error) {
            return <ErrorState message={error} />;
        }

        const allVisibleQuestions = visibleQuestions;

        const gridCols = typeof columns === "number" && columns === 1 ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2";

        return (
            <FormProvider {...methods}>
                <Form {...methods}>
                    <form dir="rtl" onSubmit={methods.handleSubmit(onSubmit)} className="w-full">
                        <div className="p-4 md:p-0 w-full">
                            <div className="w-full flex flex-col">
                                {Object.values(groupedQuestions).map(({ group, questions: groupQuestions }) => (
                                    <div key={group?.id || "ungrouped"} className="mb-8">
                                        <FormHeader
                                            title={group?.name || "פרטים"}
                                            hideTitle={hideTitle}
                                            titleStyle={titleStyle}
                                        />
                                        <div className={`grid ${gridCols} gap-6`}>
                                            {groupQuestions
                                                .filter((question) =>
                                                    areConditionsMet(
                                                        question,
                                                        formValues,
                                                        questionConditionLinks,
                                                        conditions
                                                    )
                                                )
                                                .map((question) => (
                                                    <div
                                                        key={question.id}
                                                        className="flex flex-col pb-2 border-b border-gray-100 last:border-b-0"
                                                    >
                                                        {renderField(question, methods.control, formValues)}
                                                    </div>
                                                ))}
                                        </div>
                                    </div>
                                ))}
                                {allVisibleQuestions.length > 0 && !hideSubmit && (
                                    <div className="mt-8 flex justify-end">
                                        <Button
                                            type="submit"
                                            disabled={methods.formState.isSubmitting}
                                            className="w-full text-base font-semibold"
                                            style={{ minWidth: "100%" }}
                                        >
                                            {methods.formState.isSubmitting ? TEXTS.sending : TEXTS.submitButton}
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </form>
                </Form>
            </FormProvider>
        );
    }
);
