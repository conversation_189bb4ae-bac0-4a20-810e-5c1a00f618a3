interface FormHeaderProps {
    title: string;
    hideTitle?: boolean;
    titleStyle?: string;
}

export function FormHeader({ title, hideTitle, titleStyle }: FormHeaderProps) {
    if (hideTitle) return null;

    return (
        <h2
            className={`text-xl font-bold text-gray-800 mb-4 border-b border-gray-200 pb-2 opacity-90 ${
                titleStyle || ""
            }`}
        >
            {title}
        </h2>
    );
}
