"use client";

import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

import {
    createScholarship,
    getScholarship,
    linkScholarshipGroups,
    linkScholarshipTestimonials,
    updateScholarship,
    uploadScholarshipImage
} from "@/app/actions/scholarship-crud-actions";
import {
    getAllQuestionsForConditionSelector,
    getScholarshipConditionGroupsForScholarship,
    getScholarshipDefinedConditions,
    getScholarshipDocumentTypes,
    updateScholarshipConditionGroups,
    updateScholarshipDefinedConditions,
    updateScholarshipDocumentTypes
} from "@/app/actions/scholarship-form-actions";
import {
    type ConditionDependency,
    type ConditionQuestion,
    ConditionSelector
} from "@/components/forms/fields/condition-selector";
import { DatePicker } from "@/components/forms/fields/date-picker";
import { Option } from "@/components/forms/fields/dropdown-base";
import { FileUpload } from "@/components/forms/fields/file-upload";
import { LongText } from "@/components/forms/fields/long-text";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { ScholarshipFormSkeleton } from "@/components/forms/scholarship-form-skeleton";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormField } from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useDocumentTypes } from "@/hooks/use-document-types";
import { useScholarshipConditionGroups } from "@/hooks/use-scholarship-condition-groups";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";
import { useTestimonials } from "@/hooks/use-testimonials";
import { type ScholarshipGroup } from "@/lib/scholarship-group-constants";
import { cn } from "@/lib/utils";
import {
    BackendConditionDependency,
    transformConditionsFromDatabase,
    transformConditionsToDatabase
} from "@/utils/condition-utils";
import { mapIdsToOptions } from "@/utils/form-utils";
import { textToArray } from "@/utils/formatters";

export const TEXTS = {
    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "המלגה עודכנה בהצלחה",
    editErrorMessage: "שגיאה בעדכון המלגה",
    editLoadingMessage: "טוען מלגה...",
    editNotFoundMessage: "המלגה לא נמצאה",
    createSuccessMessage: "המלגה נוצרה בהצלחה",
    createErrorMessage: "שגיאה ביצירת המלגה",
    amountValidationError: "סכום מקסימלי חייב להיות גדול או שווה לסכום המינימלי",
    questionsLoadError: "שגיאה בטעינת שאלות זמינות עבור תנאים",

    updateButtonText: "עדכן מלגה",
    cancelButtonText: "ביטול",
    createButtonText: "צור מלגה",

    statusSection: "סטטוס",
    generalInfoSection: "מידע כללי",
    associationsSection: "שיוכים",
    centralDetailsSection: "פרטים מרכזיים",
    eligibilityConditionsSection: "תנאי זכאות",
    contactDetailsSection: "פרטי יצירת קשר",

    activityStatus: {
        label: "סטטוס פעילות",
        activeDescription: "המלגה פעילה וניתן להגיש אליה מועמדות.",
        inactiveDescription: "המלגה אינה פעילה כרגע."
    },
    publicationStatus: {
        label: "סטטוס פרסום",
        publicDescription: "המלגה תופיע באתר הציבורי.",
        privateDescription: "המלגה תהיה זמינה במערכת הניהול בלבד.",
        viewPublicPage: "פתח דף מלגה ציבורי"
    },

    title: {
        label: "שם המלגה",
        placeholder: "הזן את שם המלגה",
        required: "שם המלגה הוא שדה חובה"
    },
    slug: {
        label: "קידומת המלגה",
        placeholder: "הזן את קידומת המלגה",
        required: "קידומת המלגה היא שדה חובה",
        description: "יש להשתמש רק בתווים אלפאנומריים, מקפים וקווים תחתונים. לדוגמה /scholarships/your-slug-here"
    },
    shortDescription: {
        label: "תיאור קצר",
        placeholder: "תאר את המלגה בקצרה (יוצג בתצוגה מקדימה)",
        required: "תיאור קצר הוא שדה חובה"
    },
    description: {
        label: "תיאור מלא",
        placeholder: "תאר את המלגה ומטרותיה בהרחבה",
        required: "תיאור המלגה הוא שדה חובה"
    },
    image: {
        label: "תמונת המלגה",
        required: "יש להעלות תמונה למלגה",
        acceptedFileTypeDescription: "WebP הוא פורמט תמונה מודרני שמספק איכות גבוהה בנפח קטן",
        fileTypeErrorMessage: "ניתן להעלות קבצי WebP בלבד. אנא המר את התמונה ל-WebP לפני ההעלאה",
        fileSizeErrorMessage: "הקובץ גדול מדי. גודל מקסימלי הוא",
        dragDropText: "גרור קובץ לכאן או לחץ לבחירה",
        fileFormatDescription: "קבצי WebP בלבד | גודל מקסימלי:",
        selectedFileText: "קובץ נבחר"
    },

    scholarshipGroups: {
        label: "קבוצות מלגה",
        placeholder: "בחר קבוצות מלגה",
        required: "יש לבחור לפחות קבוצת מלגה אחת"
    },
    testimonials: {
        label: "חוות דעת",
        placeholder: "בחר חוות דעת לשיוך למלגה"
    },

    amounts: {
        minLabel: "סכום מינימלי",
        minPlaceholder: "הזן את הסכום המינימלי",
        minRequired: "סכום מינימלי הוא שדה חובה",
        maxLabel: "סכום מקסימלי",
        maxPlaceholder: "הזן את הסכום המקסימלי",
        maxRequired: "סכום מקסימלי הוא שדה חובה"
    },
    dates: {
        startLabel: "תאריך פתיחת ההרשמה",
        startRequired: "תאריך פתיחת ההרשמה הוא שדה חובה",
        endLabel: "תאריך סגירת ההרשמה",
        endRequired: "תאריך סגירת ההרשמה הוא שדה חובה",
        responseLabel: "תאריך תשובות"
    },
    volunteerHours: {
        label: "שעות התנדבות נדרשות",
        placeholder: "הזן את מספר שעות ההתנדבות הנדרשות",
        required: "מספר שעות ההתנדבות הוא שדה חובה"
    },

    requirements: {
        label: "דרישות זכאות",
        placeholder: "פרט את דרישות הזכאות למלגה (כל דרישה בשורה נפרדת)",
        required: "דרישות הזכאות הן שדה חובה"
    },
    benefits: {
        label: "הטבות",
        placeholder: "פרט את ההטבות שהמלגה מעניקה (כל הטבה בשורה נפרדת)",
        required: "הטבות הן שדה חובה"
    },
    targetAudience: {
        label: "קהל יעד",
        placeholder: "תאר את קהל היעד של המלגה",
        required: "קהל יעד הוא שדה חובה"
    },
    documentTypes: {
        label: "סוגי מסמכים נדרשים",
        placeholder: "בחר סוגי מסמכים"
    },
    conditionGroups: {
        label: "קבוצות תנאים לזכאות",
        placeholder: "בחר קבוצות תנאים"
    },
    specificConditions: {
        label: "הגדר תנאים ספציפיים לזכאות"
    },

    contactPerson: {
        label: "איש קשר",
        placeholder: "שם איש הקשר"
    },
    contactEmail: {
        label: "מייל ליצירת קשר",
        placeholder: "מייל ליצירת קשר",
        patternMessage: "כתובת אימייל לא תקינה"
    },
    contactPhone: {
        label: "נייד ליצירת קשר",
        placeholder: "נייד ליצירת קשר",
        patternMessage: "מספר טלפון לא תקין"
    },
    externalUrl: {
        label: "קישור חיצוני למלגה",
        placeholder: "הזן את כתובת האתר של המלגה",
        required: "כתובת האתר היא שדה חובה",
        patternMessage: "כתובת האתר אינה חוקית"
    },

    internalNotes: {
        label: "הערות פנימיות",
        placeholder: "הערות שיוצגו רק למנהלי המערכת"
    },

    conditionsCount: "תנאים:"
};

interface ScholarshipFormValues {
    title: string;
    slug: string;
    description: string;
    short_description: string;
    volunteer_hours: number;
    min_amount: number;
    max_amount: number;
    start_date: Date | null;
    end_date: Date | null;
    requirements: string;
    benefits: string;
    target_audience: string;
    image_file: File | null;
    url: string;
    testimonial_ids: Option[];
    group_ids: Option[];
    condition_group_ids: Option[];
    document_type_ids: Option[];
    defined_conditions: ConditionDependency[];
    is_public: boolean;
    internal_notes: string;
    contact_person: string;
    contact_email: string;
    contact_phone: string;
    response_date: Date | null;
    is_active: boolean;
}

interface SelectOption {
    id: string;
    label: string;
    subtitle: string;
}

const defaultValues: ScholarshipFormValues = {
    volunteer_hours: 0,
    slug: "",
    min_amount: 0,
    max_amount: 0,
    image_file: null,
    url: "",
    testimonial_ids: [],
    group_ids: [],
    title: "",
    description: "",
    short_description: "",
    requirements: "",
    benefits: "",
    target_audience: "",
    start_date: null,
    end_date: null,
    condition_group_ids: [],
    document_type_ids: [],
    defined_conditions: [],
    is_public: true,
    internal_notes: "",
    contact_person: "",
    contact_email: "",
    contact_phone: "",
    response_date: null,
    is_active: true
};

interface ScholarshipFormProps {
    scholarshipId?: string;
}

function sanitizeSlug(slug: string): string {
    return slug.toLowerCase().replace(/[^a-z0-9-_]/g, "");
}

export function ScholarshipForm({ scholarshipId }: ScholarshipFormProps) {
    const router = useRouter();
    const isEditMode = !!scholarshipId;
    const [loading, setLoading] = useState(isEditMode);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [availableQuestions, setAvailableQuestions] = useState<ConditionQuestion[]>([]);

    const methods = useForm<ScholarshipFormValues>({
        defaultValues
    });

    const { append, remove } = useFieldArray({
        control: methods.control,
        name: "defined_conditions"
    });

    const slug = methods.watch("slug");

    const { items: testimonials, loading: testimonialsLoading } = useTestimonials();
    const { items: scholarshipGroups, loading: scholarshipGroupsLoading } = useScholarshipGroups();
    const { items: conditionGroups, loading: conditionGroupsLoading } = useScholarshipConditionGroups();
    const { items: documentTypes, loading: documentTypesLoading } = useDocumentTypes();

    const testimonialOptions: SelectOption[] = testimonials.map((testimonial) => ({
        id: testimonial.id,
        label: testimonial.name,
        subtitle: testimonial.text
    }));

    const groupOptions: SelectOption[] = scholarshipGroups.map((group: ScholarshipGroup) => ({
        id: group.id,
        label: group.title,
        subtitle: group.description
    }));

    const conditionGroupOptions: SelectOption[] = conditionGroups.map((group) => ({
        id: group.id,
        label: group.name,
        subtitle: `${TEXTS.conditionsCount} ${group.conditions_count}`
    }));

    const documentTypeOptions: SelectOption[] = documentTypes.map((docType) => ({
        id: docType.id,
        label: docType.name,
        subtitle: docType.description || ""
    }));

    useEffect(() => {
        async function fetchAllQuestions() {
            const result = await getAllQuestionsForConditionSelector();
            if (result.success && result.data) {
                setAvailableQuestions(result.data);
            } else {
                console.error("Failed to fetch questions for ConditionSelector:", result.error);
                toast.error(TEXTS.questionsLoadError, {
                    description: result.error || "Unknown error"
                });
            }
        }

        fetchAllQuestions();
    }, []);

    useEffect(() => {
        async function fetchScholarship() {
            if (!isEditMode || !scholarshipId || availableQuestions.length === 0) return;

            // Wait for all data hooks to finish loading before proceeding
            if (testimonialsLoading || scholarshipGroupsLoading || conditionGroupsLoading || documentTypesLoading) {
                return;
            }

            try {
                setLoading(true);

                const [scholarshipResult, conditionGroupsResult, definedConditionsResult, documentTypesResult] =
                    await Promise.all([
                        getScholarship(scholarshipId),
                        getScholarshipConditionGroupsForScholarship(scholarshipId),
                        getScholarshipDefinedConditions(scholarshipId),
                        getScholarshipDocumentTypes(scholarshipId)
                    ]);

                if (!scholarshipResult.success || !scholarshipResult.data) {
                    toast.error(scholarshipResult.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/scholarships");
                    return;
                }

                const scholarship = scholarshipResult.data;

                const testimonialIds =
                    scholarship.link_scholarship_to_testimonial?.map(
                        (item: { testimonial_id: string }) => item.testimonial_id
                    ) || [];

                const groupIds =
                    scholarship.link_scholarship_to_scholarship_groups?.map(
                        (item: { scholarship_group_id: string }) => item.scholarship_group_id
                    ) || [];

                // Convert string IDs to Option objects using form utilities
                const testimonialOptions = mapIdsToOptions(
                    testimonialIds,
                    testimonials,
                    (testimonial) => testimonial.id,
                    (testimonial) => testimonial.name
                );

                const groupOptions = mapIdsToOptions(
                    groupIds,
                    scholarshipGroups,
                    (group) => group.id,
                    (group) => group.title
                );

                const conditionGroupIds =
                    conditionGroupsResult.success && conditionGroupsResult.data ? conditionGroupsResult.data : [];

                const definedConditions =
                    definedConditionsResult.success && definedConditionsResult.data ? definedConditionsResult.data : [];

                const documentTypeIds =
                    documentTypesResult.success && documentTypesResult.data ? documentTypesResult.data : [];

                // Convert string IDs to Option objects using form utilities
                const conditionGroupOptions = mapIdsToOptions(
                    conditionGroupIds,
                    conditionGroups,
                    (group) => group.id,
                    (group) => group.name
                );

                const documentTypeOptions = mapIdsToOptions(
                    documentTypeIds,
                    documentTypes,
                    (docType) => docType.id,
                    (docType) => docType.name
                );

                const requirementsText = Array.isArray(scholarship.requirements)
                    ? scholarship.requirements.join("\n")
                    : typeof scholarship.requirements === "string"
                      ? scholarship.requirements
                      : "";

                const benefitsText = Array.isArray(scholarship.benefits)
                    ? scholarship.benefits.join("\n")
                    : typeof scholarship.benefits === "string"
                      ? scholarship.benefits
                      : "";

                // Use shared utility to transform conditions from database format to form format
                const mappedDefinedConditions = transformConditionsFromDatabase(
                    definedConditions as unknown as BackendConditionDependency[],
                    availableQuestions
                );

                methods.reset({
                    ...defaultValues,
                    title: scholarship.title,
                    slug: scholarship.slug,
                    description: scholarship.description,
                    short_description: scholarship.short_description,
                    volunteer_hours: scholarship.volunteer_hours,
                    min_amount: scholarship.min_amount,
                    max_amount: scholarship.max_amount,
                    start_date: scholarship.start_date ? new Date(scholarship.start_date) : null,
                    end_date: scholarship.end_date ? new Date(scholarship.end_date) : null,
                    requirements: requirementsText,
                    benefits: benefitsText,
                    target_audience: scholarship.target_audience || "",
                    url: scholarship.url || "",
                    testimonial_ids: testimonialOptions,
                    group_ids: groupOptions,
                    condition_group_ids: conditionGroupOptions,
                    document_type_ids: documentTypeOptions,
                    defined_conditions: mappedDefinedConditions,
                    is_public: scholarship.is_public ?? true,
                    internal_notes: scholarship.internal_notes || "",
                    contact_person: scholarship.contact_person || "",
                    contact_email: scholarship.contact_email || "",
                    contact_phone: scholarship.contact_phone || "",
                    response_date: scholarship.response_date ? new Date(scholarship.response_date) : null,
                    is_active: scholarship.is_active ?? true
                });
            } catch (err) {
                console.error("Error fetching scholarship:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchScholarship();
    }, [
        scholarshipId,
        isEditMode,
        methods,
        router,
        availableQuestions,
        testimonials,
        scholarshipGroups,
        conditionGroups,
        documentTypes,
        testimonialsLoading,
        scholarshipGroupsLoading,
        conditionGroupsLoading,
        documentTypesLoading
    ]);

    async function onSubmit(data: ScholarshipFormValues) {
        try {
            if (data.max_amount < data.min_amount) {
                toast.error(TEXTS.amountValidationError);
                return;
            }

            const benefitsArray = textToArray(data.benefits);
            const requirementsArray = textToArray(data.requirements);

            // Use shared utility to transform conditions from form format to database format
            const transformedDefinedConditions = transformConditionsToDatabase(data.defined_conditions);

            if (isEditMode && scholarshipId) {
                const updateResult = await updateScholarship(scholarshipId, {
                    title: data.title,
                    slug: sanitizeSlug(data.slug),
                    description: data.description,
                    short_description: data.short_description,
                    volunteer_hours: data.volunteer_hours,
                    min_amount: data.min_amount,
                    max_amount: data.max_amount,
                    start_date: data.start_date?.toISOString(),
                    end_date: data.end_date?.toISOString(),
                    requirements: requirementsArray,
                    benefits: benefitsArray,
                    target_audience: data.target_audience,
                    url: data.url?.trim() || null,
                    is_public: data.is_public,
                    internal_notes: data.internal_notes,
                    contact_person: data.contact_person,
                    contact_email: data.contact_email,
                    contact_phone: data.contact_phone,
                    response_date: data.response_date?.toISOString() || null,
                    is_active: data.is_active
                });

                if (!updateResult.success) {
                    toast.error(updateResult.error || TEXTS.editErrorMessage);
                    return;
                }

                if (selectedFile) {
                    const uploadResult = await uploadScholarshipImage(scholarshipId, selectedFile);
                    if (!uploadResult.success) {
                        toast.error(uploadResult.error || "שגיאה בהעלאת התמונה");
                        return;
                    }
                }

                const testimonialResult = await linkScholarshipTestimonials(
                    scholarshipId,
                    data.testimonial_ids.map((o) => o.id)
                );
                if (!testimonialResult.success) {
                    toast.error(testimonialResult.error || "שגיאה בעדכון חוות דעת");
                    return;
                }

                const groupResult = await linkScholarshipGroups(
                    scholarshipId,
                    data.group_ids.map((o) => o.id)
                );
                if (!groupResult.success) {
                    toast.error(groupResult.error || "שגיאה בעדכון קבוצות מלגה");
                    return;
                }

                if (data.condition_group_ids !== undefined) {
                    const conditionGroupResult = await updateScholarshipConditionGroups(
                        scholarshipId,
                        data.condition_group_ids.map((o) => o.id)
                    );
                    if (!conditionGroupResult.success) {
                        toast.error(conditionGroupResult.error || "שגיאה בעדכון קבוצות תנאים");
                        return;
                    }
                }

                if (data.defined_conditions !== undefined) {
                    const definedConditionsResult = await updateScholarshipDefinedConditions(
                        scholarshipId,
                        transformedDefinedConditions as unknown as ConditionDependency[]
                    );
                    if (!definedConditionsResult.success) {
                        toast.error(definedConditionsResult.error || "שגיאה בעדכון תנאים מוגדרים");
                        return;
                    }
                }

                const documentTypesResult = await updateScholarshipDocumentTypes(
                    scholarshipId,
                    data.document_type_ids.map((o) => o.id)
                );
                if (!documentTypesResult.success) {
                    toast.error(documentTypesResult.error || "שגיאה בעדכון סוגי מסמכים");
                    return;
                }

                toast.success(TEXTS.editSuccessMessage, { id: "scholarship-updated" });
                router.push("/admin/scholarships");
            } else {
                const createResult = await createScholarship({
                    title: data.title,
                    slug: sanitizeSlug(data.slug),
                    description: data.description,
                    short_description: data.short_description,
                    volunteer_hours: data.volunteer_hours,
                    min_amount: data.min_amount,
                    max_amount: data.max_amount,
                    start_date: data.start_date?.toISOString(),
                    end_date: data.end_date?.toISOString(),
                    requirements: requirementsArray,
                    benefits: benefitsArray,
                    target_audience: data.target_audience,
                    image_url: null,
                    url: data.url?.trim() || null,
                    is_public: data.is_public,
                    internal_notes: data.internal_notes,
                    contact_person: data.contact_person,
                    contact_email: data.contact_email,
                    contact_phone: data.contact_phone,
                    response_date: data.response_date?.toISOString() || null,
                    is_active: data.is_active,
                    scholarship_type: "submission"
                });

                if (!createResult.success) {
                    toast.error(createResult.error || TEXTS.createErrorMessage);
                    return;
                }

                const scholarshipId = createResult.data!.id;

                if (selectedFile) {
                    const uploadResult = await uploadScholarshipImage(scholarshipId, selectedFile);
                    if (!uploadResult.success) {
                        toast.error(uploadResult.error || "שגיאה בהעלאת התמונה");
                        return;
                    }
                }

                if (data.testimonial_ids.length > 0) {
                    const testimonialResult = await linkScholarshipTestimonials(
                        scholarshipId,
                        data.testimonial_ids.map((o) => o.id)
                    );
                    if (!testimonialResult.success) {
                        toast.error(testimonialResult.error || "שגיאה בשיוך חוות דעת");
                        return;
                    }
                }

                if (data.group_ids.length > 0) {
                    const groupResult = await linkScholarshipGroups(
                        scholarshipId,
                        data.group_ids.map((o) => o.id)
                    );
                    if (!groupResult.success) {
                        toast.error(groupResult.error || "שגיאה בשיוך קבוצות מלגה");
                        return;
                    }
                }

                if (transformedDefinedConditions.length > 0) {
                    const definedConditionsResult = await updateScholarshipDefinedConditions(
                        scholarshipId,
                        transformedDefinedConditions as unknown as ConditionDependency[]
                    );
                    if (!definedConditionsResult.success) {
                        toast.error(definedConditionsResult.error || "שגיאה בהוספת תנאים מוגדרים");
                        return;
                    }
                }

                if (data.document_type_ids.length > 0) {
                    const documentTypesResult = await updateScholarshipDocumentTypes(
                        scholarshipId,
                        data.document_type_ids.map((o) => o.id)
                    );
                    if (!documentTypesResult.success) {
                        toast.error(documentTypesResult.error || "שגיאה בשיוך סוגי מסמכים");
                        return;
                    }
                }

                methods.reset(defaultValues);
                setSelectedFile(null);

                toast.success(TEXTS.createSuccessMessage, { id: "scholarship-created" });
                router.push("/admin/scholarships");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} scholarship:`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                id: `scholarship-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <ScholarshipFormSkeleton />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.statusSection}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <FormField
                                control={methods.control}
                                name="is_active"
                                render={({ field }) => (
                                    <div className="flex items-center justify-between rounded-lg border p-4">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_active" className={cn("cursor-pointer")}>
                                                {TEXTS.activityStatus.label}
                                            </Label>
                                            <p className="text-sm text-muted-foreground">
                                                {field.value
                                                    ? TEXTS.activityStatus.activeDescription
                                                    : TEXTS.activityStatus.inactiveDescription}
                                            </p>
                                        </div>
                                        <Switch id="is_active" checked={field.value} onCheckedChange={field.onChange} />
                                    </div>
                                )}
                            />
                            <FormField
                                control={methods.control}
                                name="is_public"
                                render={({ field }) => (
                                    <div className="flex items-center justify-between rounded-lg border p-4">
                                        <div className="space-y-0.5">
                                            <div className="flex items-center gap-2">
                                                <Label htmlFor="is_public" className={cn("cursor-pointer")}>
                                                    {TEXTS.publicationStatus.label}
                                                </Label>
                                                {isEditMode && slug && (
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Link
                                                                    href={`/scholarships/${encodeURIComponent(sanitizeSlug(slug))}`}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className={cn(
                                                                        "text-primary",
                                                                        !field.value &&
                                                                            "pointer-events-none text-muted-foreground"
                                                                    )}
                                                                    aria-disabled={!field.value}
                                                                    tabIndex={!field.value ? -1 : undefined}
                                                                    onClick={(e) => {
                                                                        if (!field.value) {
                                                                            e.preventDefault();
                                                                        }
                                                                    }}
                                                                >
                                                                    <ExternalLink className="h-4 w-4" />
                                                                </Link>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>{TEXTS.publicationStatus.viewPublicPage}</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {field.value
                                                    ? TEXTS.publicationStatus.publicDescription
                                                    : TEXTS.publicationStatus.privateDescription}
                                            </p>
                                        </div>
                                        <Switch id="is_public" checked={field.value} onCheckedChange={field.onChange} />
                                    </div>
                                )}
                            />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.generalInfoSection}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div>
                            <ShortText
                                name="title"
                                placeholder={TEXTS.title.placeholder}
                                label={TEXTS.title.label}
                                required
                                requiredText={TEXTS.title.required}
                                minLength={2}
                            />
                        </div>
                        <div className="space-y-2">
                            <ShortText
                                name="slug"
                                placeholder={TEXTS.slug.placeholder}
                                label={TEXTS.slug.label}
                                required
                                requiredText={TEXTS.slug.required}
                                description={TEXTS.slug.description}
                            />
                        </div>
                        <div className="space-y-2">
                            <LongText
                                name="short_description"
                                label={TEXTS.shortDescription.label}
                                placeholder={TEXTS.shortDescription.placeholder}
                                required
                                requiredText={TEXTS.shortDescription.required}
                                minLength={10}
                            />
                        </div>
                        <div className="space-y-2">
                            <LongText
                                name="description"
                                label={TEXTS.description.label}
                                placeholder={TEXTS.description.placeholder}
                                required
                                requiredText={TEXTS.description.required}
                                minLength={10}
                            />
                        </div>
                        <div className="space-y-2">
                            <FileUpload
                                name="image_file"
                                label={TEXTS.image.label}
                                required={!isEditMode}
                                requiredText={TEXTS.image.required}
                                onFileSelect={setSelectedFile}
                                acceptedFileType="image/webp"
                                acceptedFileTypeDescription={TEXTS.image.acceptedFileTypeDescription}
                                maxSizeInMB={5}
                                fileTypeErrorMessage={TEXTS.image.fileTypeErrorMessage}
                                fileSizeErrorMessage={(maxSize) => `${TEXTS.image.fileSizeErrorMessage} ${maxSize}MB`}
                                dragDropText={TEXTS.image.dragDropText}
                                fileFormatDescription={(maxSize) => `${TEXTS.image.fileFormatDescription} ${maxSize}MB`}
                                selectedFileText={TEXTS.image.selectedFileText}
                            />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.associationsSection}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="space-y-2">
                            <MultiSelect
                                name="group_ids"
                                label={TEXTS.scholarshipGroups.label}
                                placeholder={TEXTS.scholarshipGroups.placeholder}
                                options={groupOptions}
                                required
                                requiredText={TEXTS.scholarshipGroups.required}
                                showSearch={true}
                            />
                        </div>
                        <div className="space-y-2">
                            <MultiSelect
                                name="testimonial_ids"
                                label={TEXTS.testimonials.label}
                                placeholder={TEXTS.testimonials.placeholder}
                                options={testimonialOptions}
                                required={false}
                                showSearch={true}
                            />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.centralDetailsSection}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <NumberInput
                                    name="min_amount"
                                    label={TEXTS.amounts.minLabel}
                                    placeholder={TEXTS.amounts.minPlaceholder}
                                    required
                                    requiredText={TEXTS.amounts.minRequired}
                                    min={0}
                                />
                            </div>
                            <div className="space-y-2">
                                <NumberInput
                                    name="max_amount"
                                    label={TEXTS.amounts.maxLabel}
                                    placeholder={TEXTS.amounts.maxPlaceholder}
                                    required
                                    requiredText={TEXTS.amounts.maxRequired}
                                    min={0}
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <DatePicker
                                    name="start_date"
                                    label={TEXTS.dates.startLabel}
                                    required
                                    requiredText={TEXTS.dates.startRequired}
                                    isFutureAllowed
                                />
                            </div>
                            <div className="space-y-2">
                                <DatePicker
                                    name="end_date"
                                    label={TEXTS.dates.endLabel}
                                    required
                                    requiredText={TEXTS.dates.endRequired}
                                    isFutureAllowed
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-3">
                                <NumberInput
                                    name="volunteer_hours"
                                    label={TEXTS.volunteerHours.label}
                                    placeholder={TEXTS.volunteerHours.placeholder}
                                    required
                                    requiredText={TEXTS.volunteerHours.required}
                                    min={0}
                                />
                            </div>
                            <div className="space-y-2">
                                <DatePicker name="response_date" label={TEXTS.dates.responseLabel} isFutureAllowed />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.eligibilityConditionsSection}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="space-y-2">
                            <LongText
                                name="requirements"
                                placeholder={TEXTS.requirements.placeholder}
                                label={TEXTS.requirements.label}
                                required
                                requiredText={TEXTS.requirements.required}
                                minLength={10}
                            />
                        </div>
                        <div className="space-y-2">
                            <LongText
                                name="benefits"
                                placeholder={TEXTS.benefits.placeholder}
                                label={TEXTS.benefits.label}
                                required
                                requiredText={TEXTS.benefits.required}
                                minLength={10}
                            />
                        </div>
                        <div className="space-y-2">
                            <LongText
                                name="target_audience"
                                placeholder={TEXTS.targetAudience.placeholder}
                                label={TEXTS.targetAudience.label}
                                required
                                requiredText={TEXTS.targetAudience.required}
                                minLength={10}
                            />
                        </div>
                        <div className="space-y-2">
                            <MultiSelect
                                name="document_type_ids"
                                label={TEXTS.documentTypes.label}
                                placeholder={TEXTS.documentTypes.placeholder}
                                options={documentTypeOptions}
                                showSearch={true}
                                required={false}
                            />
                        </div>
                        <div className="space-y-2">
                            <MultiSelect
                                name="condition_group_ids"
                                label={TEXTS.conditionGroups.label}
                                placeholder={TEXTS.conditionGroups.placeholder}
                                options={conditionGroupOptions}
                                showSearch={true}
                                required={false}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label>{TEXTS.specificConditions.label}</Label>
                            <ConditionSelector
                                availableQuestions={availableQuestions}
                                onAdd={() => {
                                    append({
                                        question_id: { id: "", label: "" },
                                        condition_type: "in",
                                        condition_value: []
                                    });
                                }}
                                onRemove={remove}
                                fieldArrayName="defined_conditions"
                            />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.contactDetailsSection}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <ShortText
                                    name="contact_person"
                                    placeholder={TEXTS.contactPerson.placeholder}
                                    label={TEXTS.contactPerson.label}
                                />
                            </div>
                            <div className="space-y-2">
                                <ShortText
                                    name="contact_email"
                                    placeholder={TEXTS.contactEmail.placeholder}
                                    type="email"
                                    label={TEXTS.contactEmail.label}
                                    pattern={/^[^\s@]+@[^\s@]+\.[^\s@]+$/}
                                    patternMessage={TEXTS.contactEmail.patternMessage}
                                />
                            </div>
                            <div className="space-y-2">
                                <ShortText
                                    name="contact_phone"
                                    placeholder={TEXTS.contactPhone.placeholder}
                                    type="tel"
                                    label={TEXTS.contactPhone.label}
                                    pattern={/^[0-9+\- ]*$/}
                                    patternMessage={TEXTS.contactPhone.patternMessage}
                                />
                            </div>
                            <div className="space-y-2">
                                <ShortText
                                    name="url"
                                    placeholder={TEXTS.externalUrl.placeholder}
                                    required={true}
                                    requiredText={TEXTS.externalUrl.required}
                                    pattern={RegExp(/^https?:\/\/.+/)}
                                    patternMessage={TEXTS.externalUrl.patternMessage}
                                    label={TEXTS.externalUrl.label}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{TEXTS.internalNotes.label}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <LongText
                            name="internal_notes"
                            placeholder={TEXTS.internalNotes.placeholder}
                            required={false}
                        />
                    </CardContent>
                </Card>

                <div className="flex justify-between pt-4">
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/scholarships")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
