"use client";

import { SendHorizonalIcon } from "lucide-react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createUserNote } from "@/app/actions/user-notes-actions";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { defaultValues, TEXTS, UserNoteFormData } from "@/lib/user-notes-constants";

interface UserNoteFormProps {
    reportedUserId: string;
    onSuccess?: () => void;
}

export function UserNoteForm({ reportedUserId, onSuccess }: UserNoteFormProps) {
    const methods = useForm<UserNoteFormData>({
        defaultValues,
        mode: "onChange"
    });

    const {
        handleSubmit,
        formState: { isSubmitting },
        reset
    } = methods;

    const onSubmit = async (data: UserNoteFormData) => {
        try {
            const result = await createUserNote(reportedUserId, data.note);

            if (result.success) {
                toast.success(TEXTS.createSuccess);
                reset();
                onSuccess?.();
            } else {
                toast.error(result.error || TEXTS.createError);
            }
        } catch (error) {
            console.error("Error creating note:", error);
            toast.error(TEXTS.createError);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(onSubmit)();
        }
    };

    return (
        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} onKeyDown={handleKeyDown} className="space-y-4">
                <div className="flex gap-2 items-end">
                    <div className="flex-1">
                        <ShortText
                            name="note"
                            placeholder={TEXTS.notePlaceholder}
                            required={true}
                            requiredText={TEXTS.noteRequired}
                        />
                    </div>
                    <Button type="submit" disabled={isSubmitting} size="icon" className="h-10 w-10">
                        <SendHorizonalIcon className="h-4 w-4 scale-x-[-1]" />
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
