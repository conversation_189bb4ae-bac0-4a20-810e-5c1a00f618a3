"use client";

import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createCoupons, getCoupon, getCouponGroups, updateCoupon } from "@/app/actions/coupon-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { DatePicker } from "@/components/forms/fields/date-picker";
import { type Option } from "@/components/forms/fields/dropdown-base";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    type CouponFormValues,
    type CouponType,
    couponTypeOptions,
    type CreationMode,
    defaultCouponFormValues,
    TEXTS
} from "@/lib/coupon-constants";
import { mapIdToOption } from "@/utils/form-utils";

interface CouponFormProps {
    couponId?: string;
}

export function CouponForm({ couponId }: CouponFormProps) {
    const router = useRouter();
    const isEditMode = !!couponId;
    const [loading, setLoading] = useState(isEditMode);
    const [couponGroups, setCouponGroups] = useState<Option[]>([]);

    const methods = useForm<CouponFormValues>({
        defaultValues: defaultCouponFormValues
    });

    const creationMode = methods.watch("creation_mode");

    const fetchCouponGroups = useCallback(async () => {
        const result = await getCouponGroups();
        if (result.success && result.data) {
            setCouponGroups(result.data.map((group) => ({ id: group.id, label: group.label })));
        } else {
            console.error("Error fetching coupon groups:", result.error);
            toast.error(result.error || TEXTS.errorMessage);
        }
    }, []);

    useEffect(() => {
        fetchCouponGroups();
    }, [fetchCouponGroups]);

    useEffect(() => {
        async function fetchCouponData() {
            if (!isEditMode || !couponId) return;

            try {
                setLoading(true);

                let currentCouponGroups = couponGroups;
                if (couponGroups.length === 0) {
                    const groupsResult = await getCouponGroups();
                    if (groupsResult.success && groupsResult.data) {
                        const fetchedGroups = groupsResult.data.map((group) => ({ id: group.id, label: group.label }));
                        setCouponGroups(fetchedGroups);
                        currentCouponGroups = fetchedGroups;
                    }
                }

                const result = await getCoupon(couponId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/coupons");
                    return;
                }

                const data = result.data;

                methods.reset({
                    ...defaultCouponFormValues,
                    coupon_code: data.coupon_code,
                    coupon_type: data.coupon_type
                        ? mapIdToOption(
                              data.coupon_type,
                              couponTypeOptions,
                              (option) => option.id,
                              (option) => option.label
                          )
                        : couponTypeOptions[0],
                    discount_value: data.discount_value,
                    expiration_date: data.expiration_date ? new Date(data.expiration_date) : null,
                    usage_limit: data.usage_limit || null,
                    coupon_group_id: data.coupon_group_id
                        ? mapIdToOption(
                              data.coupon_group_id,
                              currentCouponGroups,
                              (group) => group.id,
                              (group) => group.label
                          )
                        : null
                });
            } catch (err) {
                console.error("Error fetching coupon:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchCouponData();
    }, [couponId, isEditMode, methods, router, couponGroups]);

    async function onSubmit(data: CouponFormValues) {
        try {
            const couponType = data.coupon_type.id as CouponType;
            const couponGroupId = data.coupon_group_id?.id || null;

            if (isEditMode && couponId) {
                const result = await updateCoupon(couponId, {
                    coupon_code: data.coupon_code ?? "",
                    coupon_type: couponType,
                    discount_value: data.discount_value ?? 0,
                    expiration_date: data.expiration_date ? data.expiration_date.toISOString() : null,
                    usage_limit: data.usage_limit,
                    coupon_group_id: couponGroupId
                });

                if (!result.success) {
                    toast.error(result.error || TEXTS.editErrorMessage);
                    return;
                }

                toast.success(TEXTS.editSuccessMessage, { id: "coupon-updated" });
                router.push("/admin/coupons");
            } else {
                const result = await createCoupons({
                    creation_mode: data.creation_mode,
                    coupon_code: data.coupon_code,
                    quantity: data.quantity,
                    coupon_type: couponType,
                    discount_value: data.discount_value ?? 0,
                    expiration_date: data.expiration_date,
                    usage_limit: data.usage_limit,
                    coupon_group_id: couponGroupId
                });

                if (!result.success) {
                    toast.error(result.error || TEXTS.createErrorMessage);
                    return;
                }

                methods.reset(defaultCouponFormValues);

                const successMessage =
                    data.creation_mode === "single"
                        ? TEXTS.createSuccessMessageSingle
                        : TEXTS.createSuccessMessageMultiple(result.count || 0);

                toast.success(successMessage, { id: "coupon-created" });
                router.push("/admin/coupons");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} coupon(s):`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                id: `coupon-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                {!isEditMode && (
                    <div className="space-y-2">
                        <Tabs
                            value={creationMode}
                            onValueChange={(value) => methods.setValue("creation_mode", value as CreationMode)}
                            className="w-full"
                            dir="rtl"
                        >
                            <TabsList className="w-full grid grid-cols-2 h-12 bg-muted">
                                <TabsTrigger
                                    value="single"
                                    className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                                >
                                    {TEXTS.singleCouponLabel}
                                </TabsTrigger>
                                <TabsTrigger
                                    value="multiple"
                                    className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                                >
                                    {TEXTS.multipleCouponLabel}
                                </TabsTrigger>
                            </TabsList>
                        </Tabs>
                    </div>
                )}

                {!isEditMode && creationMode === "single" ? (
                    <ShortText
                        name="coupon_code"
                        label={TEXTS.couponCodeLabel}
                        placeholder={TEXTS.couponCodePlaceholder}
                        required
                        requiredText={TEXTS.couponCodeRequired}
                    />
                ) : !isEditMode && creationMode === "multiple" ? (
                    <NumberInput
                        name="quantity"
                        label={TEXTS.quantityLabel}
                        placeholder={TEXTS.quantityPlaceholder}
                        required
                        requiredText={TEXTS.quantityRequired}
                        min={1}
                    />
                ) : isEditMode ? (
                    <ShortText
                        name="coupon_code"
                        label={TEXTS.couponCodeLabel}
                        placeholder={TEXTS.couponCodePlaceholder}
                        required
                        requiredText={TEXTS.couponCodeRequired}
                    />
                ) : null}

                <SingleSelect
                    name="coupon_group_id"
                    label={TEXTS.couponGroupLabel}
                    placeholder={TEXTS.couponGroupPlaceholder}
                    options={couponGroups}
                    requiredText={TEXTS.couponGroupRequired}
                    required
                />

                <SingleSelect
                    name="coupon_type"
                    label={TEXTS.couponTypeLabel}
                    placeholder={TEXTS.couponTypePlaceholder}
                    options={couponTypeOptions}
                    required
                />

                <NumberInput
                    name="discount_value"
                    label={TEXTS.discountValueLabel}
                    placeholder={TEXTS.discountValuePlaceholder}
                    required
                    min={0.01}
                />

                <DatePicker name="expiration_date" label={TEXTS.expirationDateLabel} required={false} />

                <NumberInput
                    name="usage_limit"
                    label={TEXTS.usageLimitLabel}
                    placeholder={TEXTS.usageLimitPlaceholder}
                    required={false}
                    min={1}
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/coupons")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
