"use client";

import React, { useState } from "react";
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { uploadDocument } from "@/app/actions/dynamic-document-upload-actions";
import { EmptyState } from "@/components/common/empty-state";
import { LoadingIcon } from "@/components/common/loading-icon";
import { PdfExampleModal } from "@/components/common/pdf-example-modal";
import { FileUploadApproval } from "@/components/forms/fields/file-upload-approval";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useUserDocuments } from "@/hooks/use-user-documents";
import {
    DocumentUploadFormData,
    getFriendlyFileTypeNames,
    getUploadStatusBadge,
    isValidAndSafeUrl,
    TEXTS,
    UserDocumentStatus
} from "@/lib/dynamic-document-upload-constants";
import { sanitizeText } from "@/utils/sanitization";

interface DynamicDocumentUploadFormProps {
    onUploadComplete?: () => void;
    overrideUserId?: string;
}

export function DynamicDocumentUploadForm({ onUploadComplete, overrideUserId }: DynamicDocumentUploadFormProps) {
    const { documents, loading, error, refetchDocuments } = useUserDocuments(overrideUserId);
    const [pdfModalUrl, setPdfModalUrl] = useState<string | null>(null);
    const [uploadingDocuments, setUploadingDocuments] = useState<Set<string>>(new Set());

    const methods = useForm<DocumentUploadFormData>({
        defaultValues: {}
    });

    const handleFileUpload = async (documentTypeId: string, file: File | null) => {
        if (!file) return;

        const sanitizedDocumentTypeId = sanitizeText(documentTypeId, 100);
        if (!sanitizedDocumentTypeId) {
            toast.error(TEXTS.invalidDocumentIdError);
            return;
        }

        setUploadingDocuments((prev) => new Set(prev).add(sanitizedDocumentTypeId));

        try {
            const result = await uploadDocument(sanitizedDocumentTypeId, file, overrideUserId);

            if (result.success) {
                toast.success(TEXTS.uploadSuccessMessage);
                await refetchDocuments();
                onUploadComplete?.();
            } else {
                toast.error(result.error || TEXTS.uploadErrorMessage);
            }
        } catch (error) {
            console.error("Upload error:", error);
            toast.error(TEXTS.uploadErrorMessage);
        } finally {
            setUploadingDocuments((prev) => {
                const newSet = new Set(prev);
                newSet.delete(sanitizedDocumentTypeId);
                return newSet;
            });
        }
    };

    const handleOpenPdfModal = (url: string) => {
        if (!isValidAndSafeUrl(url)) {
            toast.error(TEXTS.invalidUrlError);
            return;
        }
        setPdfModalUrl(url);
    };

    const renderDocumentUploadField = (doc: UserDocumentStatus) => {
        const friendlyFileTypes = getFriendlyFileTypeNames(doc.allowedMimeTypes);
        const isUploading = uploadingDocuments.has(doc.documentTypeId);

        const fileFormatDescription = (maxSize: number) => {
            return doc.allowedMimeTypes && doc.allowedMimeTypes.length > 0
                ? TEXTS.fileFormatDescriptionWithMimeType(friendlyFileTypes, maxSize)
                : TEXTS.fileFormatDescriptionDefault(maxSize);
        };

        const fileSizeErrorMessage = (maxSize: number) => {
            return doc.maxFileSizeInMB
                ? TEXTS.fileSizeErrorMessageSpecific(maxSize)
                : TEXTS.defaultFileSizeErrorMessage(maxSize);
        };

        return (
            <div key={doc.documentTypeId} className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">{doc.documentTypeName}</h4>
                        {doc.documentTypeDescription && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {doc.documentTypeDescription}
                            </p>
                        )}
                    </div>
                </div>

                <FileUploadApproval
                    name={doc.documentTypeId}
                    label={
                        isUploading
                            ? TEXTS.uploading
                            : doc.isUploaded
                              ? TEXTS.uploadNewVersionLabel
                              : TEXTS.uploadNewLabel
                    }
                    onFileSelect={isUploading ? undefined : handleFileUpload.bind(null, doc.documentTypeId)}
                    acceptedFileType={doc.allowedMimeTypes || []}
                    acceptedFileTypeDescription={friendlyFileTypes}
                    maxSizeInMB={doc.maxFileSizeInMB || 10}
                    fileTypeErrorMessage={TEXTS.fileTypeErrorMessage}
                    fileSizeErrorMessage={fileSizeErrorMessage}
                    dragDropText={isUploading ? TEXTS.uploading : TEXTS.dragDropText}
                    fileFormatDescription={fileFormatDescription}
                    selectedFileText={TEXTS.selectedFileText}
                    documentTypeName={doc.documentTypeName}
                    documentTypeDescription={doc.documentTypeDescription || undefined}
                    isUploaded={doc.isUploaded}
                    uploadedFileUrl={doc.uploadedFileUrl || undefined}
                    exampleFileUrl={doc.exampleFileUrl || undefined}
                    linkUrl={doc.link_url || undefined}
                    statusText={
                        isUploading
                            ? TEXTS.documentStatusUploading
                            : doc.isUploaded
                              ? TEXTS.documentStatusUploaded
                              : TEXTS.documentStatusPending
                    }
                    onExampleClick={doc.exampleFileUrl ? () => handleOpenPdfModal(doc.exampleFileUrl!) : undefined}
                    onLinkClick={doc.link_url ? () => window.open(doc.link_url!, "_blank") : undefined}
                />
            </div>
        );
    };

    const renderDocumentGroup = (groupName: string, groupDocuments: UserDocumentStatus[]) => {
        const uploadedCount = groupDocuments.filter((doc) => doc.isUploaded).length;
        const totalCount = groupDocuments.length;
        const statusBadge = getUploadStatusBadge(uploadedCount, totalCount);

        return (
            <AccordionItem key={groupName} value={groupName}>
                <AccordionTrigger className="text-right hover:no-underline">
                    <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{groupName}</h3>
                            <Badge variant={statusBadge.variant} className={statusBadge.className}>
                                {TEXTS.documentsUploadedProgress(uploadedCount, totalCount)}
                            </Badge>
                        </div>
                    </div>
                </AccordionTrigger>
                <AccordionContent>
                    <div className="space-y-6 pt-4">{groupDocuments.map(renderDocumentUploadField)}</div>
                </AccordionContent>
            </AccordionItem>
        );
    };

    if (loading) {
        return <LoadingIcon text={TEXTS.loadingDocuments} />;
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertTitle>{TEXTS.errorLoadingDocuments}</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
                <Button variant="outline" size="sm" onClick={refetchDocuments} className="mt-2">
                    {TEXTS.retryButton}
                </Button>
            </Alert>
        );
    }

    if (!documents || documents.length === 0) {
        return <EmptyState message={TEXTS.noDocumentsRequired} variant="success" />;
    }

    const groupedDocuments = documents.reduce(
        (acc, doc) => {
            const groupName = doc.documentGroupName || TEXTS.uncategorizedGroupName;
            if (!acc[groupName]) {
                acc[groupName] = [];
            }
            acc[groupName].push(doc);
            return acc;
        },
        {} as Record<string, UserDocumentStatus[]>
    );

    const sortedGroupEntries = Object.entries(groupedDocuments).sort(([, docsA], [, docsB]) => {
        const allUploadedA = docsA.every((doc) => doc.isUploaded);
        const allUploadedB = docsB.every((doc) => doc.isUploaded);

        if (allUploadedA && !allUploadedB) return 1;
        if (!allUploadedA && allUploadedB) return -1;
        return 0;
    });

    const totalDocuments = documents.length;
    const uploadedDocuments = documents.filter((doc) => doc.isUploaded).length;
    const allDocumentsUploaded = uploadedDocuments === totalDocuments;

    return (
        <FormProvider {...methods}>
            <div className="space-y-6">
                {/* Progress Summary */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <span>מסמכים שהועלו</span>
                            <Badge
                                variant={allDocumentsUploaded ? "default" : "secondary"}
                                className={
                                    allDocumentsUploaded
                                        ? "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800"
                                        : "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800"
                                }
                            >
                                {TEXTS.documentsUploadedProgress(uploadedDocuments, totalDocuments)}
                            </Badge>
                        </CardTitle>
                        {allDocumentsUploaded && (
                            <CardDescription className="text-green-600 dark:text-green-400">
                                {TEXTS.allDocumentsUploaded}
                                <br />
                                {TEXTS.allDocumentsUploadedSubtitle}
                            </CardDescription>
                        )}
                    </CardHeader>
                </Card>

                {/* Document Groups */}
                <Accordion type="multiple" defaultValue={sortedGroupEntries.map(([groupName]) => groupName)}>
                    {sortedGroupEntries.map(([groupName, groupDocuments]) =>
                        renderDocumentGroup(groupName, groupDocuments)
                    )}
                </Accordion>

                {/* PDF Modal */}
                <PdfExampleModal
                    open={!!pdfModalUrl}
                    url={pdfModalUrl || ""}
                    onOpenChange={(open) => !open && setPdfModalUrl(null)}
                />
            </div>
        </FormProvider>
    );
}
