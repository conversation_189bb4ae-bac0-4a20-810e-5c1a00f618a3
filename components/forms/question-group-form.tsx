"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createQuestionGroup, getQuestionGroup, updateQuestionGroup } from "@/app/actions/question-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { defaultValues, type QuestionGroupFormValues, TEXTS } from "@/lib/question-group-constants";

interface QuestionGroupFormProps {
    groupId?: string;
}

export function QuestionGroupForm({ groupId }: QuestionGroupFormProps) {
    const router = useRouter();
    const isEditMode = !!groupId;
    const [loading, setLoading] = useState(isEditMode);

    const methods = useForm<QuestionGroupFormValues>({
        defaultValues
    });

    useEffect(() => {
        async function fetchQuestionGroup() {
            if (!isEditMode || !groupId) return;

            try {
                setLoading(true);
                const result = await getQuestionGroup(groupId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editErrorMessage);
                    router.push("/admin/questions?tab=groups");
                    return;
                }

                methods.reset({
                    name: result.data.name
                });
            } catch (err) {
                console.error("Error fetching question group:", err);
                toast.error(TEXTS.editErrorMessage);
                router.push("/admin/questions?tab=groups");
            } finally {
                setLoading(false);
            }
        }

        fetchQuestionGroup();
    }, [groupId, isEditMode, methods, router]);

    async function onSubmit(data: QuestionGroupFormValues) {
        try {
            const result = isEditMode ? await updateQuestionGroup(groupId!, data) : await createQuestionGroup(data);

            if (!result.success) {
                toast.error(result.error);
                return;
            }

            toast.success(isEditMode ? TEXTS.editSuccessMessage : TEXTS.createSuccessMessage);

            if (!isEditMode) {
                methods.reset(defaultValues);
            }

            router.push("/admin/questions?tab=groups");
        } catch (err) {
            console.error("Error submitting question group:", err);
            toast.error(TEXTS.unknownError);
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.loadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8" dir="rtl">
                <ShortText
                    name="name"
                    label={TEXTS.nameLabel}
                    placeholder={TEXTS.namePlaceholder}
                    requiredText={TEXTS.nameRequired}
                    description={TEXTS.nameDescription}
                    required={true}
                />

                <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/questions?tab=groups")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
