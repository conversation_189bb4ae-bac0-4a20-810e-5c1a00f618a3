"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createDocumentTypeGroup, updateDocumentTypeGroup } from "@/app/actions/document-type-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { Button } from "@/components/ui/button";
import { useDocumentTypeGroup } from "@/hooks/use-document-type-group";
import { defaultValues, type DocumentTypeGroupFormValues, TEXTS } from "@/lib/document-type-group-constants";

import { LongText } from "./fields/long-text";
import { ShortText } from "./fields/short-text";

interface DocumentTypeGroupFormProps {
    groupId?: string;
}

export function DocumentTypeGroupForm({ groupId }: DocumentTypeGroupFormProps) {
    const router = useRouter();
    const isEditMode = !!groupId;
    const { group, loading } = useDocumentTypeGroup(groupId);

    const methods = useForm<DocumentTypeGroupFormValues>({
        defaultValues
    });

    useEffect(() => {
        if (isEditMode && group) {
            methods.reset({
                name: group.name || "",
                description: group.description || ""
            });
        }
    }, [isEditMode, group, methods]);

    async function onSubmit(data: DocumentTypeGroupFormValues) {
        try {
            const result =
                isEditMode && groupId
                    ? await updateDocumentTypeGroup(groupId, data)
                    : await createDocumentTypeGroup(data);

            if (!result.success) {
                toast.error(result.error || (isEditMode ? TEXTS.UPDATE_ERROR : TEXTS.CREATE_ERROR), {
                    id: `document-type-group-${isEditMode ? "update" : "create"}-error`
                });
                return;
            }

            if (!isEditMode) {
                methods.reset(defaultValues);
            }

            toast.success(isEditMode ? TEXTS.UPDATE_SUCCESS : TEXTS.CREATE_SUCCESS, {
                id: `document-type-group-${isEditMode ? "updated" : "created"}`
            });

            router.push("/admin/document-types?tab=groups");
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} document type group:`, error);
            toast.error(isEditMode ? TEXTS.UPDATE_ERROR : TEXTS.CREATE_ERROR, {
                id: `document-type-group-${isEditMode ? "update" : "create"}-error`
            });
        }
    }

    if (loading) {
        return (
            <div className="p-6 text-center">
                <LoadingIcon />
                <p className="mt-2">{TEXTS.LOADING_GROUP}</p>
            </div>
        );
    }

    if (isEditMode && !group) {
        return (
            <div className="p-6 text-center">
                <p>{TEXTS.GROUP_NOT_FOUND}</p>
            </div>
        );
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="name"
                    label={TEXTS.NAME_LABEL}
                    placeholder={TEXTS.NAME_PLACEHOLDER}
                    required
                    requiredText={TEXTS.NAME_REQUIRED}
                />

                <LongText
                    name="description"
                    label={TEXTS.DESCRIPTION_LABEL}
                    placeholder={TEXTS.DESCRIPTION_PLACEHOLDER}
                    required={false}
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {methods.formState.isSubmitting && <LoadingIcon className="mr-2" />}
                        {isEditMode ? TEXTS.UPDATE_BUTTON : TEXTS.CREATE_BUTTON}
                    </Button>
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => router.push("/admin/document-types?tab=groups")}
                    >
                        {TEXTS.CANCEL_BUTTON}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
