"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createBanner, getBanner, updateBanner } from "@/app/actions/banner-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { LongText } from "@/components/forms/fields/long-text";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import { CustomSwitch } from "@/components/ui/custom-switch";
import {
    audienceOptions,
    bannerColors,
    type BannerFormValues,
    defaultValues,
    getColorSchemeFromBackgroundColor,
    iconOptions,
    TEXTS
} from "@/lib/banner-constants";
import { mapIdToOption } from "@/utils/form-utils";

interface BannerFormProps {
    bannerId?: string;
}

export function BannerForm({ bannerId }: BannerFormProps) {
    const router = useRouter();
    const isEditMode = !!bannerId;
    const [loading, setLoading] = useState(isEditMode);

    const methods = useForm<BannerFormValues>({
        defaultValues
    });

    useEffect(() => {
        async function fetchBannerData() {
            if (!isEditMode || !bannerId) return;

            try {
                setLoading(true);
                const result = await getBanner(bannerId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/banners");
                    return;
                }

                const data = result.data;

                methods.reset({
                    title: data.title || null,
                    text: data.text || null,
                    audience: data.audience
                        ? mapIdToOption(
                              data.audience,
                              audienceOptions,
                              (a) => a.id,
                              (a) => a.label
                          )
                        : audienceOptions[0],
                    color_scheme: getColorSchemeFromBackgroundColor(data.background_color || "") || bannerColors[0],
                    icon: data.icon
                        ? mapIdToOption(
                              data.icon,
                              iconOptions,
                              (o) => o.id,
                              (o) => o.label
                          )
                        : null,
                    cta_text: data.cta_text || null,
                    cta_link: data.cta_link || null,
                    days_to_live: data.days_to_live || 0,
                    seconds_before_show: data.seconds_before_show || 0,
                    enable_dismiss: data.enable_dismiss !== false,
                    enabled: data.enabled
                });
            } catch (err: unknown) {
                console.error("Error fetching banner:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchBannerData();
    }, [bannerId, isEditMode, methods, router]);

    async function onSubmit(data: BannerFormValues) {
        try {
            if (isEditMode && bannerId) {
                const result = await updateBanner(bannerId, data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.editErrorMessage);
                    return;
                }

                toast.success(TEXTS.editSuccessMessage, { id: "banner-updated" });
                router.push("/admin/banners");
            } else {
                const result = await createBanner(data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.createErrorMessage);
                    return;
                }

                methods.reset(defaultValues);
                toast.success(TEXTS.createSuccessMessage, { id: "banner-created" });
                router.push("/admin/banners");
            }
        } catch (error: unknown) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} banner:`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                id: `banner-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="title"
                    label={TEXTS.titleLabel}
                    placeholder={TEXTS.titlePlaceholder}
                    required
                    requiredText={TEXTS.titleRequired}
                />

                <LongText
                    name="text"
                    label={TEXTS.textLabel}
                    placeholder={TEXTS.textPlaceholder}
                    required
                    requiredText={TEXTS.textRequired}
                />

                <SingleSelect
                    name="audience"
                    label={TEXTS.audienceLabel}
                    placeholder={TEXTS.audiencePlaceholder}
                    options={audienceOptions}
                    required
                    requiredText={TEXTS.audienceRequired}
                />

                <SingleSelect
                    name="color_scheme"
                    label={TEXTS.colorSchemeLabel}
                    placeholder={TEXTS.colorSchemePlaceholder}
                    options={bannerColors}
                    required
                    requiredText={TEXTS.colorSchemeRequired}
                />

                <SingleSelect
                    name="icon"
                    label={TEXTS.iconLabel}
                    placeholder={TEXTS.iconPlaceholder}
                    options={iconOptions}
                    required
                    requiredText={TEXTS.iconRequired}
                />

                <ShortText
                    name="cta_text"
                    label={TEXTS.ctaTextLabel}
                    placeholder={TEXTS.ctaTextPlaceholder}
                    required={false}
                />

                <ShortText
                    name="cta_link"
                    label={TEXTS.ctaLinkLabel}
                    placeholder={TEXTS.ctaLinkPlaceholder}
                    required={false}
                    pattern={RegExp(/^https?:\/\/.+/i)}
                    patternMessage={TEXTS.ctaLinkPatternMessage}
                />

                <div>
                    <NumberInput
                        name="days_to_live"
                        label={TEXTS.daysToLiveLabel}
                        placeholder=""
                        required
                        requiredText={TEXTS.daysToLiveRequired}
                        min={0}
                        max={365}
                    />
                    <p className="text-sm text-muted-foreground mt-1 text-right">{TEXTS.daysToLiveDescription}</p>
                </div>

                <div>
                    <NumberInput
                        name="seconds_before_show"
                        label={TEXTS.secondsBeforeShowLabel}
                        placeholder=""
                        required
                        requiredText={TEXTS.secondsBeforeShowRequired}
                        min={0}
                        max={60}
                    />
                    <p className="text-sm text-muted-foreground mt-1 text-right">
                        {TEXTS.secondsBeforeShowDescription}
                    </p>
                </div>

                <div className="space-y-1">
                    <CustomSwitch name="enable_dismiss" label={TEXTS.enableDismissLabel} />
                    <p className="text-sm text-muted-foreground text-right">{TEXTS.enableDismissDescription}</p>
                </div>

                <div className="space-y-1">
                    <CustomSwitch name="enabled" label={TEXTS.enabledLabel} />
                    <p className="text-sm text-muted-foreground text-right">{TEXTS.enabledDescription}</p>
                </div>

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/banners")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
