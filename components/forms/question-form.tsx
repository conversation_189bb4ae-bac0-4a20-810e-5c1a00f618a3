"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

import {
    createQuestion,
    getQuestion,
    getQuestionFormData,
    QuestionFormData,
    updateQuestion
} from "@/app/actions/question-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import {
    ConditionDependency,
    ConditionQuestion,
    ConditionSelector
} from "@/components/forms/fields/condition-selector";
import { DropdownOptionEditor } from "@/components/forms/fields/dropdown-option-editor";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import { CustomSwitch } from "@/components/ui/custom-switch";
import { useQuestionGroups } from "@/hooks/use-question-groups";
import {
    createSelectOption,
    defaultValues,
    DependencyFromDB,
    extractId,
    getSectionLabel,
    mapScholarshipToOption,
    QuestionFormValues,
    QuestionFromDB,
    QuestionMetadataFromDB,
    QuestionSection,
    QuestionType,
    Scholarship,
    SECTION_OPTIONS,
    TEXTS,
    TYPE_TRANSLATION_KEYS,
    typeOptions,
    validateSelectOptions
} from "@/lib/question-constants";
import {
    BackendConditionDependency,
    transformConditionsFromDatabase,
    transformConditionsToDatabase
} from "@/utils/condition-utils";
import { mapIdsToOptions, mapIdToOption } from "@/utils/form-utils";

const transformFormDataToQuestionData = (data: QuestionFormValues): QuestionFormData => {
    const metadata: QuestionMetadataFromDB = {
        label: data.title,
        required: data.is_required,
        tooltip: data.tooltip,
        placeholder: data.metadata.placeholder,
        min: data.metadata.min,
        max: data.metadata.max,
        isFutureAllowed: data.metadata.isFutureAllowed,
        allowPast: data.metadata.allowPast,
        showSearch: data.metadata.showSearch,
        pattern: data.metadata.pattern,
        patternMessage: data.metadata.patternMessage
    };

    if (data.metadata.options) {
        metadata.options = data.metadata.options;
    }

    // Use shared utility to transform conditions from form format to database format
    const transformedDependencies = data.enable_dependencies
        ? transformConditionsToDatabase(data.dependencies as unknown as ConditionDependency[])
        : [];

    return {
        type: extractId(data.type) as QuestionType,
        group_id: extractId(data.group_id),
        section: extractId(data.section) as QuestionSection,
        metadata,
        scholarship_ids:
            extractId(data.section) === "specific_scholarship" ? data.scholarship_ids.map((item) => item.id) : [],
        dependencies: transformedDependencies
    };
};

const transformQuestionToFormData = (
    question: QuestionFromDB,
    scholarshipIds: string[],
    dependencies: DependencyFromDB[],
    scholarships: Scholarship[],
    processedQuestions: ConditionQuestion[],
    groups: Array<{ id: string; name: string }>
): QuestionFormValues => {
    const metadata = question.metadata as QuestionMetadataFromDB | null;

    const scholarshipOptions = mapIdsToOptions(
        scholarshipIds,
        scholarships,
        (scholarship) => scholarship.id,
        (scholarship) => scholarship.title,
        (id) => `Scholarship ID: ${id}`
    );

    // Use shared utility to transform conditions from database format to form format
    const mappedDependencies = transformConditionsFromDatabase(
        (dependencies || []) as unknown as BackendConditionDependency[],
        processedQuestions
    );

    return {
        title: metadata?.label || "",
        type: createSelectOption(question.type, TEXTS[TYPE_TRANSLATION_KEYS[question.type]]),
        tooltip: metadata?.tooltip || "",
        group_id: mapIdToOption(
            question.group_id || "",
            groups,
            (group) => group.id,
            (group) => group.name,
            (id) => id
        ),
        is_required: metadata?.required || false,
        section: question.section
            ? createSelectOption(question.section, getSectionLabel(question.section))
            : createSelectOption("data_entry", TEXTS.sectionOptionData),
        scholarship_ids: scholarshipOptions,
        enable_dependencies: (dependencies && dependencies.length > 0) || false,
        dependencies: mappedDependencies,
        metadata: {
            placeholder: metadata?.placeholder || "",
            options: Array.isArray(metadata?.options) && metadata.options.length > 0 ? metadata.options : undefined,
            min: metadata?.min,
            max: metadata?.max,
            isFutureAllowed: metadata?.isFutureAllowed || false,
            allowPast: metadata?.allowPast ?? true,
            showSearch: metadata?.showSearch || false,
            pattern: metadata?.pattern || "",
            patternMessage: metadata?.patternMessage || ""
        }
    };
};

const PlaceholderField = () => (
    <ShortText name="metadata.placeholder" label={TEXTS.placeholderLabel} placeholder={TEXTS.placeholderPlaceholder} />
);

const TextFields = () => (
    <>
        <PlaceholderField />
        <ShortText
            name="metadata.pattern"
            label={TEXTS.patternLabel}
            placeholder={TEXTS.patternPlaceholder}
            defaultValue=".*"
        />
        <ShortText
            name="metadata.patternMessage"
            label={TEXTS.patternMessageLabel}
            placeholder={TEXTS.patternMessagePlaceholder}
            required={false}
        />
    </>
);

const SelectFields = () => (
    <>
        <PlaceholderField />
        <DropdownOptionEditor name="metadata.options" label={TEXTS.optionsLabel} required />
        <CustomSwitch name="metadata.showSearch" label={TEXTS.enableSearchLabel} />
    </>
);

const NumberFields = () => (
    <>
        <PlaceholderField />
        <NumberInput name="metadata.min" label={TEXTS.minValueLabel} placeholder={TEXTS.minValuePlaceholder} />
        <NumberInput name="metadata.max" label={TEXTS.maxValueLabel} placeholder={TEXTS.maxValuePlaceholder} />
    </>
);

const DateFields = () => (
    <>
        <CustomSwitch name="metadata.isFutureAllowed" label={TEXTS.allowFutureDateLabel} />
        <CustomSwitch name="metadata.allowPast" label={TEXTS.allowPastDateLabel} />
    </>
);

interface QuestionFormProps {
    questionId?: string;
}

export function QuestionForm({ questionId }: QuestionFormProps) {
    const router = useRouter();
    const [scholarships, setScholarships] = useState<Scholarship[]>([]);
    const [availableQuestions, setAvailableQuestions] = useState<ConditionQuestion[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isInitialLoading, setIsInitialLoading] = useState(!!questionId);

    const { items: groups } = useQuestionGroups();

    const methods = useForm<QuestionFormValues>({
        defaultValues,
        mode: "onChange"
    });

    const { append: appendDependency, remove: removeDependency } = useFieldArray({
        control: methods.control,
        name: "dependencies"
    });

    const watchTypeValue = methods.watch("type");
    const watchSectionValue = methods.watch("section");
    const watchEnableDependencies = methods.watch("enable_dependencies");

    const watchType = watchTypeValue?.id as QuestionType;
    const watchSection = watchSectionValue?.id;

    const handleAddDependency = () => {
        appendDependency({ question_id: createSelectOption("", "") });
    };

    const handleRemoveDependency = (index: number) => {
        removeDependency(index);
    };

    useEffect(() => {
        if (!watchEnableDependencies) {
            methods.setValue("dependencies", []);
        }
    }, [watchEnableDependencies, methods]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const formDataResult = await getQuestionFormData();
                if (!formDataResult.success || !formDataResult.data) {
                    throw new Error(formDataResult.error || TEXTS.dataFetchError);
                }

                const { scholarships: fetchedScholarships, availableQuestions: fetchedQuestions } = formDataResult.data;

                setScholarships(fetchedScholarships);

                const processedQuestions = fetchedQuestions.map((q) => ({
                    id: q.id,
                    type: q.type,
                    metadata: q.metadata,
                    groups_question: Array.isArray(q.groups_question) ? q.groups_question[0] : q.groups_question
                })) as ConditionQuestion[];
                setAvailableQuestions(processedQuestions);

                if (questionId) {
                    setIsInitialLoading(true);
                    const result = await getQuestion(questionId);
                    if (result.success && result.data) {
                        const { question, scholarshipIds, dependencies, groupInfo } = result.data;

                        let allGroups: Array<{ id: string; name: string }> = groups.map((g) => ({
                            id: g.id,
                            name: g.name
                        }));

                        if (groupInfo && !allGroups.find((g) => g.id === groupInfo.id)) {
                            allGroups = [...allGroups, groupInfo];
                        }

                        const formData = transformQuestionToFormData(
                            question as QuestionFromDB,
                            scholarshipIds,
                            dependencies as DependencyFromDB[],
                            fetchedScholarships,
                            processedQuestions,
                            allGroups
                        );
                        methods.reset(formData);
                    } else {
                        toast.error(result.error || TEXTS.dataFetchError);
                        router.push("/admin/questions");
                    }
                    setIsInitialLoading(false);
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                toast.error(TEXTS.dataFetchError);
                setIsInitialLoading(false);
                if (questionId) {
                    router.push("/admin/questions");
                }
            }
        };

        fetchData();
    }, [questionId, methods, router, groups]);

    const onSubmit = async (data: QuestionFormValues) => {
        const validationError = validateSelectOptions(data);
        if (validationError) {
            toast.error(validationError);
            return;
        }

        setIsLoading(true);
        try {
            const questionData = transformFormDataToQuestionData(data);

            let result;
            if (questionId) {
                result = await updateQuestion(questionId, questionData);
                if (result.success) {
                    toast.success(TEXTS.successUpdateMessage);
                } else {
                    toast.error(result.error || TEXTS.errorUpdateMessage);
                }
            } else {
                result = await createQuestion(questionData);
                if (result.success) {
                    toast.success(TEXTS.successMessage);
                } else {
                    toast.error(result.error || TEXTS.errorMessage);
                }
            }

            if (result.success) {
                router.push("/admin/questions");
            }
        } catch (error) {
            console.error("Error submitting form:", error);
            toast.error(questionId ? TEXTS.errorUpdateMessage : TEXTS.errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        router.push("/admin/questions");
    };

    const renderAdditionalFields = () => {
        switch (watchType) {
            case "short_text":
            case "long_text":
                return <TextFields />;
            case "single_select":
            case "multi_select":
                return <SelectFields />;
            case "number_input":
                return <NumberFields />;
            case "date_picker":
                return <DateFields />;
            default:
                return null;
        }
    };

    if (isInitialLoading) {
        return (
            <div className="container py-8 mx-auto max-w-5xl">
                <LoadingIcon text={TEXTS.loading} />
            </div>
        );
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} dir="rtl" className="w-full space-y-6">
                <div className="space-y-6">
                    <ShortText name="title" label={TEXTS.titleLabel} placeholder={TEXTS.titlePlaceholder} required />

                    <SingleSelect
                        name="type"
                        label={TEXTS.typeLabel}
                        placeholder={TEXTS.typePlaceholder}
                        required
                        options={typeOptions}
                        showSearch={true}
                    />

                    <SingleSelect
                        name="section"
                        label={TEXTS.sectionLabel}
                        placeholder={TEXTS.sectionPlaceholder}
                        required
                        options={SECTION_OPTIONS}
                        showSearch={false}
                    />

                    <ShortText
                        name="tooltip"
                        label={TEXTS.tooltipLabel}
                        placeholder={TEXTS.tooltipPlaceholder}
                        required={false}
                    />

                    <SingleSelect
                        name="group_id"
                        label={TEXTS.groupLabel}
                        placeholder={TEXTS.groupPlaceholder}
                        required
                        options={groups.map((g) => createSelectOption(g.id, g.name))}
                    />

                    <CustomSwitch name="is_required" label={TEXTS.isRequiredLabel} />

                    {renderAdditionalFields()}

                    {watchSection === "specific_scholarship" && (
                        <MultiSelect
                            name="scholarship_ids"
                            label={TEXTS.selectScholarshipsLabel}
                            placeholder={TEXTS.selectScholarshipsPlaceholder}
                            options={scholarships.map(mapScholarshipToOption)}
                            showSearch={true}
                        />
                    )}

                    <CustomSwitch name="enable_dependencies" label={TEXTS.enableDependenciesLabel} />

                    {watchEnableDependencies && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">{TEXTS.dependenciesLabel}</h3>
                            <ConditionSelector
                                availableQuestions={availableQuestions}
                                onAdd={handleAddDependency}
                                onRemove={handleRemoveDependency}
                                fieldArrayName="dependencies"
                            />
                        </div>
                    )}
                </div>

                <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={handleCancel}>
                        {TEXTS.cancelButton}
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                        {isLoading ? TEXTS.submitting : questionId ? TEXTS.updateQuestion : TEXTS.createQuestion}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
