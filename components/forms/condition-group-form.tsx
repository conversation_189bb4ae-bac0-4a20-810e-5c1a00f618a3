"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

import {
    createConditionGroup,
    getConditionGroupWithDependencies,
    getPersonalDetailsQuestions,
    updateConditionGroup
} from "@/app/actions/condition-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { ConditionQuestion, ConditionSelector, ConditionType } from "@/components/forms/fields/condition-selector";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type ConditionGroupFormValues, defaultConditionGroupValues, TEXTS } from "@/lib/condition-group-constants";

interface ConditionGroupFormValuesProps {
    groupId?: string;
}

export function ConditionGroupForm({ groupId }: ConditionGroupFormValuesProps) {
    const router = useRouter();
    const isEditMode = !!groupId;
    const [loading, setLoading] = useState(isEditMode);
    const [personalDetailsQuestions, setPersonalDetailsQuestions] = useState<ConditionQuestion[]>([]);
    const [activeTab, setActiveTab] = useState<string>("general");

    const methods = useForm<ConditionGroupFormValues>({
        defaultValues: defaultConditionGroupValues
    });

    const { append, remove } = useFieldArray({
        control: methods.control,
        name: "dependencies"
    });

    const handleAddDependency = () => {
        append({
            question_id: { id: "", label: "" },
            condition_type: "in" as ConditionType,
            condition_value: []
        });
    };

    const handleRemoveDependency = (index: number) => {
        remove(index);
    };

    useEffect(() => {
        async function fetchQuestions() {
            try {
                const questionsResult = await getPersonalDetailsQuestions();

                if (!questionsResult.success || !questionsResult.data) {
                    toast.error(questionsResult.error || TEXTS.errorMessage);
                    return;
                }

                setPersonalDetailsQuestions(questionsResult.data as unknown as ConditionQuestion[]);
            } catch (err) {
                console.error("Error fetching questions:", err);
                toast.error(TEXTS.errorMessage);
            }
        }

        fetchQuestions();
    }, []);

    useEffect(() => {
        async function fetchConditionGroup() {
            if (!isEditMode || !groupId || personalDetailsQuestions.length === 0) {
                return;
            }

            try {
                setLoading(true);

                const result = await getConditionGroupWithDependencies(groupId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/scholarships/conditions/groups");
                    return;
                }

                const { group, dependencies } = result.data;

                methods.reset({
                    name: group.name,
                    dependencies: dependencies
                });
            } catch (err) {
                console.error("Error fetching condition group:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchConditionGroup();
    }, [groupId, isEditMode, router, methods, personalDetailsQuestions]);

    async function onSubmit(data: ConditionGroupFormValues) {
        try {
            if (isEditMode && groupId) {
                const result = await updateConditionGroup(groupId, data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.editErrorMessage);
                    return;
                }

                toast.success(TEXTS.editSuccessMessage);
                router.push("/admin/scholarships/conditions/groups");
            } else {
                const result = await createConditionGroup(data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.createErrorMessage);
                    return;
                }

                toast.success(TEXTS.createSuccessMessage);
                router.push("/admin/scholarships/conditions/groups");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} condition group:`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <Card>
                    <CardHeader>
                        <CardTitle className="text-right">{TEXTS.formTitle}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
                            <TabsList className="w-full grid grid-cols-2 h-12 bg-muted">
                                <TabsTrigger
                                    value="general"
                                    className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                                >
                                    {TEXTS.generalTabLabel}
                                </TabsTrigger>
                                <TabsTrigger
                                    value="conditions"
                                    className="text-base sm:text-sm bg-muted/80 text-muted-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                                >
                                    {TEXTS.conditionsTabLabel}
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent value="general" className="mt-6 space-y-4">
                                <ShortText
                                    name="name"
                                    label={TEXTS.nameLabel}
                                    placeholder={TEXTS.namePlaceholder}
                                    required
                                    requiredText={TEXTS.nameRequired}
                                />
                            </TabsContent>
                            <TabsContent value="conditions" className="mt-6 space-y-4">
                                <div className="space-y-2">
                                    <h2 className="text-xl font-semibold text-right">{TEXTS.conditionsHeading}</h2>
                                    <p className="text-gray-500 text-right">{TEXTS.conditionsDescription}</p>
                                </div>
                                <ConditionSelector
                                    availableQuestions={personalDetailsQuestions}
                                    onAdd={handleAddDependency}
                                    onRemove={handleRemoveDependency}
                                    fieldArrayName="dependencies"
                                />
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                        <Button type="submit" disabled={methods.formState.isSubmitting}>
                            {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                        </Button>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.push("/admin/scholarships/conditions/groups")}
                        >
                            {TEXTS.cancelButtonText}
                        </Button>
                    </CardFooter>
                </Card>
            </form>
        </FormProvider>
    );
}
