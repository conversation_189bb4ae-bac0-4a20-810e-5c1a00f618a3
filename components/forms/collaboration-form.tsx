"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createCollaboration, getCollaborationById, updateCollaboration } from "@/app/actions/collaboration-actions";
import {
    getAllQuestionsForCollaboration,
    getCollaborationConditions,
    getCollaborationQuestions
} from "@/app/actions/collaboration-form-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import {
    ConditionDependency,
    ConditionQuestion,
    ConditionSelector,
    ConditionType,
    ConditionValue
} from "@/components/forms/fields/condition-selector";
import { Option } from "@/components/forms/fields/dropdown-base";
import { LongText } from "@/components/forms/fields/long-text";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { But<PERSON> } from "@/components/ui/button";
import { FormMessage } from "@/components/ui/form";
import { authTypeOptions, authTypeToOption, optionToAuthType, TEXTS, URL_REGEX } from "@/lib/collaboration-constants";
import {
    type BackendConditionDependency,
    transformConditionsFromDatabase,
    transformConditionsToDatabase
} from "@/utils/condition-utils";
import { mapQuestionIdsToOptions } from "@/utils/form-utils";

interface CollaborationFormValues {
    name: string;
    description: string;
    api_endpoint: string;
    auth_type: Option | null;
    auth_value: string;
    dependencies: ConditionDependency[];
    question_ids: Option[];
}

const defaultValues: CollaborationFormValues = {
    name: "",
    description: "",
    api_endpoint: "",
    auth_type: null,
    auth_value: "",
    dependencies: [],
    question_ids: []
};

interface CollaborationFormProps {
    collaborationId?: string;
}

export function CollaborationForm({ collaborationId }: CollaborationFormProps) {
    const router = useRouter();
    const isEditMode = !!collaborationId;
    const [loading, setLoading] = useState(isEditMode);
    const [notFound, setNotFound] = useState(false);
    const [availableQuestions, setAvailableQuestions] = useState<ConditionQuestion[]>([]);

    const methods = useForm<CollaborationFormValues>({
        defaultValues
    });

    const authType = methods.watch("auth_type");

    useEffect(() => {
        async function fetchQuestions() {
            try {
                const result = await getAllQuestionsForCollaboration();
                if (result.success && result.data) {
                    setAvailableQuestions(result.data);
                } else {
                    throw new Error(result.error || TEXTS.fetchQuestionsError);
                }
            } catch (err) {
                console.error("Error fetching questions:", err);
                toast.error(TEXTS.fetchQuestionsError);
            }
        }

        fetchQuestions();
    }, []);

    useEffect(() => {
        async function fetchCollaboration() {
            if (!isEditMode || !collaborationId || availableQuestions.length === 0) return;

            try {
                setLoading(true);

                const collaborationResult = await getCollaborationById(collaborationId);
                if (!collaborationResult.success || !collaborationResult.data) {
                    setNotFound(true);
                    return;
                }

                const collaboration = collaborationResult.data;

                let authValue = "";
                if (collaboration.auth_type !== "none" && collaboration.auth_value && collaboration.auth_value !== "") {
                    authValue = collaboration.auth_value;
                }

                const conditionsResult = await getCollaborationConditions(collaborationId);
                const rawConditions = conditionsResult.success ? conditionsResult.data || [] : [];

                const mappedConditions = transformConditionsFromDatabase(
                    rawConditions as unknown as BackendConditionDependency[],
                    availableQuestions
                );

                const questionIdsResult = await getCollaborationQuestions(collaborationId);
                const questionIds = questionIdsResult.success ? questionIdsResult.data || [] : [];

                const mappedQuestionIds = mapQuestionIdsToOptions(questionIds, availableQuestions);

                methods.reset({
                    name: collaboration.name,
                    description: collaboration.description || "",
                    api_endpoint: collaboration.api_endpoint,
                    auth_type: authTypeToOption(collaboration.auth_type),
                    auth_value: authValue,
                    dependencies: mappedConditions,
                    question_ids: mappedQuestionIds
                });
            } catch (err) {
                console.error("Error fetching collaboration:", err);
                toast.error(TEXTS.fetchCollaborationError);
                setNotFound(true);
            } finally {
                setLoading(false);
            }
        }

        fetchCollaboration();
    }, [collaborationId, isEditMode, methods, availableQuestions]);

    async function onSubmit(data: CollaborationFormValues) {
        try {
            const transformedDependencies = transformConditionsToDatabase(data.dependencies);

            const submissionData = {
                ...data,
                auth_type: data.auth_type ? optionToAuthType(data.auth_type) : "none",
                question_ids: data.question_ids.map((option) => option.id),
                dependencies: transformedDependencies
            };

            const result = isEditMode
                ? await updateCollaboration(collaborationId!, submissionData)
                : await createCollaboration(submissionData);

            if (!result.success) {
                toast.error(result.error || (isEditMode ? TEXTS.updateErrorMessage : TEXTS.createErrorMessage));
                return;
            }

            toast.success(isEditMode ? TEXTS.updateSuccessMessage : TEXTS.createSuccessMessage);
            router.push("/admin/collaborations");
        } catch (err) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} collaboration:`, err);
            toast.error(isEditMode ? TEXTS.updateErrorMessage : TEXTS.createErrorMessage);
        }
    }

    const addDependency = () => {
        const currentDependencies = methods.getValues("dependencies") || [];
        methods.setValue("dependencies", [
            ...currentDependencies,
            {
                question_id: { id: "", label: "" },
                condition_type: "" as ConditionType,
                condition_value: null as unknown as ConditionValue
            }
        ]);
    };

    const removeDependency = (index: number) => {
        const currentDependencies = methods.getValues("dependencies") || [];
        methods.setValue(
            "dependencies",
            currentDependencies.filter((_, i) => i !== index)
        );
    };

    if (notFound) {
        return (
            <div className="text-center">
                <p>{TEXTS.notFoundText}</p>
                <div className="mt-4">
                    <Button type="button" onClick={() => router.push("/admin/collaborations")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                </div>
            </div>
        );
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.loadingText} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6" dir="rtl">
                <ShortText
                    name="name"
                    label={TEXTS.nameLabel}
                    placeholder={TEXTS.namePlaceholder}
                    required
                    requiredText={TEXTS.nameRequired}
                />

                <LongText
                    name="description"
                    label={TEXTS.descriptionLabel}
                    placeholder={TEXTS.descriptionPlaceholder}
                />

                <ShortText
                    name="api_endpoint"
                    label={TEXTS.apiEndpointLabel}
                    placeholder={TEXTS.apiEndpointPlaceholder}
                    required
                    requiredText={TEXTS.apiEndpointRequired}
                    pattern={URL_REGEX}
                    patternMessage={TEXTS.apiEndpointError}
                />

                <SingleSelect
                    name="auth_type"
                    label={TEXTS.authTypeLabel}
                    placeholder={TEXTS.authTypePlaceholder}
                    options={authTypeOptions}
                    required
                />

                {authType?.id === "bearer_token" && (
                    <ShortText
                        name="auth_value"
                        label={TEXTS.authValueLabel}
                        placeholder={TEXTS.authValuePlaceholder}
                        required
                        requiredText={TEXTS.authValueRequired}
                    />
                )}

                <div className="space-y-2">
                    <h3 className="text-lg font-medium">{TEXTS.conditionsLabel}</h3>
                    <p className="text-sm text-muted-foreground">{TEXTS.conditionsDescription}</p>
                    <ConditionSelector
                        availableQuestions={availableQuestions}
                        onAdd={addDependency}
                        onRemove={removeDependency}
                        fieldArrayName="dependencies"
                    />
                </div>

                {/* Questions Multi-Select */}
                <div className="space-y-2">
                    <h3 className="text-lg font-medium">{TEXTS.questionsLabel}</h3>
                    <p className="text-sm text-muted-foreground">{TEXTS.questionsDescription}</p>
                    <MultiSelect
                        name="question_ids"
                        label={TEXTS.questionsLabel}
                        placeholder={TEXTS.questionsPlaceholder}
                        options={availableQuestions.map((q) => ({
                            id: q.id,
                            label: q.metadata?.label || q.metadata?.placeholder || `Question ID: ${q.id}`,
                            subtitle: q.groups_question?.name || ""
                        }))}
                        required
                        requiredText={TEXTS.questionsRequired}
                        showSearch
                    />
                </div>

                <FormMessage />

                {/* Action Buttons */}
                <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/collaborations")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
