"use client";

import { useRouter } from "next/navigation";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { searchUserByEmail } from "@/app/actions/user-actions";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { defaultValues, EMAIL_REGEX, TEXTS, UserFormData } from "@/lib/user-constants";

export function UserSearchForm() {
    const router = useRouter();
    const methods = useForm<UserFormData>({
        defaultValues,
        mode: "onChange"
    });

    const {
        handleSubmit,
        formState: { isSubmitting }
    } = methods;

    const onSubmit = async (data: UserFormData) => {
        try {
            const result = await searchUserByEmail(data.email);

            if (!result.success) {
                toast.error(result.error || TEXTS.userSearchError);
                return;
            }

            if (!result.user) {
                toast.error(TEXTS.userNotFound);
                return;
            }

            router.push(`/admin/users/${result.user.user_id}`);
        } catch (error) {
            console.error("Error searching for user:", error);
            toast.error(TEXTS.userSearchError);
        }
    };

    return (
        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="email"
                    label={TEXTS.emailLabel}
                    placeholder={TEXTS.emailPlaceholder}
                    type="text"
                    required={true}
                    requiredText={TEXTS.emailRequired}
                    pattern={EMAIL_REGEX}
                    patternMessage={TEXTS.emailError}
                />

                <div className="flex justify-between">
                    <Button dir="rtl" type="submit" disabled={isSubmitting}>
                        {isSubmitting ? TEXTS.searching : TEXTS.searchButton}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
