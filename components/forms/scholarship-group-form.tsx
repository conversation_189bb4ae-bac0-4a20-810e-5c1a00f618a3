"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import {
    createScholarshipGroup,
    getScholarshipGroup,
    updateScholarshipGroup,
    uploadScholarshipGroupImage
} from "@/app/actions/scholarship-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { FileUpload } from "@/components/forms/fields/file-upload";
import { LongText } from "@/components/forms/fields/long-text";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import {
    defaultScholarshipGroupFormValues,
    ICON_MAP,
    iconOptions,
    type ScholarshipGroupFormValues,
    TEXTS
} from "@/lib/scholarship-group-constants";
import { mapIdToOption } from "@/utils/form-utils";

interface ScholarshipGroupFormProps {
    groupId?: string;
}

export function ScholarshipGroupForm({ groupId }: ScholarshipGroupFormProps) {
    const router = useRouter();
    const isEditMode = !!groupId;
    const [loading, setLoading] = useState(isEditMode);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const methods = useForm<ScholarshipGroupFormValues>({
        defaultValues: defaultScholarshipGroupFormValues
    });

    const selectedIcon = methods.watch("icon");
    const IconComponent = ICON_MAP[selectedIcon.id];

    useEffect(() => {
        async function fetchScholarshipGroup() {
            if (!isEditMode || !groupId) return;

            try {
                setLoading(true);
                const result = await getScholarshipGroup(groupId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/scholarships");
                    return;
                }

                methods.reset({
                    ...defaultScholarshipGroupFormValues,
                    title: result.data.title,
                    slug: result.data.slug,
                    description: result.data.description,
                    icon: mapIdToOption(
                        result.data.icon,
                        iconOptions,
                        (option) => option.id,
                        (option) => option.label
                    )
                });
            } catch (err) {
                console.error("Error fetching scholarship group:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchScholarshipGroup();
    }, [groupId, isEditMode, methods, router]);

    async function onSubmit(data: ScholarshipGroupFormValues) {
        try {
            if (isEditMode && groupId) {
                const updateResult = await updateScholarshipGroup(groupId, {
                    title: data.title,
                    slug: data.slug,
                    description: data.description,
                    icon: data.icon.id
                });

                if (!updateResult.success) {
                    toast.error(updateResult.error || TEXTS.editErrorMessage);
                    return;
                }

                if (selectedFile) {
                    const uploadResult = await uploadScholarshipGroupImage(groupId, selectedFile);
                    if (!uploadResult.success) {
                        toast.error(uploadResult.error || TEXTS.apiErrorUpload);
                        return;
                    }
                }

                toast.success(TEXTS.editSuccessMessage, { id: "scholarship-group-updated" });
                router.push("/admin/scholarships");
            } else {
                const createResult = await createScholarshipGroup({
                    title: data.title,
                    slug: data.slug,
                    description: data.description,
                    icon: data.icon.id,
                    image_url: null
                });

                if (!createResult.success) {
                    toast.error(createResult.error || TEXTS.createErrorMessage);
                    return;
                }

                if (selectedFile && createResult.data) {
                    const uploadResult = await uploadScholarshipGroupImage(createResult.data.id, selectedFile);
                    if (!uploadResult.success) {
                        toast.error(uploadResult.error || TEXTS.apiErrorUpload);
                        return;
                    }
                }

                methods.reset(defaultScholarshipGroupFormValues);
                setSelectedFile(null);

                toast.success(TEXTS.createSuccessMessage, {
                    id: "scholarship-group-created"
                });
                router.push("/admin/scholarships");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} scholarship group:`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                id: `scholarship-group-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="title"
                    label={TEXTS.groupNameLabel}
                    placeholder={TEXTS.groupNamePlaceholder}
                    required
                    requiredText={TEXTS.groupNameRequired}
                    minLength={2}
                />

                <ShortText
                    name="slug"
                    label={TEXTS.groupSlugLabel}
                    placeholder={TEXTS.groupSlugPlaceholder}
                    required
                    pattern={/^[a-zA-Z-]+$/}
                    patternMessage={TEXTS.groupSlugPattern}
                    requiredText={TEXTS.groupSlugRequired}
                />

                <LongText
                    name="description"
                    label={TEXTS.descriptionLabel}
                    placeholder={TEXTS.descriptionPlaceholder}
                    required
                    requiredText={TEXTS.descriptionRequired}
                    minLength={10}
                />

                <div className="flex items-end gap-3">
                    <div className="flex-1">
                        <SingleSelect
                            name="icon"
                            label={TEXTS.iconLabel}
                            placeholder={TEXTS.iconPlaceholder}
                            options={iconOptions}
                            required
                            requiredText={TEXTS.iconRequired}
                            showSearch={true}
                        />
                    </div>
                    {IconComponent && (
                        <div
                            data-testid="icon-preview"
                            className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center text-primary mb-[2px]"
                        >
                            <IconComponent size={24} strokeWidth={1.5} />
                        </div>
                    )}
                </div>

                <FileUpload
                    name="image_file"
                    label={TEXTS.imageLabel}
                    required={!isEditMode}
                    requiredText={TEXTS.imageRequired}
                    onFileSelect={setSelectedFile}
                    acceptedFileType="image/webp"
                    acceptedFileTypeDescription={TEXTS.webpDescription}
                    maxSizeInMB={5}
                    fileTypeErrorMessage={TEXTS.fileTypeError}
                    fileSizeErrorMessage={(maxSize) => `${TEXTS.fileSizeError} ${maxSize}MB`}
                    dragDropText={TEXTS.dragDropText}
                    fileFormatDescription={(maxSize) => `${TEXTS.fileFormatDesc} ${maxSize}MB`}
                    selectedFileText={TEXTS.selectedFileText}
                />

                <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/scholarships")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
