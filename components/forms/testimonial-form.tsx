"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createTestimonial, getTestimonial, updateTestimonial } from "@/app/actions/testimonial-actions";
import { LongText } from "@/components/forms/fields/long-text";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import {
    defaultTestimonialValues,
    TESTIMONIAL_TEXTS,
    type TestimonialFormValues,
    testimonialTypeOptions,
    testimonialTypeOptionsMap
} from "@/lib/testimonial-constants";

import { LoadingIcon } from "../common/loading-icon";

interface TestimonialFormProps {
    testimonialId?: string;
}

export function TestimonialForm({ testimonialId }: TestimonialFormProps) {
    const router = useRouter();
    const isEditMode = !!testimonialId;
    const [loading, setLoading] = useState(isEditMode);

    const methods = useForm<TestimonialFormValues>({
        defaultValues: defaultTestimonialValues
    });

    useEffect(() => {
        async function fetchTestimonial() {
            if (!isEditMode || !testimonialId) return;

            try {
                setLoading(true);
                const result = await getTestimonial(testimonialId);

                if (!result.success || !result.data) {
                    toast.error(TESTIMONIAL_TEXTS.editNotFoundMessage);
                    router.push("/admin/testimonials");
                    return;
                }

                methods.reset({
                    name: result.data.name,
                    text: result.data.text,
                    type: { id: result.data.type, label: testimonialTypeOptionsMap[result.data.type] }
                });
            } catch (err) {
                console.error("Error fetching testimonial:", err);
                toast.error(TESTIMONIAL_TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TESTIMONIAL_TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchTestimonial();
    }, [testimonialId, isEditMode, methods, router]);

    async function onSubmit(formData: TestimonialFormValues) {
        try {
            const result =
                isEditMode && testimonialId
                    ? await updateTestimonial(testimonialId, formData)
                    : await createTestimonial(formData);

            if (!result.success) {
                toast.error(result.error, {
                    id: `testimonial-${isEditMode ? "update" : "create"}-error`
                });
                return;
            }

            if (isEditMode) {
                toast.success(TESTIMONIAL_TEXTS.editSuccessMessage, { id: "testimonial-updated" });
                router.push("/admin/testimonials");
            } else {
                methods.reset(defaultTestimonialValues);
                toast.success(TESTIMONIAL_TEXTS.createSuccessMessage, { id: "testimonial-created" });
                router.push("/admin/testimonials");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} testimonial:`, error);
            toast.error(isEditMode ? TESTIMONIAL_TEXTS.editErrorMessage : TESTIMONIAL_TEXTS.createErrorMessage, {
                id: `testimonial-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TESTIMONIAL_TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TESTIMONIAL_TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="name"
                    label={TESTIMONIAL_TEXTS.nameLabel}
                    placeholder={TESTIMONIAL_TEXTS.namePlaceholder}
                    required
                    requiredText={TESTIMONIAL_TEXTS.nameRequired}
                />

                <LongText
                    name="text"
                    label={TESTIMONIAL_TEXTS.textLabel}
                    placeholder={TESTIMONIAL_TEXTS.textPlaceholder}
                    required
                    requiredText={TESTIMONIAL_TEXTS.textRequired}
                />

                <SingleSelect
                    name="type"
                    label={TESTIMONIAL_TEXTS.typeLabel}
                    placeholder={TESTIMONIAL_TEXTS.typePlaceholder}
                    options={testimonialTypeOptions}
                    required
                    requiredText={TESTIMONIAL_TEXTS.typeRequired}
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TESTIMONIAL_TEXTS.updateButtonText : TESTIMONIAL_TEXTS.createButtonText}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/testimonials")}>
                        {TESTIMONIAL_TEXTS.cancelButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
