"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createContact, getContactById, updateContact } from "@/app/actions/contact-actions";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { FormMessage } from "@/components/ui/form";
import { type ContactFormValues, defaultValues, TEXTS } from "@/lib/contact-constants";

interface ContactFormProps {
    contactId?: string;
}

export function ContactForm({ contactId }: ContactFormProps) {
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const methods = useForm<ContactFormValues>({
        defaultValues
    });

    useEffect(() => {
        if (contactId) {
            const loadContact = async () => {
                try {
                    const result = await getContactById(contactId);
                    if (result.success && result.data) {
                        methods.reset({
                            email: result.data.email
                        });
                    } else {
                        throw new Error(result.error || TEXTS.loadingError);
                    }
                } catch (error) {
                    console.error("Error loading contact:", error);
                    toast.error(TEXTS.loadingError, {
                        description: error instanceof Error ? error.message : undefined
                    });
                }
            };

            loadContact();
        }
    }, [contactId, methods]);

    async function onSubmit(values: ContactFormValues) {
        setIsSubmitting(true);
        const toastId = toast.loading(TEXTS.submitting);

        try {
            let result;

            if (contactId) {
                result = await updateContact(contactId, values);
            } else {
                result = await createContact(values);
            }

            if (result.success) {
                toast.success(TEXTS.submitSuccess, { id: toastId });
                router.push("/admin/contact");
            } else {
                throw new Error(result.error || (contactId ? TEXTS.updateError : TEXTS.createError));
            }
        } catch (error) {
            console.error("Error submitting contact form:", error);
            toast.error(contactId ? TEXTS.updateError : TEXTS.createError, {
                description: error instanceof Error ? error.message : undefined,
                id: toastId
            });
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6" dir="rtl">
                <ShortText
                    name="email"
                    placeholder={TEXTS.emailPlaceholder}
                    label={TEXTS.emailLabel}
                    required={true}
                    patternMessage={TEXTS.emailError}
                    pattern={/^[^\s@]+@[^\s@]+\.[^\s@]+$/}
                />
                <FormMessage />

                <div className="flex justify-between">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => router.push("/admin/contact")}
                        disabled={isSubmitting}
                        data-testid="cancel-button"
                    >
                        {TEXTS.cancel}
                    </Button>
                    <Button type="submit" disabled={isSubmitting} data-testid="submit-button">
                        {TEXTS.submit}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
