"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import {
    type CouponGroupFormValues,
    createCouponGroup,
    getCouponGroup,
    updateCouponGroup
} from "@/app/actions/coupon-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { LongText } from "@/components/forms/fields/long-text";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";

export const TEXTS = {
    errorMessage: "שגיאה בפעולה",

    editSuccessMessage: "קבוצת הקופונים עודכנה בהצלחה",
    editErrorMessage: "שגיאה בעדכון קבוצת הקופונים",
    editLoadingMessage: "טוען קבוצת קופונים...",
    editNotFoundMessage: "קבוצת הקופונים לא נמצאה",
    updateButtonText: "עדכן קבוצת קופונים",
    cancelButtonText: "ביטול",

    createSuccessMessage: "קבוצת הקופונים נוצרה בהצלחה",
    createErrorMessage: "שגיאה ביצירת קבוצת הקופונים",
    createButtonText: "צור קבוצת קופונים",

    nameLabel: "שם",
    namePlaceholder: "הזן שם לקבוצת הקופונים",
    nameRequired: "שם קבוצת הקופונים הוא שדה חובה",

    descriptionLabel: "תיאור",
    descriptionPlaceholder: "הזן תיאור לקבוצת הקופונים"
};

const defaultValues: CouponGroupFormValues = {
    name: "",
    description: ""
};

interface CouponGroupFormProps {
    groupId?: string;
}

export function CouponGroupForm({ groupId }: CouponGroupFormProps) {
    const router = useRouter();
    const isEditMode = !!groupId;
    const [loading, setLoading] = useState(isEditMode);

    const methods = useForm<CouponGroupFormValues>({
        defaultValues
    });

    useEffect(() => {
        async function fetchCouponGroup() {
            if (!isEditMode || !groupId) return;

            try {
                setLoading(true);
                const result = await getCouponGroup(groupId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.editNotFoundMessage);
                    router.push("/admin/coupons?tab=groups");
                    return;
                }

                const data = result.data;
                methods.reset({
                    name: data.name,
                    description: data.description || ""
                });
            } catch (err) {
                console.error("Error fetching coupon group:", err);
                toast.error(TEXTS.editErrorMessage, {
                    description: err instanceof Error ? err.message : TEXTS.errorMessage
                });
            } finally {
                setLoading(false);
            }
        }

        fetchCouponGroup();
    }, [groupId, isEditMode, methods, router]);

    async function onSubmit(data: CouponGroupFormValues) {
        try {
            if (isEditMode && groupId) {
                const result = await updateCouponGroup(groupId, data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.editErrorMessage);
                    return;
                }

                toast.success(TEXTS.editSuccessMessage, { id: "coupon-group-updated" });
                router.push("/admin/coupons?tab=groups");
            } else {
                const result = await createCouponGroup(data);

                if (!result.success) {
                    toast.error(result.error || TEXTS.createErrorMessage);
                    return;
                }

                methods.reset(defaultValues);
                toast.success(TEXTS.createSuccessMessage, { id: "coupon-group-created" });
                router.push("/admin/coupons?tab=groups");
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} coupon group:`, error);
            toast.error(isEditMode ? TEXTS.editErrorMessage : TEXTS.createErrorMessage, {
                id: `coupon-group-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : TEXTS.errorMessage
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.editLoadingMessage} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="name"
                    label={TEXTS.nameLabel}
                    placeholder={TEXTS.namePlaceholder}
                    required
                    requiredText={TEXTS.nameRequired}
                />

                <LongText
                    name="description"
                    label={TEXTS.descriptionLabel}
                    placeholder={TEXTS.descriptionPlaceholder}
                    required={false}
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.updateButtonText : TEXTS.createButtonText}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/coupons?tab=groups")}>
                        {TEXTS.cancelButtonText}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
