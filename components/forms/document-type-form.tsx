"use client";

import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createDocumentType, getDocumentType, updateDocumentType } from "@/app/actions/document-type-actions";
import { getDocumentTypeGroups } from "@/app/actions/document-type-group-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { FileUpload } from "@/components/forms/fields/file-upload";
import { LongText } from "@/components/forms/fields/long-text";
import { MultiSelect } from "@/components/forms/fields/multi-select";
import { NumberInput } from "@/components/forms/fields/number-input";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, FormItem, FormMessage } from "@/components/ui/form";
import {
    defaultValues,
    type DocumentTypeFormData,
    type DocumentTypeFormValues,
    MIME_TYPE_OPTIONS,
    TEXTS
} from "@/lib/document-type-constants";
import { mapIdToOption } from "@/utils/form-utils";
import { getSupabaseClient } from "@/utils/supabase/client";

interface DocumentTypeFormProps {
    documentTypeId?: string;
}

export function DocumentTypeForm({ documentTypeId }: DocumentTypeFormProps) {
    const router = useRouter();
    const isEditMode = !!documentTypeId;
    const [loading, setLoading] = useState(isEditMode);

    const [groups, setGroups] = useState<{ id: string; name: string }[]>([]);
    const [, setGroupsLoading] = useState(true);

    const groupOptions = useMemo(
        () =>
            groups.map((g) => ({
                id: g.id,
                label: g.name
            })),
        [groups]
    );

    const methods = useForm<DocumentTypeFormValues>({
        defaultValues
    });

    useEffect(() => {
        async function fetchGroups() {
            setGroupsLoading(true);
            try {
                const result = await getDocumentTypeGroups();
                if (result.success && result.data) {
                    setGroups(result.data.map((group) => ({ id: group.id, name: group.name })));
                } else {
                    console.error("Error fetching document type groups:", result.error);
                    setGroups([]);
                }
            } catch (err) {
                console.error("Error fetching document type groups:", err);
                setGroups([]);
            } finally {
                setGroupsLoading(false);
            }
        }

        fetchGroups();
    }, []);

    useEffect(() => {
        async function fetchDocumentType() {
            if (!isEditMode || !documentTypeId) return;

            try {
                setLoading(true);
                const { data, error } = await getDocumentType(documentTypeId);

                if (error || !data) {
                    toast.error(TEXTS.DOCUMENT_TYPE_NOT_FOUND_MESSAGE);
                    router.push("/admin/document-types");
                    return;
                }

                const selectedGroup = mapIdToOption(
                    data.group_id,
                    groupOptions,
                    (group) => group.id,
                    (group) => group.label
                );

                methods.reset({
                    group_id: selectedGroup,
                    name: data.name,
                    description: data.description || "",
                    link_url: data.link_url || "",
                    example_file_path: data.example_file_path,
                    allowed_mime_types: Array.isArray(data.allowed_mime_types)
                        ? (data.allowed_mime_types as string[])
                        : [],
                    max_file_size_mb: typeof data.max_file_size_mb === "number" ? data.max_file_size_mb : 10
                });
            } catch (err) {
                console.error("Error fetching document type:", err);
                toast.error(TEXTS.ERROR_MESSAGE);
            } finally {
                setLoading(false);
            }
        }

        fetchDocumentType();
    }, [documentTypeId, isEditMode, methods, router, groupOptions]);

    const handleFileUpload = async (file: File | null) => {
        if (!file) return;

        try {
            const supabase = getSupabaseClient();
            const fileExt = file.name.includes(".") ? file.name.split(".").pop() : "לא ידוע";

            const fileId =
                isEditMode && documentTypeId ? documentTypeId : `temp_${Math.random().toString(36).substring(2, 15)}`;

            const fileName = `${fileId}.${fileExt}`;
            const filePath = fileName;

            const { error } = await supabase.storage.from("document_examples").upload(filePath, file, {
                upsert: true
            });

            if (error) throw error;

            methods.setValue("example_file_path", filePath);
            toast.success(TEXTS.FILE_UPLOAD_SUCCESS);
        } catch (error) {
            console.error("Error uploading file:", error);
            toast.error(TEXTS.UPLOAD_ERROR);
        }
    };

    async function onSubmit(data: DocumentTypeFormValues) {
        try {
            if (!data.group_id) {
                toast.error(TEXTS.GROUP_REQUIRED);
                return;
            }

            const payload: DocumentTypeFormData = {
                ...data,
                group_id: data.group_id
            };

            const result =
                isEditMode && documentTypeId
                    ? await updateDocumentType(documentTypeId, payload)
                    : await createDocumentType(payload);

            if (!result.success) {
                toast.error(
                    result.error || (isEditMode ? TEXTS.DOCUMENT_TYPE_UPDATE_ERROR : TEXTS.DOCUMENT_TYPE_CREATE_ERROR)
                );
                return;
            }

            toast.success(isEditMode ? TEXTS.UPDATE_SUCCESS : TEXTS.CREATE_SUCCESS);
            router.push("/admin/document-types");
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} document type:`, error);
            toast.error(isEditMode ? TEXTS.DOCUMENT_TYPE_UPDATE_ERROR : TEXTS.DOCUMENT_TYPE_CREATE_ERROR);
        }
    }

    if (loading) {
        return (
            <div className="p-6 text-center">
                <LoadingIcon />
                <p className="mt-2">{TEXTS.LOADING_DOCUMENT_TYPE}</p>
            </div>
        );
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                    control={methods.control}
                    name="group_id"
                    rules={{ required: TEXTS.GROUP_REQUIRED }}
                    render={({ field }) => (
                        <FormItem>
                            <SingleSelect
                                label={TEXTS.GROUP_LABEL}
                                placeholder={TEXTS.GROUP_PLACEHOLDER}
                                options={groupOptions}
                                required
                                requiredText={TEXTS.GROUP_REQUIRED}
                                showSearch
                                {...field}
                            />
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <ShortText
                    name="name"
                    label={TEXTS.NAME_LABEL}
                    placeholder={TEXTS.NAME_PLACEHOLDER}
                    required
                    requiredText={TEXTS.NAME_REQUIRED}
                />

                <LongText
                    name="description"
                    label={TEXTS.DESCRIPTION_LABEL}
                    placeholder={TEXTS.DESCRIPTION_PLACEHOLDER}
                />

                <ShortText
                    name="link_url"
                    label={TEXTS.LINK_URL_LABEL}
                    placeholder={TEXTS.LINK_URL_PLACEHOLDER}
                    required={false}
                    pattern={/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/}
                    patternMessage={TEXTS.LINK_URL_INVALID}
                />

                <NumberInput
                    name="max_file_size_mb"
                    label={TEXTS.MAX_FILE_SIZE_LABEL}
                    placeholder={TEXTS.MAX_FILE_SIZE_PLACEHOLDER}
                    min={1}
                    required
                    requiredText={TEXTS.MAX_FILE_SIZE_REQUIRED}
                />

                <FileUpload
                    name="example_file_path"
                    label={TEXTS.EXAMPLE_FILE_LABEL}
                    acceptedFileType="application/pdf"
                    acceptedFileTypeDescription={TEXTS.PDF_FILE_TYPE_DESCRIPTION}
                    maxSizeInMB={methods.watch("max_file_size_mb") || 10}
                    fileTypeErrorMessage={TEXTS.FILE_TYPE_ERROR}
                    fileSizeErrorMessage={TEXTS.FILE_SIZE_ERROR}
                    dragDropText={TEXTS.DRAG_DROP_TEXT}
                    fileFormatDescription={TEXTS.FILE_FORMAT_DESCRIPTION}
                    selectedFileText={TEXTS.SELECTED_FILE_TEXT}
                    onFileSelect={handleFileUpload}
                />

                <MultiSelect
                    name="allowed_mime_types"
                    label={TEXTS.ALLOWED_MIME_TYPES_LABEL}
                    placeholder={TEXTS.ALLOWED_MIME_TYPES_PLACEHOLDER}
                    options={MIME_TYPE_OPTIONS}
                    required
                    requiredText={TEXTS.MIME_TYPE_REQUIRED}
                    showSearch
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {methods.formState.isSubmitting && <LoadingIcon className="mr-2" />}
                        {isEditMode ? TEXTS.UPDATE_BUTTON : TEXTS.CREATE_BUTTON}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/document-types")}>
                        {TEXTS.CANCEL_BUTTON}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
