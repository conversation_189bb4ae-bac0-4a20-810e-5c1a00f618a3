"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

import { createFaq, getFaq, updateFaq } from "@/app/actions/faq-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { LongText } from "@/components/forms/fields/long-text";
import { ShortText } from "@/components/forms/fields/short-text";
import { Button } from "@/components/ui/button";
import { defaultValues, type FaqFormValues, TEXTS } from "@/lib/faq-constants";

interface FaqFormProps {
    faqId?: string;
}

export function FaqForm({ faqId }: FaqFormProps) {
    const router = useRouter();
    const isEditMode = !!faqId;
    const [loading, setLoading] = useState(isEditMode);
    const [initialValues, setInitialValues] = useState<FaqFormValues>(defaultValues);

    const methods = useForm<FaqFormValues>({
        values: initialValues
    });

    useEffect(() => {
        async function fetchFaq() {
            if (!isEditMode || !faqId) return;

            try {
                setLoading(true);
                const result = await getFaq(faqId);

                if (!result.success || !result.data) {
                    toast.error(result.error || TEXTS.EDIT_NOT_FOUND_MESSAGE);
                    router.push("/admin/faq");
                    return;
                }

                setInitialValues({
                    question: result.data.question,
                    answer: result.data.answer
                });
            } catch (err) {
                console.error("Error fetching FAQ:", err);
                toast.error(TEXTS.EDIT_ERROR_MESSAGE, {
                    description: err instanceof Error ? err.message : "Error fetching FAQ"
                });
            } finally {
                setLoading(false);
            }
        }

        fetchFaq();
    }, [faqId, isEditMode, router]);

    async function onSubmit(data: FaqFormValues) {
        try {
            let result;

            if (isEditMode && faqId) {
                result = await updateFaq(faqId, data);

                if (!result.success) {
                    throw new Error(result.error);
                }

                toast.success(TEXTS.EDIT_SUCCESS_MESSAGE, { id: "faq-updated" });
            } else {
                result = await createFaq(data);

                if (!result.success) {
                    throw new Error(result.error);
                }

                methods.reset(defaultValues);
                toast.success(TEXTS.CREATE_SUCCESS_MESSAGE, { id: "faq-created" });
            }

            router.push("/admin/faq");
        } catch (error) {
            console.error(`Error ${isEditMode ? "updating" : "creating"} FAQ:`, error);
            toast.error(isEditMode ? TEXTS.EDIT_ERROR_MESSAGE : TEXTS.CREATE_ERROR_MESSAGE, {
                id: `faq-${isEditMode ? "update" : "create"}-error`,
                description: error instanceof Error ? error.message : "Error occurred"
            });
        }
    }

    if (loading) {
        return <LoadingIcon text={TEXTS.EDIT_LOADING_MESSAGE} />;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
                <ShortText
                    name="question"
                    label={TEXTS.QUESTION_LABEL}
                    placeholder={TEXTS.QUESTION_PLACEHOLDER}
                    required
                    requiredText={TEXTS.QUESTION_REQUIRED}
                />

                <LongText
                    name="answer"
                    label={TEXTS.ANSWER_LABEL}
                    placeholder={TEXTS.ANSWER_PLACEHOLDER}
                    required
                    requiredText={TEXTS.ANSWER_REQUIRED}
                />

                <div className="flex justify-between">
                    <Button type="submit" disabled={methods.formState.isSubmitting}>
                        {isEditMode ? TEXTS.UPDATE_BUTTON_TEXT : TEXTS.CREATE_BUTTON_TEXT}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.push("/admin/faq")}>
                        {TEXTS.CANCEL_BUTTON_TEXT}
                    </Button>
                </div>
            </form>
        </FormProvider>
    );
}
