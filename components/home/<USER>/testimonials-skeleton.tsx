"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON> } from "@/components/ui/card";

export default function TestimonialsSkeleton() {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
                <Card key={i} className="bg-white overflow-hidden shadow-sm border border-gray-100 rounded-xl relative">
                    <CardHeader className="text-center pb-2 bg-gradient-to-r from-primary/5 to-blue-50/50 pt-6">
                        <div className="flex justify-center mb-3">
                            {[...Array(5)].map((_, j) => (
                                <div key={j} className="h-4 w-4 mx-0.5 rounded-full bg-primary/20 animate-pulse" />
                            ))}
                        </div>
                    </CardHeader>

                    <CardContent className="px-6 py-6">
                        <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded-full w-3/4 mx-auto animate-pulse" />
                            <div className="h-4 bg-gray-200 rounded-full w-full mx-auto animate-pulse" />
                            <div className="h-4 bg-gray-200 rounded-full w-5/6 mx-auto animate-pulse" />
                        </div>
                    </CardContent>

                    <CardFooter className="flex justify-end pt-2 pb-6 px-6 border-t border-gray-50">
                        <div className="flex items-center gap-2">
                            <div className="h-4 w-24 bg-gray-200 rounded-full animate-pulse" />
                            <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse" />
                        </div>
                    </CardFooter>
                </Card>
            ))}
        </div>
    );
}
