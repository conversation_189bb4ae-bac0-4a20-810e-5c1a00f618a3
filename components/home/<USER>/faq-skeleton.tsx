import { ChevronDown } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";

const FAQSkeleton: React.FC = () => {
    return (
        <div className="w-full backdrop-blur-md bg-card rounded-[var(--radius)] shadow-[0_10px_30px_rgba(0,0,0,0.08)] overflow-hidden border border-border">
            <div className="divide-y divide-border/50">
                {/* Create 5 skeleton accordion items */}
                {Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className={`py-8 px-8 ${index === 0 ? "bg-accent/30" : ""}`}>
                        <div className="flex flex-row-reverse justify-between items-center">
                            {/* Chevron icon on right side */}
                            <div className="opacity-70">
                                <ChevronDown size={20} className={index === 0 ? "transform rotate-180" : ""} />
                            </div>

                            {/* Question skeleton - right aligned */}
                            <span className="font-medium text-xl md:text-2xl flex-1 text-right ml-4">
                                <Skeleton className="h-8 w-3/4 mr-0 ml-auto" />
                            </span>
                        </div>

                        {/* Show an "open" accordion for the first item */}
                        {index === 0 && (
                            <div className="bg-secondary/50 p-6 rounded-[calc(var(--radius)-4px)] mt-8" dir="rtl">
                                <div className="flex flex-col gap-3">
                                    <Skeleton className="h-6 w-full mr-0" />
                                    <Skeleton className="h-6 w-5/6 mr-0 ml-auto" />
                                    <Skeleton className="h-6 w-4/6 mr-0 ml-auto" />
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default FAQSkeleton;
