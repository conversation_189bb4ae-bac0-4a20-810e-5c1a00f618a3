"use client";

import { motion } from "framer-motion";
import { ArrowDown, Award, BookOpen, Building2, GraduationCap, MapPin, Medal, Shield, Star } from "lucide-react";
import Link from "next/link";

import { ScholarshipGroupSkeletonList } from "@/components/scholarship-groups/scholarship-group-skeleton";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { useScholarshipGroups } from "@/hooks/use-scholarship-groups";

const TEXTS = {
    title: "מלגות לפי קטגוריות",
    description: "מצאו את המלגה המתאימה לכם לפי תחום העניין",
    error: "אירעה שגיאה בטעינת המלגות. אנא נסו שוב מאוחר יותר.",
    discoverMore: "גלה עוד",
    loadMore: "טען עוד"
};

const iconMap = {
    "graduation-cap": GraduationCap,
    certificate: Medal,
    award: Award,
    shield: Shield,
    books: BookO<PERSON>,
    building: Building2,
    map: MapPin,
    star: Star
} as const;

export default function ScholarshipGroups() {
    const { displayedGroups, loading, error, hasMore, loadMore } = useScholarshipGroups();

    return (
        <section className="w-full">
            <div className="w-full">
                {loading && <ScholarshipGroupSkeletonList />}

                {error && (
                    <div className="text-center">
                        <p className="text-red-600">{TEXTS.error}</p>
                    </div>
                )}

                {!loading && !error && displayedGroups.length > 0 && (
                    <>
                        <motion.div
                            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6"
                            initial="hidden"
                            animate="visible"
                            layout
                        >
                            {displayedGroups.map((group, index) => {
                                const Icon = iconMap[group.icon as keyof typeof iconMap] || Star;
                                return (
                                    <motion.div
                                        key={group.id}
                                        initial={group.isNew ? { opacity: 0, y: 20, scale: 0.95 } : false}
                                        animate={group.isNew ? { opacity: 1, y: 0, scale: 1 } : false}
                                        transition={{
                                            type: "spring",
                                            stiffness: 260,
                                            damping: 20,
                                            delay: index * 0.05
                                        }}
                                        className="h-full"
                                        layout
                                    >
                                        <Link
                                            href={`/scholarships/groups/${group.slug}`}
                                            className="group block h-full"
                                        >
                                            <Card className="h-full border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md hover:border-primary/20 hover:scale-[1.02] flex flex-col">
                                                <CardHeader className="pb-0 mb-4">
                                                    <motion.div
                                                        className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center text-primary transition-colors group-hover:bg-primary group-hover:text-white"
                                                        initial={group.isNew ? { scale: 0.8, opacity: 0 } : false}
                                                        animate={group.isNew ? { scale: 1, opacity: 1 } : false}
                                                        transition={{ delay: index * 0.05 + 0.2 }}
                                                    >
                                                        <Icon size="24" strokeWidth={1.5} />
                                                    </motion.div>
                                                </CardHeader>
                                                <CardContent className="space-y-2 flex-grow">
                                                    <motion.h3
                                                        className="font-semibold text-lg text-gray-900 group-hover:text-primary"
                                                        initial={group.isNew ? { opacity: 0, x: -10 } : false}
                                                        animate={group.isNew ? { opacity: 1, x: 0 } : false}
                                                        transition={{ delay: index * 0.05 + 0.1 }}
                                                    >
                                                        {group.title}
                                                    </motion.h3>
                                                    <motion.p
                                                        className="text-sm text-gray-600 line-clamp-3"
                                                        initial={group.isNew ? { opacity: 0 } : false}
                                                        animate={group.isNew ? { opacity: 1 } : false}
                                                        transition={{ delay: index * 0.05 + 0.15 }}
                                                    >
                                                        {group.description}
                                                    </motion.p>
                                                </CardContent>
                                                <CardFooter>
                                                    <motion.div
                                                        className="flex items-center text-primary text-sm font-medium"
                                                        initial={group.isNew ? { opacity: 0, y: 5 } : false}
                                                        animate={group.isNew ? { opacity: 1, y: 0 } : false}
                                                        transition={{ delay: index * 0.05 + 0.25 }}
                                                    >
                                                        <span>{TEXTS.discoverMore}</span>
                                                        <ArrowDown size="16" className="mr-1" />
                                                    </motion.div>
                                                </CardFooter>
                                            </Card>
                                        </Link>
                                    </motion.div>
                                );
                            })}
                        </motion.div>

                        {hasMore && (
                            <motion.div
                                className="flex justify-center mt-8"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                    <Button
                                        variant="outline"
                                        onClick={loadMore}
                                        className="border-primary text-primary hover:bg-primary/5"
                                    >
                                        {TEXTS.loadMore}
                                    </Button>
                                </motion.div>
                            </motion.div>
                        )}
                    </>
                )}
            </div>
        </section>
    );
}
