"use client";

import { useUser } from "@clerk/nextjs";
import Link from "next/link";

import { Database } from "@/types/database.types";

type Scholarship = Database["public"]["Enums"]["scholarship_type"];

interface FinalCTAProps {
    title?: string;
    description?: string;
    buttonText?: string;
    scholarshipType?: Scholarship;
}

const TEXTS = {
    guest: "לחצו לקבלת רשימת מלגות אישית",
    submission: "להרשמה וקבלת הגשה אוטומטית",
    guidance: "לקבלת סיוע אישי בהגשת המלגה"
};

export default function FinalCTA({
    title = "מוכנים להתחיל את המסע לקבלת מלגה?",
    description = "הרשמו עכשיו וקבלו גישה למאות מלגות מותאמות אישית עבורכם",
    buttonText = "לחצו וקבלו רשימת מלגות מותאמת אישית",
    scholarshipType
}: FinalCTAProps) {
    const { user, isLoaded: isUserLoaded } = useUser();
    const isAuthenticated = !!user && isUserLoaded;

    const getButtonText = () => {
        if (!isUserLoaded) return buttonText;
        if (!isAuthenticated) return TEXTS.guest;
        if (scholarshipType) return TEXTS[scholarshipType];
        return buttonText;
    };

    const getButtonLink = () => {
        if (!isAuthenticated) return "/login";
        return "/subscriptions";
    };

    return (
        <div className="text-center bg-gradient-to-r from-blue-50 to-primary/10 p-10 rounded-xl shadow-sm">
            <h3 className="text-2xl font-bold mb-4 text-gray-800">{title}</h3>
            <p className="text-gray-600 mb-6 max-w-xl mx-auto">{description}</p>
            <Link href={getButtonLink()}>
                <button className="bg-primary text-white px-10 py-4 rounded-lg text-lg font-semibold hover:bg-primary/90 transition-all shadow-md hover:shadow-lg">
                    {getButtonText()}
                </button>
            </Link>
        </div>
    );
}
