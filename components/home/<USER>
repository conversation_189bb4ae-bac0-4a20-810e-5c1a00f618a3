"use client";

import FAQSkeleton from "@/components/home/<USER>/faq-skeleton";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useFAQ } from "@/hooks/use-faq";

interface FAQProps {
    title?: string;
    subtitle?: string;
}

const FAQ: React.FC<FAQProps> = ({ title, subtitle }) => {
    const { items, loading, error } = useFAQ();

    return (
        <div className="w-full bg-background">
            <div className="w-full max-w-4xl mx-auto">
                <div className="text-center mb-6">
                    <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-l from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2">
                        {title}
                    </h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subtitle}</p>
                </div>

                {loading ? (
                    <FAQSkeleton />
                ) : error ? (
                    <div className="text-center text-red-500 py-4">{error}</div>
                ) : (
                    <div className="w-full backdrop-blur-md bg-card rounded-[var(--radius)] shadow-[0_10px_30px_rgba(0,0,0,0.08)] overflow-hidden border border-border">
                        <Accordion
                            type="single"
                            collapsible
                            defaultValue={items.length > 0 ? `item-${items[0].id}` : undefined}
                            className="w-full"
                        >
                            {items.map((item, index) => (
                                <div key={item.id} className={`${index !== 0 ? "border-t border-border/50" : ""}`}>
                                    <AccordionItem value={`item-${item.id}`} className="border-none group">
                                        <AccordionTrigger
                                            className="flex w-full justify-between items-center text-right py-8 px-8 hover:bg-accent/30 transition-all duration-300 hover:no-underline group-hover:text-primary cursor-pointer"
                                            aria-label={`${item.question}`}
                                        >
                                            <span className="font-medium text-xl md:text-2xl flex-1 text-right mr-4 text-card-foreground">
                                                {item.question}
                                            </span>
                                        </AccordionTrigger>
                                        <AccordionContent className="text-right p-8 text-muted-foreground leading-relaxed text-lg">
                                            <div className="bg-secondary/50 p-6 rounded-[calc(var(--radius)-4px)]">
                                                {item.answer}
                                            </div>
                                        </AccordionContent>
                                    </AccordionItem>
                                </div>
                            ))}
                        </Accordion>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FAQ;
