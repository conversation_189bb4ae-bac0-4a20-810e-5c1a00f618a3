"use client";

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    BookOpen,
    Building2,
    GraduationCap,
    MapPin,
    Medal,
    Shield,
    Star
} from "lucide-react";
import Link from "next/link";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { useScholarships } from "@/hooks/use-scholarships";
import { Database } from "@/types/database.types";

type Scholarship = Database["public"]["Tables"]["scholarships"]["Row"];

const TEXTS = {
    title: "מלגות",
    viewAll: "צפה בכל המלגות",
    discoverMore: "גלה עוד"
};

const iconMap = {
    "graduation-cap": GraduationCap,
    certificate: Medal,
    award: Award,
    shield: Shield,
    books: BookOpen,
    building: Building2,
    map: MapPin,
    star: Star
} as const;

type IconType = keyof typeof iconMap;

const Scholarships = () => {
    const { displayedScholarships } = useScholarships();

    return (
        <section className="max-w-4xl mx-auto w-full">
            <div className="w-full">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl md:text-3xl font-bold">{TEXTS.title}</h2>
                    <Link href="/scholarships" className="flex items-center gap-2">
                        {TEXTS.viewAll}
                        <ArrowLeft size="16" />
                    </Link>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                    {displayedScholarships.map((scholarship: Scholarship) => {
                        const defaultIcon = "graduation-cap" as IconType;
                        const Icon = iconMap[defaultIcon];
                        return (
                            <Link
                                href={`/scholarships/${scholarship.slug}`}
                                key={scholarship.id}
                                className="group block h-full"
                            >
                                <Card className="h-full border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md hover:border-primary/20 hover:scale-[1.02]">
                                    <CardHeader className="pb-0">
                                        <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center text-primary transition-colors group-hover:bg-primary group-hover:text-white">
                                            <Icon size="24" strokeWidth={1.5} />
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-2">
                                        <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary">
                                            {scholarship.title}
                                        </h3>
                                        <p className="text-sm text-gray-600 line-clamp-3">
                                            {scholarship.short_description}
                                        </p>
                                    </CardContent>
                                    <CardFooter>
                                        <div className="flex items-center text-primary text-sm font-medium">
                                            <span>{TEXTS.discoverMore}</span>
                                            <ArrowDown size="16" className="mr-1" />
                                        </div>
                                    </CardFooter>
                                </Card>
                            </Link>
                        );
                    })}
                </div>
            </div>
        </section>
    );
};

export default Scholarships;
