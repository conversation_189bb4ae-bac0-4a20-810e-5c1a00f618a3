"use client";

import { motion } from "framer-motion";
import { Building, Star, User } from "lucide-react";
import React, { useMemo } from "react";

import TestimonialsSkeleton from "@/components/home/<USER>/testimonials-skeleton";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card";
import { useTestimonials } from "@/hooks/use-testimonials";
import { type TestimonialType } from "@/lib/testimonial-constants";
import { cn } from "@/lib/utils";

const TEXTS = {
    title: "סטודנטים מספרים עלינו",
    subtitle: "מה הסטודנטים אומרים על הסיוע שקיבלו מאיתנו",
    viewAll: "צפה בכל",
    discoverMore: "גלה עוד"
};

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const StarRating = () => {
    return (
        <div className="flex justify-center mb-3">
            {[1, 2, 3, 4, 5].map((star) => (
                <Star
                    key={star}
                    className="h-4 w-4 mx-0.5 text-primary fill-primary transition-transform duration-300 group-hover:scale-110"
                />
            ))}
        </div>
    );
};

const TestimonialTypeIcon = ({ type }: { type: TestimonialType }) => {
    return type === "institution" ? (
        <Building className="h-5 w-5 text-primary opacity-70" />
    ) : (
        <User className="h-5 w-5 text-primary opacity-70" />
    );
};

interface TestimonialsProps {
    ids?: string[];
}

const Testimonials = ({ ids }: TestimonialsProps) => {
    const { items, loading, error } = useTestimonials(ids);

    const displayItems = useMemo(() => items, [items]);

    return (
        <motion.div
            className="w-full bg-gradient-to-br from-blue-50 via-white to-primary/5 py-16 rounded-3xl relative overflow-hidden"
            initial="visible"
            animate="visible"
            variants={{
                hidden: { opacity: 0 },
                visible: { opacity: 1 }
            }}
        >
            {/* Background pattern */}
            <div
                className="absolute inset-0 opacity-5"
                style={{
                    backgroundImage: `radial-gradient(circle, rgba(59,130,246,0.3) 1px, transparent 1px)`,
                    backgroundSize: "20px 20px"
                }}
            />

            {/* Decorative elements */}
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>

            <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 relative z-10">
                <motion.div className="text-center mb-12" variants={fadeInUp}>
                    <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
                        <span className="relative inline-block">{TEXTS.title}</span>
                    </h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">{TEXTS.subtitle}</p>
                </motion.div>

                {loading ? (
                    <TestimonialsSkeleton />
                ) : error ? (
                    <div className="text-center text-red-500 py-4">{error}</div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {displayItems.map((item, index) => (
                            <motion.div
                                key={item.id}
                                initial={{ opacity: 1, y: 0 }}
                                variants={{
                                    hidden: { opacity: 0, y: 20 },
                                    visible: {
                                        opacity: 1,
                                        y: 0,
                                        transition: {
                                            duration: 0.6,
                                            delay: index * 0.1
                                        }
                                    }
                                }}
                            >
                                <Card
                                    className={cn(
                                        "bg-white overflow-hidden shadow-md group h-full",
                                        "transition-all duration-300 ease-in-out",
                                        "hover:shadow-lg hover:-translate-y-2",
                                        "border border-gray-100 hover:border-primary/20",
                                        "rounded-xl relative flex flex-col"
                                    )}
                                >
                                    <CardHeader className="text-center pb-2 bg-gradient-to-r from-primary/5 to-blue-50/50 pt-6">
                                        <StarRating />
                                    </CardHeader>

                                    <CardContent className="px-6 py-6 flex-grow">
                                        <p className="italic text-gray-700 text-center leading-relaxed transition-all duration-300 group-hover:text-gray-900">
                                            &ldquo;{item.text}&rdquo;
                                        </p>
                                    </CardContent>

                                    <CardFooter className="flex justify-end pt-2 pb-6 px-6 mt-auto border-t border-gray-50">
                                        <div className="flex items-center gap-2">
                                            <p
                                                className={cn(
                                                    "font-medium text-sm transition-all duration-300",
                                                    item.type === "institution"
                                                        ? "text-blue-700 group-hover:text-blue-800"
                                                        : "text-primary group-hover:text-primary/80"
                                                )}
                                            >
                                                {item.name}
                                            </p>
                                            <TestimonialTypeIcon type={item.type} />
                                        </div>
                                    </CardFooter>
                                </Card>
                            </motion.div>
                        ))}
                    </div>
                )}
            </div>
        </motion.div>
    );
};

export default Testimonials;
