"use client";

import Image from "next/image";
import Link from "next/link";

interface PressProps {
    title?: string;
}

const PRESS_ITEMS = [
    {
        id: "Ynet",
        title: "Ynet",
        image: "/press/Ynet.svg",
        link: "http://www.ynet.co.il/articles/0,7340,L-4845735,00.html"
    },
    {
        id: "YsraelHayom",
        title: "Ysrae<PERSON> Hayom",
        image: "/press/YsraelHayom.svg",
        link: "http://www.israelhayom.co.il/article/407455"
    },
    {
        id: "Ma<PERSON>",
        title: "<PERSON><PERSON>",
        image: "/press/Mako.svg",
        link: "http://www.mako.co.il/study-career-study/articles/Article-27988298f42b651006.htm"
    },
    {
        id: "Geektime",
        title: "Geektime",
        image: "/press/Geektime.svg",
        link: "http://www.geektime.co.il/new-scholarship-site/"
    }
];

export function Press({ title }: PressProps) {
    return (
        <section className="bg-background py-12">
            <div className="container mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-l from-gray-900 to-gray-600 bg-clip-text text-transparent mb-10">
                    {title}
                </h2>
                <div className="flex flex-wrap justify-center gap-8 mx-auto">
                    {PRESS_ITEMS.map((item) => (
                        <Link
                            key={item.id}
                            href={item.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="transition-opacity hover:opacity-80 group"
                        >
                            <div className="relative w-40 h-24 flex items-center justify-center">
                                <Image
                                    src={item.image}
                                    alt={item.title}
                                    width={160}
                                    height={96}
                                    className="w-full h-full object-contain opacity-90 group-hover:opacity-100 transition-all"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = "none";
                                        target.nextElementSibling?.classList.remove("hidden");
                                    }}
                                />
                                <div className="hidden w-full h-full flex items-center justify-center text-sm text-gray-500 border border-gray-200 rounded">
                                    {item.title}
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>
            </div>
        </section>
    );
}

export default Press;
