"use client";

import { motion } from "framer-motion";
import dynamic from "next/dynamic";
import type { FC } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import animationData from "@/public/home/<USER>";

const TEXTS = {
    title: "קצת עלינו",
    description:
        "המלגות שלנו מאפשרות לכם לקבל מימון עבור הלימודים האקדמיים שלכם. אנו מציעים מגוון רחב של מלגות המותאמות לצרכים שונים ולמסלולי לימוד מגוונים. בין אם אתם סטודנטים מצטיינים, מתמודדים עם קשיים כלכליים, או מחפשים תמיכה בתחום לימודים ספציפי, יש סיכוי טוב שתמצאו מלגה שמתאימה לכם.",
    button: "למד עוד"
};

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const ContentWithImage: FC = () => {
    return (
        <div className="w-full max-w-4xl mx-auto">
            <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-50px" }}
                variants={{
                    hidden: {},
                    visible: {
                        transition: {
                            staggerChildren: 0.2
                        }
                    }
                }}
            >
                <Card className="bg-white overflow-hidden shadow-xl rounded-3xl border-0">
                    <CardContent className="p-0">
                        <div className="grid grid-cols-1 md:grid-cols-2">
                            {/* Image side */}
                            <motion.div
                                className="relative aspect-square md:aspect-auto bg-gradient-to-br from-blue-50 to-primary/5"
                                variants={fadeInUp}
                            >
                                <div
                                    className="absolute inset-0 opacity-5"
                                    style={{
                                        backgroundImage: `radial-gradient(circle, rgba(59,130,246,0.3) 1px, transparent 1px)`,
                                        backgroundSize: "15px 15px"
                                    }}
                                />
                                <div className="h-full w-full flex items-center justify-center p-6">
                                    <Lottie
                                        animationData={animationData}
                                        loop={true}
                                        className="w-full max-w-xs rounded-3xl overflow-hidden"
                                    />
                                </div>
                                <div className="absolute bottom-4 left-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
                            </motion.div>

                            <motion.div
                                className="p-8 md:p-10 lg:p-12 flex flex-col justify-center"
                                variants={fadeInUp}
                            >
                                <CardHeader className="p-0 mb-6">
                                    <CardTitle className="text-2xl md:text-3xl lg:text-4xl font-bold text-right leading-tight text-gray-900">
                                        <span className="relative inline-block">
                                            {TEXTS.title}
                                            <span className="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 rounded-full"></span>
                                        </span>
                                    </CardTitle>
                                </CardHeader>

                                <motion.p
                                    className="text-base md:text-lg text-gray-700 text-right leading-relaxed mb-8"
                                    variants={fadeInUp}
                                >
                                    {TEXTS.description}
                                </motion.p>

                                <CardFooter className="p-0">
                                    <motion.div className="text-right w-full" variants={fadeInUp}>
                                        <Button
                                            size="lg"
                                            className="bg-primary hover:bg-primary/90 text-white font-medium text-base px-8 py-6 h-2 transition-all shadow-md hover:shadow-lg"
                                            aria-label={TEXTS.button}
                                        >
                                            {TEXTS.button}
                                        </Button>
                                    </motion.div>
                                </CardFooter>
                            </motion.div>
                        </div>
                    </CardContent>
                </Card>
            </motion.div>
        </div>
    );
};

export default ContentWithImage;
