"use client";

import React, { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { LongText } from "@/components/forms/fields/long-text";
import { ShortText } from "@/components/forms/fields/short-text";
import { SingleSelect } from "@/components/forms/fields/single-select";
import { Button } from "@/components/ui/button";

const TEXTS = {
    subject: "נושא ההודעה",
    message: "תוכן ההודעה",
    email: "כתובת אימייל",
    successMessage: "הודעה נשלחה בהצלחה",
    errors: {
        emailMissing: "אימייל הוא שדה חובה",
        emailInvalid: "אימייל אינו תקין",
        subjectMissing: "נושא הוא שדה חובה",
        messageMissing: "הודעה היא שדה חובה",
        emailSendingFailed: "שגיאה בשליחת האימייל"
    },
    button: "שלח טופס",
    subjects: [
        { id: "שאלה כללית", label: "שאלה כללית", subtitle: "שאלה כללית" },
        { id: "מידע על מלגה ספציפית", label: "מידע על מלגה ספציפית", subtitle: "מידע על מלגה ספציפית" },
        { id: "עזרה בהגשת בקשה", label: "עזרה בהגשת בקשה", subtitle: "עזרה בהגשת בקשה" },
        { id: "הצעה לשיפור האתר", label: "הצעה לשיפור האתר", subtitle: "הצעה לשיפור האתר" },
        { id: "אחר", label: "אחר" }
    ]
};

interface ContactFormValues {
    email: string;
    subject: string;
    message: string;
}

interface ContactProps {
    title?: string;
    subtitle?: string;
}

const Contact: React.FC<ContactProps> = ({ title, subtitle }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [successMessage, setSuccessMessage] = useState("");
    const [errorMessage, setErrorMessage] = useState("");

    const methods = useForm<ContactFormValues>({
        defaultValues: {
            email: "",
            subject: "",
            message: ""
        }
    });

    const { handleSubmit, reset } = methods;

    const onSubmit = async (data: ContactFormValues) => {
        setIsLoading(true);
        setSuccessMessage("");
        setErrorMessage("");

        try {
            const response = await fetch("/api/email", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            });

            const responseData = await response.json();

            if (!response.ok) {
                throw new Error(responseData.error || TEXTS.errors.emailSendingFailed);
            }

            reset();
            setSuccessMessage(TEXTS.successMessage);
        } catch (error: unknown) {
            let message = TEXTS.errors.emailSendingFailed;
            if (error instanceof Error) {
                message = error.message || message;
            }
            setErrorMessage(message);
        } finally {
            setIsLoading(false);
        }
    };

    const subjectOptions = TEXTS.subjects;

    return (
        <div className="w-full bg-gray-50">
            <div className="w-full">
                <div className="text-center mb-4">
                    <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-l from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2">
                        {title}
                    </h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subtitle}</p>
                </div>

                <div className="max-w-4xl mx-auto">
                    <div className="bg-white shadow-[0_0_40px_rgba(0,0,0,0.06)] rounded-2xl p-8 md:p-10">
                        <FormProvider {...methods}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" dir="rtl">
                                <ShortText
                                    name="email"
                                    label=""
                                    placeholder={TEXTS.email}
                                    required={true}
                                    requiredText={TEXTS.errors.emailMissing}
                                    pattern={/^[^\s@]+@[^\s@]+\.[^\s@]+$/}
                                    patternMessage={TEXTS.errors.emailInvalid}
                                />

                                <SingleSelect
                                    name="subject"
                                    label=""
                                    placeholder={TEXTS.subject}
                                    options={subjectOptions}
                                    required={true}
                                    requiredText={TEXTS.errors.subjectMissing}
                                />

                                <LongText
                                    name="message"
                                    label=""
                                    placeholder={TEXTS.message}
                                    required={true}
                                    requiredText={TEXTS.errors.messageMissing}
                                />

                                <Button
                                    type="submit"
                                    disabled={isLoading}
                                    className="send-button w-full bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-600 transition-all duration-300"
                                    aria-label={TEXTS.button}
                                >
                                    {TEXTS.button}
                                </Button>

                                {successMessage && (
                                    <div className="mt-4 p-3 bg-green-50 border border-green-200 text-green-600 rounded-lg text-right">
                                        {successMessage}
                                    </div>
                                )}

                                {errorMessage && (
                                    <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-lg text-right">
                                        {errorMessage}
                                    </div>
                                )}
                            </form>
                        </FormProvider>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Contact;
