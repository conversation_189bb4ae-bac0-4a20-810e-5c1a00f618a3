"use client";

import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import dynamic from "next/dynamic";
import Link from "next/link";
import type { FC } from "react";
import { Suspense } from "react";

import HeroSkeleton from "@/components/home/<USER>/hero-skeleton";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import animationData from "@/public/home/<USER>";

const TEXTS = {
    title: "מלגות לסטודנטים לכל התארים",
    description:
        "מצאו את כל המלגות הרלוונטיות עבורכם בקלות - סטודנטים לתואר ראשון, שני, או שלישי. אתר המלגות שלנו מאגד את כל המידע במקום אחד כדי לעזור לכם למצוא את המימון שאתם צריכים להצליח בלימודים.",
    findScholarships: "לחצו וקבלו רשימת מלגות המותאמת לכם אישית"
};

const Lottie = dynamic(() => import("lottie-react"), {
    ssr: false,
    loading: () => <HeroSkeleton />
});

const Hero: FC = () => {
    return (
        <Suspense fallback={<HeroSkeleton />}>
            <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-50 via-white to-primary/5 py-12 sm:py-16">
                {/* Background pattern */}
                <div
                    className="absolute inset-0 opacity-5"
                    style={{
                        backgroundImage: `radial-gradient(circle, rgba(59,130,246,0.3) 1px, transparent 1px)`,
                        backgroundSize: "20px 20px"
                    }}
                />

                {/* Hero content container */}
                <div className="max-w-4xl mx-auto px-4 sm:px-6 relative z-10">
                    <div className="flex flex-col-reverse md:flex-row items-center gap-8 md:gap-12 lg:gap-16">
                        {/* Text content */}
                        <motion.div
                            className="w-full md:w-[45%] space-y-6 text-right"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.7, delay: 0.2 }}
                        >
                            <motion.h1
                                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-l from-blue-900 to-primary leading-tight"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.3 }}
                            >
                                {TEXTS.title}
                            </motion.h1>

                            <motion.p
                                className="text-base sm:text-lg text-gray-700 leading-relaxed"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.4 }}
                            >
                                {TEXTS.description}
                            </motion.p>

                            <motion.div
                                className="pt-4"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: 0.5 }}
                            >
                                <Link href="/#scholarship-groups">
                                    <Button
                                        size="lg"
                                        className="bg-primary hover:bg-primary/90 text-white px-3 sm:px-4 py-2.5 sm:py-3 h-auto text-sm sm:text-base font-medium transition-all duration-200 shadow-md hover:shadow-lg flex items-center gap-2 group w-full sm:w-auto break-words sm:whitespace-nowrap sm:max-w-none"
                                        aria-label={TEXTS.findScholarships}
                                    >
                                        <span className="text-right">{TEXTS.findScholarships}</span>
                                        <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:-translate-x-1 rtl:rotate-180 flex-shrink-0" />
                                    </Button>
                                </Link>
                            </motion.div>
                        </motion.div>

                        {/* Animation */}
                        <motion.div
                            className="w-full md:w-[55%]"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.7 }}
                        >
                            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                                <div className="relative aspect-[600/450] rounded-2xl overflow-hidden">
                                    <div className="absolute inset-0 bg-gradient-to-tr from-primary/10 z-10" />
                                    <Lottie animationData={animationData} loop={true} />
                                </div>
                            </Card>
                        </motion.div>
                    </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-primary/10 rounded-full blur-3xl"></div>
                <div className="absolute -top-16 -right-16 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl"></div>
            </div>
        </Suspense>
    );
};

export default Hero;
