"use client";

import { useAuth } from "@clerk/nextjs";
import { User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { DesktopNav } from "@/components/layout/navbar/desktop-nav";
import { MobileMenu } from "@/components/layout/navbar/mobile-menu";
import { SiteLogo } from "@/components/layout/site-logo";
import { Button } from "@/components/ui/button";
import { useUserOrgRole } from "@/hooks/use-user-org-role";
import { TEXTS } from "@/lib/auth-constants";
import { cn } from "@/lib/utils";

export function Navbar() {
    const [scrolled, setScrolled] = useState(false);
    const [isAccountButtonDisabled, setIsAccountButtonDisabled] = useState(false);
    const router = useRouter();
    const { userId, isLoaded: authLoaded } = useAuth();
    const { role, isLoaded: roleLoaded } = useUserOrgRole();

    const isAuthenticated = authLoaded && !!userId;
    const isLoaded = authLoaded && roleLoaded;

    useEffect(() => {
        const handleScroll = () => {
            setScrolled(window.scrollY > 20);
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    const handleAccountClick = (e: React.MouseEvent) => {
        e.preventDefault();
        setIsAccountButtonDisabled(true);

        if (!isLoaded) return;

        if (isAuthenticated) {
            if (role === "admin" || role === "employee") {
                router.push("/admin");
            } else {
                router.push("/dashboard");
            }
        } else {
            router.push("/signup");
        }
    };
    const renderAccountButton = () => {
        const buttonText = isAuthenticated ? TEXTS.account : TEXTS.signup;
        const ariaLabel = isAuthenticated ? TEXTS.toggleAccount : TEXTS.toggleSignup;

        return (
            <Button
                variant="default"
                size="icon"
                aria-label={ariaLabel}
                className={cn("md:hidden text-white", "md:flex md:h-10 md:w-auto md:px-4 md:py-2")}
                onClick={handleAccountClick}
                disabled={isAccountButtonDisabled}
            >
                <div className="relative">
                    <User className="h-5 w-5" />
                </div>
                <span className="hidden md:inline">{buttonText}</span>
            </Button>
        );
    };

    return (
        <div className="w-full z-50">
            <nav
                className={cn(
                    "w-full transition-all duration-300",
                    scrolled ? "bg-white shadow-sm border-b border-border/40" : "bg-white"
                )}
            >
                <div className="container flex h-16 items-center justify-between">
                    <div className="flex items-center">
                        <MobileMenu />
                        <div className="hidden md:block">
                            <SiteLogo size="lg" href="/" />
                        </div>
                    </div>

                    <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 md:static md:transform-none">
                        <div className="md:hidden">
                            <SiteLogo href="/" />
                        </div>
                        <DesktopNav />
                    </div>

                    {renderAccountButton()}
                </div>
            </nav>
        </div>
    );
}
