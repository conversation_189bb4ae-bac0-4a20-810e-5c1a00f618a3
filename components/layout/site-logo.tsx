"use client";

import Image from "next/image";
import Link from "next/link";

import { cn } from "@/lib/utils";

interface SiteLogoProps {
    href: string;
    color?: string;
    className?: string;
    size?: "sm" | "md" | "lg" | "xl";
}

const sizePx = {
    sm: 24,
    md: 32,
    lg: 40,
    xl: 48
};

export function SiteLogo({ href, className, size = "md" }: SiteLogoProps) {
    const height = sizePx[size];

    const width = Math.round(height * (122.03 / 45.69));

    return (
        <Link href={href} className={cn("flex items-center transition-transform duration-200 ease-in-out", className)}>
            <div
                className={cn("relative transition-colors duration-200")}
                style={{
                    height,
                    width
                }}
            >
                <Image
                    src="/logo.svg"
                    alt="Milgapo Logo"
                    width={width}
                    height={height}
                    priority
                    className={cn("transition-colors duration-200")}
                />
            </div>
        </Link>
    );
}
