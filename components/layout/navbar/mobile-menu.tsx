"use client";

import { Menu } from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

import { NAV_ITEMS } from "@/components/layout/navigation/nav-config";
import { NavItem } from "@/components/layout/navigation/nav-item";
import { isNavItemActive } from "@/components/layout/navigation/nav-utils";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";

import { SiteLogo } from "../site-logo";

const TEXTS = {
    toggleMenu: "פתח/סגור תפריט"
} as const;

export function MobileMenu() {
    const [open, setOpen] = useState(false);
    const pathname = usePathname();

    useEffect(() => {
        setOpen(false);
    }, [pathname]);

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
                <Button variant="default" size="icon" className="md:hidden text-white" aria-label={TEXTS.toggleMenu}>
                    <Menu className="h-5 w-5" />
                </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[85%] sm:w-[385px] p-0 rtl">
                <SheetHeader className="p-6 text-right border-b">
                    <SheetTitle className="text-xl font-medium">
                        <SiteLogo href="/" size="lg" />
                    </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col py-2">
                    {NAV_ITEMS.map((item) => (
                        <NavItem
                            key={item.href}
                            item={item}
                            isActive={isNavItemActive(pathname, item.href)}
                            onClick={() => setOpen(false)}
                            variant="mobile"
                        />
                    ))}
                </div>
            </SheetContent>
        </Sheet>
    );
}
