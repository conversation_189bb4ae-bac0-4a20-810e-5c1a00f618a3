"use client";

import { usePathname } from "next/navigation";

import { NAV_ITEMS } from "@/components/layout/navigation/nav-config";
import { NavItem } from "@/components/layout/navigation/nav-item";
import { isNavItemActive } from "@/components/layout/navigation/nav-utils";

export function DesktopNav() {
    const pathname = usePathname();

    return (
        <div className="hidden md:flex items-center space-x-6 space-x-reverse text-right">
            {NAV_ITEMS.map((item) => (
                <NavItem key={item.href} item={item} isActive={isNavItemActive(pathname, item.href)} variant="navbar" />
            ))}
        </div>
    );
}
