"use client";

import { useUser } from "@clerk/nextjs";
import type { LucideIcon } from "lucide-react";
import { ShieldAlert } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import { getCurrentUserSubscription } from "@/app/actions/subscriptions-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { hasAccess } from "@/components/layout/sidebar/plan-access";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { PlanType } from "@/lib/subscription-constants";

const TEXTS = {
    checkingPermissions: "בודק הרשאות...",
    accessDeniedTitle: "אין לך גישה",

    loginRequired: "עליך להתחבר כדי לגשת לדף זה.",
    planUpgradeRequired: "התוכנית הנוכחית שלך אינה מאפשרת גישה לדף זה.",
    upgradePrompt: "לשדרוג התוכנית וקבלת גישה, אנא בקר בדף המנויים.",
    loginPrompt: "לחץ למטה כדי להתחבר.",

    upgradePlan: "לשדרוג התוכנית",
    login: "התחבר"
};

interface NavItemConfig {
    name: string;
    url: string;
    icon?: LucideIcon;
    requiredPlan?: PlanType;
    items?: NavItemConfig[];
    title?: string;
}
interface DashboardConfig {
    navMain?: NavItemConfig[];
    personalDataGroup?: { title: string; icon?: LucideIcon; items: NavItemConfig[] };
    scholarshipTrackingGroup?: { title: string; icon?: LucideIcon; items: NavItemConfig[] };
    navSecondary?: NavItemConfig[];
}

interface PlanAccessGuardProps {
    children: React.ReactNode;
    dashboardConfig: DashboardConfig;
}

export function PlanAccessGuard({ children, dashboardConfig }: PlanAccessGuardProps) {
    const { isLoaded, isSignedIn } = useUser();
    const [userPlan, setUserPlan] = useState<PlanType | null>(null);
    const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(true);
    const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

    const pathname = usePathname();
    const router = useRouter();

    useEffect(() => {
        if (isLoaded && isSignedIn) {
            let isActive = true;
            setIsSubscriptionLoading(true);

            (async () => {
                try {
                    const subscription = await getCurrentUserSubscription();
                    if (!isActive) return;
                    const newPlan: PlanType = subscription?.planType || "free";
                    setUserPlan(newPlan);
                } catch (error) {
                    if (!isActive) return;
                    console.error("Unexpected error fetching subscription details:", error);
                    setUserPlan("free");
                } finally {
                    if (isActive) {
                        setIsSubscriptionLoading(false);
                    }
                }
            })();

            return () => {
                isActive = false;
            };
        } else if (isLoaded && !isSignedIn) {
            setUserPlan("free");
            setIsSubscriptionLoading(false);
        }
    }, [isLoaded, isSignedIn, setUserPlan, setIsSubscriptionLoading]);

    useEffect(() => {
        if (!isLoaded || isSubscriptionLoading) {
            setIsAuthorized(null);
            return;
        }

        if (!isSignedIn) {
            setIsAuthorized(false);

            return;
        }

        const allNavItems: NavItemConfig[] = [
            ...(dashboardConfig.navMain || []).map((item) => ({ ...item, name: item.title || item.name })),
            ...(dashboardConfig.personalDataGroup?.items || []),
            ...(dashboardConfig.scholarshipTrackingGroup?.items || []),
            ...(dashboardConfig.navSecondary || []).map((item) => ({ ...item, name: item.title || item.name }))
        ];

        const currentNavItem = allNavItems.find((item) => item.url === pathname);

        const requiredPlanForPage = currentNavItem?.requiredPlan || "free";

        if (userPlan !== null) {
            const canAccess = hasAccess(userPlan, requiredPlanForPage);
            setIsAuthorized(canAccess);
        }
    }, [isLoaded, isSignedIn, userPlan, pathname, router, dashboardConfig, isSubscriptionLoading]);

    if (!isLoaded || isSubscriptionLoading || isAuthorized === null) {
        return (
            <div className="fixed inset-0 flex items-center justify-center bg-background">
                <LoadingIcon text={TEXTS.checkingPermissions} />
            </div>
        );
    }

    if (!isAuthorized) {
        return (
            <div dir="rtl" className="container flex min-h-[calc(100vh-4rem)] items-center justify-center py-8">
                <Card className="w-full max-w-2xl">
                    <CardHeader className="pb-0">
                        <div className="w-full max-w-md mx-auto flex justify-center">
                            <ShieldAlert className="h-24 w-24 text-destructive" />
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-4 text-center pt-6">
                        <h1 className="text-3xl sm:text-4xl scroll-m-20 tracking-tight">{TEXTS.accessDeniedTitle}</h1>
                        <div className="space-y-2">
                            <p className="text-base text-muted-foreground sm:text-lg leading-7">
                                {!isSignedIn ? TEXTS.loginRequired : TEXTS.planUpgradeRequired}
                            </p>
                            <p className="text-base text-muted-foreground sm:text-lg leading-7">
                                {isSignedIn ? TEXTS.upgradePrompt : TEXTS.loginPrompt}
                            </p>
                        </div>
                        <Button asChild className="mt-6">
                            <Link href={isSignedIn ? "/subscriptions" : "/login"}>
                                {isSignedIn ? TEXTS.upgradePlan : TEXTS.login}
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return <>{children}</>;
}
