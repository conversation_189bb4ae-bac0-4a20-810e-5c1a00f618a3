import { PlanType } from "@/lib/subscription-constants";

const planHierarchy: PlanType[] = ["free", "milgapro", "elite", "vip"];

export function hasAccess(userPlan: PlanType, requiredPlan: PlanType | undefined): boolean {
    if (requiredPlan === undefined) return true;
    const userLevel = planHierarchy.indexOf(userPlan);
    const requiredLevel = planHierarchy.indexOf(requiredPlan);
    if (userLevel === -1 || requiredLevel === -1) return false;
    return userLevel >= requiredLevel;
}
