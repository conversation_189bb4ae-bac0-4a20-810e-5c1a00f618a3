"use client";

import { LucideIcon } from "lucide-react";
import { ComponentProps } from "react";
import * as React from "react";

import { NavGroup } from "@/components/layout/navigation/nav-group";
import { NavMain } from "@/components/layout/navigation/nav-main";
import { NavSecondary } from "@/components/layout/navigation/nav-secondary";
import { BaseNavItem } from "@/components/layout/navigation/types";
import { UserProvider } from "@/components/layout/navigation/user-provider";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarSeparator,
    SidebarTrigger
} from "@/components/ui/sidebar";
import { PlanType } from "@/lib/subscription-constants";

interface MainNavItem {
    title: string;
    url: string;
    icon?: LucideIcon;
}

export interface GroupNavItem extends Omit<BaseNavItem, "label" | "href"> {
    name: string;
    url: string;
    requiredPlan?: PlanType;
}

interface NavGroupData {
    title: string;
    icon?: LucideIcon;
    items: GroupNavItem[];
}

interface AppSidebarProps extends ComponentProps<typeof Sidebar> {
    navMain?: MainNavItem[];
    navGroups?: NavGroupData[];
    navSecondary?: MainNavItem[];
}

export function AppSidebar({ navMain, navGroups, navSecondary, ...props }: AppSidebarProps) {
    const convertGroupItems = (
        items: GroupNavItem[]
    ): (BaseNavItem & { jsx?: React.ReactNode; className?: string; requiredPlan?: PlanType })[] => {
        return items.map((item) => ({
            label: item.name,
            href: item.url,
            icon: item.icon,
            disabled: item.disabled,
            locked: item.locked,
            requiredPlan: item.requiredPlan
        }));
    };

    const convertMainItems = (items: MainNavItem[]): (BaseNavItem & { title?: string; url?: string })[] => {
        return items.map((item) => ({
            label: item.title,
            href: item.url,
            icon: item.icon,
            title: item.title,
            url: item.url
        }));
    };

    return (
        <Sidebar collapsible="icon" side="right" dir="rtl" className="border-l" {...props}>
            <SidebarHeader>
                <SidebarTrigger className="mb-2" />
            </SidebarHeader>
            <SidebarContent className="text-right">
                {navMain && navMain.length > 0 && (
                    <>
                        <NavMain items={convertMainItems(navMain)} />
                        {navGroups && navGroups.length > 0 && <SidebarSeparator className="my-0" />}
                    </>
                )}
                {navGroups &&
                    navGroups.map((group, index) => (
                        <React.Fragment key={group.title || `group-${index}`}>
                            <NavGroup items={convertGroupItems(group.items)} label={group.title} />
                            {/* Add separator if it's not the last group or if navSecondary follows and has items */}
                            {(index < navGroups.length - 1 || (navSecondary && navSecondary.length > 0)) &&
                                navGroups.length > 0 && <SidebarSeparator className="my-0" />}
                        </React.Fragment>
                    ))}
                {navSecondary && navSecondary.length > 0 && (
                    <NavSecondary items={convertMainItems(navSecondary)} className="mt-auto" />
                )}
            </SidebarContent>
            <SidebarFooter>
                <UserProvider />
            </SidebarFooter>
        </Sidebar>
    );
}
