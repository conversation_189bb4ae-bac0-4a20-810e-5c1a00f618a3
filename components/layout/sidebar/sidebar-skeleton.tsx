"use client";

export function SidebarSkeleton() {
    return (
        <div className="hidden md:flex flex-col items-center border-l bg-card h-full py-4 space-y-4 w-12">
            {[0, 1, 2, 3, 4].map((i) => (
                <div key={i} className="h-8 w-8 rounded-lg bg-muted animate-pulse mb-2" />
            ))}
            <div className="flex-1" />
            <div className="h-8 w-8 rounded-lg bg-muted animate-pulse mb-2" />
        </div>
    );
}
