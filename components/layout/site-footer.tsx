import Link from "next/link";
import { FaFacebookF } from "react-icons/fa";
import { FaYoutube } from "react-icons/fa";
import { FaLinkedin } from "react-icons/fa";
import { FaTiktok } from "react-icons/fa";
import { RiInstagramFill } from "react-icons/ri";

import { But<PERSON> } from "@/components/ui/button";
import { Footer, FooterColumn, FooterContent } from "@/components/ui/footer";

const TEXTS = {
    copyright: "כל הזכויות שמורות",
    links: [
        { href: "/terms-of-use", label: "תקנון" },
        { href: "/privacy-policy", label: "מדיניות הפרטיות" },
        { href: "/contact", label: "צור קשר" }
    ],
    socialLinks: [
        { href: "https://www.youtube.com/watch?v=uhNEtEFRq5M", label: "YouTube", icon: FaYoutube },
        { href: "https://www.instagram.com/milgapo/?ref=badge", label: "Instagram", icon: RiInstagramFill },
        { href: "https://www.tiktok.com/@milgapo_", label: "TikTok", icon: FaTiktok },
        { href: "https://www.facebook.com/milgapo/", label: "Facebook", icon: FaFacebookF },
        { href: "https://www.linkedin.com/in/milgapo", label: "LinkedIn", icon: FaLinkedin }
    ]
};

export function SiteFooter() {
    const currentYear = new Date().getFullYear();
    return (
        <Footer className="border-t">
            <div className="container py-4 md:py-6">
                <FooterContent>
                    <FooterColumn className="col-span-full mb-2 md:mb-3">
                        <div className="flex flex-wrap justify-center gap-4 text-sm">
                            {TEXTS.links.map((link) => (
                                <Link
                                    key={link.href}
                                    href={link.href}
                                    className="text-muted-foreground transition-colors hover:text-primary"
                                >
                                    {link.label}
                                </Link>
                            ))}
                        </div>
                    </FooterColumn>

                    <FooterColumn className="col-span-full mb-2 md:mb-3">
                        <div className="flex flex-wrap justify-center gap-4">
                            {TEXTS.socialLinks.map((link) => (
                                <Button
                                    key={link.href}
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 rounded-md transition-transform hover:scale-110"
                                    aria-label={link.label}
                                    asChild
                                >
                                    <Link href={link.href} target="_blank" rel="noopener noreferrer">
                                        <link.icon className="h-4 w-4" />
                                        <span className="sr-only">{link.label}</span>
                                    </Link>
                                </Button>
                            ))}
                        </div>
                    </FooterColumn>

                    <FooterColumn className="col-span-full">
                        <div className="text-sm text-muted-foreground text-center">
                            © {currentYear} {TEXTS.copyright}
                        </div>
                    </FooterColumn>
                </FooterContent>
            </div>
        </Footer>
    );
}
