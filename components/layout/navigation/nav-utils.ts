export function isNavItemActive(pathname: string, href: string, exact: boolean = true): boolean {
    if (href === "#") return false;

    const normalizedPathname = pathname.endsWith("/") ? pathname.slice(0, -1) : pathname;
    const normalizedHref = href.endsWith("/") ? href.slice(0, -1) : href;

    if (exact) {
        return normalizedPathname === normalizedHref;
    }

    return normalizedPathname.startsWith(normalizedHref);
}

export function isNavItemActiveWithQuery(
    currentPathname: string,
    currentSearchParams: URLSearchParams,
    itemHref: string,
    exactPathMatch: boolean = true
): boolean {
    if (itemHref === "#") return false;

    const [itemPath, itemQueryString] = itemHref.split("?");

    const pathMatch = isNavItemActive(currentPathname, itemPath, exactPathMatch);

    if (!pathMatch) {
        return false;
    }

    if (itemQueryString) {
        const itemQueryParams = new URLSearchParams(itemQueryString);
        let allItemQueryMatched = true;
        itemQueryParams.forEach((value, key) => {
            if (currentSearchParams.get(key) !== value) {
                allItemQueryMatched = false;
            }
        });
        if (!allItemQueryMatched) {
            return false;
        }
    }

    return true;
}

export const navigateToSection = (sectionId: string, currentPath: string, e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    if (currentPath !== "/") {
        window.location.href = `/#${sectionId}`;
        return;
    }

    const element = document.getElementById(sectionId);
    if (element) {
        const navbarOffset = 80;
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({
            top: elementPosition - navbarOffset,
            behavior: "smooth"
        });
    }
};
