"use client";

import { SignOutButton, UserProfile } from "@clerk/nextjs";
import { LogOut, User } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

const TEXTS = {
    userDashboard: "פרופיל משתמש",
    logout: "התנתקות"
};

export function NavUser({
    user
}: {
    user: {
        name: string;
        email: string;
        avatar: string;
    };
}) {
    const { state } = useSidebar();
    const isMobile = useIsMobile();
    const [profileOpen, setProfileOpen] = useState(false);

    return (
        <>
            <DropdownMenu>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <DropdownMenuTrigger asChild>
                            <SidebarMenuButton
                                size="lg"
                                className="relative cursor-pointer"
                                tooltip={state === "collapsed" ? user.name : undefined}
                                aria-label={user.name}
                                tabIndex={0}
                            >
                                <div className="h-8 w-8 rounded-lg bg-gray-200 flex items-center justify-center">
                                    <Image
                                        src={user.avatar || "/logo.svg"}
                                        alt={user.name}
                                        className="h-full w-full rounded-lg object-cover"
                                        width={32}
                                        height={32}
                                        priority
                                    />
                                </div>
                                {(state === "expanded" || isMobile) && (
                                    <div className="grid flex-1 text-right text-sm leading-tight">
                                        <span className={`truncate font-medium${isMobile ? " text-gray-800" : ""}`}>
                                            {user.name}
                                        </span>
                                        <span
                                            className={`truncate text-xs${isMobile ? " text-gray-500" : " text-muted-foreground"}`}
                                        >
                                            {user.email}
                                        </span>
                                    </div>
                                )}
                            </SidebarMenuButton>
                        </DropdownMenuTrigger>
                    </SidebarMenuItem>
                </SidebarMenu>
                <DropdownMenuContent
                    align="end"
                    className="w-56 p-1 rounded-lg"
                    sideOffset={8}
                    aria-label="תפריט משתמש"
                >
                    <DropdownMenuItem
                        onSelect={() => setProfileOpen(true)}
                        className="flex items-center justify-between py-3 px-4 text-base focus:bg-gray-100 focus:outline-none cursor-pointer"
                        aria-label={TEXTS.userDashboard}
                        tabIndex={0}
                    >
                        <span className="text-right flex-1">{TEXTS.userDashboard}</span>
                        <User className="w-5 h-5 ms-3 flex-shrink-0" aria-hidden="true" />
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="py-3 px-4 text-base focus:bg-gray-100 focus:outline-none cursor-pointer"
                        aria-label={TEXTS.logout}
                        onSelect={(e) => {
                            e.preventDefault();
                        }}
                        asChild
                    >
                        <SignOutButton redirectUrl="/login">
                            <div className="flex items-center justify-between w-full">
                                <span className="text-right flex-1">{TEXTS.logout}</span>
                                <LogOut className="w-5 h-5 ms-3 flex-shrink-0" aria-hidden="true" />
                            </div>
                        </SignOutButton>
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
            <Dialog open={profileOpen} onOpenChange={setProfileOpen}>
                <DialogContent className="max-w-lg p-0" aria-label={TEXTS.userDashboard}>
                    <DialogTitle
                        style={{
                            position: "absolute",
                            width: 1,
                            height: 1,
                            padding: 0,
                            margin: -1,
                            overflow: "hidden",
                            clip: "rect(0,0,0,0)",
                            whiteSpace: "nowrap",
                            border: 0
                        }}
                        tabIndex={-1}
                    >
                        {TEXTS.userDashboard}
                    </DialogTitle>
                    <UserProfile routing="hash" />
                </DialogContent>
            </Dialog>
        </>
    );
}
