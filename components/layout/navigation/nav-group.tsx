"use client";

import { usePathname, useSearchParams } from "next/navigation";
import * as React from "react";
import { Suspense } from "react";

import { LockIcon } from "@/components/layout/navigation/lock-icon";
import { NavItem } from "@/components/layout/navigation/nav-item";
import { BaseNavItem } from "@/components/layout/navigation/types";
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
} from "@/components/ui/sidebar";
import { getPlanDisplayName } from "@/lib/plan-utils";
import { cn } from "@/lib/utils";

const TEXTS = {
    scholarships: "מלגות",
    scholarshipsDescription: "כל המלגות במקום אחד",
    scholarshipsTooltip: "כל המלגות במקום אחד, כולל מלגות ייחודיות למשתמשי MilgaPro",
    required: "תוכנית נדרשת"
} as const;

interface NavGroupProps extends React.ComponentPropsWithoutRef<typeof SidebarGroup> {
    label: string;
    items: (BaseNavItem & {
        jsx?: React.ReactNode;
        className?: string;
    })[];
}

function NavGroupContent({ items, label = "מלגות", ...props }: NavGroupProps) {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const { isMobile, setOpenMobile, state } = useSidebar();

    const isActive = (url: string) => {
        if (url === "#") return false;

        const [itemPath, itemQuery] = url.split("?");

        const normalizedPathname = pathname.endsWith("/") ? pathname.slice(0, -1) : pathname;
        const normalizedItemPath = itemPath.endsWith("/") ? itemPath.slice(0, -1) : itemPath;

        const pathMatch = normalizedPathname === normalizedItemPath;

        if (!pathMatch) {
            return false;
        }

        if (itemQuery && searchParams) {
            const queryParams = new URLSearchParams(itemQuery);

            const keys = Array.from(queryParams.keys());

            for (const key of keys) {
                if (searchParams.get(key) !== queryParams.get(key)) {
                    return false;
                }
            }
        }

        return true;
    };

    const handleLinkClick = () => {
        if (isMobile) {
            setOpenMobile(false);
        }
    };

    return (
        <SidebarGroup {...props}>
            {state === "expanded" && (
                <SidebarGroupLabel className="text-right px-1 py-1 font-medium text-base">{label}</SidebarGroupLabel>
            )}
            <SidebarMenu className="space-y-0">
                {items.map((item) => (
                    <SidebarMenuItem key={item.href ?? item.label}>
                        {item.jsx ? (
                            item.jsx
                        ) : (
                            <div>
                                <SidebarMenuButton
                                    tooltip={
                                        state === "collapsed"
                                            ? item.disabled && item.locked
                                                ? {
                                                      children: (
                                                          <div className="flex flex-col gap-1 items-center">
                                                              <span className="flex items-center justify-center gap-2">
                                                                  {item.label}
                                                                  <LockIcon />
                                                              </span>
                                                              {item.requiredPlan && (
                                                                  <span className="text-xs text-muted-foreground text-start">
                                                                      {TEXTS.required}{" "}
                                                                      {getPlanDisplayName(item.requiredPlan)}
                                                                  </span>
                                                              )}
                                                          </div>
                                                      )
                                                  }
                                                : item.label
                                            : undefined
                                    }
                                    disabled={item.disabled}
                                    aria-disabled={item.disabled}
                                    asChild
                                    className={cn(
                                        "transition-all duration-200 ease-in-out",
                                        !item.disabled && "hover:bg-secondary/50 hover:text-secondary-foreground",
                                        isActive(item.href) ? "bg-secondary text-secondary-foreground" : ""
                                    )}
                                >
                                    <div className="w-full">
                                        <NavItem
                                            item={item}
                                            isActive={isActive(item.href)}
                                            onClick={handleLinkClick}
                                            variant="sidebar"
                                            collapsed={state === "collapsed" && !isMobile}
                                        />
                                    </div>
                                </SidebarMenuButton>
                            </div>
                        )}
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}

export function NavGroup(props: NavGroupProps) {
    const { state } = useSidebar();

    const fallbackItems = props.items.map((item) => (
        <SidebarMenuItem key={`${item.label}-fallback`}>
            {item.jsx || (
                <SidebarMenuButton
                    tooltip={
                        state === "collapsed"
                            ? item.disabled && item.locked
                                ? {
                                      children: (
                                          <div className="flex flex-col gap-1 items-center">
                                              <span className="flex items-center justify-center gap-2">
                                                  {item.label}
                                                  <LockIcon />
                                              </span>
                                              {item.requiredPlan && (
                                                  <span className="text-xs text-muted-foreground text-center">
                                                      {TEXTS.required} {getPlanDisplayName(item.requiredPlan)}
                                                  </span>
                                              )}
                                          </div>
                                      )
                                  }
                                : item.label
                            : undefined
                    }
                    className={cn(
                        "transition-all duration-200 ease-in-out",
                        "w-full text-right py-1 px-2 rounded-md",
                        state === "collapsed" ? "justify-center" : "gap-2"
                    )}
                >
                    <div className="flex items-center w-full">
                        {item.icon && <item.icon className="h-5 w-5 shrink-0 text-muted-foreground" />}
                        {state === "expanded" && (
                            <span className="font-medium flex-1 text-right text-base">{item.label}</span>
                        )}
                    </div>
                </SidebarMenuButton>
            )}
        </SidebarMenuItem>
    ));

    const fallbackContent = (
        <SidebarGroup>
            {state === "expanded" && (
                <SidebarGroupLabel className="text-right px-1 py-1 font-medium text-base">
                    {props.label}
                </SidebarGroupLabel>
            )}
            <SidebarMenu className="space-y-0">{fallbackItems}</SidebarMenu>
        </SidebarGroup>
    );

    return (
        <Suspense fallback={fallbackContent}>
            <NavGroupContent {...props} />
        </Suspense>
    );
}
