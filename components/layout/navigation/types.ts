import { LucideIcon } from "lucide-react";

import { PlanType } from "@/lib/subscription-constants";

export type Variant = "navbar" | "sidebar" | "mobile";

export interface BaseNavItem {
    href: string;
    label: string;
    icon?: LucideIcon;
    disabled?: boolean;
    locked?: boolean;
    requiredPlan?: PlanType;
}

export interface NavItemProps {
    item: BaseNavItem;
    isActive: boolean;
    onClick?: () => void;
    variant?: Variant;
    collapsed?: boolean;
}
