"use client";

import { usePathname } from "next/navigation";
import * as React from "react";

import { LockIcon } from "@/components/layout/navigation/lock-icon";
import { NavItem } from "@/components/layout/navigation/nav-item";
import { isNavItemActive } from "@/components/layout/navigation/nav-utils";
import { BaseNavItem } from "@/components/layout/navigation/types";
import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

export function NavSecondary({
    items,
    ...props
}: {
    items: (BaseNavItem & {
        title?: string;
        url?: string;
    })[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
    const pathname = usePathname();
    const { isMobile, setOpenMobile, state } = useSidebar();

    const handleLinkClick = () => {
        if (isMobile) {
            setOpenMobile(false);
        }
    };

    const normalizedItems = items.map((item) => ({
        ...item,
        href: item.href || item.url || "#",
        label: item.label || item.title || ""
    }));

    return (
        <SidebarGroup {...props}>
            <SidebarGroupContent>
                <SidebarMenu className="space-y-0">
                    {normalizedItems.map((item) => (
                        <SidebarMenuItem key={item.href ?? item.label}>
                            <SidebarMenuButton
                                asChild
                                tooltip={
                                    state === "collapsed"
                                        ? item.disabled && item.locked
                                            ? {
                                                  children: (
                                                      <span className="flex items-center gap-2">
                                                          {item.label}
                                                          <LockIcon />
                                                      </span>
                                                  )
                                              }
                                            : item.label
                                        : undefined
                                }
                                disabled={item.disabled}
                                aria-disabled={item.disabled}
                                className={cn(
                                    "transition-all duration-200 ease-in-out",
                                    "hover:bg-secondary/50 hover:text-secondary-foreground",
                                    isNavItemActive(pathname, item.href, false)
                                        ? "bg-secondary text-secondary-foreground"
                                        : ""
                                )}
                            >
                                {!item.disabled && item.href !== "#" ? (
                                    <div>
                                        <NavItem
                                            item={item}
                                            isActive={isNavItemActive(pathname, item.href, false)}
                                            onClick={handleLinkClick}
                                            variant="sidebar"
                                            collapsed={state === "collapsed" && !isMobile}
                                        />
                                    </div>
                                ) : (
                                    <div
                                        className={cn(
                                            "flex items-center w-full text-right gap-2",
                                            state === "collapsed" && "justify-center h-8 w-8 mx-auto"
                                        )}
                                    >
                                        {item.icon && (
                                            <item.icon
                                                className={cn(
                                                    "transition-colors duration-200 flex-shrink-0 text-muted-foreground",
                                                    state === "collapsed" && "mx-auto"
                                                )}
                                            />
                                        )}
                                        {state === "expanded" && (
                                            <span className={cn("font-medium flex-1 text-right text-base")}>
                                                {item.label}
                                            </span>
                                        )}
                                    </div>
                                )}
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    );
}
