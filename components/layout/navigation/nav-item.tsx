"use client";

import Link from "next/link";

import { LockIcon } from "@/components/layout/navigation/lock-icon";
import { NavItemProps, Variant } from "@/components/layout/navigation/types";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { getPlanDisplayName } from "@/lib/plan-utils";
import { cn } from "@/lib/utils";

const TEXTS = {
    required: "תוכנית נדרשת"
} as const;

export function NavItem({ item, isActive, onClick, variant = "sidebar", collapsed = false }: NavItemProps) {
    const Icon = item.icon;
    const commonClassName = getNavItemClassName(variant, isActive, item.disabled, collapsed);

    const content = (
        <>
            {Icon && (
                <Icon
                    className={cn(
                        getIconClassName(variant),
                        isActive && !item.disabled
                            ? "text-secondary-foreground"
                            : "text-muted-foreground group-hover:text-secondary-foreground",
                        collapsed && "mx-auto"
                    )}
                />
            )}
            {!collapsed && (
                <span
                    className={cn(
                        "font-medium text-base",
                        variant === "navbar" ? "text-right whitespace-nowrap" : "flex-1 text-right",
                        isActive && !item.disabled && "font-semibold"
                    )}
                >
                    {item.label}
                </span>
            )}
            {renderActiveIndicator(variant, isActive)}

            {item.locked && !collapsed && (
                <div className={cn("flex flex-col items-center justify-center", variant !== "navbar" && "mr-auto")}>
                    <span className="flex items-center justify-center" style={{ marginLeft: "8px" }}>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <span>
                                    <LockIcon />
                                </span>
                            </TooltipTrigger>
                            <TooltipContent side="top" dir="rtl">
                                <p>
                                    {TEXTS.required} {getPlanDisplayName(item.requiredPlan)}
                                </p>
                            </TooltipContent>
                        </Tooltip>
                    </span>
                </div>
            )}
        </>
    );

    return (
        <div className="w-full">
            {item.disabled ? (
                <span role="link" aria-disabled="true" className={commonClassName}>
                    {content}
                </span>
            ) : (
                <Link href={item.href} onClick={onClick} className={commonClassName}>
                    {content}
                </Link>
            )}
        </div>
    );
}

function getNavItemClassName(variant: Variant, isActive: boolean, disabled?: boolean, collapsed?: boolean): string {
    const baseClasses = "transition-all duration-200 ease-in-out flex items-center";

    if (variant === "navbar") {
        return cn(
            baseClasses,
            "px-2 py-1 font-medium text-gray-600 hover:text-gray-900 text-right relative group gap-2",
            isActive && "text-primary"
        );
    } else if (variant === "mobile") {
        return cn(
            baseClasses,
            "px-6 py-3 font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 text-right relative group gap-2",
            isActive && "text-primary font-semibold bg-primary/5"
        );
    } else {
        return cn(
            baseClasses,
            "w-full text-right py-1 px-2 rounded-md",
            collapsed ? "justify-center" : "gap-2",
            !disabled && "hover:bg-secondary/50 hover:text-secondary-foreground",
            isActive && "bg-secondary text-secondary-foreground",
            {
                "opacity-90 cursor-pointer": !disabled,
                "opacity-65 cursor-not-allowed": disabled
            }
        );
    }
}

function getIconClassName(variant: Variant): string {
    if (variant === "navbar") {
        return "h-4 w-4 shrink-0";
    } else if (variant === "mobile") {
        return "h-5 w-5 shrink-0";
    } else {
        return "h-5 w-5 shrink-0 transition-colors duration-200";
    }
}

function renderActiveIndicator(variant: Variant, isActive: boolean) {
    if (variant === "navbar") {
        return (
            <div
                className={cn(
                    "absolute right-0 left-0 -bottom-1 h-0.5 bg-primary rounded-full transition-all",
                    isActive ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                )}
            />
        );
    } else if (variant === "mobile") {
        return (
            <div
                className={cn(
                    "absolute right-0 top-0 bottom-0 w-1 bg-primary transition-all",
                    isActive ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                )}
            />
        );
    }

    return null;
}
