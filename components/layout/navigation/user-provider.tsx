"use client";

import { useUser } from "@clerk/nextjs";

import { NavUser } from "@/components/layout/navigation/nav-user";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";

export function UserProvider() {
    const { user, isLoaded } = useUser();

    if (!isLoaded) {
        return (
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg">
                        <Skeleton className="h-8 w-8 rounded-lg bg-muted-foreground/20" />
                        <div className="grid flex-1 gap-1 text-right">
                            <Skeleton className="h-4 w-24 bg-muted-foreground/20" />
                            <Skeleton className="h-3 w-32 bg-muted-foreground/20" />
                        </div>
                        <Skeleton className="mr-auto h-4 w-4 bg-muted-foreground/20" />
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        );
    }

    if (!user) {
        return null;
    }

    const email = user.emailAddresses?.[0]?.emailAddress || "";
    const name = user.fullName || email || user.username || "User";
    const avatar = user.imageUrl || "";

    return <NavUser user={{ name, email, avatar }} />;
}
