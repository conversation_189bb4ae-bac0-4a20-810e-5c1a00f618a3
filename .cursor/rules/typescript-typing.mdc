---
description:
globs:
alwaysApply: true
---
# TypeScript Typing Practices

This comprehensive guide outlines the TypeScript typing practices for the Milgapo project. The focus is on maintaining type safety, leveraging auto-generated database type definitions, and ensuring consistency between the database and application code.

## Type safety principles

- Never use the `any` type in the codebase
- Use explicit types for all variables, function parameters, and return values
- When working with unknown data, use `unknown` instead of `any` and perform proper type narrowing
- Prefer union types over type assertions when the exact shape is known

## Database types - Why and how

### Why use database types

- **Auto-generated**: The `database.types.ts` file is automatically generated from the actual database schema, ensuring it's always up-to-date
- **Type safety**: Prevents mismatches between application code and database structure
- **Single source of truth**: Eliminates duplicate type definitions and reduces maintenance overhead
- **Automatic updates**: When the database schema changes, types are automatically updated

### Database operations

- Always import and use types from `types/database.types.ts` when working with database data
- Use the appropriate table types for database operations:
    - `Tables` for SELECT operations
    - `TablesInsert` for INSERT operations
    - `TablesUpdate` for UPDATE operations

### Database enum preference

- Always use database enums instead of custom string literal unions
- Database enums are defined in `Database["public"]["Enums"]`

## Examples

### ✅ Correct - Using database types

```typescript
import { Tables, TablesInsert } from "@/types/database.types";
import type { Database } from "@/types/database.types";

// Use database table types
function getScholarship(id: string): Promise<Tables<"scholarships"> | null> {
    // Implementation
}

// For inserting new data
function createQuestion(question: TablesInsert<"questions">): Promise<string> {
    // Implementation
}

// Use database enum types
interface ComponentProps {
    scholarshipType: Database["public"]["Enums"]["scholarship_type"];
    questionType: Database["public"]["Enums"]["question_type"];
}
```

### ❌ Incorrect - Avoid these patterns

```typescript
// Don't use any
function processData(data: any): any {
    // Implementation
}

// Don't create custom enums that already exist in database
interface ComponentProps {
    scholarshipType: "submission" | "guidance"; // This duplicates database enum
    questionType: "single_select" | "multi_select" | "short_text"; // This duplicates database enum
}

// Don't create custom interfaces that duplicate database tables
interface Scholarship {
    id: string;
    title: string;
    description: string;
    // This duplicates Tables<"scholarships">
}
```

## Type narrowing

- When dealing with data that could be null or undefined, use proper type guards:

```typescript
// Good practice
function processScholarship(scholarship: Tables<"scholarships"> | null): string {
    if (!scholarship) {
        return "Scholarship not found";
    }

    return scholarship.title;
}
```

## When to create custom types

Custom types are appropriate when:

- The type represents frontend-specific data that doesn't exist in the database
- The type is a computed/derived type based on multiple database types
- The type represents UI state or component-specific props that have no database equivalent

### Examples of appropriate custom types

```typescript
// Frontend-specific types that don't exist in database
type UserPlan = "free" | "premium";
type LoadingState = "idle" | "loading" | "success" | "error";

// Computed types based on database types
type ScholarshipWithEligibility = Tables<"scholarships"> & {
    isEligible: boolean;
    eligibilityScore: number;
};
```

## Generic types

- Use generic types when creating reusable components or functions that work with different data types
- Constrain generics when possible to provide better type safety

```typescript
// Good practice with generic constraints
function updateRecord<T extends keyof Database["public"]["Tables"]>(
    table: T,
    data: TablesUpdate<T>
): Promise<Tables<T>> {
    // Implementation
}
```

## Migration strategy

When refactoring existing code:

1. Identify custom types that duplicate database schema
2. Replace with appropriate database types
3. Update all usage locations
4. Remove the custom type definitions

This ensures the codebase stays aligned with the database schema automatically.
