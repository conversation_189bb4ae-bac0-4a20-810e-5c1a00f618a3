---
description: 
globs: 
alwaysApply: true
---
# Form Development Best Practices (Milgapo)

---
**Summary:**
This document defines the authoritative standards for building forms in the Milgapo project. It ensures:
- **Type safety** and zero drift by always using auto-generated database types
- **Security** through mandatory data sanitization in all server actions
- **Consistent, secure, and maintainable** form and server action patterns
- **Centralized constants** for UI text, types, and options
- **Strict separation** of client/server logic and data transformation
- **Comprehensive testing** and error handling

**Key Principles:**
1. **Database types first**: Always use types from `@/types/database.types.ts` (auto-generated from the DB schema)
2. **Server actions only**: Never use direct Supabase calls in forms; always use server actions
3. **Centralized constants**: All UI text, types, and options for an entity must be in a single `lib/` file
4. **Consistent error handling**: All errors must be handled and surfaced in Hebrew
5. **Type-safe data transformation**: Use form utilities for mapping between IDs and Option objects
6. **Comprehensive testing**: All logic, forms, and utilities must have unit tests
7. **Import conventions**: Always use the `@/` alias for internal imports

---

## Database Types - Critical Principle

### Always Use Auto-Generated Database Types

- Types in `@/types/database.types.ts` are always up-to-date and reflect the DB schema
- Never duplicate DB schema in custom types (except for UI/computed types)
- Use `Tables`, `TablesInsert`, `TablesUpdate` for all DB operations

#### Example: Correct Usage
```typescript
import { type Tables, type TablesInsert, type TablesUpdate } from "@/types/database.types";

export async function createEntity(data: unknown): Promise<{ success: boolean; error?: string }> {
    // ...validation...
    const sanitizedData: TablesInsert<"table_name"> = { ... };
    // ...insert logic...
}
```

#### Example: Incorrect Usage
```typescript
// ❌ Don't duplicate DB schema
type CustomUser = { id: string; email: string; ... };
```

### When Custom Types Are Acceptable
- UI state or computed fields
- Combined/derived types
- Form-specific types (not duplicating DB)

---

## Server Actions Pattern

- All form data processing must happen in Next.js server actions
- Actions must:
  - Validate and sanitize input
  - Use DB types for all operations
  - Return `{ success: boolean; error?: string }`
  - Handle authentication, authorization, and rate limiting
  - Log errors server-side, return generic messages client-side

#### Example: Action Implementation
```typescript
"use server";
import { type TablesInsert } from "@/types/database.types";
import { createClientFromRequest } from "@/utils/supabase/server";

export async function createEntity(data: EntityFormData) {
    // ...validation, sanitization...
    const supabase = await createClientFromRequest();
    const { error } = await supabase.from("table_name").insert(sanitizedData);
    if (error) throw error;
    return { success: true };
}
```

---

## Data Sanitization - Critical Security Practice

### Why Sanitization is Required

- **Security**: Prevent XSS attacks, script injection, and malicious content
- **Data integrity**: Ensure consistent, clean data in the database
- **Server safety**: The sanitization utility automatically detects server vs client environments
- **Compliance**: Meet security standards for user-generated content

### Server Actions Sanitization Pattern

All user input in server actions MUST be sanitized before database operations:

#### Required Import
```typescript
import { sanitizeText } from "@/utils/sanitization";
```

#### Basic Text Sanitization
```typescript
export async function createEntity(data: EntityFormData): Promise<{ success: boolean; error?: string }> {
    // Always sanitize text inputs with appropriate max length
    const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
    const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";
    
    // Validate sanitized data
    if (!sanitizedName?.trim()) {
        return { success: false, error: "Name is required" };
    }

    const sanitizedData: TablesInsert<"table_name"> = {
        name: sanitizedName.trim(),
        description: sanitizedDescription.trim()
    };
    
    // ...database operations...
}
```

### Available Sanitization Functions

#### `sanitizeText(input: string, maxLength?: number)`
- **Use for**: Names, titles, short descriptions, IDs
- **Default max length**: 1000 characters
- **Strips**: All HTML tags, dangerous scripts, excess whitespace
- **Server-safe**: Works in both server and client environments

```typescript
const sanitizedName = sanitizeText(data.name, 100);
const sanitizedId = sanitizeText(data.id, 50);
```

#### `sanitizeEmail(input: string)`
- **Use for**: Email addresses
- **Features**: Validates email format, converts to lowercase
- **Max length**: 100 characters

```typescript
const sanitizedEmail = sanitizeEmail(data.email);
```

#### `sanitizePhoneNumber(input: string)`
- **Use for**: Phone numbers
- **Features**: Removes non-numeric characters except +, -, (, ), spaces
- **Max length**: 20 characters

```typescript
const sanitizedPhone = sanitizePhoneNumber(data.phone);
```

#### `sanitizeUrls(input: string)`
- **Use for**: URL inputs
- **Features**: Validates URL format, removes dangerous protocols

```typescript
const sanitizedUrl = sanitizeUrls(data.website);
```

### Environment-Aware Behavior

The sanitization utility automatically detects the environment:

- **Server environment**: Uses regex-based sanitization (safe for Node.js)
- **Client environment**: Uses DOMPurify for comprehensive HTML sanitization
- **Fallback**: If DOMPurify fails, automatically falls back to server-safe sanitization

### Sanitization Best Practices

#### 1. Always Sanitize User Input
```typescript
// ✅ Correct
const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";

// ❌ Never do this
const name = data.name; // Raw user input
```

#### 2. Choose Appropriate Max Lengths
```typescript
// ✅ Appropriate lengths for different data types
const sanitizedTitle = sanitizeText(data.title, 100);        // Short titles
const sanitizedDescription = sanitizeText(data.description, 1000); // Descriptions
const sanitizedContent = sanitizeText(data.content, 5000);   // Long content
```

#### 3. Validate After Sanitization
```typescript
const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
if (!sanitizedName?.trim()) {
    return { success: false, error: TEXTS.nameRequired };
}
```

#### 4. Trim After Sanitization
```typescript
const sanitizedData: TablesInsert<"table_name"> = {
    name: sanitizedName.trim(),
    description: sanitizedDescription.trim()
};
```

### Required Pattern for All Server Actions

Every server action that handles user input must follow this pattern:

```typescript
"use server";

import { sanitizeText } from "@/utils/sanitization";
import { type TablesInsert } from "@/types/database.types";

export async function createEntity(data: EntityFormData): Promise<{ success: boolean; error?: string }> {
    try {
        // 1. Sanitize ALL user inputs
        const sanitizedName = data.name ? sanitizeText(data.name, 100) : "";
        const sanitizedDescription = data.description ? sanitizeText(data.description, 1000) : "";
        
        // 2. Validate sanitized data
        if (!sanitizedName?.trim()) {
            return { success: false, error: TEXTS.nameRequired };
        }
        
        // 3. Create sanitized data object
        const sanitizedData: TablesInsert<"table_name"> = {
            name: sanitizedName.trim(),
            description: sanitizedDescription.trim()
        };
        
        // 4. Database operations with sanitized data
        const supabase = await createClientFromRequest();
        const { error } = await supabase.from("table_name").insert(sanitizedData);
        
        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error("Error creating entity:", error);
        return { success: false, error: TEXTS.CREATE_ERROR };
    }
}
```

### Testing Sanitization

All sanitization logic must be tested:

```typescript
describe("createEntity", () => {
    it("sanitizes user input correctly", async () => {
        const maliciousData = {
            name: "<script>alert('xss')</script>Valid Name",
            description: "Normal description with <b>bold</b> text"
        };
        
        const result = await createEntity(maliciousData);
        
        expect(result.success).toBe(true);
        // Verify the data was sanitized in the database
    });
});
```

---

## Form Component Structure

- Use `react-hook-form` for all forms
- Use `LoadingIcon` for loading states
- Use server actions for all mutations
- Handle errors with toast notifications (in Hebrew)
- Use centralized constants for all UI text
- Use typed field components from `@/components/forms/fields/`

#### Example: Form Implementation
```typescript
import { useForm, FormProvider } from "react-hook-form";
import { createEntity } from "@/app/actions/entity-actions";
import { LoadingIcon } from "@/components/common/loading-icon";
import { TEXTS, defaultValues } from "@/lib/entity-constants";

export function EntityForm({ entityId }) {
    // ...form logic...
    return (
        <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)}>
                {/* Form fields */}
            </form>
        </FormProvider>
    );
}
```

---

## Form Utilities

- Use `@/utils/form-utils` for all Option/ID mapping
- Never manually map IDs to Option objects
- Utilities are fully typed and tested

#### Example: Mapping IDs to Options
```typescript
import { mapIdsToOptions, mapIdToOption } from "@/utils/form-utils";
const selected = mapIdToOption(data.audience, audienceOptions, o => o.id, o => o.label);
```

---

## Centralized Constants for Entities

- All UI text, types, and options for an entity must be in a single file in `lib/`
- Export a `TEXTS` object (Hebrew), form value types, default values, and select options
- Use these constants in forms, actions, and tests

#### Example: `lib/contact-constants.ts`
```typescript
export const contactTexts = { ... };
export interface ContactFormValues { ... }
export const defaultValues: ContactFormValues = { ... };
```

---

## Required Patterns & Conventions

- Never use direct Supabase calls in forms
- Always use DB types, never duplicate schema
- **Mandatory data sanitization**: Use `sanitizeText()` for all user inputs in server actions
- All UI text in Hebrew, grouped in `TEXTS` constants
- All errors handled and surfaced in Hebrew
- Use `LoadingIcon` for loading states
- Use typed field components
- Use form utilities for all Option/ID mapping
- Always use `@/` alias for internal imports

---

## Data Flow
1. User submits form → calls server action
2. Server action validates, sanitizes, and processes data
3. Server action returns result
4. Form handles result, shows toast, redirects if needed
5. Use form utilities for all Option/ID transformations

---

## Testing Requirements
- All logic, utilities, and forms must have unit tests
- Use Jest and @testing-library/react
- Test all code paths, including errors and edge cases
- Mock external dependencies
- Place tests close to code or in `tests/`

---

## File Organization
```
app/actions/           # Server actions
components/forms/      # Form components
components/forms/fields/ # Typed field components
lib/                   # Centralized constants
utils/form-utils.ts    # Form utilities
types/database.types.ts # Auto-generated DB types
```

---

## Error Messages
- All user-facing errors in Hebrew
- Log detailed errors server-side
- Never expose sensitive info to clients

---

## Key Principles Summary
1. **Database types first**
2. **Server actions only**
3. **Data sanitization mandatory**
4. **Centralized constants**
5. **Consistent error handling**
6. **Type-safe data transformation**
7. **Comprehensive testing**
8. **Import conventions**

---

This approach ensures consistency, type safety, and automatic synchronization with the database schema across all forms in the application.
