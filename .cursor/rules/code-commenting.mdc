---
description:
globs:
alwaysApply: true
---
# Code Commenting Practices

This set of guidelines outlines the code commenting practices for this project. The focus is on maintaining clean, readable code with minimal comments.

## Comment usage

- Do not add comments to code unless the logic is very complicated
- When logic is complex enough to warrant explanation, use concise comments that explain the "why" rather than the "what"
- Prefer self-documenting code through clear variable and function names over explanatory comments
- Avoid obvious comments that simply restate what the code is doing

## Function documentation

- Do not write documentation comments for functions (no JSDoc, docstrings, etc.)
- Functions should have clear, descriptive names that indicate their purpose
- Function parameters should have intuitive names that make their purpose clear without additional documentation

## Examples

- Avoid this:

    ```typescript
    // Get the user by ID
    function getUser(id: string) {
        return db.users.findUnique({ where: { id } });
    }
    ```

- Prefer this:

    ```typescript
    function getUser(id: string) {
        return db.users.findUnique({ where: { id } });
    }
    ```

- For complex logic, this is acceptable:
    ```typescript
    function calculateTaxRate(income: number, filingStatus: FilingStatus) {
        // Using progressive tax brackets with special handling for joint filers
        // to account for the marriage penalty in certain income ranges
        if (filingStatus === "joint" && income > THRESHOLD) {
            // Complex calculation logic here
        }
        // More code...
    }
    ```
