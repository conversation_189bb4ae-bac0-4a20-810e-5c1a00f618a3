---
description:
globs:
alwaysApply: true
---
# Hebrew Text Constants

This set of guidelines outlines how to handle text strings in the application, specifically for Hebrew text content. The project uses a consistent pattern of defining all UI text as constants in a `TEXTS` object at the top of component files.

## Text constants pattern

- Define all UI text strings in a `TEXTS` constant object at the top of each component file
- Use uppercase for the constant name: `TEXTS`
- Use descriptive camelCase keys that indicate the purpose of the text
- Place all Hebrew text values in this object, never inline in the JSX
- Group related text strings together in the object

## Example implementation

```typescript
const TEXTS = {
    pageTitle: "באנרים",
    newBanner: "באנר חדש",
    loading: "טוען באנרים...",
    errorPrefix: "שגיאה בטעינת הבאנרים:",
    noBanners: "לא נמצאו באנרים",

    // Group related fields together
    text: "טקסט",
    backgroundColor: "צבע רקע",
    textColor: "צבע טקסט",
    enabled: "פעיל האם",

    // Action and button texts
    edit: "עריכ<PERSON>",
    cancel: "בי<PERSON><PERSON><PERSON>",
    confirmDelete: "מח<PERSON>",

    // Messages
    deleteError: "שגיא<PERSON> במחיקת הבאנר",
    deleteSuccess: "הבאנר נמחק בהצלחה"
};
```

## Usage in components

- Reference text constants using the `TEXTS` object throughout the component
- Never hardcode Hebrew strings directly in JSX or component logic
- For dynamic text that includes variables, use template literals with the `TEXTS` constant

```typescript
// Correct usage
<AdminTable
    title={TEXTS.pageTitle}
    loadingText={TEXTS.loading}
    errorPrefix={TEXTS.errorPrefix}
/>

// For dynamic content
const errorMessage = `${TEXTS.errorPrefix} ${error.message}`;
```

## Benefits

- Centralizes all text in one location for each component
- Makes it easier to identify and update text content
- Facilitates future internationalization if needed
- Improves code readability by separating Hebrew text from component logic
- Ensures consistent terminology throughout the application
