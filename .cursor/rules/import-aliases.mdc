---
description:
globs:
alwaysApply: true
---
# Import Path Conventions

This set of guidelines outlines the import path aliasing conventions for the Milgapo project. The project uses TypeScript path aliases to simplify imports and maintain consistent, readable code.

## Import path conventions

- All internal project imports MUST use the `@/` alias prefix
- Never use relative imports (e.g., `../../../components`) for internal project files
- External package imports should use their standard package names without the `@/` prefix
- The path alias is configured in tsconfig.json with `"@/*": ["./*"]`

## Examples

- Correct internal imports:

    ```typescript
    import { AdminTable } from "@/components/table/admin-table";
    import { deleteQuestion } from "@/app/actions/question-actions";
    import { useQuestions } from "@/hooks/use-questions";
    import { formatDate } from "@/utils/formatters";
    ```

- Incorrect internal imports (do not use):

    ```typescript
    import { AdminTable } from "../../../components/table/admin-table";
    import { deleteQuestion } from "app/actions/question-actions";
    ```

- Correct external package imports:
    ```typescript
    import { format } from "date-fns";
    import { useRouter } from "next/navigation";
    import { ArrowRight } from "lucide-react";
    ```

## Benefits

- Improved code readability and maintainability
- Easier refactoring when files are moved
- Consistent import style across the codebase
- Shorter import paths compared to relative imports
- Better IDE support for imports with path aliases
