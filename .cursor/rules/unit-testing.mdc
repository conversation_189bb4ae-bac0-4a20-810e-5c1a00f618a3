---
description: 
globs: 
alwaysApply: true
---
# Unit Testing Requirements

This rule enforces comprehensive unit testing for all logic written in the Milgapo project. Every piece of business logic, utility function, component, and service must have corresponding unit tests.

## Testing framework and setup

- Use Jest as the primary testing framework
- Use @testing-library/react for React component testing
- Use @testing-library/jest-dom for additional DOM matchers
- Place test files in `__tests__` directories or with `.test.ts/.test.tsx` extensions
- Configure proper TypeScript support with jest.config.js and tsconfig.jest.json

## What requires unit tests

### Always test
- **Business logic functions**: Any function that processes data, calculates values, or makes decisions
- **Utility functions**: Helper functions, formatters, validators, and data transformers
- **React components**: All components with logic, state management, or user interactions
- **Custom hooks**: Any custom React hooks with state or side effects
- **API actions**: Server actions, data fetching functions, and database operations
- **Form validation**: All validation logic and form submission handlers
- **State management**: Reducers, state updates, and complex state logic

### Test coverage requirements
- Aim for 80%+ code coverage on all logic files
- 100% coverage on critical business logic and utility functions
- All code paths should be tested, including error conditions
- Test both happy path and edge cases

## Testing patterns and best practices

### Component testing
```typescript
// Example component test structure
describe("ComponentName", () => {
    // Setup and teardown
    afterEach(cleanup);

    // Test rendering and basic functionality
    it("renders with initial state", () => {
        render(<ComponentName />);
        expect(screen.getByText("Expected Text")).toBeInTheDocument();
    });

    // Test user interactions
    it("handles user interactions correctly", async () => {
        const user = userEvent.setup();
        render(<ComponentName />);
        
        await user.click(screen.getByRole("button"));
        expect(screen.getByText("Updated Text")).toBeInTheDocument();
    });

    // Test error states
    it("displays error state when operation fails", () => {
        // Test error scenarios
    });
});
```

### Function testing
```typescript
// Example utility function test
describe("utilityFunction", () => {
    it("processes valid input correctly", () => {
        const result = utilityFunction(validInput);
        expect(result).toEqual(expectedOutput);
    });

    it("handles edge cases", () => {
        expect(utilityFunction(null)).toBe(null);
        expect(utilityFunction(undefined)).toBe(undefined);
        expect(utilityFunction([])).toEqual([]);
    });

    it("throws error for invalid input", () => {
        expect(() => utilityFunction(invalidInput)).toThrow("Expected error message");
    });
});
```

### Mock strategies
- Mock external dependencies (APIs, database calls, third-party libraries)
- Use proper TypeScript types for mocks
- Mock at the module level for consistent behavior
- Avoid over-mocking - test real logic when possible

## File organization
- Place tests in a single directory under the root `tests` under thier relative project path
- Name test files clearly: `component-name.test.tsx`, `utility-function.test.ts`
- Group related tests in describe blocks
- Use descriptive test names that explain the expected behavior

## Testing database types and forms
- When testing components that use database types, create proper mock data
- Use the actual database types from `@/types/database.types` in test mocks
- Test form validation thoroughly, including edge cases
- Test conditional rendering based on different data states

## Continuous integration
- All tests must pass before code can be merged
- Run tests in CI/CD pipeline
- Include test coverage reporting
- Fail builds if coverage drops below thresholds

## When creating new code
- Write tests alongside new features (TDD approach preferred)
- Never commit logic without corresponding tests
- Update tests when modifying existing functionality
- Add tests for bug fixes to prevent regressions

## Testing complex components
For complex components like forms with dynamic questions:
- Test all question types and their rendering
- Test conditional logic and question dependencies
- Test form submission and validation
- Test loading and error states
- Mock complex dependencies appropriately

This rule ensures code quality, prevents regressions, and maintains confidence in the codebase through comprehensive test coverage.
